(()=>{"use strict";var e={868:(e,t,r)=>{r.r(t),r.d(t,{default:()=>n});const n=r.p+"vendor/Genymobile/scrcpy/scrcpy-server.jar"},9369:(e,t,r)=>{r.r(t),r.d(t,{default:()=>n});const n=r.p+"LICENSE"},5977:(e,t,r)=>{r.r(t),r.d(t,{default:()=>n});const n=r.p+"vendor/Genymobile/scrcpy/LICENSE"},2332:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(){}return e.filterTrailingZeroes=function(e){var t=0;return e.reverse().filter((function(e){return t||(t=e)})).reverse()},e.prettyBytes=function(t){for(var r=0;t>=512;)r++,t/=1024;return"".concat(t.toFixed(r?1:0)).concat(e.SUFFIX[r])},e.escapeUdid=function(e){return"udid_"+e.replace(/[. :]/g,"_")},e.parse=function(e,t,r){var n=e.get(t);if(r&&null===n)throw TypeError('Missing required parameter "'.concat(t,'"'));return n},e.parseString=function(e,t,r){var n=e.get(t);if(r&&null===n)throw TypeError('Missing required parameter "'.concat(t,'"'));return n||""},e.parseBoolean=function(e,t,r){var n=this.parse(e,t,r);return"1"===n||!!n&&"true"===n.toString()},e.parseInt=function(e,t,r){var n=this.parse(e,t,r);if(null===n)return 0;var o=parseInt(n,10);return isNaN(o)?0:o},e.parseBooleanEnv=function(e){return"boolean"==typeof e?e:null!=e?(Array.isArray(e)&&(e=e[e.length-1]),"1"===e||"true"===e.toLowerCase()):void 0},e.parseStringEnv=function(e){if(null!=e)return Array.isArray(e)&&(e=e[e.length-1]),e},e.parseIntEnv=function(e){if("number"==typeof e)return e;if(null!=e){Array.isArray(e)&&(e=e[e.length-1]);var t=parseInt(e,10);if(!isNaN(t))return t}},e.utf8ByteArrayToString=function(e){for(var t=[],r=0,n=0;r<e.length;){var o=e[r++];if(o<128)t[n++]=String.fromCharCode(o);else if(o>191&&o<224){var i=e[r++];t[n++]=String.fromCharCode((31&o)<<6|63&i)}else if(o>239&&o<365){var s=((7&o)<<18|(63&(i=e[r++]))<<12|(63&(a=e[r++]))<<6|63&e[r++])-65536;t[n++]=String.fromCharCode(55296+(s>>10)),t[n++]=String.fromCharCode(56320+(1023&s))}else{i=e[r++];var a=e[r++];t[n++]=String.fromCharCode((15&o)<<12|(63&i)<<6|63&a)}}return t.join("")},e.supportsPassive=function(){if("boolean"==typeof e.supportsPassiveValue)return e.supportsPassiveValue;var t=!1;try{var r=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("testPassive",null,r),window.removeEventListener("testPassive",null,r)}catch(e){}return e.supportsPassiveValue=t},e.setImmediate=function(e){Promise.resolve().then(e)},e.SUFFIX={0:"B",1:"KiB",2:"MiB",3:"GiB",4:"TiB"},e.stringToUtf8ByteArray=function(e){for(var t=[],r=0,n=0;n<e.length;n++){var o=e.charCodeAt(n);o<128?t[r++]=o:o<2048?(t[r++]=o>>6|192,t[r++]=63&o|128):55296==(64512&o)&&n+1<e.length&&56320==(64512&e.charCodeAt(n+1))?(o=65536+((1023&o)<<10)+(1023&e.charCodeAt(++n)),t[r++]=o>>18|240,t[r++]=o>>12&63|128,t[r++]=o>>6&63|128,t[r++]=63&o|128):(t[r++]=o>>12|224,t[r++]=o>>6&63|128,t[r++]=63&o|128)}return Uint8Array.from(t)},e}();t.default=r},5994:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CommandControlMessage=t.FilePushState=void 0;var n,o=r(5584),i=r(831),s=o.__importDefault(r(2332));!function(e){e[e.NEW=0]="NEW",e[e.START=1]="START",e[e.APPEND=2]="APPEND",e[e.FINISH=3]="FINISH",e[e.CANCEL=4]="CANCEL"}(n=t.FilePushState||(t.FilePushState={}));var a=function(e){function t(t){var r=e.call(this,t)||this;return r.type=t,r}return o.__extends(t,e),t.createSetVideoSettingsCommand=function(e){var r=e.toBuffer(),n=new t(i.ControlMessage.TYPE_CHANGE_STREAM_PARAMETERS),o=t.PAYLOAD_LENGTH+1,s=Buffer.alloc(o+r.length);return s.writeUInt8(n.type,0),r.forEach((function(e,t){s.writeUInt8(e,t+o)})),n.buffer=s,n},t.createSetClipboardCommand=function(e,r){void 0===r&&(r=!1);var n=new t(i.ControlMessage.TYPE_SET_CLIPBOARD),o=e?s.default.stringToUtf8ByteArray(e):null,a=o?o.length:0,c=0,u=Buffer.alloc(6+a);return c=u.writeInt8(n.type,c),c=u.writeInt8(r?1:0,c),c=u.writeInt32BE(a,c),o&&o.forEach((function(e,t){u.writeUInt8(e,t+c)})),n.buffer=u,n},t.createSetScreenPowerModeCommand=function(e){var r=new t(i.ControlMessage.TYPE_SET_SCREEN_POWER_MODE),n=0,o=Buffer.alloc(2);return n=o.writeInt8(r.type,n),o.writeUInt8(e?1:0,n),r.buffer=o,r},t.createPushFileCommand=function(e){var t=e.id,r=e.fileName,o=e.fileSize,i=e.chunk,s=e.state;if(s===n.START)return this.createPushFileStartCommand(t,r,o);if(s===n.APPEND){if(!i)throw TypeError("Invalid type");return this.createPushFileChunkCommand(t,i)}if(s===n.CANCEL||s===n.FINISH||s===n.NEW)return this.createPushFileOtherCommand(t,s);throw TypeError('Unsupported state: "'.concat(s,'"'))},t.createPushFileStartCommand=function(e,r,o){var a=new t(i.ControlMessage.TYPE_PUSH_FILE),c=s.default.stringToUtf8ByteArray(r),u=c.length,l=t.PAYLOAD_LENGTH,d=Buffer.alloc(l+1+2+1+4+2+u);return d.writeUInt8(a.type,l),l+=1,d.writeInt16BE(e,l),l+=2,d.writeInt8(n.START,l),l+=1,d.writeUInt32BE(o,l),l+=4,d.writeUInt16BE(u,l),l+=2,c.forEach((function(e,t){d.writeUInt8(e,t+l)})),a.buffer=d,a},t.createPushFileChunkCommand=function(e,r){var o=new t(i.ControlMessage.TYPE_PUSH_FILE),s=r.byteLength,a=t.PAYLOAD_LENGTH,c=Buffer.alloc(a+1+2+1+4+s);return c.writeUInt8(o.type,a),a+=1,c.writeInt16BE(e,a),a+=2,c.writeInt8(n.APPEND,a),a+=1,c.writeUInt32BE(s,a),a+=4,Array.from(r).forEach((function(e,t){c.writeUInt8(e,t+a)})),o.buffer=c,o},t.createPushFileOtherCommand=function(e,r){var n=new t(i.ControlMessage.TYPE_PUSH_FILE),o=t.PAYLOAD_LENGTH,s=Buffer.alloc(o+1+2+1);return s.writeUInt8(n.type,o),o+=1,s.writeInt16BE(e,o),o+=2,s.writeInt8(r,o),n.buffer=s,n},t.pushFileCommandFromBuffer=function(e){var r=0,o=e.readUInt8(r);if(r+=1,o!==t.TYPE_PUSH_FILE)throw TypeError('Incorrect type: "'.concat(o,'"'));var i=e.readInt16BE(r);r+=2;var a,c,u,l=e.readInt8(r);if(r+=1,l===n.APPEND){var d=e.readUInt32BE(r);r+=4,a=e.slice(r,r+d)}else if(l===n.START){c=e.readUInt32BE(r),r+=4;var f=e.readUInt16BE(r);r+=2,u=s.default.utf8ByteArrayToString(e.slice(r,r+f))}return{id:i,state:l,chunk:a,fileName:u,fileSize:c}},t.prototype.toBuffer=function(){if(!this.buffer){var e=Buffer.alloc(t.PAYLOAD_LENGTH+1);e.writeUInt8(this.type,0),this.buffer=e}return this.buffer},t.prototype.toString=function(){var e=this.buffer?", buffer=[".concat(this.buffer.join(","),"]"):"";return"CommandControlMessage{action=".concat(this.type).concat(e,"}")},t.PAYLOAD_LENGTH=0,t.Commands=new Map([[i.ControlMessage.TYPE_EXPAND_NOTIFICATION_PANEL,"Expand notifications"],[i.ControlMessage.TYPE_EXPAND_SETTINGS_PANEL,"Expand settings"],[i.ControlMessage.TYPE_COLLAPSE_PANELS,"Collapse panels"],[i.ControlMessage.TYPE_GET_CLIPBOARD,"Get clipboard"],[i.ControlMessage.TYPE_SET_CLIPBOARD,"Set clipboard"],[i.ControlMessage.TYPE_ROTATE_DEVICE,"Rotate device"],[i.ControlMessage.TYPE_CHANGE_STREAM_PARAMETERS,"Change video settings"]]),t}(i.ControlMessage);t.CommandControlMessage=a},831:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ControlMessage=void 0;var r=function(){function e(e){this.type=e}return e.prototype.toBuffer=function(){throw Error("Not implemented")},e.prototype.toString=function(){return"ControlMessage"},e.prototype.toJSON=function(){return{type:this.type}},e.TYPE_KEYCODE=0,e.TYPE_TEXT=1,e.TYPE_TOUCH=2,e.TYPE_SCROLL=3,e.TYPE_BACK_OR_SCREEN_ON=4,e.TYPE_EXPAND_NOTIFICATION_PANEL=5,e.TYPE_EXPAND_SETTINGS_PANEL=6,e.TYPE_COLLAPSE_PANELS=7,e.TYPE_GET_CLIPBOARD=8,e.TYPE_SET_CLIPBOARD=9,e.TYPE_SET_SCREEN_POWER_MODE=10,e.TYPE_ROTATE_DEVICE=11,e.TYPE_CHANGE_STREAM_PARAMETERS=101,e.TYPE_PUSH_FILE=102,e}();t.ControlMessage=r},8461:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.FilePushResponseStatus=void 0,(r=t.FilePushResponseStatus||(t.FilePushResponseStatus={}))[r.NEW_PUSH_ID=1]="NEW_PUSH_ID",r[r.NO_ERROR=0]="NO_ERROR",r[r.ERROR_INVALID_NAME=-1]="ERROR_INVALID_NAME",r[r.ERROR_NO_SPACE=-2]="ERROR_NO_SPACE",r[r.ERROR_FAILED_TO_DELETE=-3]="ERROR_FAILED_TO_DELETE",r[r.ERROR_FAILED_TO_CREATE=-4]="ERROR_FAILED_TO_CREATE",r[r.ERROR_FILE_NOT_FOUND=-5]="ERROR_FILE_NOT_FOUND",r[r.ERROR_FAILED_TO_WRITE=-6]="ERROR_FAILED_TO_WRITE",r[r.ERROR_FILE_IS_BUSY=-7]="ERROR_FILE_IS_BUSY",r[r.ERROR_INVALID_STATE=-8]="ERROR_INVALID_STATE",r[r.ERROR_UNKNOWN_ID=-9]="ERROR_UNKNOWN_ID",r[r.ERROR_NO_FREE_ID=-10]="ERROR_NO_FREE_ID",r[r.ERROR_INCORRECT_SIZE=-11]="ERROR_INCORRECT_SIZE",r[r.ERROR_OTHER=-12]="ERROR_OTHER"},7802:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ACTION=void 0,(r=t.ACTION||(t.ACTION={})).LIST_HOSTS="list-hosts",r.APPL_DEVICE_LIST="appl-device-list",r.GOOG_DEVICE_LIST="goog-device-list",r.MULTIPLEX="multiplex",r.SHELL="shell",r.PROXY_WS="proxy-ws",r.PROXY_ADB="proxy-adb",r.DEVTOOLS="devtools",r.STREAM_SCRCPY="stream",r.STREAM_WS_QVH="stream-qvh",r.STREAM_MJPEG="stream-mjpeg",r.PROXY_WDA="proxy-wda",r.FILE_LISTING="list-files"},6804:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ChannelCode=void 0,(r=t.ChannelCode||(t.ChannelCode={})).FSLS="FSLS",r.HSTS="HSTS",r.SHEL="SHEL",r.GTRC="GTRC",r.ATRC="ATRC",r.WDAP="WDAP",r.QVHS="QVHS"},471:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ARGS_STRING=t.SERVER_PROCESS_NAME=t.LOG_LEVEL=t.SERVER_TYPE=t.SERVER_VERSION=t.SERVER_PORT=t.SERVER_PACKAGE=void 0,t.SERVER_PACKAGE="com.genymobile.scrcpy.Server",t.SERVER_PORT=8886,t.SERVER_VERSION="1.19-ws6",t.SERVER_TYPE="web",t.LOG_LEVEL="ERROR";var r=[t.SERVER_VERSION,t.SERVER_TYPE,t.LOG_LEVEL,t.SERVER_PORT,!0];t.SERVER_PROCESS_NAME="app_process",t.ARGS_STRING="/ ".concat(t.SERVER_PACKAGE," ").concat(r.join(" ")," 2>&1 > /dev/null")},1453:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ControlCenterCommand=void 0;var r=function(){function e(){this.id=-1,this.type="",this.pid=0,this.udid="",this.method=""}return e.fromJSON=function(t){var r=JSON.parse(t);if(!r)throw new Error("Invalid input");var n=new e,o=n.data=r.data;switch(n.id=r.id,n.type=r.type,"string"==typeof o.udid&&(n.udid=o.udid),r.type){case this.KILL_SERVER:if("number"!=typeof o.pid&&o.pid<=0)throw new Error('Invalid "pid" value');return n.pid=o.pid,n;case this.REQUEST_WDA:if("string"!=typeof o.method)throw new Error('Invalid "method" value');return n.method=o.method,n.args=o.args,n;case this.START_SERVER:case this.UPDATE_INTERFACES:case this.CONFIGURE_STREAM:case this.RUN_WDA:return n;default:throw new Error('Unknown command "'.concat(r.command,'"'))}},e.prototype.getType=function(){return this.type},e.prototype.getPid=function(){return this.pid},e.prototype.getUdid=function(){return this.udid},e.prototype.getId=function(){return this.id},e.prototype.getMethod=function(){return this.method},e.prototype.getData=function(){return this.data},e.prototype.getArgs=function(){return this.args},e.KILL_SERVER="kill_server",e.START_SERVER="start_server",e.UPDATE_INTERFACES="update_interfaces",e.CONFIGURE_STREAM="configure_stream",e.RUN_WDA="run-wda",e.REQUEST_WDA="request-wda",e}();t.ControlCenterCommand=r},1233:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.DeviceState=void 0,(r=t.DeviceState||(t.DeviceState={})).DEVICE="device",r.DISCONNECTED="disconnected",r.CONNECTED="Connected"},589:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.MessageType=void 0,(r=t.MessageType||(t.MessageType={})).HOSTS="hosts",r.ERROR="error"},462:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TypedEmitter=void 0;var n=r(6261),o=function(){function e(){this.emitter=new n.EventEmitter}return e.prototype.addEventListener=function(e,t){this.emitter.on(e,t)},e.prototype.removeEventListener=function(e,t){this.emitter.off(e,t)},e.prototype.dispatchEvent=function(e){return this.emitter.emit(e.type,e)},e.prototype.on=function(e,t){this.emitter.on(e,t)},e.prototype.once=function(e,t){this.emitter.once(e,t)},e.prototype.off=function(e,t){this.emitter.off(e,t)},e.prototype.emit=function(e,t){return this.emitter.emit(e,t)},e}();t.TypedEmitter=o},6402:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CloseEventClass=t.CloseEvent2=void 0;var n=r(5584),o=function(e){function t(t,r){var n=void 0===r?{}:r,o=n.code,i=n.reason,s=e.call(this,t)||this;return s.code=o||0,s.reason=i||"",s.wasClean=0===s.code,s}return n.__extends(t,e),t}(r(9958).Event2);t.CloseEvent2=o,t.CloseEventClass="undefined"!=typeof CloseEvent?CloseEvent:o},5524:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ErrorEventClass=t.ErrorEvent2=void 0;var n=r(5584),o=function(e){function t(t,r){var n=void 0===r?{}:r,o=n.colno,i=n.error,s=n.filename,a=n.lineno,c=n.message,u=e.call(this,t)||this;return u.error=i,u.colno=o||0,u.filename=s||"",u.lineno=a||0,u.message=c||"",u}return n.__extends(t,e),t}(r(9958).Event2);t.ErrorEvent2=o,t.ErrorEventClass="undefined"!=typeof ErrorEvent?ErrorEvent:o},9958:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EventClass=t.Event2=void 0;var n=r(5584),o=function(){function e(e,t){void 0===t&&(t={cancelable:!0,bubbles:!0,composed:!1}),this.isTrusted=!0,this.AT_TARGET=0,this.BUBBLING_PHASE=0,this.CAPTURING_PHASE=0,this.NONE=0;var r=n.__assign({},t),o=r.cancelable,i=r.bubbles,s=r.composed;this.cancelable=!!o,this.bubbles=!!i,this.composed=!!s,this.type="".concat(e),this.defaultPrevented=!1,this.timeStamp=Date.now(),this.target=null}return e.prototype.stopImmediatePropagation=function(){},e.prototype.preventDefault=function(){this.defaultPrevented=!0},Object.defineProperty(e.prototype,"currentTarget",{get:function(){return this.target},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"srcElement",{get:function(){return this.target},enumerable:!1,configurable:!0}),e.prototype.composedPath=function(){return this.target?[this.target]:[]},Object.defineProperty(e.prototype,"returnValue",{get:function(){return!this.defaultPrevented},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventPhase",{get:function(){return this.target?Event.AT_TARGET:Event.NONE},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cancelBubble",{get:function(){return!1},set:function(e){e&&this.stopPropagation()},enumerable:!1,configurable:!0}),e.prototype.stopPropagation=function(){},e.prototype.initEvent=function(e,t,r){this.type=e,arguments.length>1&&(this.bubbles=!!t),arguments.length>2&&(this.cancelable=!!r)},e.NONE=0,e.CAPTURING_PHASE=1,e.AT_TARGET=2,e.BUBBLING_PHASE=3,e}();t.Event2=o,t.EventClass="undefined"!=typeof Event?Event:o},9989:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Message=void 0;var n=r(5584),o=r(2701),i=n.__importDefault(r(2332)),s=r(6402),a=function(){function e(e,t,r){this.type=e,this.channelId=t,this.data=r}return e.parse=function(t){var r=Buffer.from(t);return new e(r.readUInt8(0),r.readUInt32LE(1),t.slice(5))},e.fromCloseEvent=function(t,r,n){var s=n?i.default.stringToUtf8ByteArray(n):Buffer.alloc(0),a=Buffer.alloc(6+s.byteLength);return a.writeUInt16LE(r,0),s.byteLength&&(a.writeUInt32LE(s.byteLength,2),a.set(s,6)),new e(o.MessageType.CloseChannel,t,a)},e.createBuffer=function(e,t,r){var n=Buffer.alloc(5+(r?r.byteLength:0));return n.writeUInt8(e,0),n.writeUInt32LE(t,1),(null==r?void 0:r.byteLength)&&n.set(Buffer.from(r),5),n},e.prototype.toCloseEvent=function(){var e,t;if(this.data&&this.data.byteLength){var r=Buffer.from(this.data);if(e=r.readUInt16LE(0),r.byteLength>6){var n=r.readUInt32LE(2);t=i.default.utf8ByteArrayToString(r.slice(6,6+n))}}return new s.CloseEventClass("close",{code:e,reason:t,wasClean:1e3===e})},e.prototype.toBuffer=function(){return e.createBuffer(this.type,this.channelId,this.data)},e}();t.Message=a},69:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MessageEventClass=t.MessageEvent2=void 0;var n=r(5584),o=function(e){function t(t,r){var o=void 0===r?{}:r,i=o.data,s=void 0===i?null:i,a=o.origin,c=void 0===a?"":a,u=o.lastEventId,l=void 0===u?"":u,d=o.source,f=void 0===d?null:d,h=o.ports,p=void 0===h?[]:h,v=e.call(this,t)||this;return v.data=s,v.origin="".concat(c),v.lastEventId="".concat(l),v.source=f,v.ports=n.__spreadArray([],n.__read(p),!1),v}return n.__extends(t,e),t.prototype.initMessageEvent=function(){throw Error("Deprecated method")},t}(r(9958).Event2);t.MessageEvent2=o,t.MessageEventClass="undefined"!=typeof MessageEvent?MessageEvent:o},2701:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.MessageType=void 0,(r=t.MessageType||(t.MessageType={}))[r.CreateChannel=4]="CreateChannel",r[r.CloseChannel=8]="CloseChannel",r[r.RawBinaryData=16]="RawBinaryData",r[r.RawStringData=32]="RawStringData",r[r.Data=64]="Data"},7305:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Multiplexer=void 0;var n=r(5584),o=r(462),i=r(9989),s=r(2701),a=r(9958),c=r(6402),u=r(5524),l=r(69),d=n.__importDefault(r(2332)),f=function(e){function t(r,n,o){void 0===n&&(n=0);var a=e.call(this)||this;a.ws=r,a._id=n,a.CONNECTING=0,a.OPEN=1,a.CLOSING=2,a.CLOSED=3,a.binaryType="blob",a.channels=new Map,a.nextId=0,a.maxId=4294967296,a.storage=[],a.emptyTimerScheduled=!1,a.onclose=null,a.onerror=null,a.onmessage=null,a.onopen=null,a.url="",a.readyState=a.CONNECTING,0===a._id&&(r.binaryType="arraybuffer",a.readyState=a.ws.readyState),a.messageEmitter=o||r;var c=function(e){a.readyState=a.ws.readyState,a.dispatchEvent(e)},f=function(e){a.readyState=a.ws.readyState,a.dispatchEvent(e),a.channels.clear()},h=function(e){a.readyState=a.ws.readyState,a.dispatchEvent(e),a.channels.clear()},p=function(e){var t=e.data,r=i.Message.parse(t);switch(r.type){case s.MessageType.CreateChannel:var n=r.channelId,o=r.data;a.nextId<n&&(a.nextId=n);var c=a._createChannel(n,!1);a.emit("channel",{channel:c,data:o});break;case s.MessageType.RawStringData:var f=a.channels.get(r.channelId);if(f){c=f.channel;var h=new l.MessageEventClass("message",{data:d.default.utf8ByteArrayToString(Buffer.from(r.data)),lastEventId:e.lastEventId,origin:e.origin,source:e.source});c.dispatchEvent(h)}else console.error("Channel with id (".concat(r.channelId,") not found"));break;case s.MessageType.RawBinaryData:var p=a.channels.get(r.channelId);p?(c=p.channel,h=new l.MessageEventClass("message",{data:r.data,lastEventId:e.lastEventId,origin:e.origin,source:e.source}),c.dispatchEvent(h)):console.error("Channel with id (".concat(r.channelId,") not found"));break;case s.MessageType.Data:var v=a.channels.get(r.channelId);if(v){var _=v.emitter;h=new l.MessageEventClass("message",{data:r.data,lastEventId:e.lastEventId,origin:e.origin,source:e.source}),_.dispatchEvent(h)}else console.error("Channel with id (".concat(r.channelId,") not found"));break;case s.MessageType.CloseChannel:var m=a.channels.get(r.channelId);if(m){(c=m.channel).readyState=c.CLOSING;try{c.dispatchEvent(r.toCloseEvent())}finally{c.readyState=c.CLOSED}}else console.error("Channel with id (".concat(r.channelId,") not found"));break;default:var E=new Error("Unsupported message type: ".concat(r.type));a.dispatchEvent(new u.ErrorEventClass("error",{error:E}))}},v=function(){if(a.storage.length){var e=a.ws;e instanceof t?a.storage.forEach((function(t){return e.sendData(t)})):a.storage.forEach((function(t){return e.send(t)})),a.storage.length=0}},_=function(){r.removeEventListener("open",c),r.removeEventListener("error",h),r.removeEventListener("close",f),a.messageEmitter.removeEventListener("message",p),a.off("close",_),a.off("open",v)};return r.addEventListener("open",c),r.addEventListener("error",h),r.addEventListener("close",f),a.messageEmitter.addEventListener("message",p),a.on("close",_),a.on("open",v),a.scheduleEmptyEvent(),a}return n.__extends(t,e),t.wrap=function(e){return new t(e)},Object.defineProperty(t.prototype,"bufferedAmount",{get:function(){return 0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"extensions",{get:function(){return""},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"protocol",{get:function(){return""},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"id",{get:function(){return this._id},enumerable:!1,configurable:!0}),t.prototype.scheduleEmptyEvent=function(){var e=this;this.emptyTimerScheduled||(this.emptyTimerScheduled=!0,Promise.resolve().then((function(){e.emptyTimerScheduled&&(e.emptyTimerScheduled=!1,e.emit("empty",e))})))},t.prototype.clearEmptyEvent=function(){this.emptyTimerScheduled&&(this.emptyTimerScheduled=!1)},t.prototype.close=function(e,r){if(void 0===e&&(e=1e3),this.readyState!==this.CLOSED&&this.readyState!==this.CLOSING)if(this._id){this.readyState=this.CLOSING;try{var n=i.Message.fromCloseEvent(this._id,e,r).toBuffer();this.ws instanceof t?this.ws.sendData(n):this.ws.send(n),this.emit("close",new c.CloseEventClass("close",{code:e,reason:r}))}finally{this.readyState=this.CLOSED}}else this.ws.close(e,r)},t.prototype.send=function(e){this.ws instanceof t&&(e="string"==typeof e?i.Message.createBuffer(s.MessageType.RawStringData,this._id,Buffer.from(e)):i.Message.createBuffer(s.MessageType.RawBinaryData,this._id,Buffer.from(e))),this._send(e)},t.prototype.sendData=function(e){this.ws instanceof t&&(e=i.Message.createBuffer(s.MessageType.Data,this._id,Buffer.from(e))),this._send(e)},t.prototype._send=function(e){var r=this.readyState;if(r===this.OPEN)this.ws instanceof t?this.ws.sendData(e):this.ws.send(e);else{if(r!==this.ws.CONNECTING)throw Error("Socket is already in CLOSING or CLOSED state.");this.storage.push(e)}},t.prototype._createChannel=function(e,r){var n=this,i=new o.TypedEmitter,s=new t(this,e,i);return this.channels.set(e,{channel:s,emitter:i}),r?this.readyState===this.OPEN&&d.default.setImmediate((function(){s.readyState=n.OPEN,s.dispatchEvent(new a.EventClass("open"))})):s.readyState=this.readyState,s.addEventListener("close",(function(){n.channels.delete(e),n.channels.size||n.scheduleEmptyEvent()})),this.clearEmptyEvent(),s},t.prototype.createChannel=function(e){if(this.readyState===this.CLOSING||this.readyState===this.CLOSED)throw Error("Incorrect socket state");var t=this.getNextId(),r=this._createChannel(t,!0);return this.sendData(i.Message.createBuffer(s.MessageType.CreateChannel,t,e)),r},t.prototype.getNextId=function(){for(var e=!1;this.channels.has(++this.nextId);)if(this.nextId===this.maxId){if(e)throw Error("No available id");this.nextId=0,e=!0}return this.nextId},t.prototype.dispatchEvent=function(t){return"close"===t.type&&"function"==typeof this.onclose&&Reflect.apply(this.onclose,this,[t]),"open"===t.type&&"function"==typeof this.onopen&&Reflect.apply(this.onopen,this,[t]),"message"===t.type&&"function"==typeof this.onmessage&&Reflect.apply(this.onmessage,this,[t]),"error"===t.type&&"function"==typeof this.onerror&&Reflect.apply(this.onerror,this,[t]),e.prototype.dispatchEvent.call(this,t)},t}(o.TypedEmitter);t.Multiplexer=f},6348:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Config=void 0;var n=r(5584),o=n.__importStar(r(5949)),i=n.__importStar(r(9896)),s=n.__importStar(r(6928)),a=r(264),c=n.__importDefault(r(2115)),u=8e3,l=/^.+\.(yaml|yml)$/i,d=/^.+\.(json|js)$/i,f=function(){function e(e){this.fullConfig=e}return e.initConfig=function(e){var t=this;void 0===e&&(e={});var r=function(){return s.join(__dirname,"..","config.yaml")},n=u;try{var o=r(),i=c.default.parse(this.readFile(o));n=i.scrcpy_port||u,this.flaskPort=i.flask_port}catch(e){console.warn("无法读取 config.yaml 中的 scrcpy_port，使用默认端口:",r(),u,e)}var a={runGoogTracker:!0,runApplTracker:!1,announceGoogTracker:!0,announceApplTracker:!1,server:[{secure:!1,port:n}],remoteHostList:[]},l=Object.assign({},a,e);return l.server=l.server.map((function(e){return t.parseServerItem(e)})),l},e.parseServerItem=function(e){void 0===e&&(e={});var t=e.secure||!1,r=e.port||(t?443:80),n=e.options,o=e.redirectToSecure||!1;if(t&&!n)throw Error('Must provide "options" for secure server configuration');if(null==n?void 0:n.certPath){if(n.cert)throw Error('Can\'t use "cert" and "certPath" together');n.cert=this.readFile(n.certPath)}if(null==n?void 0:n.keyPath){if(n.key)throw Error('Can\'t use "key" and "keyPath" together');n.key=this.readFile(n.keyPath)}var i={secure:t,port:r,redirectToSecure:o};return void 0!==n&&(i.options=n),"boolean"==typeof o&&(i.redirectToSecure=o),i},e.getInstance=function(){if(!this.instance){var t=o.env[a.EnvName.CONFIG_PATH],r=void 0;if(t)if(t.match(l))r=c.default.parse(this.readFile(t));else{if(!t.match(d))throw Error("Unknown file type: ".concat(t));r=JSON.parse(this.readFile(t))}else r={};var n=this.initConfig(r);this.instance=new e(n)}return this.instance},e.readFile=function(e){var t=e.startsWith("/")?e:s.resolve(o.cwd(),e);if(!i.existsSync(t))throw Error("Can't find file \"".concat(t,'"'));return i.readFileSync(t).toString()},e.prototype.getHostList=function(){if(!this.fullConfig.remoteHostList||!this.fullConfig.remoteHostList.length)return[];var e=[];return this.fullConfig.remoteHostList.forEach((function(t){var r=t.hostname,n=t.port,o=t.pathname,i=t.secure,s=t.useProxy;Array.isArray(t.type)?t.type.forEach((function(t){e.push({hostname:r,port:n,pathname:o,secure:i,useProxy:s,type:t})})):e.push({hostname:r,port:n,pathname:o,secure:i,useProxy:s,type:t.type})})),e},Object.defineProperty(e.prototype,"runLocalGoogTracker",{get:function(){return this.fullConfig.runGoogTracker},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"announceLocalGoogTracker",{get:function(){return this.fullConfig.runGoogTracker},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"runLocalApplTracker",{get:function(){return this.fullConfig.runApplTracker},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"announceLocalApplTracker",{get:function(){return this.fullConfig.runApplTracker},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"servers",{get:function(){return this.fullConfig.server},enumerable:!1,configurable:!0}),e.getFlaskPort=function(){try{return this.flaskPort}catch(e){return console.warn("无法读取 config.yaml 中的 flask_port:",e),5e3}},e}();t.Config=f},264:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.EnvName=void 0,(r=t.EnvName||(t.EnvName={})).CONFIG_PATH="WS_SCRCPY_CONFIG",r.WS_SCRCPY_PATHNAME="WS_SCRCPY_PATHNAME"},2913:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Utils=void 0;var n=r(5584).__importStar(r(857)),o=function(){function e(){}return e.printListeningMsg=function(e,t,r){var o=[],i=[];Object.keys(n.networkInterfaces()).map((function(e){return n.networkInterfaces()[e]})).forEach((function(n){n.forEach((function(n){var s;if("IPv6"===n.family)s=n.scopeid;else{if("IPv4"!==n.family)return;s=void 0}!function(n,s){void 0!==s?0===s&&i.push("".concat(e,"://[").concat(n,"]:").concat(t).concat(r)):o.push("".concat(e,"://").concat(n,":").concat(t).concat(r))}(n.address,s)}))}));var s=[encodeURI("".concat(e,"://").concat(n.hostname(),":").concat(t).concat(r)),encodeURI("".concat(e,"://localhost:").concat(t).concat(r))];console.log("Listening on:\n\t"+s.join(" ")),o.length&&console.log("\t"+o.join(" ")),i.length&&console.log("\t"+i.join(" "))},e}();t.Utils=o},4790:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AdbUtils=void 0;var n=r(5584),o=n.__importStar(r(3399)),i=n.__importStar(r(8611)),s=n.__importStar(r(6928)),a=r(7802),c=r(6900),u=r(7016),l=n.__importDefault(r(6065)),d=n.__importDefault(r(521)),f="http://",h="127.0.0.1:6666",p=/127\.0\.0\.1:6666/,v=function(){function e(){}return e.formatStatsMin=function(e){return n.__awaiter(this,void 0,void 0,(function(){return n.__generator(this,(function(t){return[2,{name:e.name,isDir:e.isDirectory()?1:0,size:e.size,dateModified:e.mtimeMs?e.mtimeMs:e.mtime.getTime()}]}))}))},e.push=function(e,t,r){return n.__awaiter(this,void 0,void 0,(function(){var o,i;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,(o=c.AdbExtended.createClient()).push(e,t,r)];case 1:return i=n.sent(),o.on("error",(function(e){i.emit("error",e)})),[2,i]}}))}))},e.stats=function(e,t,r,o){return void 0===o&&(o=0),n.__awaiter(this,void 0,void 0,(function(){var i;return n.__generator(this,(function(n){switch(n.label){case 0:return!r||r.isSymbolicLink()&&t.endsWith("/")?[4,c.AdbExtended.createClient().stat(e,t)]:[3,2];case 1:r=n.sent(),n.label=2;case 2:if(!r.isSymbolicLink())return[3,7];if(5===o)throw Error("Too deep");t.endsWith("/")||(t+="/"),n.label=3;case 3:return n.trys.push([3,5,,6]),[4,this.stats(e,t,r,o++)];case 4:return r=n.sent(),[3,6];case 5:if("Too deep"===(i=n.sent()).message){if(0===o)return console.error("Symlink is too deep: ".concat(t)),[2,r];throw i}return"ENOENT"!==i.code&&console.error(i.message),[3,6];case 6:case 7:return[2,r]}}))}))},e.readdir=function(t,r){return n.__awaiter(this,void 0,void 0,(function(){var o,i,a=this;return n.__generator(this,(function(u){switch(u.label){case 0:return[4,c.AdbExtended.createClient().readdir(t,r)];case 1:return o=u.sent(),i=o.map((function(o){return n.__awaiter(a,void 0,void 0,(function(){var i,a;return n.__generator(this,(function(n){switch(n.label){case 0:return o.isSymbolicLink()?[4,this.stats(t,s.join(r,o.name))]:[3,2];case 1:i=n.sent(),a=i.mtimeMs?i.mtimeMs:i.mtime.getTime(),o=new l.default(o.name,i.mode,i.size,a/1e3|0),n.label=2;case 2:return[2,e.formatStatsMin(o)]}}))}))})),[2,Promise.all(i)]}}))}))},e.pipePullFile=function(e,t){return n.__awaiter(this,void 0,void 0,(function(){var r;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,c.AdbExtended.createClient().pull(e,t)];case 1:return(r=n.sent()).on("progress",(function(r){console.log("[%s] [%s] Pulled %d bytes so far",e,t,r.bytesTransferred)})),r.on("end",(function(){console.log("[%s] [%s] Pull complete",e,t)})),[2,new Promise((function(e,t){r.on("readable",(function(){e(r)})),r.on("error",(function(e){t(e)}))}))]}}))}))},e.pipeStatToStream=function(e,t,r){return n.__awaiter(this,void 0,void 0,(function(){return n.__generator(this,(function(n){return[2,c.AdbExtended.createClient().pipeStat(e,t,r)]}))}))},e.pipeReadDirToStream=function(e,t,r){return n.__awaiter(this,void 0,void 0,(function(){return n.__generator(this,(function(n){return[2,c.AdbExtended.createClient().pipeReadDir(e,t,r)]}))}))},e.pipePullFileToStream=function(e,t,r){return n.__awaiter(this,void 0,void 0,(function(){var o;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,c.AdbExtended.createClient().pull(e,t)];case 1:return(o=n.sent()).on("data",(function(e){r.send(Buffer.concat([Buffer.from(d.default.DATA,"ascii"),e]))})),[2,new Promise((function(e,t){o.on("end",(function(){r.send(Buffer.from(d.default.DONE,"ascii")),r.close(),e()})),o.on("error",(function(e){t(e)}))}))]}}))}))},e.forward=function(e,t){return n.__awaiter(this,void 0,void 0,(function(){var r,i,s,a,u,l;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,(r=c.AdbExtended.createClient()).listForwards(e)];case 1:return i=n.sent(),(s=i.find((function(r){return r.remote===t&&r.local.startsWith("tcp:")&&r.serial===e})))?(a=s.local,[2,parseInt(a.split("tcp:")[1],10)]):[4,o.getPortPromise()];case 2:return u=n.sent(),l="tcp:".concat(u),[4,r.forward(e,l,t)];case 3:return n.sent(),[2,u]}}))}))},e.getDevtoolsRemoteList=function(e){return n.__awaiter(this,void 0,void 0,(function(){var t,r,o,i;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,c.AdbExtended.createClient().shell(e,"cat /proc/net/unix")];case 1:return t=n.sent(),[4,c.AdbExtended.util.readAll(t)];case 2:return r=n.sent(),o=r.toString().split("\n").filter((function(e){return!!e&&e.includes("devtools_remote")})),i=[],o.forEach((function(e){var t=e.split(" ");if(8===t.length&&"01"===t[5]){var r=t[7].substr(1);i.push(r)}})),[2,i]}}))}))},e.createHttpRequest=function(e,t,r){return n.__awaiter(this,void 0,void 0,(function(){var o,s,a,u;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,c.AdbExtended.createClient().openLocal(e,"localabstract:".concat(t))];case 1:return o=n.sent(),s=new i.ClientRequest(r,{createConnection:function(){return o}}),[4,new Promise((function(e,t){s.on("response",(function(t){e(t)})),s.on("socket",(function(){s.end()})),s.on("error",(function(e){t(e)}))}))];case 2:return a=n.sent(),u="",[2,new Promise((function(e,t){a.on("data",(function(e){u+=e})),a.on("end",(function(){var t=a.statusCode;e({statusCode:t,contentType:a.headers["content-type"],body:u})})),a.on("error",(function(e){t(e)}))}))]}}))}))},e.parseResponse=function(e){if(!e)throw Error("empty response");var t=e.contentType,r=e.statusCode;if("number"!=typeof r||200!==r)throw Error("wrong status code: ".concat(r));if(!(null==t?void 0:t.startsWith("application/json")))throw Error("wrong content type: ".concat(t));return JSON.parse(e.body)},e.patchWebSocketDebuggerUrl=function(e,t,r,n){if(n){var o="localabstract:".concat(r),i=n.replace(/ws:\/\//,"").replace(p,"");return"".concat(e,"/").concat(a.ACTION.PROXY_ADB,"/").concat(t,"/").concat(o,"/").concat(i)}return n},e.getRemoteDevtoolsVersion=function(e,t,r){return n.__awaiter(this,void 0,void 0,(function(){var o,i;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,this.createHttpRequest(t,r,"".concat(f).concat(h,"/json/version"))];case 1:if(!(o=n.sent()))throw Error("Empty response");return(i=this.parseResponse(o)).webSocketDebuggerUrl&&(i.webSocketDebuggerUrl=this.patchWebSocketDebuggerUrl(e,t,r,i.webSocketDebuggerUrl)),[2,i]}}))}))},e.getRemoteDevtoolsTargets=function(e,t,r){return n.__awaiter(this,void 0,void 0,(function(){var o,i,s=this;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,this.createHttpRequest(t,r,"".concat(f).concat(h,"/json"))];case 1:return o=n.sent(),(i=this.parseResponse(o))&&i.length?[2,i.map((function(n){var o=n.devtoolsFrontendUrl,i=n.webSocketDebuggerUrl;if(o){var a=o,c=!1,l=s.patchWebSocketDebuggerUrl(e,t,r,i);a.startsWith("http")||(c=!0,a="".concat(f).concat(h).concat(a));var d=new u.URL(a);d.searchParams.delete("ws");var p=d.toString();p.includes("?")?p+="&":p+="?",p+="ws=".concat(l),c&&(p=p.substr("".concat(f).concat(h).length)),n.devtoolsFrontendUrl=p,n.webSocketDebuggerUrl=l}return n}))]:[2,[]]}}))}))},e.getRemoteDevtoolsInfo=function(e,t){return n.__awaiter(this,void 0,void 0,(function(){var r,o,i,s=this;return n.__generator(this,(function(a){switch(a.label){case 0:return[4,this.getDevtoolsRemoteList(t)];case 1:return(r=a.sent())&&r.length?[3,3]:[4,this.getDeviceName(t)];case 2:return[2,{deviceName:a.sent(),deviceSerial:t,browsers:[]}];case 3:return o=[],r.forEach((function(r){var i=s.getRemoteDevtoolsVersion(e,t,r).catch((function(e){return console.error("getRemoteDevtoolsVersion failed:",e.message),{"Android-Package":"string",Browser:"string","Protocol-Version":"string","User-Agent":"string","V8-Version":"string","WebKit-Version":"string",webSocketDebuggerUrl:"string"}})),a=s.getRemoteDevtoolsTargets(e,t,r).catch((function(e){return console.error("getRemoteDevtoolsTargets failed:",e.message),[]})),c=Promise.all([i,a]).then((function(e){var t=n.__read(e,2),o=t[0],i=t[1];return{socket:r,version:o,targets:i}}));o.push(c)})),o.unshift(this.getDeviceName(t)),[4,Promise.all(o)];case 4:return[2,{deviceName:(i=a.sent()).shift(),deviceSerial:t,browsers:i}]}}))}))},e.getDeviceName=function(e){return n.__awaiter(this,void 0,void 0,(function(){return n.__generator(this,(function(t){switch(t.label){case 0:return[4,c.AdbExtended.createClient().getProperties(e)];case 1:return[2,t.sent()["ro.product.model"]||"Unknown device"]}}))}))},e}();t.AdbUtils=v},1422:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Device=void 0;var n,o=r(5584),i=r(6900),s=r(5317),a=r(462),c=r(6309),u=r(6081);!function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.PIDOF=1]="PIDOF",e[e.GREP_PS=2]="GREP_PS",e[e.GREP_PS_A=3]="GREP_PS_A",e[e.LS_PROC=4]="LS_PROC"}(n||(n={}));var l=function(e){function t(r,o){var s=e.call(this)||this;return s.udid=r,s.connected=!0,s.pidDetectionVariant=n.UNKNOWN,s.spawnServer=!0,s.updateTimeout=t.INITIAL_UPDATE_TIMEOUT,s.updateCount=0,s.lastEmit=0,s.interfacesSort=function(e,t){return e.name>t.name?1:e.name<t.name?-1:0},s.fetchDeviceInfo=function(){if(s.connected){var e=s.getProperties().then((function(e){if(!e)return!1;var t=!1;return u.Properties.forEach((function(r){e[r]!==s.descriptor[r]&&(t=!0,s.descriptor[r]=e[r])})),t&&s.emitUpdate(),!0})),r=s.updateInterfaces().then((function(e){return!!e.length})),n=(s.spawnServer?s.startServer():s.getServerPid()).then((function(){return!(-1===s.descriptor.pid&&s.spawnServer)}));Promise.all([e,r,n]).then((function(e){s.updateTimeoutId=void 0,e.filter((function(e){return!e})).length?s.scheduleInfoUpdate():(s.updateCount=0,s.updateTimeout=t.INITIAL_UPDATE_TIMEOUT)})).catch((function(){s.updateTimeoutId=void 0,s.scheduleInfoUpdate()}))}else s.updateCount=0,s.updateTimeout=t.INITIAL_UPDATE_TIMEOUT,s.updateTimeoutId=void 0,s.emitUpdate()},s.TAG="[".concat(r,"]"),s.descriptor={udid:r,state:o,interfaces:[],pid:-1,"wifi.interface":"","ro.build.version.release":"","ro.build.version.sdk":"","ro.product.manufacturer":"","ro.product.model":"","ro.product.cpu.abi":"","last.update.timestamp":0},s.client=i.AdbExtended.createClient(),s.setState(o),s}return o.__extends(t,e),t.prototype.setState=function(e){"device"===e?(this.connected=!0,this.properties=void 0):this.connected=!1,this.descriptor.state=e,this.emitUpdate(),this.fetchDeviceInfo()},t.prototype.isConnected=function(){return this.connected},t.prototype.getPidOf=function(e){return o.__awaiter(this,void 0,void 0,(function(){var t;return o.__generator(this,(function(r){switch(r.label){case 0:return this.connected?this.pidDetectionVariant!==n.UNKNOWN?[3,2]:(t=this,[4,this.findDetectionVariant()]):[2];case 1:t.pidDetectionVariant=r.sent(),r.label=2;case 2:switch(this.pidDetectionVariant){case n.PIDOF:return[2,this.pidOf(e)];case n.GREP_PS:return[2,this.grepPs(e)];case n.GREP_PS_A:return[2,this.grepPs_A(e)];default:return[2,this.listProc(e)]}return[2]}}))}))},t.prototype.killProcess=function(e){var t="kill ".concat(e);return this.runShellCommandAdbKit(t)},t.prototype.runShellCommandAdb=function(e){return o.__awaiter(this,void 0,void 0,(function(){var t=this;return o.__generator(this,(function(r){return[2,new Promise((function(r,n){var o=["-s","".concat(t.udid),"shell",e],i=(0,s.spawn)("adb",o,{stdio:["ignore","pipe","pipe"]}),a="";i.stdout.on("data",(function(e){a+=e.toString(),console.log(t.TAG,"stdout: ".concat(e.toString().replace(/\n$/,"")))})),i.stderr.on("data",(function(e){console.error(t.TAG,"stderr: ".concat(e))})),i.on("error",(function(e){console.error(t.TAG,"failed to spawn adb process.\n".concat(e.stack)),n(e)})),i.on("close",(function(e){console.log(t.TAG,"adb process (".concat(o.join(" "),") exited with code ").concat(e)),r(a)}))}))]}))}))},t.prototype.runShellCommandAdbKit=function(e){return o.__awaiter(this,void 0,void 0,(function(){return o.__generator(this,(function(t){return[2,this.client.shell(this.udid,e).then(i.AdbExtended.util.readAll).then((function(e){return e.toString().trim()}))]}))}))},t.prototype.push=function(e,t){return o.__awaiter(this,void 0,void 0,(function(){return o.__generator(this,(function(r){return[2,this.client.push(this.udid,e,t)]}))}))},t.prototype.getProperties=function(){return o.__awaiter(this,void 0,void 0,(function(){var e;return o.__generator(this,(function(t){switch(t.label){case 0:return this.properties?[2,this.properties]:this.connected?(e=this,[4,this.client.getProperties(this.udid)]):[2];case 1:return e.properties=t.sent(),[2,this.properties]}}))}))},t.prototype.getNetInterfaces=function(){return o.__awaiter(this,void 0,void 0,(function(){var e;return o.__generator(this,(function(t){switch(t.label){case 0:return this.connected?(e=[],[4,this.runShellCommandAdbKit("ip -4 -f inet -o a | grep 'scope global'")]):[2,[]];case 1:return t.sent().split("\n").filter((function(e){return!!e})).forEach((function(t){var r=t.split(" ").filter((function(e){return!!e})),n=r[1],o=r[3].split("/")[0];e.push({name:n,ipv4:o})})),[2,e.sort(this.interfacesSort)]}}))}))},t.prototype.pidOf=function(e){return o.__awaiter(this,void 0,void 0,(function(){return o.__generator(this,(function(t){return[2,this.runShellCommandAdbKit("pidof ".concat(e)).then((function(e){return e.split(" ").map((function(e){return parseInt(e,10)})).filter((function(e){return!isNaN(e)}))})).catch((function(){return[]}))]}))}))},t.prototype.filterPsOutput=function(e,t){var r=[];return t.split("\n").map((function(t){var n=t.trim().split(" ").filter((function(e){return e.length}));if(n[n.length-1]===e){var o=parseInt(n[1],10);isNaN(o)||r.push(o)}})),r},t.prototype.grepPs_A=function(e){return o.__awaiter(this,void 0,void 0,(function(){var t=this;return o.__generator(this,(function(r){return[2,this.runShellCommandAdbKit("ps -A | grep ".concat(e)).then((function(r){return t.filterPsOutput(e,r)})).catch((function(){return[]}))]}))}))},t.prototype.grepPs=function(e){return o.__awaiter(this,void 0,void 0,(function(){var t=this;return o.__generator(this,(function(r){return[2,this.runShellCommandAdbKit("ps | grep ".concat(e)).then((function(r){return t.filterPsOutput(e,r)})).catch((function(){return[]}))]}))}))},t.prototype.listProc=function(e){return o.__awaiter(this,void 0,void 0,(function(){var t,r,n;return o.__generator(this,(function(o){switch(o.label){case 0:return[4,this.runShellCommandAdbKit("for L in `".concat("find /proc -maxdepth 2 -name cmdline  2>/dev/null","`; do grep -sae '^").concat(e,"' $L 2>&1 >/dev/null && echo $L; done"))];case 1:return t=o.sent(),r=/\/proc\/([0-9]+)\/cmdline/,n=[],t.split("\n").map((function(e){var t=e.trim().match(r);t&&n.push(parseInt(t[1],10))})),[2,n]}}))}))},t.prototype.executedWithoutError=function(e){return o.__awaiter(this,void 0,void 0,(function(){return o.__generator(this,(function(t){return[2,this.runShellCommandAdbKit(e).then((function(e){return 0===parseInt(e,10)})).catch((function(){return!1}))]}))}))},t.prototype.hasPs=function(){return o.__awaiter(this,void 0,void 0,(function(){return o.__generator(this,(function(e){return[2,this.executedWithoutError("ps | grep init 2>&1 >/dev/null; echo $?")]}))}))},t.prototype.hasPs_A=function(){return o.__awaiter(this,void 0,void 0,(function(){return o.__generator(this,(function(e){return[2,this.executedWithoutError("ps -A | grep init 2>&1 >/dev/null; echo $?")]}))}))},t.prototype.hasPidOf=function(){return o.__awaiter(this,void 0,void 0,(function(){return o.__generator(this,(function(e){switch(e.label){case 0:return[4,this.executedWithoutError("which pidof 2>&1 >/dev/null && echo $?")];case 1:return e.sent()?[2,this.runShellCommandAdbKit("echo $PPID; pidof init").then((function(e){var t=e.split("\n").filter((function(e){return e.length}));if(t.length<2)return!1;var r=t[0].replace("\r",""),n=t[1].split(" ");return!n.includes(r)&&n.includes("1")})).catch((function(){return!1}))]:[2,!1]}}))}))},t.prototype.findDetectionVariant=function(){return o.__awaiter(this,void 0,void 0,(function(){return o.__generator(this,(function(e){switch(e.label){case 0:return[4,this.hasPidOf()];case 1:return e.sent()?[2,n.PIDOF]:[4,this.hasPs_A()];case 2:return e.sent()?[2,n.GREP_PS_A]:[4,this.hasPs()];case 3:return e.sent()?[2,n.GREP_PS]:[2,n.LS_PROC]}}))}))},t.prototype.scheduleInfoUpdate=function(){this.updateTimeoutId||(++this.updateCount>t.MAX_UPDATES_COUNT?console.error(this.TAG,"The maximum number of attempts to fetch device info has been reached."):(this.updateTimeoutId=setTimeout(this.fetchDeviceInfo,this.updateTimeout),this.updateTimeout*=2))},t.prototype.emitUpdate=function(e){var t=this;void 0===e&&(e=!0);var r=Date.now(),n=r-this.lastEmit;if(e&&(this.descriptor["last.update.timestamp"]=r),n>300)return this.lastEmit=r,void this.emit("update",this);this.throttleTimeoutId||(this.throttleTimeoutId=setTimeout((function(){delete t.throttleTimeoutId,t.emitUpdate(!1)}),300-n))},t.prototype.getServerPid=function(){return o.__awaiter(this,void 0,void 0,(function(){var e,t;return o.__generator(this,(function(r){switch(r.label){case 0:return[4,c.ScrcpyServer.getServerPid(this)];case 1:return e=r.sent(),t=Array.isArray(e)&&e.length?e[0]:-1,this.descriptor.pid!==t&&(this.descriptor.pid=t,this.emitUpdate()),-1!==t?[2,t]:[2]}}))}))},t.prototype.updateInterfaces=function(){return o.__awaiter(this,void 0,void 0,(function(){var e=this;return o.__generator(this,(function(t){return[2,this.getNetInterfaces().then((function(t){var r=!1,n=e.descriptor.interfaces;return n.length!==t.length?r=!0:n.forEach((function(e,n){e.name===t[n].name&&e.ipv4===t[n].ipv4||(r=!0)})),r&&(e.descriptor.interfaces=t,e.emitUpdate()),e.descriptor.interfaces}))]}))}))},t.prototype.killServer=function(e){return o.__awaiter(this,void 0,void 0,(function(){var t,r,n;return o.__generator(this,(function(o){switch(o.label){case 0:return this.spawnServer=!1,[4,this.getServerPid()];case 1:if("number"!=typeof(t=o.sent()))return[2];t!==e&&console.error(this.TAG,"Requested to kill server with PID ".concat(e,". Real server PID is ").concat(t,".")),o.label=2;case 2:return o.trys.push([2,4,,5]),[4,this.killProcess(t)];case 3:return(r=o.sent())&&console.log(this.TAG,'kill server: "'.concat(r,'"')),this.descriptor.pid=-1,this.emitUpdate(),[3,5];case 4:throw n=o.sent(),console.error(this.TAG,"Error: ".concat(n.message)),n;case 5:return[2]}}))}))},t.prototype.startServer=function(){return o.__awaiter(this,void 0,void 0,(function(){var e,t,r;return o.__generator(this,(function(n){switch(n.label){case 0:return this.spawnServer=!0,[4,this.getServerPid()];case 1:if("number"==typeof(e=n.sent()))return[2,e];n.label=2;case 2:return n.trys.push([2,4,,5]),[4,c.ScrcpyServer.run(this)];case 3:return(t=n.sent())&&console.log(this.TAG,'start server: "'.concat(t,'"')),[2,this.getServerPid()];case 4:throw r=n.sent(),console.error(this.TAG,"Error: ".concat(r.message)),r;case 5:return[2]}}))}))},t.INITIAL_UPDATE_TIMEOUT=1500,t.MAX_UPDATES_COUNT=7,t}(a.TypedEmitter);t.Device=l},6081:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Properties=void 0,t.Properties=["ro.product.cpu.abi","ro.product.manufacturer","ro.product.model","ro.build.version.release","ro.build.version.sdk","wifi.interface"]},6309:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ScrcpyServer=void 0;var n=r(5584);r(868),r(5977);var o=r(471),i=n.__importDefault(r(6928)),s=r(2363),a="/data/local/tmp/",c=i.default.join(__dirname,"vendor/Genymobile/scrcpy"),u="scrcpy-server.jar",l="CLASSPATH=".concat(a).concat(u," nohup app_process ").concat(o.ARGS_STRING),d=function(){function e(){}return e.copyServer=function(e){return n.__awaiter(this,void 0,void 0,(function(){var t,r;return n.__generator(this,(function(n){return t=i.default.join(c,u),r=a+u,[2,e.push(t,r)]}))}))},e.waitForServerPid=function(t,r){return n.__awaiter(this,void 0,void 0,(function(){var o,i,s,a,c,u,l,d,f,h=this;return n.__generator(this,(function(n){switch(n.label){case 0:return o=r.tryCounter,i=r.processExited,s=r.lookPidFile,i?[2]:(a=500+100*o,s?(c=e.PID_FILE_PATH,[4,t.runShellCommandAdbKit("test -f ".concat(c," && cat ").concat(c))]):[3,4]);case 1:return(u=n.sent()).trim()?!(l=parseInt(u,10))||isNaN(l)?[3,3]:[4,this.getServerPid(t)]:[3,3];case 2:if(null==(d=n.sent())?void 0:d.includes(l))return[2,d];r.lookPidFile=!1,n.label=3;case 3:return[3,6];case 4:return[4,this.getServerPid(t)];case 5:if(f=n.sent(),Array.isArray(f)&&f.length)return[2,f];n.label=6;case 6:if(++r.tryCounter>5)throw new Error("Failed to start server");return[2,new Promise((function(e){setTimeout((function(){e(h.waitForServerPid(t,r))}),a)}))]}}))}))},e.getServerPid=function(e){return n.__awaiter(this,void 0,void 0,(function(){var t,r,i;return n.__generator(this,(function(n){switch(n.label){case 0:return e.isConnected()?[4,e.getPidOf(o.SERVER_PROCESS_NAME)]:[2];case 1:return t=n.sent(),Array.isArray(t)&&t.length?(r=[],i=t.map((function(t){return e.runShellCommandAdbKit("cat /proc/".concat(t,"/cmdline")).then((function(n){var i=n.split("\0");if(i.length&&i[0]===o.SERVER_PROCESS_NAME){for(var a=i[0];i.length&&a!==o.SERVER_PACKAGE;)i.shift(),a=i[0];if(!(i.length<3)){var c=i[1];if(c===o.SERVER_VERSION)r.push(t);else{var u=new s.ServerVersion(c);u.isCompatible()&&new s.ServerVersion(o.SERVER_VERSION).gt(u)&&(console.log(e.TAG,"Found old server version running (PID: ".concat(t,", Version: ").concat(c,")")),console.log(e.TAG,"Perform kill now"),e.killProcess(t))}}}}))})),[4,Promise.all(i)]):[2];case 2:return n.sent(),[2,r]}}))}))},e.run=function(e){return n.__awaiter(this,void 0,void 0,(function(){var t,r,o;return n.__generator(this,(function(n){switch(n.label){case 0:return e.isConnected()?[4,this.getServerPid(e)]:[2];case 1:return t=n.sent(),Array.isArray(t)&&t.length?[2,t]:[4,this.copyServer(e)];case 2:return n.sent(),r={tryCounter:0,processExited:!1,lookPidFile:!0},(o=e.runShellCommandAdb(l)).then((function(t){e.isConnected()&&console.log(e.TAG,"Server exited:",t)})).catch((function(t){console.log(e.TAG,"Error:",t.message)})).finally((function(){r.processExited=!0})),[4,Promise.race([o,this.waitForServerPid(e,r)])];case 3:return t=n.sent(),Array.isArray(t)&&t.length?[2,t]:[2]}}))}))},e.PID_FILE_PATH="/data/local/tmp/ws_scrcpy.pid",e}();t.ScrcpyServer=d},2363:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ServerVersion=void 0;var r=function(){function e(e){this.versionString=e,this.parts=[];var t=e.split("-"),r=t.shift();this.suffix=t.join("-"),r&&(this.parts=r.split(".")),this.compatible=this.suffix.startsWith("ws")&&this.parts.length>=2}return e.prototype.equals=function(e){var t="string"==typeof e?e:e.versionString;return this.versionString===t},e.prototype.gt=function(t){if(this.equals(t))return!1;"string"==typeof t&&(t=new e(t));for(var r=Math.min(this.parts.length,t.parts.length),n=0;n<r;n++)if(this.parts[n]>t.parts[n])return!0;return this.parts.length>t.parts.length||!(this.parts.length<t.parts.length)&&this.suffix>t.suffix},e.prototype.isCompatible=function(){return this.compatible},e}();t.ServerVersion=r},2524:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExtendedClient=void 0;var n=r(5584),o=n.__importDefault(r(9978)),i=r(2122),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n.__extends(t,e),t.prototype.pipeSyncService=function(e){return n.__awaiter(this,void 0,void 0,(function(){var t;return n.__generator(this,(function(r){switch(r.label){case 0:return[4,this.transport(e)];case 1:return t=r.sent(),[2,new i.SyncCommand(t).execute()]}}))}))},t.prototype.pipeReadDir=function(e,t,r){return n.__awaiter(this,void 0,void 0,(function(){var o;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,this.pipeSyncService(e)];case 1:return[2,(o=n.sent()).pipeReadDir(t,r).then((function(){o.end()}))]}}))}))},t.prototype.pipePull=function(e,t,r){return n.__awaiter(this,void 0,void 0,(function(){var o;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,this.pipeSyncService(e)];case 1:return[2,(o=n.sent()).pipePull(t,r).then((function(){o.end()}))]}}))}))},t.prototype.pipeStat=function(e,t,r){return n.__awaiter(this,void 0,void 0,(function(){var o;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,this.pipeSyncService(e)];case 1:return[2,(o=n.sent()).pipeStat(t,r).then((function(){o.end()}))]}}))}))},t}(o.default);t.ExtendedClient=s},1756:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExtendedSync=void 0;var n=r(5584),o=n.__importDefault(r(521)),i=function(){function e(e){this.connection=e,this.connection=e,this.parser=this.connection.parser}return e.prototype.pipeReadDir=function(e,t){return n.__awaiter(this,void 0,void 0,(function(){var r,i=this;return n.__generator(this,(function(s){return r=function(){return n.__awaiter(i,void 0,void 0,(function(){var e,i,s,a;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,this.parser.readAscii(4)];case 1:switch(e=n.sent()){case o.default.DENT:return[3,2];case o.default.DONE:return[3,5];case o.default.FAIL:return[3,7]}return[3,8];case 2:case 5:return[4,this.parser.readBytes(16)];case 3:return i=n.sent(),s=i.readUInt32LE(12),[4,this.parser.readBytes(s)];case 4:return a=n.sent(),t.send(Buffer.concat([Buffer.from(e),i,a])),[2,r()];case 6:return n.sent(),t.close(0),[2];case 7:return[2,this._readError(t)];case 8:return[2,this.parser.unexpected(e,"DENT, DONE or FAIL")]}}))}))},this._sendCommandWithArg(o.default.LIST,e),[2,r()]}))}))},e.prototype.pipePull=function(e,t){return this._sendCommandWithArg(o.default.RECV,"".concat(e)),this._readData(t)},e.prototype.pipeStat=function(e,t){return n.__awaiter(this,void 0,void 0,(function(){var r,i;return n.__generator(this,(function(n){switch(n.label){case 0:return this._sendCommandWithArg(o.default.STAT,"".concat(e)),[4,this.parser.readAscii(4)];case 1:switch(r=n.sent()){case o.default.STAT:return[3,2];case o.default.FAIL:return[3,4]}return[3,5];case 2:return[4,this.parser.readBytes(12)];case 3:return i=n.sent(),t.send(Buffer.concat([Buffer.from(r),i])),t.close(1e3),[3,6];case 4:return[2,this._readError(t)];case 5:return[2,this.parser.unexpected(r,"STAT or FAIL")];case 6:return[2]}}))}))},e.prototype._readData=function(e){var t=this,r=function(){return n.__awaiter(t,void 0,void 0,(function(){var t,i,s,a;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,this.parser.readAscii(4)];case 1:switch(t=n.sent()){case o.default.DATA:return[3,2];case o.default.DONE:return[3,5];case o.default.FAIL:return[3,7]}return[3,8];case 2:case 5:return[4,this.parser.readBytes(4)];case 3:return i=n.sent(),s=i.readUInt32LE(0),[4,this.parser.readBytes(s)];case 4:return a=n.sent(),e.send(Buffer.concat([Buffer.from(t),a])),[2,r()];case 6:return n.sent(),e.close(1e3),[2];case 7:return[2,this._readError(e)];case 8:return[2,this.parser.unexpected(t,"DATA, DONE or FAIL")]}}))}))};return r()},e.prototype._sendCommandWithArg=function(e,t){var r=Buffer.byteLength(t,"utf-8"),n=Buffer.alloc(e.length+4+r),o=0;return n.write(e,o,e.length),o+=e.length,n.writeUInt32LE(r,o),o+=4,n.write(t,o),this.connection.write(n)},e.prototype._readError=function(e){return n.__awaiter(this,void 0,void 0,(function(){var t,r;return n.__generator(this,(function(n){switch(n.label){case 0:return[4,this.parser.readBytes(4)];case 1:return t=n.sent(),[4,this.parser.readAscii(t.readUInt32LE(0))];case 2:return r=n.sent(),e.close(4e3,r),[4,this.parser.end()];case 3:return n.sent(),[2]}}))}))},e.prototype.end=function(){return this.connection.end(),this},e}();t.ExtendedSync=i},2122:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SyncCommand=void 0;var n=r(5584),o=n.__importDefault(r(521)),i=n.__importDefault(r(3014)),s=r(1756),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n.__extends(t,e),t.prototype.execute=function(){var e=this;return this._send("sync:"),this.parser.readAscii(4).then((function(t){switch(t){case o.default.OKAY:return new s.ExtendedSync(e.connection);case o.default.FAIL:return e.parser.readError();default:return e.parser.unexpected(t,"OKAY or FAIL")}}))},t}(i.default);t.SyncCommand=a},6900:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AdbExtended=void 0;var n=r(5584),o=n.__importDefault(r(3216)),i=r(2524),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n.__extends(t,e),t.createClient=function(e){void 0===e&&(e={});var t={bin:e.bin,host:e.host||process.env.ADB_HOST||"127.0.0.1",port:e.port||0};if(!t.port){var r=parseInt(process.env.ADB_PORT||"",10);isNaN(r)?t.port=5037:t.port=r}return new i.ExtendedClient(t)},t}(o.default);t.AdbExtended=s},1100:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FilePushReader=void 0;var n,o=r(5584),i=r(5994),s=r(8461),a=r(3857),c=r(6900);!function(e){e[e.INITIAL=0]="INITIAL",e[e.NEW=1]="NEW",e[e.START=2]="START",e[e.APPEND=3]="APPEND",e[e.FINISH=4]="FINISH",e[e.CANCEL=5]="CANCEL"}(n||(n={}));var u=function(){function e(t,r){var a=this;this.serial=t,this.channel=r,this.fileName="",this.fileSize=0,this.pushId=-1,this.state=n.INITIAL,this.createStreamPromiseMap=new Map,this.disposed=!1,this.onMessage=function(t){return o.__awaiter(a,void 0,void 0,(function(){var r,a,c,u,l,d,f,h;return o.__generator(this,(function(o){switch(o.label){case 0:switch(r=i.CommandControlMessage.pushFileCommandFromBuffer(Buffer.from(t.data)),a=r.id,r.state){case i.FilePushState.NEW:return[3,1];case i.FilePushState.START:return[3,2];case i.FilePushState.APPEND:return[3,3];case i.FilePushState.FINISH:return[3,7];case i.FilePushState.CANCEL:return[3,10]}return[3,11];case 1:return this.state!==n.INITIAL?(this.closeWithError(s.FilePushResponseStatus.ERROR_INVALID_STATE),[2]):(this.state=n.NEW,this.pushId=e.getNextId(),this.sendResponse(s.FilePushResponseStatus.NEW_PUSH_ID),[3,12]);case 2:return this.verifyId(a)?this.state!==n.NEW?(this.closeWithError(s.FilePushResponseStatus.ERROR_INVALID_STATE),[2]):(c=r.fileName,u=r.fileSize,c?u?(this.fileName=c,this.fileSize=u,this.state=n.START,this.sendResponse(s.FilePushResponseStatus.NO_ERROR),[3,12]):(this.closeWithError(s.FilePushResponseStatus.ERROR_INCORRECT_SIZE),[2]):(this.closeWithError(s.FilePushResponseStatus.ERROR_INVALID_NAME),[2])):[2];case 3:return this.verifyId(a)?(l=r.chunk)&&l.length?this.state!==n.START?[3,5]:(d=this.createStream(l),this.createStreamPromiseMap.set(a,d),[4,d]):(this.closeWithError(s.FilePushResponseStatus.ERROR_INCORRECT_SIZE),[2]):[2];case 4:return o.sent(),this.createStreamPromiseMap.delete(a),this.state=n.APPEND,[2];case 5:if(this.state!==n.APPEND)return this.closeWithError(s.FilePushResponseStatus.ERROR_INVALID_STATE),[2];o.label=6;case 6:return null===(h=this.readStream)||void 0===h||h.push(l),[3,12];case 7:return this.verifyId(a)?(f=this.createStreamPromiseMap.get(a))?[4,f]:[3,9]:[2];case 8:o.sent(),o.label=9;case 9:return this.state!==n.APPEND?(this.closeWithError(s.FilePushResponseStatus.ERROR_INVALID_STATE),[2]):(this.state=n.FINISH,this.readStream&&(this.readStream.push(null),this.readStream.close(),this.readStream=void 0),[3,12]);case 10:return this.verifyId(a)?(this.state=n.CANCEL,this.readStream&&(this.readStream.push(null),this.readStream.close(),this.readStream=void 0),this.pushTransfer&&this.pushTransfer.cancel(),[3,12]):[2];case 11:if(!this.verifyId(a))return[2];this.closeWithError(s.FilePushResponseStatus.ERROR_INVALID_STATE),o.label=12;case 12:return[2]}}))}))},this.onClose=function(){a.release()},this.onPushError=function(e){a.closeWithError(s.FilePushResponseStatus.ERROR_OTHER,e.message)},this.onPushEnd=function(){a.state===n.FINISH?(a.sendResponse(s.FilePushResponseStatus.NO_ERROR),a.release()):a.closeWithError(s.FilePushResponseStatus.ERROR_INVALID_STATE)},this.onPushCancel=function(){a.state===n.CANCEL?(a.sendResponse(s.FilePushResponseStatus.NO_ERROR),a.release()):a.closeWithError(s.FilePushResponseStatus.ERROR_INVALID_STATE)},r.addEventListener("message",this.onMessage),r.addEventListener("close",this.onClose)}return e.handle=function(t,r){return new e(t,r)},e.getNextId=function(){return this.fileId++,this.fileId>this.maxId&&(this.fileId=1),this.fileId},e.createResponse=function(e,t){var r=Buffer.alloc(3),n=0;return n=r.writeInt16BE(e,n),r.writeInt8(t,n),r},e.prototype.verifyId=function(e){return e===this.pushId||(this.closeWithError(s.FilePushResponseStatus.ERROR_UNKNOWN_ID),!1)},e.prototype.sendResponse=function(t){this.channel.readyState!==this.channel.CLOSING&&this.channel.readyState!==this.channel.CLOSED&&this.channel.send(e.createResponse(this.pushId,t))},e.prototype.closeWithError=function(e,t){this.channel.removeEventListener("message",this.onMessage),this.channel.removeEventListener("close",this.onClose),this.channel.close(4e3-e,t),this.release()},e.prototype.createStream=function(e){return o.__awaiter(this,void 0,void 0,(function(){var t,r,n,i=this;return o.__generator(this,(function(o){switch(o.label){case 0:return t={construct:function(e){e(null)},read:function(){i.readStream&&(i.readStream.bytesRead>i.fileSize&&console.error("bytesRead (".concat(i.readStream.bytesRead,") > fileSize (").concat(i.fileSize,")")),i.sendResponse(s.FilePushResponseStatus.NO_ERROR))}},this.readStream=new a.ReadStream(this.fileName,t),this.readStream.push(e),r=c.AdbExtended.createClient(),n=this,[4,r.push(this.serial,this.readStream,this.fileName)];case 1:return n.pushTransfer=o.sent(),r.on("error",(function(e){console.error("Client error (".concat(i.serial," | ").concat(i.fileName,"):"),e.message),i.closeWithError(s.FilePushResponseStatus.ERROR_OTHER,e.message)})),this.pushTransfer.on("error",this.onPushError),this.pushTransfer.on("end",this.onPushEnd),this.pushTransfer.on("cancel",this.onPushCancel),[2]}}))}))},e.prototype.release=function(){if(!this.disposed){this.disposed=!0,this.createStreamPromiseMap.clear(),this.readStream&&(this.readStream.close(),this.readStream=void 0),this.pushTransfer&&(this.pushTransfer.off("error",this.onPushError),this.pushTransfer.off("end",this.onPushEnd),this.pushTransfer.off("cancel",this.onPushCancel),this.pushTransfer=void 0),this.channel.removeEventListener("message",this.onMessage),this.channel.removeEventListener("close",this.onClose);var e=this.channel,t=e.readyState,r=e.CLOSED,n=e.CLOSING;t!==r&&t!==n&&this.channel.close()}},e.fileId=1,e.maxId=4294967295,e}();t.FilePushReader=u},3857:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ReadStream=void 0;var n=r(5584),o=function(e){function t(t,r){var n=e.call(this,r)||this;return n._path=t,n._bytesRead=0,n}return n.__extends(t,e),Object.defineProperty(t.prototype,"bytesRead",{get:function(){return this._bytesRead},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"path",{get:function(){return this._path},enumerable:!1,configurable:!0}),t.prototype.push=function(t,r){return t&&(this._bytesRead+=t.length),e.prototype.push.call(this,t,r)},t.prototype.close=function(){this.destroy()},t}(r(2203).Readable);t.ReadStream=o},4113:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DeviceTracker=void 0;var n=r(5584),o=r(6237),i=r(1453),s=r(4619),a=r(7802),c=r(6804),u=function(e){function t(r){var n=e.call(this,r)||this;return n.adt=s.ControlCenter.getInstance(),n.sendDeviceMessage=function(e){var t={device:e,id:n.id,name:n.adt.getName()};n.sendMessage({id:-1,type:"device",data:t})},n.buildAndSendMessage=function(e){var t={list:e,id:n.id,name:n.adt.getName()};n.sendMessage({id:-1,type:"devicelist",data:t})},n.id=n.adt.getId(),n.adt.init().then((function(){n.adt.on("device",n.sendDeviceMessage),n.buildAndSendMessage(n.adt.getDevices())})).catch((function(e){console.error("[".concat(t.TAG,"] Error: ").concat(e.message))})),n}return n.__extends(t,e),t.processChannel=function(e,r){if(r===c.ChannelCode.GTRC)return new t(e)},t.processRequest=function(e,r){if(r.action===a.ACTION.GOOG_DEVICE_LIST)return new t(e)},t.prototype.onSocketMessage=function(e){var r;try{r=i.ControlCenterCommand.fromJSON(e.data.toString())}catch(r){return void console.error("[".concat(t.TAG,"], Received message: ").concat(e.data,". Error: ").concat(null==r?void 0:r.message))}this.adt.runCommand(r).catch((function(r){console.error("[".concat(t.TAG,"], Received message: ").concat(e.data,". Error: ").concat(r.message))}))},t.prototype.release=function(){e.prototype.release.call(this),this.adt.off("device",this.sendDeviceMessage)},t.TAG="DeviceTracker",t.type="android",t}(o.Mw);t.DeviceTracker=u},8901:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FileListing=void 0;var n=r(5584),o=r(6237),i=r(4790),s=n.__importDefault(r(2332)),a=n.__importDefault(r(521)),c=r(6804),u=r(1100),l=function(e){function t(r,n){var o=e.call(this,r)||this;return o.serial=n,o.name="FileListing",o.sendMessage=function(){throw Error("Do not use this method. You must send data over channels")},r.on("channel",(function(e){t.handleNewChannel(o.serial,e.channel,e.data)})),o}return n.__extends(t,e),t.processChannel=function(e,r,n){if(r===c.ChannelCode.FSLS&&n&&!(n.byteLength<4)){var o=Buffer.from(n),i=o.readInt32LE(0);return new t(e,s.default.utf8ByteArrayToString(o.slice(4,4+i)))}},t.prototype.onSocketMessage=function(){},t.handleNewChannel=function(e,r,n){var o=Buffer.from(n);if(o.length<4)console.error("[".concat(t.TAG,"]"),"Invalid message. Too short (".concat(o.length,")"));else{var i=0,c=s.default.utf8ByteArrayToString(o.slice(i,4));switch(i+=4,c){case a.default.LIST:case a.default.STAT:case a.default.RECV:var l=o.readUInt32LE(i);i+=4;var d=o.slice(i,i+l),f=s.default.utf8ByteArrayToString(d);t.handle(c,e,f,r).catch((function(e){console.error("[".concat(t.TAG,"]"),e.message)}));break;case a.default.SEND:u.FilePushReader.handle(e,r);break;default:console.error("[".concat(t.TAG,"]"),"Invalid message. Wrong command (".concat(c,")")),r.close(4001,"Invalid message. Wrong command (".concat(c,")"))}}},t.handle=function(e,r,o,s){return n.__awaiter(this,void 0,void 0,(function(){return n.__generator(this,(function(n){try{if(e===a.default.STAT)return[2,i.AdbUtils.pipeStatToStream(r,o,s)];if(e===a.default.LIST)return[2,i.AdbUtils.pipeReadDirToStream(r,o,s)];if(e===a.default.RECV)return[2,i.AdbUtils.pipePullFileToStream(r,o,s)]}catch(e){t.sendError(null==e?void 0:e.message,s)}return[2]}))}))},t.sendError=function(e,t){if(t.readyState===t.OPEN){var r=Buffer.byteLength(e,"utf-8"),n=Buffer.alloc(8+r),o=n.write(a.default.FAIL,"ascii");o=n.writeUInt32LE(r,o),n.write(e,o,"utf-8"),t.send(n),t.close()}},t.TAG="FileListing",t}(o.Mw);t.FileListing=l},8847:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoteDevtools=void 0;var n=r(5584),o=r(6237),i=r(9493),s=r(4790),a=r(7802),c=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.ws=t,o.host=r,o.udid=n,o}return n.__extends(t,e),t.processRequest=function(e,r){var n=r.action,o=r.request,i=r.url;if(n===a.ACTION.DEVTOOLS){var s=o.headers.host,c=i.searchParams.get("udid");if(c){if("string"==typeof s&&s)return new t(e,s,c);e.close(4003,"[".concat(this.TAG,'] Invalid value "').concat(s,'" in "Host" header'))}else e.close(4003,"[".concat(this.TAG,'] Invalid value "').concat(c,'" for "udid" parameter'))}},t.prototype.onSocketMessage=function(e){var t,r=this;try{t=JSON.parse(e.data.toString())}catch(t){return void console.log("Received message: ".concat(e.data))}if(t&&t.command){var n=t.command;n===i.RemoteDevtoolsCommand.LIST_DEVTOOLS?s.AdbUtils.getRemoteDevtoolsInfo(this.host,this.udid).then((function(e){r.ws.send(JSON.stringify({type:a.ACTION.DEVTOOLS,data:e}))})).catch((function(e){var t=e.message;console.error('Command: "'.concat(n,'", error: ').concat(t)),r.ws.send(JSON.stringify({command:n,error:t}))})):console.warn('Unsupported command: "'.concat(t.command,'"'))}else console.log("Received message: ".concat(e.data))},t.TAG="RemoteDevtools",t}(o.Mw);t.RemoteDevtools=c},3323:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoteShell=void 0;var n=r(5584),o=r(6237),i=n.__importStar(r(4566)),s=n.__importStar(r(857)),a=r(7802),c=r(6804),u="win32"===s.platform(),l=!u,d=function(e){function t(t){var r=e.call(this,t)||this;return r.ws=t,r.initialized=!1,r.timeoutString=null,r.timeoutBuffer=null,r.terminated=!1,r.closeCode=1e3,r.closeReason="",r.handleMessage=function(e){return n.__awaiter(r,void 0,void 0,(function(){var t,r;return n.__generator(this,(function(n){return"shell"!==e.type||(t=e.data,"start"===(r=t.type)&&(this.term=this.createTerminal(t),this.initialized=!0),"stop"===r&&this.release()),[2]}))}))},r}return n.__extends(t,e),t.processChannel=function(e,r){if(r===c.ChannelCode.SHEL)return new t(e)},t.processRequest=function(e,r){if(r.action===a.ACTION.SHELL)return new t(e)},t.prototype.createTerminal=function(e){var r=this,n=Object.assign({},process.env);n.COLORTERM="truecolor";var o=e.cols,s=void 0===o?80:o,a=e.rows,c=void 0===a?24:a,d=n.PWD||"/",f=u?"adb.exe":"adb",h=i.spawn(f,["-s",e.udid,"shell"],{name:"xterm-256color",cols:s,rows:c,cwd:d,env:n,encoding:null}),p=l?this.bufferUtf8(5):this.buffer(5);return h.on("data",p),h.on("exit",(function(e){r.closeCode=0===e?1e3:4500,r.closeReason="[".concat([t.TAG],"] terminal process exited with code: ").concat(e),r.timeoutString||r.timeoutBuffer?r.terminated=!0:r.ws.close(r.closeCode,r.closeReason)})),h},t.prototype.onSocketMessage=function(e){if(this.initialized){if(!this.term)return;return this.term.write(e.data)}var r;try{r=JSON.parse(e.data.toString())}catch(e){return void console.error("[".concat(t.TAG,"]"),null==e?void 0:e.message)}this.handleMessage(r).catch((function(e){console.error("[".concat(t.TAG,"]"),e.message)}))},t.prototype.buffer=function(e){var t=this,r="";return function(n){r+=n,t.timeoutString||(t.timeoutString=setTimeout((function(){t.ws.send(r),r="",t.timeoutString=null,t.terminated&&t.ws.close(t.closeCode,t.closeReason)}),e))}},t.prototype.bufferUtf8=function(e){var t=this,r=[],n=0;return function(o){r.push(o),n+=o.length,t.timeoutBuffer||(t.timeoutBuffer=setTimeout((function(){t.ws.send(Buffer.concat(r,n)),r=[],t.timeoutBuffer=null,n=0,t.terminated&&t.ws.close(t.closeCode,t.closeReason)}),e))}},t.prototype.release=function(){e.prototype.release.call(this),this.timeoutBuffer&&clearTimeout(this.timeoutBuffer),this.timeoutString&&clearTimeout(this.timeoutString),this.term&&this.term.kill()},t.TAG="RemoteShell",t}(o.Mw);t.RemoteShell=d},4819:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WebsocketProxyOverAdb=void 0;var n=r(5584),o=r(3060),i=r(4790),s=r(7802),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n.__extends(t,e),t.processRequest=function(e,t){var r=t.action,n=t.url,o="",i="",a="",c=!1;if(r===s.ACTION.PROXY_ADB&&(c=!0,i=n.searchParams.get("remote"),o=n.searchParams.get("udid"),a=n.searchParams.get("path")),n&&n.pathname){var u=n.pathname.split("/");u.length>=4&&""===u[0]&&u[1]===s.ACTION.PROXY_ADB&&(c=!0,u.splice(0,2),o=decodeURIComponent(u.shift()||""),i=decodeURIComponent(u.shift()||""),a=u.join("/")||"/")}if(c)if("string"==typeof i&&i)if("string"==typeof o&&o){if(!a||"string"==typeof a)return this.createProxyOverAdb(e,o,i,a);e.close(4003,"[".concat(this.TAG,'] Invalid value "').concat(a,'" for "path" parameter'))}else e.close(4003,"[".concat(this.TAG,'] Invalid value "').concat(o,'" for "udid" parameter'));else e.close(4003,"[".concat(this.TAG,'] Invalid value "').concat(i,'" for "remote" parameter'))},t.createProxyOverAdb=function(e,t,r,n){var s=this,a=new o.WebsocketProxy(e);return i.AdbUtils.forward(t,r).then((function(e){return a.init("ws://127.0.0.1:".concat(e).concat(n||""))})).catch((function(t){var r="[".concat(s.TAG,"] Failed to start service: ").concat(t.message);console.error(r),e.close(4005,r)})),a},t}(o.WebsocketProxy);t.WebsocketProxyOverAdb=a},4619:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ControlCenter=void 0;var n=r(5584),o=r(1422),i=r(6900),s=r(6526),a=r(1453),c=n.__importStar(r(857)),u=n.__importStar(r(6982)),l=r(1233),d=function(e){function t(){var r=e.call(this)||this;r.initialized=!1,r.client=i.AdbExtended.createClient(),r.waitAfterError=1e3,r.deviceMap=new Map,r.descriptors=new Map,r.restartTracker=function(){r.restartTimeoutId||(console.log("Device tracker is down. Will try to restart in ".concat(r.waitAfterError,"ms")),r.restartTimeoutId=setTimeout((function(){r.stopTracker(),r.waitAfterError*=1.2,r.init()}),r.waitAfterError))},r.onChangeSet=function(e){var o,i,s,a,c,u;if(r.waitAfterError=t.defaultWaitAfterError,e.added.length)try{for(var d=n.__values(e.added),f=d.next();!f.done;f=d.next()){var h=(g=f.value).id,p=g.type;r.handleConnected(h,p)}}catch(e){o={error:e}}finally{try{f&&!f.done&&(i=d.return)&&i.call(d)}finally{if(o)throw o.error}}if(e.removed.length)try{for(var v=n.__values(e.removed),_=v.next();!_.done;_=v.next())h=(g=_.value).id,r.handleConnected(h,l.DeviceState.DISCONNECTED)}catch(e){s={error:e}}finally{try{_&&!_.done&&(a=v.return)&&a.call(v)}finally{if(s)throw s.error}}if(e.changed.length)try{for(var m=n.__values(e.changed),E=m.next();!E.done;E=m.next()){var g;h=(g=E.value).id,p=g.type,r.handleConnected(h,p)}}catch(e){c={error:e}}finally{try{E&&!E.done&&(u=m.return)&&u.call(m)}finally{if(c)throw c.error}}},r.onDeviceUpdate=function(e){var t=e.udid,n=e.descriptor;r.descriptors.set(t,n),r.emit("device",n)};var o="goog|".concat(c.hostname(),"|").concat(c.uptime());return r.id=u.createHash("md5").update(o).digest("hex"),r}return n.__extends(t,e),t.getInstance=function(){return this.instance||(this.instance=new t),this.instance},t.hasInstance=function(){return!!t.instance},t.prototype.handleConnected=function(e,t){var r=this.deviceMap.get(e);r?r.setState(t):((r=new o.Device(e,t)).on("update",this.onDeviceUpdate),this.deviceMap.set(e,r))},t.prototype.init=function(){return n.__awaiter(this,void 0,void 0,(function(){var e,t=this;return n.__generator(this,(function(r){switch(r.label){case 0:return this.initialized?[2]:(e=this,[4,this.startTracker()]);case 1:return e.tracker=r.sent(),[4,this.client.listDevices()];case 2:return r.sent().forEach((function(e){var r=e.id,n=e.type;t.handleConnected(r,n)})),this.initialized=!0,[2]}}))}))},t.prototype.startTracker=function(){return n.__awaiter(this,void 0,void 0,(function(){var e;return n.__generator(this,(function(t){switch(t.label){case 0:return this.tracker?[2,this.tracker]:[4,this.client.trackDevices()];case 1:return(e=t.sent()).on("changeSet",this.onChangeSet),e.on("end",this.restartTracker),e.on("error",this.restartTracker),[2,e]}}))}))},t.prototype.stopTracker=function(){this.tracker&&(this.tracker.off("changeSet",this.onChangeSet),this.tracker.off("end",this.restartTracker),this.tracker.off("error",this.restartTracker),this.tracker.end(),this.tracker=void 0),this.tracker=void 0,this.initialized=!1},t.prototype.getDevices=function(){return Array.from(this.descriptors.values())},t.prototype.getDevice=function(e){return this.deviceMap.get(e)},t.prototype.getId=function(){return this.id},t.prototype.getName=function(){return"aDevice Tracker [".concat(c.hostname(),"]")},t.prototype.start=function(){var e=this;return this.init().catch((function(t){console.error('Error: Failed to init "'.concat(e.getName(),'". ').concat(t.message))}))},t.prototype.release=function(){this.stopTracker()},t.prototype.runCommand=function(e){return n.__awaiter(this,void 0,void 0,(function(){var t,r,o;return n.__generator(this,(function(n){switch(n.label){case 0:if(t=e.getUdid(),!(r=this.getDevice(t)))return console.error('Device with udid:"'.concat(t,'" not found')),[2];switch(o=e.getType()){case a.ControlCenterCommand.KILL_SERVER:return[3,1];case a.ControlCenterCommand.START_SERVER:return[3,3];case a.ControlCenterCommand.UPDATE_INTERFACES:return[3,5]}return[3,7];case 1:return[4,r.killServer(e.getPid())];case 2:case 4:case 6:return n.sent(),[2];case 3:return[4,r.startServer()];case 5:return[4,r.updateInterfaces()];case 7:throw new Error('Unsupported command: "'.concat(o,'"'))}}))}))},t.defaultWaitAfterError=1e3,t}(s.BaseControlCenter);t.ControlCenter=d},9479:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HostTracker=void 0;var n=r(5584),o=r(6237),i=r(6348),s=r(589),a=r(6804),c=function(e){function t(r){var n=e.call(this,r)||this,o=Array.from(t.localTrackers.keys()).map((function(e){return{type:e.type}}));if(!t.remoteHostItems){var a=i.Config.getInstance();t.remoteHostItems=Array.from(a.getHostList())}var c={id:-1,type:s.MessageType.HOSTS,data:{local:o,remote:t.remoteHostItems}};return n.sendMessage(c),n}return n.__extends(t,e),t.processChannel=function(e,r){if(r===a.ChannelCode.HSTS)return new t(e)},t.registerLocalTracker=function(e){this.localTrackers.add(e)},t.prototype.onSocketMessage=function(e){var t={id:-1,type:s.MessageType.ERROR,data:'Unsupported message: "'.concat(e.data.toString(),'"')};this.sendMessage(t)},t.prototype.release=function(){e.prototype.release.call(this)},t.TAG="HostTracker",t.localTrackers=new Set,t}(o.Mw);t.HostTracker=c},6237:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Mw=void 0;var r=function(){function e(e){var t=this;this.ws=e,this.name="Mw",this.sendMessage=function(e){t.ws.readyState===t.ws.OPEN&&t.ws.send(JSON.stringify(e))},this.ws.addEventListener("message",this.onSocketMessage.bind(this)),this.ws.addEventListener("close",this.onSocketClose.bind(this))}return e.processChannel=function(e,t,r){},e.processRequest=function(e,t){},e.prototype.onSocketClose=function(){this.release()},e.prototype.release=function(){var e=this.ws,t=e.readyState,r=e.CLOSED,n=e.CLOSING;t!==r&&t!==n&&this.ws.close()},e}();t.Mw=r},511:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WebsocketMultiplexer=void 0;var n=r(5584),o=r(6237),i=r(7802),s=r(7305),a=n.__importDefault(r(2332)),c=function(e){function t(t){var r=e.call(this,t)||this;return r.multiplexer=s.Multiplexer.wrap(t),r}return n.__extends(t,e),t.processRequest=function(e,t){if(t.action===i.ACTION.MULTIPLEX)return this.createMultiplexer(e)},t.createMultiplexer=function(e){var r=this,n=new t(e);return n.init().catch((function(t){var n="[".concat(r.TAG,"] Failed to start service: ").concat(t.message);console.error(n),e.close(4005,n)})),n},t.prototype.init=function(){return n.__awaiter(this,void 0,void 0,(function(){return n.__generator(this,(function(e){return this.multiplexer.addEventListener("channel",this.onChannel),[2]}))}))},t.registerMw=function(e){this.mwFactories.add(e)},t.prototype.onSocketMessage=function(e){},t.prototype.onChannel=function(e){var r,o,i=e.channel,s=e.data,c=!1;try{for(var u=n.__values(t.mwFactories.values()),l=u.next();!l.done;l=u.next()){var d=l.value;try{var f=a.default.utf8ByteArrayToString(Buffer.from(s).slice(0,4)),h=s.byteLength>4?s.slice(4):void 0;d.processChannel(i,f,h)&&(c=!0)}finally{}}}catch(e){r={error:e}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}c||i.close(4002,"[".concat(t.TAG,"] Unsupported request"))},t.prototype.release=function(){e.prototype.release.call(this)},t.TAG="WebsocketMultiplexer",t.mwFactories=new Set,t}(o.Mw);t.WebsocketMultiplexer=c},3060:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WebsocketProxy=void 0;var n=r(5584),o=r(6237),i=n.__importDefault(r(5086)),s=r(7802),a=function(e){function t(t){var r=e.call(this,t)||this;return r.released=!1,r.storage=[],r}return n.__extends(t,e),t.processRequest=function(e,t){var r=t.action,n=t.url;if(r===s.ACTION.PROXY_WS){var o=n.searchParams.get("ws");if(o)return this.createProxy(e,o);e.close(4003,"[".concat(this.TAG,'] Invalid value "').concat(e,'" for "ws" parameter'))}},t.createProxy=function(e,r){var n=this,o=new t(e);return o.init(r).catch((function(t){var r="[".concat(n.TAG,"] Failed to start service: ").concat(t.message);console.error(r),e.close(4005,r)})),o},t.prototype.init=function(e){return n.__awaiter(this,void 0,void 0,(function(){var r,o=this;return n.__generator(this,(function(n){return this.name="[".concat(t.TAG,"{$").concat(e,"}]"),(r=new i.default(e)).onopen=function(){o.remoteSocket=r,o.flush()},r.onmessage=function(e){o.ws&&o.ws.readyState===o.ws.OPEN&&(Array.isArray(e.data)?e.data.forEach((function(e){return o.ws.send(e)})):o.ws.send(e.data))},r.onclose=function(e){o.ws.readyState===o.ws.OPEN&&o.ws.close(e.wasClean?1e3:4010)},r.onerror=function(e){o.ws.readyState===o.ws.OPEN&&o.ws.close(4011,e.message)},[2]}))}))},t.prototype.flush=function(){if(this.remoteSocket){for(;this.storage.length;){var e=this.storage.shift();e&&e.data&&this.remoteSocket.send(e.data)}this.released&&this.remoteSocket.close()}this.storage.length=0},t.prototype.onSocketMessage=function(e){this.remoteSocket?this.remoteSocket.send(e.data):this.storage.push(e)},t.prototype.release=function(){this.released||(e.prototype.release.call(this),this.released=!0,this.flush())},t.TAG="WebsocketProxy",t}(o.Mw);t.WebsocketProxy=a},6526:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaseControlCenter=void 0;var n=r(5584),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n.__extends(t,e),t}(r(462).TypedEmitter);t.BaseControlCenter=o},4102:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HttpServer=void 0;var n=r(5584),o=n.__importStar(r(8611)),i=n.__importStar(r(5692)),s=n.__importDefault(r(6928)),a=r(2913),c=n.__importDefault(r(7252)),u=r(6348),l=r(462),d=n.__importStar(r(5949)),f=r(264),h=s.default.join(__dirname,"./public"),p=d.env[f.EnvName.WS_SCRCPY_PATHNAME]||"/",v=function(e){function t(){var t=e.call(this)||this;return t.servers=[],t.started=!1,t}return n.__extends(t,e),t.getInstance=function(){return this.instance||(this.instance=new t),this.instance},t.hasInstance=function(){return!!this.instance},t.setPublicDir=function(e){if(t.instance)throw Error("Unable to change value after instantiation");t.PUBLIC_DIR=e},t.setServeStatic=function(e){if(t.instance)throw Error("Unable to change value after instantiation");t.SERVE_STATIC=e},t.prototype.getServers=function(){return n.__awaiter(this,void 0,void 0,(function(){var e=this;return n.__generator(this,(function(t){return this.started?[2,n.__spreadArray([],n.__read(this.servers),!1)]:[2,new Promise((function(t){e.once("started",(function(){t(n.__spreadArray([],n.__read(e.servers),!1))}))}))]}))}))},t.prototype.getName=function(){return"HTTP(s) Server Service"},t.prototype.start=function(){return n.__awaiter(this,void 0,void 0,(function(){var e=this;return n.__generator(this,(function(r){return this.mainApp=(0,c.default)(),t.SERVE_STATIC&&t.PUBLIC_DIR&&(this.mainApp.use(p,c.default.static(t.PUBLIC_DIR)),this.mainApp.get("/api/flask-port",(function(e,t){var r=u.Config.getFlaskPort();t.json({flaskPort:r})}))),u.Config.getInstance().servers.forEach((function(t){var r,s,u=t.secure,l=t.port,d=t.redirectToSecure;if(u){if(!t.options)throw Error("Must provide option for secure server configuration");s=i.createServer(t.options,e.mainApp),r="https"}else{var f=t.options?n.__assign({},t.options):{};r="http";var h=e.mainApp,v="",_=443,m=!1;!0===d?m=!0:"object"==typeof d&&(m=!0,"number"==typeof d.port&&(_=d.port),"string"==typeof d.host&&(v=d.host)),m&&(h=(0,c.default)()).use((function(e,t){var r=new URL("https://".concat(v||e.headers.host).concat(e.url));return _&&443!==_&&(r.port=_.toString()),t.redirect(301,r.toString())})),s=o.createServer(f,h)}e.servers.push({server:s,port:l}),s.listen(l,(function(){a.Utils.printListeningMsg(r,l,p)}))})),this.started=!0,this.emit("started",!0),[2]}))}))},t.prototype.release=function(){this.servers.forEach((function(e){e.server.close()}))},t.PUBLIC_DIR=h,t.SERVE_STATIC=!0,t}(l.TypedEmitter);t.HttpServer=v},2193:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WebSocketServer=void 0;var n=r(5584),o=r(5086),i=r(4102),s=function(){function e(){this.servers=[],this.mwFactories=new Set}return e.getInstance=function(){return this.instance||(this.instance=new e),this.instance},e.hasInstance=function(){return!!this.instance},e.prototype.registerMw=function(e){this.mwFactories.add(e)},e.prototype.attachToServer=function(e){var t=this,r=e.server,i=e.port,s="WebSocket Server {tcp:".concat(i,"}"),a=new o.Server({server:r});return a.on("connection",(function(e,r){return n.__awaiter(t,void 0,void 0,(function(){var t,o,i,a,c,u,l;return n.__generator(this,(function(d){if(!r.url)return e.close(4001,"[".concat(s,"] Invalid url")),[2];t=new URL(r.url,"https://example.org/"),o=t.searchParams.get("action")||"",i=!1;try{for(a=n.__values(this.mwFactories.values()),c=a.next();!c.done;c=a.next())c.value.processRequest(e,{action:o,request:r,url:t})&&(i=!0)}catch(e){u={error:e}}finally{try{c&&!c.done&&(l=a.return)&&l.call(a)}finally{if(u)throw u.error}}return i||e.close(4002,"[".concat(s,"] Unsupported request")),[2]}))}))})),a.on("close",(function(){console.log("".concat(s," stopped"))})),this.servers.push(a),a},e.prototype.getServers=function(){return this.servers},e.prototype.getName=function(){return"WebSocket Server Service"},e.prototype.start=function(){return n.__awaiter(this,void 0,void 0,(function(){var e=this;return n.__generator(this,(function(t){switch(t.label){case 0:return[4,i.HttpServer.getInstance().getServers()];case 1:return t.sent().forEach((function(t){e.attachToServer(t)})),[2]}}))}))},e.prototype.release=function(){this.servers.forEach((function(e){e.close()}))},e}();t.WebSocketServer=s},9493:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoteDevtoolsCommand=void 0,(t.RemoteDevtoolsCommand||(t.RemoteDevtoolsCommand={})).LIST_DEVTOOLS="list_devtools"},3216:e=>{e.exports=require("@dead50f7/adbkit/lib/adb")},9978:e=>{e.exports=require("@dead50f7/adbkit/lib/adb/client")},3014:e=>{e.exports=require("@dead50f7/adbkit/lib/adb/command")},521:e=>{e.exports=require("@dead50f7/adbkit/lib/adb/protocol")},6065:e=>{e.exports=require("@dead50f7/adbkit/lib/adb/sync/entry")},6261:e=>{e.exports=require("events")},7252:e=>{e.exports=require("express")},4566:e=>{e.exports=require("node-pty")},3399:e=>{e.exports=require("portfinder")},5949:e=>{e.exports=require("process")},5584:e=>{e.exports=require("tslib")},5086:e=>{e.exports=require("ws")},2115:e=>{e.exports=require("yaml")},5317:e=>{e.exports=require("child_process")},6982:e=>{e.exports=require("crypto")},9896:e=>{e.exports=require("fs")},8611:e=>{e.exports=require("http")},5692:e=>{e.exports=require("https")},857:e=>{e.exports=require("os")},6928:e=>{e.exports=require("path")},3785:e=>{e.exports=require("readline")},2203:e=>{e.exports=require("stream")},7016:e=>{e.exports=require("url")}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.p="",(()=>{var e=r(5584);r(9369);var t=e.__importStar(r(3785)),n=r(6348),o=r(4102),i=r(2193),s=r(3060),a=r(9479),c=r(511),u=[o.HttpServer,i.WebSocketServer],l=[s.WebsocketProxy,c.WebsocketMultiplexer],d=[a.HostTracker],f=[],h=[],p=n.Config.getInstance();h.push(function(){return e.__awaiter(this,void 0,void 0,(function(){var t,n,o,i,s,c;return e.__generator(this,(function(f){switch(f.label){case 0:return[4,Promise.resolve().then((function(){return e.__importStar(r(4619))}))];case 1:return t=f.sent().ControlCenter,[4,Promise.resolve().then((function(){return e.__importStar(r(4113))}))];case 2:return n=f.sent().DeviceTracker,[4,Promise.resolve().then((function(){return e.__importStar(r(4819))}))];case 3:return o=f.sent().WebsocketProxyOverAdb,p.runLocalGoogTracker&&d.push(n),p.announceLocalGoogTracker&&a.HostTracker.registerLocalTracker(n),u.push(t),[4,Promise.resolve().then((function(){return e.__importStar(r(3323))}))];case 4:return i=f.sent().RemoteShell,d.push(i),[4,Promise.resolve().then((function(){return e.__importStar(r(8847))}))];case 5:return s=f.sent().RemoteDevtools,l.push(s),[4,Promise.resolve().then((function(){return e.__importStar(r(8901))}))];case 6:return c=f.sent().FileListing,d.push(c),l.push(o),[2]}}))}))}()),Promise.all(h).then((function(){return u.map((function(e){var t=e.getInstance();return f.push(t),t.start()}))})).then((function(){var e=i.WebSocketServer.getInstance();l.forEach((function(t){e.registerMw(t)})),d.forEach((function(e){c.WebsocketMultiplexer.registerMw(e)})),"win32"===process.platform&&t.createInterface({input:process.stdin,output:process.stdout}).on("SIGINT",_),process.on("SIGINT",_),process.on("SIGTERM",_)})).catch((function(e){console.error(e.message),_("1")}));var v=!1;function _(e){if(console.log("\nReceived signal ".concat(e)),v)return console.log("Force exit"),void process.exit(0);v=!0,f.forEach((function(e){var t=e.getName();console.log("Stopping ".concat(t," ...")),e.release()}))}})()})();