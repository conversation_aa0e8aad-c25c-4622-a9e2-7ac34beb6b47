export interface ElectronAPI {
  getBrowserHomepageConfig(): Promise<string>;
  setBrowserHomepageConfig(homepage: string): Promise<void>;
  getDefaultSearchEngineConfig(): Promise<string>;
  setDefaultSearchEngineConfig(searchEngine: string): Promise<void>;
  
  // 配置验证和迁移 API
  validateConfiguration(): Promise<{
    isValid: boolean;
    missingKeys: string[];
    errors: string[];
  }>;
  getConfigMigrationStatus(): Promise<{
    mainConfig: { exists: boolean; needsMigration: boolean; missingKeys: string[] };
    flaskConfig: { exists: boolean; needsMigration: boolean; missingKeys: string[] };
    templateFiles: { main: boolean; flask: boolean };
  }>;
  forceConfigMigration(): Promise<{ success: boolean; message: string }>;
  offAutoDeviceSelected(): unknown;
  onAutoDeviceSelected(handleAutoDeviceSelected: (deviceInfo: any) => void): unknown;
  checkAndStartFlaskServer: () => Promise<{ success: boolean; message: string }>;
  getConfig: () => Promise<{ 
    flask_port: number; 
    scrcpy_port: number;
    device_port: number;
  }>;
  setPortConfig: (config: { 
    flask_port: number; 
    scrcpy_port: number;
    device_port: number;
  }) => Promise<boolean>;
  getLanguageConfig: () => Promise<string>;
  setLanguageConfig: (language: string) => Promise<void>;
  setApiKeyConfig: (apiKey: string) => Promise<void>;
  getAppLanguageConfig: () => Promise<string>;
  setAppLanguageConfig: (appLanguage: string) => Promise<void>;
  getUseAnnotationConfig: () => Promise<boolean>;
  setUseAnnotationConfig: (useAnnotation: boolean) => Promise<void>;
  setDataDirPathConfig: (taskName: string) => Promise<boolean>;
  getDeviceCountConfig: () => Promise<{
    browserDeviceCount: number;
    cloudPhoneCount: number;
  }>;
  setDeviceCountConfig: (deviceCounts: {
    browserDeviceCount: number;
    cloudPhoneCount: number;
  }) => Promise<boolean>;

  getDeviceSerialIdConfig: () => Promise<string>;
  // setDeviceSerialIdConfig: (deviceSerialId: string) => Promise<boolean>;
  getDeviceMappingsConfig: () => Promise<Record<string, string>>;
  setDeviceMappingsConfig: (deviceMappings: Record<string, string>) => Promise<boolean>;
  setRuyiAgentDeviceConfig: (deviceName: string, deviceType: string) => Promise<boolean>;
  onToggleScriptLanguage: (callback: () => void) => void;
  offToggleScriptLanguage: () => void;
  runAdbCommand: (command: string) => Promise<string>;
  createAnnotationDataDir: () => Promise<string>;
  saveAnnotationData: (data: {
    screenshot: string;
    annotatedImage: string;
    viewHierarchy: string;
    annotations: Array<{
      x?: number;
      y?: number;
      width?: number;
      height?: number;
      label?: string;
      mode?: string;
      groupTitle?: string | string[];
      description?: string | string[];
      value?: boolean;
    }>;
  }) => Promise<string>;
  getAnnotations: () => Promise<Array<{
    mode: string;
    screenshot: string;
    annotations: Array<{
      x?: number;
      y?: number;
      width?: number;
      height?: number;
      label?: string;
      mode?: string;
      groupTitle?: string | string[];
      description?: string | string[];
      value?: boolean;
    }>;
    directory: string;
  }>>;
  deleteAnnotation: (directory: string) => Promise<boolean>;
  deleteAllAnnotations: () => Promise<boolean>;

  saveImage: (imageData: string) => Promise<{
    success: boolean;
    path?: string;
    error?: string;
    reason?: string;
  }>;
  
  // 保存任务到本地仓库
  saveTaskToLocalRepo: (taskData: {
    taskName: string;
    taskDescription: string;
    codeScript: string;
    NLScript: string;
  }) => Promise<{
    success: boolean;
    message: string;
    path?: string;
  }>;
  
  // 获取本地仓库任务列表
  getLocalRepoTasks: () => Promise<Array<{
    name: string;
    description: string;
    path: string;
    created_at: string;
    updated_at: string;
  }>>;
  
  // 从本地仓库加载任务
  loadTaskFromLocalRepo: (taskName: string) => Promise<{
    success: boolean;
    message: string;
    task?: {
      name: string;
      description: string;
      code_script: string;
      nl_script: string;
      created_at: string;
      updated_at: string;
    };
  }>;
  
  // 从本地仓库删除任务
  deleteTaskFromLocalRepo: (taskName: string) => Promise<{
    success: boolean;
    message: string;
  }>;
  
  // 更新相关的 API
  checkForUpdates: () => Promise<void>;
  downloadUpdate: () => Promise<void>;
  confirmDownloadUpdate: (confirmed: boolean) => Promise<void>;
  quitAndInstall: () => void;
  onUpdateMessage: (callback: (message: {
    type: 'checking' | 'available' | 'available-confirm' | 'not-available' | 'downloaded' | 'error' | 'progress';
    version?: string;
    message?: string;
    showRestart?: boolean;
    needsConfirm?: boolean;
    percent?: number;
    error?: string;
  }) => void) => void;
  offUpdateMessage: () => void;

  // Update preferences management
  getUpdatePreferences: () => Promise<{
    dismissedVersions: string[];
    remindLater: {
      version?: string;
      remindAfter?: number;
    };
    showBadgeOnButton: boolean;
    showBadgeOnAvatar: boolean;
    notificationMethod: 'modal' | 'notification' | 'badge-only';
  }>;
  setUpdatePreferences: (preferences: {
    dismissedVersions: string[];
    remindLater: {
      version?: string;
      remindAfter?: number;
    };
    showBadgeOnButton: boolean;
    showBadgeOnAvatar: boolean;
    notificationMethod: 'modal' | 'notification' | 'badge-only';
  }) => Promise<boolean>;
  dismissUpdateVersion: (version: string) => Promise<boolean>;
  setUpdateRemindLater: (version: string, hours: number) => Promise<boolean>;

  // ws-scrcpy 相关的 API
  startWsScrcpy: () => Promise<void>;
  stopWsScrcpy: () => Promise<void>;
  updateWsIp: (wsIp?: string) => Promise<{ success: boolean; error?: string }>;

  // Flask 服务相关的 API
  startFlaskServer: () => Promise<{ success: boolean; message: string }>;
  stopFlaskServer: () => Promise<void>;
  getFlaskDetails: () => Promise<{ pythonPath: string; flaskScript: string }>;

  request: (method: string, url: string, body?: any) => Promise<any>;
  answerQuestion: (data: { request_id: string, answer: string }) => Promise<void>;
  onFlaskMessage: (callback: (message: any) => void) => void;
  offFlaskMessage: () => void;

  // BrowserView 相关的接口
  createBrowserView: (url: string, deviceId: string) => Promise<void>;
  destroyBrowserView: () => Promise<void>;
  hideBrowserView: (temporary?: boolean) => Promise<void>;
  openExternalUrl: (url: string) => Promise<void>;

  // Browser control functions
  goBack: () => Promise<void>;
  goForward: () => Promise<void>;
  reload: () => Promise<void>;
  loadURL: (url: string) => Promise<void>;

  // Browser event listeners
  onBrowserLoadingState: (callback: (state: { isLoading: boolean }) => void) => void;
  offBrowserLoadingState: () => void;
  onBrowserLoadError: (callback: (error: { errorCode: number; errorDescription: string }) => void) => void;
  offBrowserLoadError: () => void;
  onBrowserNavigationState: (callback: (state: { canGoBack: boolean; canGoForward: boolean; url: string }) => void) => void;
  offBrowserNavigationState: () => void;
  
  // Browser device switching event listeners
  onBrowserDeviceSwitched: (callback: (data: {
    device: {
      id: string;
      deviceNumber: string;
      type: string;
      url: string;
      brand: string;
      product: string;
    };
    showDeviceSelect: boolean;
  }) => void) => void;
  offBrowserDeviceSwitched: () => void;

  // Git 操作相关
  initializeAndPushGitRepo: (data: {
    taskName: string;
    repoToken: string;
    projectDescription?: string;
    userInfo?: {
      username?: string;
      displayName?: string;
      email?: string;
    };
  }) => Promise<{
    success: boolean;
    message: string;
  }>;
  
  // 保存并推送项目到Git
  saveAndPushProjectToGit: (data: {
    taskName: string;
    repoToken: string;
    commitMessage?: string;
    userInfo?: {
      username?: string;
      displayName?: string;
      email?: string;
    };
  }) => Promise<{
    success: boolean;
    message: string;
  }>;

  // 文件管理器相关操作
  getTaskFiles: (taskName: string) => Promise<Array<{
    name: string;
    path: string;
    size: number;
    modified: string;
    created: string;
    isPinned: boolean;
    pinnedTime: string | null;
    fileType?: 'knowledge' | 'task' | 'data';
  }>>;
  
  // 获取目录树结构
  getTaskFileTree: (taskName: string) => Promise<{
    success: boolean;
    tree?: FileTreeNode[];
    message?: string;
  }>;
  
  refreshTaskFiles: (taskName: string) => Promise<Array<{
    name: string;
    path: string;
    size: number;
    modified: string;
    created: string;
    isPinned: boolean;
    pinnedTime: string | null;
  }>>;
  
  createEmptyTaskFile: (data: {
    taskName: string;
    taskShortName?: string;
    description: string;
    projectName: string;
  }) => Promise<{
    success: boolean;
    message: string;
    fileName?: string;
  }>;
  
  createTaskFile: (data: {
    taskName: string;
    fileName: string;
    content: string;
    folderPath?: string;
  }) => Promise<{
    success: boolean;
    message: string;
  }>;
  
  // 创建目录
  createTaskFolder: (data: {
    taskName: string;
    folderName: string;
    parentPath?: string;
  }) => Promise<{
    success: boolean;
    message: string;
  }>;
  
  readTaskFile: (data: {
    taskName: string;
    fileName: string;
  }) => Promise<{
    success: boolean;
    content?: string;
    message: string;
  }>;
  
  deleteTaskFile: (data: {
    taskName: string;
    fileName: string;
    repoToken?: string;
    userInfo?: {
      username?: string;
      displayName?: string;
      email?: string;
    };
  }) => Promise<{
    success: boolean;
    message: string;
    gitPushed?: boolean;
    gitPushSuccess?: boolean;
    gitPushMessage?: string;
  }>;
  
  // 删除目录
  deleteTaskFolder: (data: {
    taskName: string;
    folderPath: string;
    repoToken?: string;
    userInfo?: {
      username?: string;
      displayName?: string;
      email?: string;
    };
  }) => Promise<{
    success: boolean;
    message: string;
    gitPushed?: boolean;
    gitPushSuccess?: boolean;
    gitPushMessage?: string;
  }>;
  
  // 重命名文件/目录
  renameTaskItem: (data: {
    taskName: string;
    oldPath: string;
    newName: string;
  }) => Promise<{
    success: boolean;
    message: string;
  }>;
  
  updateTaskFile: (data: {
    taskName: string;
    fileName: string;
    content: string;
  }) => Promise<{
    success: boolean;
    message: string;
  }>;
  
  // 聊天消息保存和加载
  saveTaskChatMessages: (data: {
    taskName: string;
    fileName: string;
    messages: Array<{
      id: string;
      sender: string;
      content: string;
      isQuestion?: boolean;
      isDemoing?: boolean;
      isLoading?: boolean;
      isComplete?: boolean;
      isError?: boolean;
      messageType?: 'text' | 'ask_question' | 'notify_message' | 'notify_table' | 'notify_image';
      payload?: any;
    }>;
  }) => Promise<{
    success: boolean;
    message: string;
  }>;
  
  loadTaskChatMessages: (data: {
    taskName: string;
    fileName: string;
  }) => Promise<{
    success: boolean;
    messages?: Array<{
      id: string;
      sender: string;
      content: string;
      isQuestion?: boolean;
      isDemoing?: boolean;
      isLoading?: boolean;
      isComplete?: boolean;
      isError?: boolean;
      messageType?: 'text' | 'ask_question' | 'notify_message' | 'notify_table' | 'notify_image';
      payload?: any;
    }>;
    message: string;
  }>;
  
  // 监听项目文件创建事件
  onProjectFileCreated: (callback: (data: { taskName: string; fileName: string }) => void) => void;
  offProjectFileCreated: () => void;
  updateKnowledgeFile: (data: {
    taskName: string;
    knowledgeContent: string;
  }) => Promise<{ success: boolean; message: string }>;

  // Git 同步操作相关
  cloneProjectFromGit: (data: {
    projectName: string;
    repoToken: string;
    userInfo?: {
      username?: string;
      displayName?: string;
      email?: string;
    };
  }) => Promise<{ success: boolean; message: string }>;
  
  pullProjectFromGit: (data: {
    projectName: string;
    repoToken: string;
    userInfo?: {
      username?: string;
      displayName?: string;
      email?: string;
    };
  }) => Promise<{ 
    success: boolean; 
    message: string; 
    hasConflict?: boolean;
  }>;
  
  forceUseLocalVersion: (data: {
    projectName: string;
    repoToken: string;
    userInfo?: {
      username?: string;
      displayName?: string;
      email?: string;
    };
  }) => Promise<{ success: boolean; message: string }>;
  
  forceUseRemoteVersion: (data: {
    projectName: string;
    repoToken: string;
    userInfo?: {
      username?: string;
      displayName?: string;
      email?: string;
    };
  }) => Promise<{ success: boolean; message: string }>;
  
  // 自动保存和同步相关
  autoSaveAndSync: (data: {
    taskName: string;
    fileName: string;
    content: string;
    repoToken?: string;
    userInfo?: {
      username?: string;
      displayName?: string;
      email?: string;
    };
  }) => Promise<{ success: boolean; message: string }>;
  
  // 文件置顶功能
  toggleFilePinned: (data: {
    taskName: string;
    fileName: string;
  }) => Promise<{
    success: boolean;
    isPinned: boolean;
    message: string;
  }>;
  
  // 文件导入功能
  selectAndImportFile: (data: {
    taskName: string;
    targetFolderPath?: string;
    repoToken?: string;
    userInfo?: {
      username?: string;
      displayName?: string;
      email?: string;
    };
  }) => Promise<{
    success: boolean;
    message: string;
    fileName?: string;
  }>;

  // 读取并解析数据文件
  readAndParseDataFile: (data: {
    taskName: string;
    fileName: string;
  }) => Promise<{
    success: boolean;
    message: string;
    data?: {
      name: string;
      columns: string[];
      data: Record<string, any>[];
    };
  }>;
  
  // 删除浏览器设备状态
  removeBrowserDeviceState: (deviceId: string) => Promise<{
    success: boolean;
    message: string;
  }>;

  // 设备连接状态监测相关
  startDeviceMonitoring: (deviceId: string) => Promise<{
    success: boolean;
    error?: string;
  }>;
  stopDeviceMonitoring: () => Promise<{
    success: boolean;
    error?: string;
  }>;
  reloadDeviceIframe: () => Promise<{
    success: boolean;
    error?: string;
  }>;
  checkDeviceConnection: (deviceId: string) => Promise<{
    success: boolean;
    isConnected: boolean;
    error?: string;
  }>;

  // 监听设备连接状态变化事件
  onDeviceConnectionStatusChanged: (callback: (data: {
    deviceId: string;
    isConnected: boolean;
  }) => void) => void;
  offDeviceConnectionStatusChanged: () => void;

  // 监听重新加载iframe事件
  onReloadDeviceIframe: (callback: () => void) => void;
  offReloadDeviceIframe: () => void;

  // 加载自定义提示模板
  loadPromptTemplate: () => Promise<string | null>;

  // 用户管理相关
  setCurrentUser: (userInfo: {
    username?: string | null;
    displayName?: string;
    email?: string;
    uuid?: string;
  }) => Promise<{
    success: boolean;
    message: string;
  }>;
  
  clearUserRepoData: (username: string) => Promise<{
    success: boolean;
    message: string;
  }>;
  
  getCurrentUser: () => Promise<{
    username?: string;
    displayName?: string;
    email?: string;
  } | null>;
  
  // 用户切换事件监听
  onUserSwitched: (callback: (data: {
    previousUser: string;
    newUser: string;
  }) => void) => void;
  offUserSwitched: () => void;

  // 代码执行高亮监听
  onHighlightExecutingLine: (callback: (data: {
    labeled_code_script: string;
    labeled_NL_script: string;
  }) => void) => void;
  offHighlightExecutingLine: () => void;
  
  // 清除代码执行高亮
  clearExecutingLineHighlight: () => Promise<void>;
  
  // 监听清除高亮事件
  onClearExecutingLineHighlight: (callback: () => void) => void;
  offClearExecutingLineHighlight: () => void;

  // 添加getFontPath方法的类型声明
  getFontPath: (fontName: string) => Promise<string>;
}

// 文件树节点类型定义
interface FileTreeNode {
  name: string;
  path: string;
  isDirectory: boolean;
  size?: number;
  modified: string;
  created: string;
  isPinned: boolean;
  pinnedTime: string | null;
  fileType?: 'knowledge' | 'task' | 'data' | 'folder';
  children?: FileTreeNode[];
  depth: number;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}