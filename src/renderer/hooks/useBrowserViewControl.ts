import { useEffect, useRef, useCallback } from 'react';

/**
 * Hook to control browser view visibility when modal is shown/hidden
 * @param isVisible - Modal visibility state
 */
export const useBrowserViewControl = (isVisible: boolean) => {
  useEffect(() => {
    const electronAPI = (window as any).electronAPI;
    if (!electronAPI || !electronAPI.toggleBrowserView) {
      return;
    }

    // 添加延迟和重试机制来处理异步操作
    const toggleBrowserViewWithRetry = async (shouldShow: boolean, retries = 3) => {
      for (let i = 0; i < retries; i++) {
        try {
          await electronAPI.toggleBrowserView(shouldShow);
          break; // 成功则退出重试循环
        } catch (error) {
          console.warn(`Browser view toggle attempt ${i + 1} failed:`, error);
          if (i === retries - 1) {
            console.error('Failed to toggle browser view after all retries');
          } else {
            // 等待一小段时间后重试
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }
      }
    };

    // Toggle browser view visibility when modal visibility changes
    // 添加小延迟确保状态更新完成
    const timeoutId = setTimeout(() => {
      toggleBrowserViewWithRetry(!isVisible);
    }, 50);

    // Cleanup function to show browser view when component unmounts
    return () => {
      clearTimeout(timeoutId);
      if (electronAPI && electronAPI.toggleBrowserView) {
        // 组件卸载时立即显示 browser view，不需要重试
        electronAPI.toggleBrowserView(true).catch((error: any) => {
          console.warn('Failed to show browser view on cleanup:', error);
        });
      }
    };
  }, [isVisible]);
};

/**
 * Debounced hook to control browser view visibility for components that toggle frequently
 * Designed for search overlays, autocomplete dropdowns, etc.
 * @param isVisible - Component visibility state
 * @param debounceDelay - Delay in milliseconds before applying the change (default: 300ms)
 * @param preserveFocus - Whether to preserve focus on search input when browser view is toggled
 */
export const useDebouncedBrowserViewControl = (
  isVisible: boolean, 
  debounceDelay: number = 300,
  preserveFocus: boolean = true
) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastStateRef = useRef<boolean>(isVisible);
  const isInitializedRef = useRef<boolean>(false);
  const activeElementRef = useRef<Element | null>(null);

  const toggleBrowserView = useCallback(async (shouldShow: boolean) => {
    const electronAPI = (window as any).electronAPI;
    if (!electronAPI || !electronAPI.toggleBrowserView) {
      return;
    }

    try {
      // Store the currently focused element before browser view operations
      if (preserveFocus && !shouldShow) {
        activeElementRef.current = document.activeElement;
        console.log(`[DebouncedBrowserViewControl] Storing focus element:`, activeElementRef.current);
      }

      console.log(`[DebouncedBrowserViewControl] ${shouldShow ? 'Showing' : 'Hiding'} browser view`);
      await electronAPI.toggleBrowserView(shouldShow);

      // Restore focus after browser view operations if needed
      if (preserveFocus && shouldShow && activeElementRef.current) {
        // Use a small delay to ensure the browser view operation is complete
        setTimeout(() => {
          if (activeElementRef.current && 
              activeElementRef.current instanceof HTMLElement &&
              document.contains(activeElementRef.current)) {
            console.log(`[DebouncedBrowserViewControl] Restoring focus to:`, activeElementRef.current);
            (activeElementRef.current as HTMLElement).focus();
          }
          activeElementRef.current = null;
        }, 100);
      }
    } catch (error) {
      console.warn('Failed to toggle browser view:', error);
    }
  }, [preserveFocus]);

  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // On first mount, immediately hide browser view if overlay is visible
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;
      if (isVisible) {
        toggleBrowserView(false);
        lastStateRef.current = isVisible;
        return;
      }
    }

    // For subsequent changes, use debounced approach
    timeoutRef.current = setTimeout(() => {
      // Only toggle if the state has actually changed and enough time has passed
      if (lastStateRef.current !== isVisible) {
        toggleBrowserView(!isVisible);
        lastStateRef.current = isVisible;
      }
    }, debounceDelay);

    // Cleanup function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isVisible, debounceDelay, toggleBrowserView]);

  // Cleanup on unmount - always show browser view
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      // Show browser view when component unmounts
      toggleBrowserView(true);
    };
  }, [toggleBrowserView]);
};

/**
 * Focus-aware hook to control browser view visibility specifically for search inputs
 * Only hides browser view when search input actually has focus, not just when overlay is visible
 * @param searchInputRef - Ref to the search input element
 * @param overlayVisible - Whether the search overlay is visible
 * @param debounceDelay - Delay in milliseconds before applying the change (default: 200ms)
 */
export const useSearchInputBrowserViewControl = (
  searchInputRef: React.RefObject<any>,
  overlayVisible: boolean,
  debounceDelay: number = 200
) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const browserViewHiddenRef = useRef<boolean>(false);
  const lastFocusStateRef = useRef<boolean>(false);

  const toggleBrowserView = useCallback(async (shouldShow: boolean) => {
    const electronAPI = (window as any).electronAPI;
    if (!electronAPI || !electronAPI.toggleBrowserView) {
      return;
    }

    try {
      console.log(`[SearchInputBrowserViewControl] ${shouldShow ? 'Showing' : 'Hiding'} browser view`);
      await electronAPI.toggleBrowserView(shouldShow);
      browserViewHiddenRef.current = !shouldShow;
    } catch (error) {
      console.warn('Failed to toggle browser view:', error);
    }
  }, []);

  const checkAndToggleBrowserView = useCallback(() => {
    if (!searchInputRef.current) return;

    const inputElement = searchInputRef.current.input || searchInputRef.current;
    const hasFocus = document.activeElement === inputElement;
    const shouldHide = overlayVisible && hasFocus;

    console.log(`[SearchInputBrowserViewControl] Focus check: hasFocus=${hasFocus}, overlayVisible=${overlayVisible}, shouldHide=${shouldHide}`);

    // Only toggle if the focus state has actually changed
    if (lastFocusStateRef.current !== shouldHide) {
      lastFocusStateRef.current = shouldHide;
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        toggleBrowserView(!shouldHide);
      }, debounceDelay);
    }
  }, [searchInputRef, overlayVisible, debounceDelay, toggleBrowserView]);

  // Monitor focus changes on the search input
  useEffect(() => {
    if (!searchInputRef.current) return;

    const inputElement = searchInputRef.current.input || searchInputRef.current;
    
    const handleFocus = () => {
      console.log('[SearchInputBrowserViewControl] Search input gained focus');
      checkAndToggleBrowserView();
    };
    
    const handleBlur = () => {
      console.log('[SearchInputBrowserViewControl] Search input lost focus');
      // Delay the check to allow for overlay interactions
      setTimeout(checkAndToggleBrowserView, 150);
    };

    inputElement.addEventListener('focus', handleFocus);
    inputElement.addEventListener('blur', handleBlur);

    return () => {
      inputElement.removeEventListener('focus', handleFocus);
      inputElement.removeEventListener('blur', handleBlur);
    };
  }, [checkAndToggleBrowserView]);

  // Monitor overlay visibility changes
  useEffect(() => {
    checkAndToggleBrowserView();
  }, [checkAndToggleBrowserView]);

  // Cleanup on unmount - always show browser view
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (browserViewHiddenRef.current) {
        toggleBrowserView(true);
      }
    };
  }, [toggleBrowserView]);
}; 