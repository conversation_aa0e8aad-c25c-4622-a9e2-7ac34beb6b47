import React, { useEffect, useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import ResetPassword from './pages/ResetPassword';
import { I18nUtils } from './pages/ruyiDevice/languageSupport/i18nUtils';
import { LanguageProvider } from './pages/ruyiDevice/languageSupport/LanguageContext';
import { Spin } from 'antd';
import { userApi, tokenApi } from '../services/api'; // 确保引入 tokenApi

interface LoginProps {
  setIsLoggedIn: (isLoggedIn: boolean) => void;
}

const App = () => {
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);

  useEffect(() => {
    const initializeApp = async () => {
      await I18nUtils.initialize();

      // 检查用户是否已登录
      try {
        const userInfoResponse = await userApi.getUserInfo();
        if (userInfoResponse.success && userInfoResponse.data) {
          const userInfo = userInfoResponse.data;
          const tokenResponse = await tokenApi.getApiKey();
          await window.electronAPI.setApiKeyConfig(tokenResponse.data[0].key);
          
          // 获取用户信息并设置当前用户
          await window.electronAPI.setCurrentUser({
            username: userInfo.username,
            displayName: userInfo.display_name,
            email: userInfo.email,
            uuid: userInfo.id
          });
          console.log('自动登录 - 当前用户已设置:', userInfo.username);
          setIsLoggedIn(true);
        } else {
          setIsLoggedIn(false);
          // 如果在 dashboard 页面但未登录，则重定向到登录页
          if (window.location.hash.includes('/dashboard')) {
            window.location.hash = '/login';
          }
        }
      } catch (error) {
        setIsLoggedIn(false);
        if (window.location.hash.includes('/dashboard')) {
          window.location.hash = '/login';
        }
      }

      setIsInitialized(true);
    };
    initializeApp();
  }, []);

  if (!isInitialized) {
    return <Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }} />;
  }

  return (
    <LanguageProvider>
      <Routes>
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/" element={isLoggedIn ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />} />
        <Route path="/login" element={<Login setIsLoggedIn={setIsLoggedIn} />} />
        <Route path="/register" element={<Register />} />
        <Route path="/reset-password" element={<ResetPassword />} />
      </Routes>
    </LanguageProvider>
  );
};

export default App;