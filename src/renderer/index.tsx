import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>hRouter } from 'react-router-dom';
import App from './App';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import './pages/styles/fonts.css';

// 在生产环境中动态加载字体
if (!process.env.NODE_ENV || process.env.NODE_ENV === 'production') {
  const loadProductionFonts = async () => {
    try {
      // 获取字体文件路径
      const hackNerdFontPath = await window.electronAPI.getFontPath('hacknerd.ttf');
      const mapleCNFontPath = await window.electronAPI.getFontPath('maplecn.ttf');
      
      // 创建样式元素
      const style = document.createElement('style');
      style.textContent = `
        @font-face {
          font-family: 'HackNerdFont';
          src: url('${hackNerdFontPath}') format('truetype');
          font-weight: normal;
          font-style: normal;
          font-display: swap;
        }
        
        @font-face {
          font-family: 'MapleCNFont';
          src: url('${mapleCNFontPath}') format('truetype');
          font-weight: normal;
          font-style: normal;
          font-display: swap;
        }
      `;
      
      // 添加样式到文档头
      document.head.appendChild(style);
      console.log('Production fonts loaded successfully');
    } catch (error) {
      console.error('Failed to load production fonts:', error);
    }
  };
  
  loadProductionFonts();
}

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <ConfigProvider locale={zhCN}>
      <HashRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}
      >
        <App />
      </HashRouter>
    </ConfigProvider>
  </React.StrictMode>
);