import React from 'react';
import { notification, Button, Space, Select } from 'antd';
import { UpdatePreferencesManager } from '../utils/updatePreferences';
import { I18nUtils } from '../pages/ruyiDevice/languageSupport/i18nUtils';

export interface UpdateInfo {
  version: string;
  type: 'available-confirm' | 'downloaded' | 'not-available' | 'checking' | 'progress' | 'error';
  message?: string;
  needsConfirm?: boolean;
  showRestart?: boolean;
  percent?: number;
  error?: string;
}

/**
 * Improved update notification system
 * Provides less intrusive notifications with user control options
 */
export class UpdateNotificationManager {
  private static currentNotificationKey: string | null = null;

  /**
   * Show update available notification with user options
   */
  static async showUpdateAvailable(updateInfo: UpdateInfo): Promise<void> {
    const { version } = updateInfo;

    // Check if user has dismissed this version or wants to be reminded later
    const shouldShow = await UpdatePreferencesManager.shouldShowUpdate(version);
    if (!shouldShow) {
      console.log(`Update ${version} is dismissed or user wants to be reminded later`);
      return;
    }

    const key = `update-available-${version}`;
    this.currentNotificationKey = key;

    notification.open({
      key,
      message: I18nUtils.getText('newVersionFoundTitle'),
      description: (
        <div style={{ marginTop: '8px' }}>
          <p>{I18nUtils.getText('newVersionFoundContent')} {version}</p>
          <Space direction="vertical" style={{ width: '100%', marginTop: '12px' }}>
            <Space wrap>
              <Button 
                type="primary" 
                size="small"
                onClick={() => {
                  notification.destroy(key);
                  window.electronAPI.confirmDownloadUpdate(true);
                }}
              >
                {I18nUtils.getText('downloadNow')}
              </Button>
              <Button 
                size="small"
                onClick={() => {
                  notification.destroy(key);
                  this.showRemindLaterOptions(version);
                }}
              >
                {I18nUtils.getText('remindMeLater')}
              </Button>
              <Button
                size="small"
                onClick={async () => {
                  notification.destroy(key);
                  await UpdatePreferencesManager.dismissVersion(version);
                  // Trigger badge update
                  window.dispatchEvent(new CustomEvent('updatePreferencesChanged'));
                }}
              >
                {I18nUtils.getText('dismissThisVersion')}
              </Button>
            </Space>
          </Space>
        </div>
      ),
      duration: 0, // Don't auto-close
      placement: 'topRight',
      style: {
        width: '350px'
      },
      onClose: () => {
        this.currentNotificationKey = null;
      }
    });
  }

  /**
   * Show remind later options
   */
  private static showRemindLaterOptions(version: string): void {
    const key = `remind-later-${version}`;
    
    notification.open({
      key,
      message: I18nUtils.getText('remindMeLater'),
      description: (
        <div style={{ marginTop: '8px' }}>
          <p>{I18nUtils.getText('remindMeLaterDescription')}</p>
          <Space direction="vertical" style={{ width: '100%', marginTop: '12px' }}>
            <Space wrap>
              <Button
                size="small"
                onClick={async () => {
                  await UpdatePreferencesManager.setRemindLater(version, 1);
                  notification.destroy(key);
                  notification.success({
                    message: I18nUtils.getText('remindLaterSet'),
                    description: I18nUtils.getText('remindLater1Hour'),
                    duration: 3
                  });
                  window.dispatchEvent(new CustomEvent('updatePreferencesChanged'));
                }}
              >
                {I18nUtils.getText('in1Hour')}
              </Button>
              <Button
                size="small"
                onClick={async () => {
                  await UpdatePreferencesManager.setRemindLater(version, 24);
                  notification.destroy(key);
                  notification.success({
                    message: I18nUtils.getText('remindLaterSet'),
                    description: I18nUtils.getText('remindLater1Day'),
                    duration: 3
                  });
                  window.dispatchEvent(new CustomEvent('updatePreferencesChanged'));
                }}
              >
                {I18nUtils.getText('in1Day')}
              </Button>
              <Button
                size="small"
                onClick={async () => {
                  await UpdatePreferencesManager.setRemindLater(version, 168); // 7 days
                  notification.destroy(key);
                  notification.success({
                    message: I18nUtils.getText('remindLaterSet'),
                    description: I18nUtils.getText('remindLater1Week'),
                    duration: 3
                  });
                  window.dispatchEvent(new CustomEvent('updatePreferencesChanged'));
                }}
              >
                {I18nUtils.getText('in1Week')}
              </Button>
            </Space>
          </Space>
        </div>
      ),
      duration: 10, // Auto-close after 10 seconds
      placement: 'topRight',
      style: {
        width: '300px'
      }
    });
  }

  /**
   * Show update downloaded notification
   */
  static showUpdateDownloaded(updateInfo: UpdateInfo): void {
    const key = 'update-downloaded';
    
    notification.info({
      key,
      message: I18nUtils.getText('newVersionTitle'),
      description: (
        <div>
          <p>{I18nUtils.getText('updateDownloadedMessage')}</p>
          <p><strong>{I18nUtils.getText('saveWorkReminder')}</strong></p>
          <Button 
            type="primary" 
            size="small"
            onClick={() => {
              notification.destroy(key);
              window.electronAPI.quitAndInstall();
            }}
            style={{ marginTop: '10px' }}
          >
            {I18nUtils.getText('restartNow')}
          </Button>
        </div>
      ),
      duration: 0,
      placement: 'topRight',
      style: {
        width: '300px'
      }
    });
  }

  /**
   * Show simple status messages
   */
  static showStatusMessage(updateInfo: UpdateInfo): void {
    const { type, message, percent } = updateInfo;
    
    switch (type) {
      case 'not-available':
        notification.info({
          message: I18nUtils.getText('updateNotAvailable'),
          duration: 3,
          placement: 'topRight'
        });
        break;
      case 'checking':
        notification.info({
          message: I18nUtils.getText('checkingUpdate'),
          duration: 2,
          placement: 'topRight'
        });
        break;
      case 'progress':
        if (percent !== undefined) {
          notification.info({
            key: 'update-progress',
            message: I18nUtils.getText('updateProgress'),
            description: `${I18nUtils.getText('downloadProgress')}: ${Math.floor(percent)}%`,
            duration: 0,
            placement: 'topRight'
          });
        }
        break;
      case 'error':
        notification.error({
          message: I18nUtils.getText('updateError'),
          description: message,
          duration: 5,
          placement: 'topRight'
        });
        break;
    }
  }

  /**
   * Handle update message from main process
   */
  static async handleUpdateMessage(updateInfo: UpdateInfo): Promise<void> {
    switch (updateInfo.type) {
      case 'available-confirm':
        if (updateInfo.needsConfirm) {
          await this.showUpdateAvailable(updateInfo);
        }
        break;
      case 'downloaded':
        if (updateInfo.showRestart) {
          this.showUpdateDownloaded(updateInfo);
        }
        break;
      default:
        this.showStatusMessage(updateInfo);
        break;
    }
  }

  /**
   * Clear all update notifications
   */
  static clearAllNotifications(): void {
    if (this.currentNotificationKey) {
      notification.destroy(this.currentNotificationKey);
      this.currentNotificationKey = null;
    }
    notification.destroy('update-progress');
    notification.destroy('update-downloaded');
  }
}
