import React from 'react';
import { Badge } from 'antd';

interface UpdateBadgeProps {
  children: React.ReactNode;
  show: boolean;
  size?: 'small' | 'default';
  offset?: [number, number];
  className?: string;
}

/**
 * Update badge component for showing red dot indicators
 * Used on update button when updates are available
 */
export const UpdateBadge: React.FC<UpdateBadgeProps> = ({
  children,
  show,
  size = 'small',
  offset,
  className
}) => {
  if (!show) {
    return <>{children}</>;
  }

  return (
    <Badge
      dot
      size={size}
      offset={offset}
      className={className}
      style={{
        '--ant-color-error': '#ff4d4f',
      } as React.CSSProperties}
    >
      {children}
    </Badge>
  );
};

/**
 * CSS styles for update badges
 * Add this to your global CSS or component styles
 */
export const updateBadgeStyles = `
  .update-badge-button .ant-badge-dot {
    background-color: #ff4d4f;
    box-shadow: 0 0 0 1px #fff;
    animation: update-pulse 2s infinite;
  }

  @keyframes update-pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Ensure badge is visible on different backgrounds */
  .ant-badge-dot {
    z-index: 10;
  }
`;

export default UpdateBadge;
