/**
 * Search Engine Configuration and Utilities
 */

export interface SearchEngine {
  id: string;
  name: string;
  searchUrl: string;
  homepage: string;
}

export const SEARCH_ENGINES: Record<string, SearchEngine> = {
  google: {
    id: 'google',
    name: 'Google',
    searchUrl: 'https://www.google.com/search?q=',
    homepage: 'https://www.google.com'
  },
  baidu: {
    id: 'baidu',
    name: '百度',
    searchUrl: 'https://www.baidu.com/s?wd=',
    homepage: 'https://www.baidu.com'
  },
  bing: {
    id: 'bing',
    name: 'Bing',
    searchUrl: 'https://www.bing.com/search?q=',
    homepage: 'https://www.bing.com'
  },
  sougou: {
    id: 'sougou',
    name: '搜狗',
    searchUrl: 'https://www.sogou.com/web?query=',
    homepage: 'https://www.sogou.com'
  }
};

/**
 * Get search engine configuration by ID
 * @param engineId Search engine identifier
 * @returns Search engine configuration
 */
export function getSearchEngine(engineId: string): SearchEngine {
  return SEARCH_ENGINES[engineId] || SEARCH_ENGINES.baidu;
}

/**
 * Generate search URL for a given query and search engine
 * @param query Search query
 * @param engineId Search engine identifier
 * @returns Complete search URL
 */
export function generateSearchUrl(query: string, engineId: string = 'baidu'): string {
  const engine = getSearchEngine(engineId);
  return engine.searchUrl + encodeURIComponent(query);
}

/**
 * Get homepage URL for a search engine
 * @param engineId Search engine identifier
 * @returns Homepage URL
 */
export function getSearchEngineHomepage(engineId: string): string {
  const engine = getSearchEngine(engineId);
  return engine.homepage;
}

/**
 * Get all available search engines
 * @returns Array of search engine configurations
 */
export function getAllSearchEngines(): SearchEngine[] {
  return Object.values(SEARCH_ENGINES);
}

/**
 * Validate if a search engine ID is supported
 * @param engineId Search engine identifier
 * @returns true if supported, false otherwise
 */
export function isValidSearchEngine(engineId: string): boolean {
  return engineId in SEARCH_ENGINES;
}

/**
 * Get search engine options for UI components
 * @returns Array of option objects with value and label
 */
export function getSearchEngineOptions(): Array<{ value: string; label: string }> {
  return getAllSearchEngines().map(engine => ({
    value: engine.id,
    label: engine.name
  }));
} 