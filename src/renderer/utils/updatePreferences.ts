/**
 * Update preferences management utility
 * Handles user preferences for update notifications including:
 * - Remind me later functionality
 * - Dismiss specific version functionality
 * - Update notification display preferences
 */

export interface UpdatePreferences {
  // Dismissed versions that user doesn't want to see again
  dismissedVersions: string[];
  // Remind later settings
  remindLater: {
    version?: string;
    remindAfter?: number; // timestamp
  };
  // Notification preferences
  showBadgeOnButton: boolean;
  showBadgeOnAvatar: boolean;
  notificationMethod: 'modal' | 'notification' | 'badge-only';
}

const DEFAULT_PREFERENCES: UpdatePreferences = {
  dismissedVersions: [],
  remindLater: {},
  showBadgeOnButton: true,
  showBadgeOnAvatar: true,
  notificationMethod: 'notification'
};

const STORAGE_KEY = 'ruyi-update-preferences';

export class UpdatePreferencesManager {
  private static preferences: UpdatePreferences | null = null;

  /**
   * Load preferences from electron store
   */
  static async loadPreferences(): Promise<UpdatePreferences> {
    if (this.preferences) {
      return this.preferences;
    }

    try {
      const stored = await window.electronAPI.getUpdatePreferences();
      this.preferences = { ...DEFAULT_PREFERENCES, ...stored };
    } catch (error) {
      console.error('Failed to load update preferences:', error);
      this.preferences = { ...DEFAULT_PREFERENCES };
    }

    return this.preferences;
  }

  /**
   * Save preferences to electron store
   */
  static async savePreferences(preferences: UpdatePreferences): Promise<void> {
    try {
      await window.electronAPI.setUpdatePreferences(preferences);
      this.preferences = preferences;
    } catch (error) {
      console.error('Failed to save update preferences:', error);
    }
  }

  /**
   * Check if a version is dismissed
   */
  static async isVersionDismissed(version: string): Promise<boolean> {
    const prefs = await this.loadPreferences();
    return prefs.dismissedVersions.includes(version);
  }

  /**
   * Dismiss a specific version
   */
  static async dismissVersion(version: string): Promise<void> {
    try {
      await window.electronAPI.dismissUpdateVersion(version);
      // Update local cache
      const prefs = await this.loadPreferences();
      if (!prefs.dismissedVersions.includes(version)) {
        prefs.dismissedVersions.push(version);
        this.preferences = prefs;
      }
    } catch (error) {
      console.error('Failed to dismiss version:', error);
    }
  }

  /**
   * Check if user wants to be reminded later for a version
   */
  static async shouldRemindLater(version: string): Promise<boolean> {
    const prefs = await this.loadPreferences();
    const { remindLater } = prefs;

    if (remindLater.version !== version) {
      return false;
    }

    if (!remindLater.remindAfter) {
      return false;
    }

    return Date.now() < remindLater.remindAfter;
  }

  /**
   * Set remind later for a version
   */
  static async setRemindLater(version: string, hours: number = 24): Promise<void> {
    try {
      await window.electronAPI.setUpdateRemindLater(version, hours);
      // Update local cache
      const prefs = await this.loadPreferences();
      prefs.remindLater = {
        version,
        remindAfter: Date.now() + (hours * 60 * 60 * 1000)
      };
      this.preferences = prefs;
    } catch (error) {
      console.error('Failed to set remind later:', error);
    }
  }

  /**
   * Clear remind later setting
   */
  static async clearRemindLater(): Promise<void> {
    const prefs = await this.loadPreferences();
    prefs.remindLater = {};
    await this.savePreferences(prefs);
  }

  /**
   * Check if update should be shown to user
   */
  static async shouldShowUpdate(version: string): Promise<boolean> {
    const isDismissed = await this.isVersionDismissed(version);
    const shouldRemind = await this.shouldRemindLater(version);
    return !isDismissed && !shouldRemind;
  }

  /**
   * Get current preferences
   */
  static async getPreferences(): Promise<UpdatePreferences> {
    return await this.loadPreferences();
  }

  /**
   * Update specific preference
   */
  static async updatePreference<K extends keyof UpdatePreferences>(
    key: K,
    value: UpdatePreferences[K]
  ): Promise<void> {
    const prefs = await this.loadPreferences();
    prefs[key] = value;
    await this.savePreferences(prefs);
  }

  /**
   * Reset all preferences to default
   */
  static async resetPreferences(): Promise<void> {
    this.preferences = { ...DEFAULT_PREFERENCES };
    await this.savePreferences(this.preferences);
  }

  /**
   * Clean up old dismissed versions (optional, for maintenance)
   */
  static async cleanupOldDismissedVersions(keepCount: number = 10): Promise<void> {
    const prefs = await this.loadPreferences();
    if (prefs.dismissedVersions.length > keepCount) {
      // Keep only the most recent dismissed versions
      prefs.dismissedVersions = prefs.dismissedVersions.slice(-keepCount);
      await this.savePreferences(prefs);
    }
  }
}

