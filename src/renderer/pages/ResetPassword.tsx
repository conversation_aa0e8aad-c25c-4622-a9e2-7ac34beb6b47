import React, { useState } from 'react';
import { Form, Input, Button, Card, message } from 'antd';
import { MailOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { userApi } from '../../services/api';
import { useLanguage } from './ruyiDevice/languageSupport/LanguageContext';
import { I18nUtils } from './ruyiDevice/languageSupport/i18nUtils';
import './styles/auth.css';

interface ResetPasswordFormValues {
  email: string;
}

const ResetPassword = () => {
  const [form] = Form.useForm<ResetPasswordFormValues>();
  const { language } = useLanguage();
  const [countdown, setCountdown] = useState<number>(0);

  const startCountdown = () => {
    setCountdown(30);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const onFinish = async (values: ResetPasswordFormValues) => {
    try {
      const response = await userApi.sendResetPasswordEmail(values.email);
      if (response.success) {
        message.success(I18nUtils.getText('resetPasswordSuccess'));
        startCountdown();
      } else {
        message.error(response.message || I18nUtils.getText('resetPasswordFailed'));
      }
    } catch (error: any) {
      message.error(error.message || I18nUtils.getText('resetPasswordFailed'));
    }
  };

  return (
    <div className="auth-container">
      <Card className="auth-card" title={I18nUtils.getText('resetPasswordTitle')}>
        <Form
          form={form}
          name="resetPassword"
          onFinish={onFinish}
          autoComplete="off"
          layout="vertical"
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: I18nUtils.getText('emailRequired') },
              { type: 'email', message: I18nUtils.getText('emailFormatInvalid') }
            ]}
          >
            <Input
              className="auth-input"
              prefix={<MailOutlined />}
              placeholder={I18nUtils.getText('emailPlaceholder')}
            />
          </Form.Item>

          <Form.Item>
            <Button
              className="auth-button"
              type="primary"
              htmlType="submit"
              block
              disabled={countdown > 0}
            >
              {countdown > 0
                ? I18nUtils.getText('retryInSeconds').replace('{seconds}', countdown.toString())
                : I18nUtils.getText('resetPasswordButton')
              }
            </Button>
          </Form.Item>

          <div style={{ textAlign: 'center' }}>
            <Link className="auth-link" to="/login">{I18nUtils.getText('backToLogin')}</Link>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default ResetPassword;