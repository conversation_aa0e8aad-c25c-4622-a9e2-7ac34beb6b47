import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, message } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import { userApi } from '../../services/api';
import { useLanguage } from './ruyiDevice/languageSupport/LanguageContext';
import { I18nUtils } from './ruyiDevice/languageSupport/i18nUtils';
import './styles/auth.css';

interface RegisterFormValues {
  username?: string;
  email?: string;
  verification_code?: string;
  password?: string;
  confirmPassword?: string;
}

const Register = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm<RegisterFormValues>();
  const { language } = useLanguage();
  const [countdown, setCountdown] = useState<number>(0);
  const [isVerificationSent, setIsVerificationSent] = useState<boolean>(false);
  const [emailVerificationEnabled, setEmailVerificationEnabled] = useState(true); // 默认启用邮箱

  useEffect(() => {
    // 检查服务器状态以确定是否启用邮箱验证
    const checkServerStatus = async () => {
      try {
        const statusResponse = await userApi.status();
        if (statusResponse.success && statusResponse.data) {
          setEmailVerificationEnabled(statusResponse.data.email_verification || false);
        }
      } catch (error) {
        console.warn('Failed to check server status for email verification:', error);
        // 发生错误时保持默认值 true
      }
    };

    checkServerStatus();
  }, []);

  // 倒计时处理函数
  const startCountdown = () => {
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const onFinish = async (values: RegisterFormValues) => {
    try {
      const response = await userApi.register(
        values.username!,
        values.password!,
        values.confirmPassword!,
        emailVerificationEnabled ? values.email! : '',
        emailVerificationEnabled ? values.verification_code! : '',
        null
      );
      console.log("login response:", response);

      if (response.success) {
        message.success(I18nUtils.getText('registerSuccess'));
        navigate('/login');
      } else {
        message.error(response.message || I18nUtils.getText('registerFailed'));
      }
    } catch (error: any) {
      message.error(error.message || I18nUtils.getText('registerFailed'));
    }
  };

  const sendVerificationCode = async () => {
    try {
      const email = form.getFieldValue('email');
      if (!email) {
        message.error(I18nUtils.getText('emailRequired'));
        return;
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        message.error(I18nUtils.getText('emailFormatInvalid'));
        return;
      }

      const verification_res = await userApi.verification(email);
      if (verification_res.success) {
        setIsVerificationSent(true);
        startCountdown();
        message.success(I18nUtils.getText('verificationCodeSent'));
      } else {
        message.error(verification_res.message || I18nUtils.getText('verificationCodeFailed'));
      }
    } catch (error: any) {
      message.error(error.message || I18nUtils.getText('verificationCodeFailed'));
    }
  };

  return (
    <div className="auth-container">
      <Card className="auth-card" title={I18nUtils.getText('registerTitle')}>
        <Form
          form={form}
          name="register"
          onFinish={onFinish}
          autoComplete="off"
          layout="vertical"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: I18nUtils.getText('usernameRequired') },
              {
                validator: (_, value) => {
                  if (value && /^\d/.test(value)) {
                    return Promise.reject(new Error(I18nUtils.getText('usernameCannotStartWithNumber')));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input className="auth-input" prefix={<UserOutlined />} placeholder={emailVerificationEnabled ? I18nUtils.getText('usernamePlaceholder') : I18nUtils.getText('usernameOnlyPlaceholder')} />
          </Form.Item>

          {emailVerificationEnabled && (
            <Form.Item
              name="email"
              validateTrigger="onBlur"
              rules={[
                { required: true, message: I18nUtils.getText('emailRequired') },
                {
                  type: 'email',
                  message: I18nUtils.getText('emailFormatInvalid'),
                },
              ]}
            >
              <Input className="auth-input" prefix={<MailOutlined />} placeholder={I18nUtils.getText('emailPlaceholder')} />
            </Form.Item>
          )}

          {emailVerificationEnabled && (
            <Form.Item
              name="verification_code"
              rules={[{ required: true, message: I18nUtils.getText('verificationCodeRequired') }]}
            >
              <div className="verification-container">
                <Input
                  className="auth-input verification-input"
                  placeholder={I18nUtils.getText('verificationCodePlaceholder')}
                />
                <Button
                  className="verification-button"
                  onClick={sendVerificationCode}
                  disabled={countdown > 0}
                >
                  {countdown > 0
                    ? `${countdown}s`
                    : I18nUtils.getText('sendVerificationCode')}
                </Button>
              </div>
            </Form.Item>
          )}

          <Form.Item
            name="password"
            rules={[
              { required: true, message: I18nUtils.getText('passwordRequired') },
              { min: 8, message: I18nUtils.getText('passwordMinLength') },
              {
                pattern: /^(?=.*[a-zA-Z])(?=.*[0-9])/,
                message: I18nUtils.getText('passwordComplexity'),
              },
            ]}
          >
            <Input.Password className="auth-input" prefix={<LockOutlined />} placeholder={I18nUtils.getText('passwordPlaceholder')} />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            dependencies={['password']}
            rules={[
              { required: true, message: I18nUtils.getText('confirmPasswordRequired') },
              { min: 8, message: I18nUtils.getText('passwordMinLength') },
              {
                pattern: /^(?=.*[a-zA-Z])(?=.*[0-9])/,
                message: I18nUtils.getText('passwordComplexity'),
              },
              ({
                getFieldValue
              }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(I18nUtils.getText('passwordMismatch')));
                },
              }),
            ]}
          >
            <Input.Password className="auth-input" prefix={<LockOutlined />} placeholder={I18nUtils.getText('confirmPasswordPlaceholder')} />
          </Form.Item>

          <Form.Item>
            <Button className="auth-button" type="primary" htmlType="submit" block>
              {I18nUtils.getText('registerButton')}
            </Button>
          </Form.Item>

          <div style={{ textAlign: 'center' }}>
            {I18nUtils.getText('haveAccount')} <Link className="auth-link" to="/login">{I18nUtils.getText('loginNow')}</Link>
            {emailVerificationEnabled && (
              <>
                <span style={{ margin: '0 8px' }}>|</span>
                <Link className="auth-link" to="/reset-password">{I18nUtils.getText('forgotPassword')}</Link>
              </>
            )}
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default Register;