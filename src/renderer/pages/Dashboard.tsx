import React, { useState, useEffect, useRef, FormEvent, ChangeEvent, useCallback } from 'react';
import { Layout, Button, Input, message, Modal, Spin, notification, Drawer } from 'antd';
import { ChatInterface } from './ruyiDevice/ChatInterface';
import { I18nUtils } from './ruyiDevice/languageSupport/i18nUtils';
import { CodeEditor } from './ruyiDevice/CodeEditor';
import { Row, Col } from 'antd';
import { Toolbar } from './ruyiDevice/Toolbar';
import { NavBarComponent } from './ruyiDevice/NavBar';
import './styles/antd.css';
import './styles/Dashboard.css';
import Select, { Device } from './ruyiDevice/Select';
import { InstallationProgress, ConfigurationGuide } from './ruyiDevice/RuyiClientGuide';
import { flaskApi } from '../../services/FlaskApiService';
import { DeviceState } from './ruyiDevice/DeviceState';
import { LogPanel } from './ruyiDevice/LogPanel';
import { userApi, systemApi } from '../../services/api';
import { useLanguage } from './ruyiDevice/languageSupport/LanguageContext';
import { TablePanel } from './ruyiDevice/TablePanel';
import { ImagePanel } from './ruyiDevice/ImagePanel';
import { TaskFilesList } from './ruyiDevice/TaskFilesList';
import { UpdateNotificationManager } from '../components/UpdateNotification';
import { DeviceProvider } from './ruyiDevice/DeviceContext';
import { StateManager } from './ruyiDevice/StateManager';

interface BrowserState {
  isLoading: boolean;
  canGoBack: boolean;
  canGoForward: boolean;
  currentUrl: string;
  error: {
    errorCode: number;
    errorDescription: string;
  } | null;
}

const Dashboard = () => {
  // I18nUtils.initialize();
  const { language } = useLanguage();
  const [deviceId, setDeviceId] = useState('');
  const [iframeSrc, setIframeSrc] = useState('');
  const [showDeviceSelect, setShowDeviceSelect] = useState(true);
  const [manualDevices, setManualDevices] = useState<Device[]>([]);
  const [isInstallingClient, setIsInstallingClient] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerContentKey, setDrawerContentKey] = useState<string | null>(null);
  const [scrcpyPort, setScrcpyPort] = useState(8000);

  const [deviceBrand, setDeviceBrand] = useState('');
  const [deviceProduct, setDeviceProduct] = useState('');
  const [selectedDevice, setSelectedDevice] = useState<any>(null);
  
  const [isRuyiClientConnected, setIsRuyiClientConnected] = useState(false);
  const [isRuyiClientAuthorized, setIsRuyiClientAuthorized] = useState(false);
  const [isRuyiClientInstalled, setIsRuyiClientInstalled] = useState(false);

  const [logPanelVisible, setLogPanelVisible] = useState(true);
  const [logPanelHeight, setLogPanelHeight] = useState(120); // Changed from 150 to 120
  
  // Add state for left container heights - combined into single state to prevent multiple re-renders
  const [leftContainerHeights, setLeftContainerHeights] = useState(() => {
    const containerHeight = window.innerHeight - 40; // Account for navbar
    const chatHeight = Math.floor(containerHeight * 0.7 - 12); // 70% minus margin - ChatInterface gets more space
    const taskFilesHeight = Math.floor(containerHeight * 0.3 - 12); // 30% minus margin - TaskFilesList gets less space
    return { chatHeight, taskFilesHeight };
  });

  // Add dragging state to optimize rendering during resize
  const [isDraggingChat, setIsDraggingChat] = useState(false);

  // Optimized height change handler with throttling
  const handleChatHeightChange = useCallback((newHeight: number) => {
    // Calculate the remaining space for task files
    const containerHeight = window.innerHeight - 40;
    const newTaskFilesHeight = containerHeight - newHeight - 24;

    // Update both heights in a single state update to prevent multiple re-renders
    setLeftContainerHeights({
      chatHeight: newHeight,
      taskFilesHeight: newTaskFilesHeight
    });
  }, []);

  // Handle window resize to maintain proportional heights
  useEffect(() => {
    const handleResize = () => {
      const containerHeight = window.innerHeight - 40;
      // Maintain the 70:30 ratio for chat:taskFiles
      const chatHeight = Math.floor(containerHeight * 0.7 - 12);
      const taskFilesHeight = Math.floor(containerHeight * 0.3 - 12);

      setLeftContainerHeights({
        chatHeight,
        taskFilesHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []); // Remove dependency to avoid unnecessary re-renders

  const [tableData, setTableData] = useState<any | null>(null);
  const [tablePanelVisible, setTablePanelVisible] = useState(false);

  const [imageData, setImageData] = useState<{ metadata: any; image_data: string } | null>(null);
  const [imagePanelVisible, setImagePanelVisible] = useState(false);

  // 检查用户 Token 余量
  let tokenModalShown = false;

  const noticeShownRef = useRef(false);

  // 添加浏览器状态
  const [browserState, setBrowserState] = useState<BrowserState>({
    isLoading: false,
    canGoBack: false,
    canGoForward: false,
    currentUrl: '',
    error: null
  });

  // 设备连接状态相关
  const [isDeviceConnected, setIsDeviceConnected] = useState<boolean>(true);
  const [isMonitoringDevice, setIsMonitoringDevice] = useState<boolean>(false);
  const [iframeKey, setIframeKey] = useState<number>(0); // 用于强制重新加载iframe
  const [previousConnectionStatus, setPreviousConnectionStatus] = useState<boolean | null>(null); // 跟踪前一次连接状态
  const [isReconnecting, setIsReconnecting] = useState<boolean>(false); // 跟踪重连状态

  const checkRuyiClientStatus = async () => {
    // 如果是浏览器设备，不需要检查 Ruyi Client 状态
    if (selectedDevice?.type === 'browser') {
      setIsRuyiClientInstalled(true);
      setIsRuyiClientConnected(true);
      setIsRuyiClientAuthorized(true);
      return;
    }

    try {
      const [installedRes, connectedRes, authorizedRes] = await Promise.all([
        flaskApi.checkRuyiClientInstalled(deviceId),
        flaskApi.checkRuyiClientConnected(deviceId),
        flaskApi.checkRuyiClientAuthorized(deviceId)
      ]);
      
      setIsRuyiClientInstalled(installedRes.is_ruyi_client_installed);
      setIsRuyiClientConnected(connectedRes.is_ruyi_client_connected);
      setIsRuyiClientAuthorized(authorizedRes.is_ruyi_client_authorized);
    } catch (error) {
      console.error('状态检测失败:', error);
    }
  };

  useEffect(() => {
    let intervalId: ReturnType<typeof setInterval>;
    const startPolling = () => {
      // 根据当前状态动态调整检测间隔
      const baseInterval = 1000; // 基础检测间隔5秒
      const allTrue = isRuyiClientInstalled && isRuyiClientConnected && isRuyiClientAuthorized;
      intervalId = setInterval(checkRuyiClientStatus, allTrue ? 1000 : baseInterval);
    };

    if (deviceId) {
      checkRuyiClientStatus(); // 立即执行一次检测
      startPolling();
    }
    
    return () => clearInterval(intervalId);
  }, [deviceId, isRuyiClientInstalled, isRuyiClientConnected, isRuyiClientAuthorized]);

  useEffect(() => {
    // 获取配置文件中的端口号
    const getScrcpyPort = async () => {
      try {
        const config = await window.electronAPI.getConfig();
        setScrcpyPort(config.scrcpy_port);
      } catch (error) {
        console.error('获取端口配置失败:', error);
        message.error(I18nUtils.getText('loadPortConfigFailed'));
      }
    };
    
    getScrcpyPort();
  }, []);

  // 在 useEffect 中监听 language 变化
  useEffect(() => {
    // 更新导航栏等需要翻译的内容
    console.log('Language changed, updating NavBar UI');
  }, [language]);

  useEffect(() => {
    if (deviceId) {
      setIframeSrc(show_h264_converter(deviceId));
      setShowDeviceSelect(false);
    }
  }, [deviceId]);

  useEffect(() => {
    // 组件加载时检查 Flask 服务器
    const checkFlaskServer = async () => {
      try {
        const result = await window.electronAPI.checkAndStartFlaskServer();
        if (!result.success) {
          message.error('Flask 服务器启动失败：' + result.message);
        }
      } catch (error) {
        message.error('检查 Flask 服务器状态失败：' + (error as any).message);
      }
    };
    
    checkFlaskServer();

    // 添加更新消息监听 - 使用新的UpdateNotificationManager
    const handleUpdateMessage = async (updateMessage: any) => {
      await UpdateNotificationManager.handleUpdateMessage(updateMessage);
    };

    window.electronAPI.onUpdateMessage(handleUpdateMessage);

    // 清理监听器
    return () => {
      window.electronAPI.offUpdateMessage && window.electronAPI.offUpdateMessage();
    };
  }, []);

  useEffect(() => {
    const initializeApi = async () => {
      await flaskApi.init();
    };
    
    initializeApi();

    ChatInterface.setOnShowTableCallback((data: any) => {
      setTableData(data);
      setTablePanelVisible(true);
    });

    ChatInterface.setOnShowImageCallback((data: any) => {
      setImageData(data);
      setImagePanelVisible(true);
    });

    // 监听用户切换事件
    const handleUserSwitched = (data: { previousUser: string; newUser: string }) => {
      console.log(`[Dashboard] 用户切换事件: ${data.previousUser} -> ${data.newUser}`);
      
      // 重置用户状态
      StateManager.resetToWelcomeState();
      
      // 可以在这里添加其他需要重置的UI状态
      setTableData(null);
      setTablePanelVisible(false);
      setImageData(null);
      setImagePanelVisible(false);
      
      console.log('[Dashboard] 用户切换后状态重置完成');
    };

    // 注册用户切换事件监听器
    window.electronAPI.onUserSwitched(handleUserSwitched);

    return () => {
      ChatInterface.setOnShowTableCallback(undefined);
      ChatInterface.setOnShowImageCallback(undefined);
      // 清理用户切换事件监听器
      window.electronAPI.offUserSwitched();
    }
  }, []);

  useEffect(() => {
    // 检查用户 Token 余量
    const checkTokenBalance = async () => {
      try {
        const response = await userApi.getUserInfo();
        if (response.success && response.data && response.data.quota === 0 && !tokenModalShown) {
          tokenModalShown = true;
          Modal.warning({
            title: I18nUtils.getText('tokenBalanceZeroTitle'),
            content: I18nUtils.getText('tokenBalanceZeroContent'),
            okText: I18nUtils.getText('confirm'),
            onOk: () => { tokenModalShown = false; },
            onCancel: () => { tokenModalShown = false; },
          });
        }
      } catch (error) {
        // 可以选择忽略或打印错误
        console.error('Token 检查失败:', error);
      }
    };
    checkTokenBalance();
  }, []);

  useEffect(() => {
    // 获取服务器通知
    const getServerNotice = async () => {
      if (noticeShownRef.current) return;
      noticeShownRef.current = true;
      try {
        const response = await systemApi.getNotice();
        console.log("----getServerNotice----", response);
        if (response.success && (response.data || response.message)) {
          
          const descriptionArr = [response.data, response.message].filter(Boolean);
          const description = (
            <div>
              {descriptionArr.map((line, idx) => (
                <div key={idx}>{line}</div>
              ))}
            </div>
          );

          Modal.info({
            title: I18nUtils.getText('serverNotice'),
            content: description,
            okText: I18nUtils.getText('confirm'),
            centered: true,
            width: 520,
            maskClosable: false,
            wrapClassName: 'server-notice-modal',
            maskStyle: {
              background: 'rgba(0, 0, 0, 0.65)',
              backdropFilter: 'blur(8px)',
            },
            icon: null,
          });
        }
      } catch (error) {
        console.error('获取服务器通知失败:', error);
      }
    };

    getServerNotice();
  }, []);

  const show_h264_converter = (deviceId: string) => {
    // return `http://localhost:8000/#!action=stream&udid=${deviceId}&player=mse&ws=ws%3A%2F%2Flocalhost%3A8000%2F%3Faction%3Dproxy-adb%26remote%3Dtcp%253A8886%26udid%3D${deviceId}`
    return `http://localhost:${scrcpyPort}#!action=stream&udid=${deviceId}&player=mse&ws=ws%3A%2F%2Flocalhost%3A${scrcpyPort}%2F%3Faction%3Dproxy-adb%26remote%3Dtcp%253A8886%26udid%3D${deviceId}`
  }

  // 注意：Ruyi Client 的检查和安装现在在 Select.tsx 中自动进行
  // 这个函数保留用于兼容性，但不再执行安装逻辑
  const checkAndInstallRuyiClient = async (deviceId: string) => {
    // 如果是浏览器设备，不需要安装 Ruyi Client
    if (selectedDevice?.type === 'browser') {
      setIsRuyiClientInstalled(true);
      return;
    }

    try {
      // 只检查状态，不再主动安装
      const checkResult = await flaskApi.checkRuyiClientInstalled(deviceId);
      setIsRuyiClientInstalled(checkResult.is_ruyi_client_installed);
      console.log("checkResult", checkResult);

      // 如果已安装，则获取版本信息并打印
      if (checkResult.is_ruyi_client_installed) {
        try {
          const versionResult = await flaskApi.checkRuyiClientVersion(deviceId);
          console.log(`设备 ${deviceId} 当前安装的 Ruyi Client 版本: ${versionResult.installed_version || '未获取到版本信息'}`);
          console.log(`设备 ${deviceId} 目标 Ruyi Client 版本: ${versionResult.target_version || '未获取到目标版本'}`);
          console.log(`设备 ${deviceId} 是否需要更新: ${versionResult.needs_update ? '是' : '否'}`);
        } catch (versionError) {
          console.error(`获取设备 ${deviceId} 版本信息失败:`, versionError);
        }
      }

      // 移除自动安装逻辑，因为在设备检测时已经自动处理
      // 如果需要手动触发安装，可以通过其他方式
    } catch (error) {
      console.error('RuyiAssistant status check failed:', error);
    }
  };

  const handleDeviceSelect = async (selectedDevice: any) => {
    try {
      setSelectedDevice(selectedDevice);
      
      // 将当前选择的设备暴露到全局，以便在 handleExecuteCode 函数中访问
      (window as any).__selectedDevice = selectedDevice;
      
      // 保存设备序列号到 RuyiAgent 配置中
      // try {
      //   const saveResult = await window.electronAPI.setDeviceSerialIdConfig(selectedDevice.id);
      //   if (saveResult) {
      //     console.log('设备序列号已保存到配置:', selectedDevice.id);
      //   } else {
      //     console.warn('保存设备序列号失败:', selectedDevice.id);
      //   }
      // } catch (error) {
      //   console.error('保存设备序列号时出错:', error);
      // }
      
      if ('type' in selectedDevice && selectedDevice.type === 'browser') {
        // 处理浏览器设备
        setDeviceId(selectedDevice.id);
        setDeviceBrand(selectedDevice.brand);
        setDeviceProduct(selectedDevice.product);
        // 创建 BrowserView 并加载 URL，传递设备ID以支持持久化session
        await window.electronAPI.createBrowserView(selectedDevice.url, selectedDevice.id);
        // 设置设备类型为 browser
        const response = await flaskApi.setDevice(selectedDevice.id, 'browser');
        if (!response.ok) {
          throw new Error('set_device failed');
        }
        return;
      }
      
      // 如果不是浏览器设备，先销毁 BrowserView
      await window.electronAPI.destroyBrowserView();
      
      const response = await flaskApi.setDevice(selectedDevice.id, 'websocket');
      if (response.ok) {
        setDeviceId(selectedDevice.id);
        setDeviceBrand(selectedDevice.brand);
        setDeviceProduct(selectedDevice.product);
        
        // 先检查设备初始连接状态，然后再启动监测（仅针对非浏览器设备）
        try {
          // 首先检查设备当前连接状态
          const connectionCheck = await window.electronAPI.checkDeviceConnection(selectedDevice.id);
          if (connectionCheck.success) {
            const initiallyConnected = connectionCheck.isConnected;
            console.log(`设备 ${selectedDevice.id} 初始连接状态: ${initiallyConnected}`);
            
            // 设置初始连接状态
            setIsDeviceConnected(initiallyConnected);
            setPreviousConnectionStatus(initiallyConnected);
            
            // 启动设备监测
            const result = await window.electronAPI.startDeviceMonitoring(selectedDevice.id);
            if (result.success) {
              setIsMonitoringDevice(true);
              console.log(`开始监测设备 ${selectedDevice.id} 的连接状态`);
            } else {
              console.error('启动设备监测失败:', result.error);
            }
          } else {
            console.error('检查设备初始连接状态失败:', connectionCheck.error);
            // 如果检查失败，默认认为设备已连接并启动监测
            setIsDeviceConnected(true);
            setPreviousConnectionStatus(true);
            const result = await window.electronAPI.startDeviceMonitoring(selectedDevice.id);
            if (result.success) {
              setIsMonitoringDevice(true);
            }
          }
        } catch (error) {
          console.error('设备连接状态检查或监测启动失败:', error);
          // 出现异常时，默认认为设备已连接
          setIsDeviceConnected(true);
          setPreviousConnectionStatus(true);
        }
        
        // await checkAndInstallRuyiClient(selectedDevice.id);
        await checkRuyiClientStatus();
      } else {
        throw new Error('set_device failed');
      }
    } catch (error) {
      message.error(I18nUtils.getText('selectDeviceError'));
      console.error('set_device failed:', error);
    }
  };

  // 在组件卸载时清理 BrowserView（移除自动销毁逻辑以保持状态）
  useEffect(() => {
    return () => {
      // 不再自动销毁BrowserView，保持浏览器设备状态
      // window.electronAPI.destroyBrowserView();
    };
  }, []);

  // 修改设备选择按钮的处理函数
  const handleSelectDevice = () => {
    // 停止设备监测
    if (isMonitoringDevice) {
      window.electronAPI.stopDeviceMonitoring().then(() => {
        console.log('已停止设备监测');
        setIsMonitoringDevice(false);
        setIsDeviceConnected(true);
        setPreviousConnectionStatus(null); // 重置前一次连接状态
        setIsReconnecting(false); // 重置重连状态
      }).catch(error => {
        console.error('停止设备监测失败:', error);
      });
    }
    
    // 切换到设备选择界面时，隐藏当前 BrowserView 而不是销毁
    if (selectedDevice?.type === 'browser') {
      window.electronAPI.hideBrowserView(false);
    }
    setShowDeviceSelect(true);
    setDeviceId('');
    setSelectedDevice(null);
    // 清除全局选择的设备
    (window as any).__selectedDevice = null;
  };

  // 添加浏览器事件监听
  useEffect(() => {
    if (selectedDevice?.type === 'browser') {
      window.electronAPI.onBrowserLoadingState((state) => {
        setBrowserState(prev => ({ ...prev, isLoading: state.isLoading }));
      });

      window.electronAPI.onBrowserLoadError((error) => {
        setBrowserState(prev => ({ ...prev, error }));
        message.error(`Failed to load page: ${error.errorDescription}`);
      });

      window.electronAPI.onBrowserNavigationState((state) => {
        setBrowserState(prev => ({
          ...prev,
          canGoBack: state.canGoBack,
          canGoForward: state.canGoForward,
          currentUrl: state.url
        }));
      });

      return () => {
        window.electronAPI.offBrowserLoadingState();
        window.electronAPI.offBrowserLoadError();
        window.electronAPI.offBrowserNavigationState();
      };
    }
  }, [selectedDevice]);

  // 监听通过脚本触发的浏览器设备切换
  useEffect(() => {
    const handleBrowserDeviceSwitched = (data: any) => {
      console.log('收到浏览器设备切换事件:', data);
      
      if (data.device) {
        const { device, showDeviceSelect } = data;
        
        // 更新设备相关状态
        setSelectedDevice(device);
        setDeviceId(device.id);
        setDeviceBrand(device.brand || 'Browser');
        setDeviceProduct(device.product);
        setShowDeviceSelect(showDeviceSelect !== undefined ? showDeviceSelect : false);
        
        // 初始化浏览器状态
        setBrowserState({
          isLoading: false,
          canGoBack: false,
          canGoForward: false,
          currentUrl: device.url || 'https://www.baidu.com',
          error: null
        });
        
        // 设置全局选择的设备
        (window as any).__selectedDevice = device;
        
        // 立即设置浏览器事件监听器
        if (device.type === 'browser') {
          console.log('立即设置浏览器事件监听器');
          
          // 清理之前的监听器
          window.electronAPI.offBrowserLoadingState();
          window.electronAPI.offBrowserLoadError();
          window.electronAPI.offBrowserNavigationState();
          
          // 设置新的监听器
          window.electronAPI.onBrowserLoadingState((state) => {
            console.log('收到浏览器加载状态:', state);
            setBrowserState(prev => ({ ...prev, isLoading: state.isLoading }));
          });

          window.electronAPI.onBrowserLoadError((error) => {
            console.log('收到浏览器加载错误:', error);
            setBrowserState(prev => ({ ...prev, error }));
            message.error(`Failed to load page: ${error.errorDescription}`);
          });

          window.electronAPI.onBrowserNavigationState((state) => {
            console.log('收到浏览器导航状态:', state);
            setBrowserState(prev => ({
              ...prev,
              canGoBack: state.canGoBack,
              canGoForward: state.canGoForward,
              currentUrl: state.url
            }));
          });
        }
        
        console.log('浏览器设备状态已更新:', {
          deviceId: device.id,
          deviceName: device.deviceNumber,
          showDeviceSelect: showDeviceSelect
        });
      }
    };

    window.electronAPI.onBrowserDeviceSwitched(handleBrowserDeviceSwitched);

    return () => {
      window.electronAPI.offBrowserDeviceSwitched();
    };
  }, []);

  // 设备连接状态监听
  useEffect(() => {
    // 监听设备连接状态变化
    window.electronAPI.onDeviceConnectionStatusChanged((data) => {
      console.log('设备连接状态变化:', data);
      const { isConnected } = data;
      
      // 更新连接状态
      setIsDeviceConnected(isConnected);
      
      // 只有在真正的重连时（从断连恢复）才刷新iframe和显示提示
      if (isConnected && previousConnectionStatus === false) {
        // 这是真正的重连：从断连状态恢复到连接状态
        console.log('检测到设备重新连接，立即刷新iframe');
        setIsReconnecting(true);
        
        // 立即刷新iframe，不延迟
        setIframeKey(prev => prev + 1);
        
        // 短暂延迟后显示成功消息并完成重连
        setTimeout(() => {
          setIsReconnecting(false);
          message.success(I18nUtils.getText('deviceReconnected'));
        }, 2000); // 给iframe 2秒时间加载
      } else if (!isConnected) {
        // 设备断连
        setIsReconnecting(false);
        message.warning(I18nUtils.getText('deviceDisconnected'));
      }
      
      // 更新前一次连接状态
      setPreviousConnectionStatus(isConnected);
    });

    // 监听重新加载iframe事件
    window.electronAPI.onReloadDeviceIframe(() => {
      console.log('收到重新加载iframe指令');
      setIframeKey(prev => prev + 1);
    });

    return () => {
      window.electronAPI.offDeviceConnectionStatusChanged();
      window.electronAPI.offReloadDeviceIframe();
    };
  }, [previousConnectionStatus]);

  // 添加浏览器控制函数
  const handleBrowserBack = () => {
    window.electronAPI.goBack();
  };

  const handleBrowserForward = () => {
    window.electronAPI.goForward();
  };

  const handleBrowserReload = () => {
    window.electronAPI.reload();
  };

  const handleBrowserUrlChange = (url: string) => {
    window.electronAPI.loadURL(url);
  };

  return (
    <DeviceProvider>
      <div>
        <NavBarComponent 
          title={'Ruyi Studio'} 
          onSelectDevice={handleSelectDevice}
        />
        <Row className='main-container'>
          <Col span={6} style={{ height: '100%' }}>
            <div className='left-container'>
              <div
                className={`task-files-container ${isDraggingChat ? 'dragging' : ''}`}
                style={{
                  height: `${leftContainerHeights.taskFilesHeight}px`
                }}
              >
                <TaskFilesList onFileSelect={(fileName) => console.log('Selected file:', fileName)} />
              </div>
              <div
                className='chat-container'
                style={{
                  height: `${leftContainerHeights.chatHeight}px`
                }}
              >
                <ChatInterface
                  height={leftContainerHeights.chatHeight}
                  onHeightChange={handleChatHeightChange}
                  onDragStateChange={setIsDraggingChat}
                />
              </div>
            </div>
          </Col>
          <Col span={11} style={{ height: '100%' }}>
            <div className='middle-container'>
              <div className='control-buttons-container'>
                <Toolbar />
              </div>
              <div id='code-editor-container' className='code-editor-container'>
                <CodeEditor />
              </div>
              <LogPanel 
                visible={true}
                height={logPanelHeight}
                onHeightChange={setLogPanelHeight}
              />
            </div>
          </Col>
          <Col span={7} style={{ height: '100%' }}>
            {showDeviceSelect ? (
              <div className='right-container'>
                <Select 
                  onDeviceSelect={handleDeviceSelect} 
                  manualDevices={manualDevices}
                  setManualDevices={setManualDevices}
                />
              </div>
            ) : (
              <div className='right-container'>
                <DeviceState
                  deviceId={deviceId}
                  brand={deviceBrand}
                  productName={deviceProduct}
                  isConnected={isRuyiClientConnected}
                  isAuthorized={isRuyiClientAuthorized}
                  isInstalled={isRuyiClientInstalled}
                  onSelectDevice={handleSelectDevice}
                  isBrowser={selectedDevice?.type === 'browser'}
                  browserState={browserState}
                />

                {selectedDevice?.type !== 'browser' && (
                  <div style={{ position: 'relative', height: '100%' }}>
                    <iframe
                      key={iframeKey}
                      className='phone-container'
                      src={iframeSrc}
                      title="ws-scrcpy"
                    />
                    {/* 设备断连或重连遮罩 */}
                    {(!isDeviceConnected || isReconnecting) && (
                      <div className="device-disconnected-overlay">
                        <div className="device-disconnected-content">
                          {isReconnecting ? (
                            // 重连中状态
                            <>
                              <div className="device-reconnecting-icon">
                                📱🔄
                              </div>
                              <h3>{I18nUtils.getText('deviceReconnecting')}</h3>
                              <p>{I18nUtils.getText('deviceReconnectingDesc')}</p>
                              <div className="reconnecting-spinner">
                                <div className="spinner"></div>
                              </div>
                            </>
                          ) : (
                            // 断连状态
                            <>
                              <div className="device-disconnected-icon">
                                📱❌
                              </div>
                              <h3>{I18nUtils.getText('deviceDisconnected')}</h3>
                              <p>{I18nUtils.getText('deviceDisconnectedDesc')}</p>
                              <div className="device-disconnected-actions">
                                <button 
                                  className="reconnect-device-btn"
                                  onClick={() => {
                                    setIsReconnecting(true);
                                    setIframeKey(prev => prev + 1);
                                    message.info(I18nUtils.getText('reconnectDevice'));
                                    
                                    // 手动重连也有超时处理
                                    setTimeout(() => {
                                      setIsReconnecting(false);
                                    }, 3000);
                                  }}
                                >
                                  {I18nUtils.getText('reconnectDevice')}
                                </button>
                                <button 
                                  className="select-other-device-btn"
                                  onClick={handleSelectDevice}
                                >
                                  {I18nUtils.getText('selectOtherDevice')}
                                </button>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </Col>
        </Row>
        <Drawer
          title={
            drawerContentKey === 'installation'
              ? I18nUtils.getText('ruyiClientInstallationDrawerTitle')
              : drawerContentKey === 'configuration'
                ? I18nUtils.getText('ruyiClientConfigurationGuideDrawerTitle')
                : ''
          }
          placement="left"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          mask={false}
          maskClosable={false}
          width={400}
        >
          {drawerContentKey === 'installation' && <InstallationProgress />}
          {drawerContentKey === 'configuration' && <ConfigurationGuide />}
        </Drawer>
        <TablePanel
          visible={tablePanelVisible}
          onClose={() => setTablePanelVisible(false)}
          tableData={tableData}
        />
        <ImagePanel
          visible={imagePanelVisible}
          onClose={() => setImagePanelVisible(false)}
          imageData={imageData}
        />
      </div>
    </DeviceProvider>
  );
};

export default Dashboard; 