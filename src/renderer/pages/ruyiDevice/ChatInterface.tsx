import React, { useEffect, useRef, useState } from 'react';
import { Input, Button, message, Card, ConfigProvider, Flex, theme } from 'antd';
import { Send, X, Loader2, CheckCircle, XCircle, User, Bo<PERSON>, Settings, Flame, Rocket, Smile, MessageCircle, Table, AlertTriangle } from 'lucide-react';
import { Welcome, Bubble, Sender, Prompts } from "@ant-design/x";

import { CodeEditorUtils } from './CodeEditor';
import { I18nUtils } from './languageSupport/i18nUtils';
import { ToolbarState } from './Toolbar';
import { generateUniqueId } from './utils/commonUtils';
import { flaskApi } from '../../../services/FlaskApiService';
import { useLanguage } from './languageSupport/LanguageContext';
import { promptApi } from '../../../services/api';
import '../styles/ChatInterface.css';

interface ChatInterfaceProps {
  height?: number;
  onHeightChange?: (height: number) => void;
  onDragStateChange?: (isDragging: boolean) => void;
}

interface ChatMessage {
  id: string;
  sender: string;
  content: string;
  isQuestion?: boolean;
  isDemoing?: boolean;
  isLoading?: boolean;
  isComplete?: boolean;
  isError?: boolean;
  messageType?: 'text' | 'ask_question' | 'notify_message' | 'notify_table' | 'notify_image';
  payload?: any;
}

interface QuoteHintProps {
  message: string;
  onClose: () => void;
}

interface ScriptData {
  NL_script: string;
  code_script: string;
  task_name: string;
}

// 工具函数：移除脚本中的标号
const removeLabelFromScript = (labeledScript: string): string => {
  if (!labeledScript) return '';
  
  return labeledScript
    .split('\n')
    .map(line => {
      // 移除行首的 [数字] 标号，但保留标号后的缩进空格 (NL script格式)
      const lineWithoutPrefixLabel = line.replace(/^\[\d+\] ?/, '');
      
      // 移除行尾的注释标号 # [数字] (Code script格式)
      const lineWithoutSuffixLabel = lineWithoutPrefixLabel.replace(/\s*#\s*\[\d+\]\s*$/, '');
      
      return lineWithoutSuffixLabel;
    })
    .join('\n');
};

// 解析工作流响应的函数
const parseWorkflowResponse = (response: string): {
  thought?: string;
  workflow?: string;
  question?: string;
  taskName?: string;
} => {
  const result: any = {};
  
  // 提取 thought
  const thoughtMatch = response.match(/<thought>([\s\S]*?)<\/thought>/);
  if (thoughtMatch) {
    result.thought = thoughtMatch[1].trim();
  }
  
  // 提取 workflow
  const workflowMatch = response.match(/<workflow>([\s\S]*?)<\/workflow>/);
  if (workflowMatch) {
    result.workflow = workflowMatch[1].trim();
  }
  
  // 提取 question
  const questionMatch = response.match(/<question>([\s\S]*?)<\/question>/);
  if (questionMatch) {
    result.question = questionMatch[1].trim();
  }
  
  // 提取 task_name
  const taskNameMatch = response.match(/<task_name>([\s\S]*?)<\/task_name>/);
  if (taskNameMatch) {
    result.taskName = taskNameMatch[1].trim();
  }
  
  return result;
};

// 从工作流更新现有任务文件的函数
const updateTaskWithWorkflow = async (
  workflow: string,
  taskName: string,
  taskDescription: string,
  projectName: string,
  loadingMsgId: string
) => {
  try {
    let processedWorkflow = workflow.trim();
    if (processedWorkflow.startsWith('```') && processedWorkflow.endsWith('```')) {
      const lines = processedWorkflow.split('\n');
      processedWorkflow = lines.slice(1, -1).join('\n');
    }

    const labeledWorkflow = CodeEditorUtils.addLabelToScript(processedWorkflow);

    // 获取当前任务文件名
    const currentFileName = ChatInterface.currentEditingFile;
    if (!currentFileName) {
      throw new Error('没有当前编辑的任务文件');
    }

    // 读取现有的任务文件
    const readResult = await window.electronAPI.readTaskFile({
      taskName: projectName,
      fileName: currentFileName
    });

    if (!readResult.success || !readResult.content) {
      throw new Error('读取任务文件失败: ' + readResult.message);
    }

    // 解析现有的任务数据
    let taskData;
    try {
      taskData = JSON.parse(readResult.content);
    } catch (error) {
      throw new Error('解析任务文件失败: ' + (error as Error).message);
    }

    // 获取当前聊天消息
    const currentMessages = ChatInterface.getAllMessages ? ChatInterface.getAllMessages() : [];
    
    // 更新任务数据
    taskData.task_name = taskName;
    taskData.task_description = taskDescription;
    taskData.NL_script = processedWorkflow;
    taskData.code_script = taskData.code_script || ''; // 保持现有的 code_script，如果没有则为空
    taskData.NL_script_labeled = labeledWorkflow;
    taskData.code_script_labeled = taskData.code_script_labeled || ''; // 保持现有的带标号代码脚本
    taskData.NL_script_version = 1; // 设置NL脚本版本为1，表示已更新
    taskData.code_script_version = taskData.code_script_version || 0; // 保持现有的code_script_version
    taskData.messages = currentMessages; // 更新聊天消息
    taskData.updated_at = new Date().toISOString();

    // 更新任务文件
    const updateResult = await window.electronAPI.updateTaskFile({
      taskName: projectName,
      fileName: currentFileName,
      content: JSON.stringify(taskData, null, 2)
    });
    
    if (!updateResult.success) {
      throw new Error('更新任务文件失败: ' + updateResult.message);
    }

    // 更新 ChatInterface 中的任务信息
    ChatInterface.setTask(taskName);
    ChatInterface.setTaskDescription(taskDescription);
    
    // 设置脚本内容到编辑器，并设置NL脚本版本为1
    CodeEditorUtils.setNLScript(processedWorkflow, true);
    CodeEditorUtils.labeledNLScript = labeledWorkflow;
    
    // 延迟触发自动保存
    setTimeout(() => {
      CodeEditorUtils.triggerAutoSave();
    }, 2000);

    // Git 同步逻辑
    try {
      const { repoApi, userApi } = await import('../../../services/api');
      const userInfoResponse = await userApi.getUserInfo();
      const userInfo = userInfoResponse.data;
      
      const tokenResponse = await repoApi.getRepoToken(projectName);
      
      if (tokenResponse && tokenResponse.token) {
        const gitResult = await window.electronAPI.initializeAndPushGitRepo({
          taskName: projectName,
          repoToken: tokenResponse.token,
          projectDescription: ChatInterface.taskDescription || '',
          userInfo
        });
        
        if (gitResult.success) {
          console.log('Git commit + push 成功:', gitResult.message);
        } else {
          console.warn('Git push 失败:', gitResult.message);
        }
      }
    } catch (gitError) {
      console.error('Git 操作失败:', gitError);
    }

    // 通知文件列表刷新
    window.dispatchEvent(new CustomEvent('taskDetailsUpdated', {
      detail: { projectName, fileName: currentFileName }
    }));

    ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('taskGenSuccess'));
    ChatInterface.addChatMessage('assistant', `任务"${taskName}"已创建完成！`);
    
    // 任务创建成功后重置创建状态
    ChatInterface.setIsCreatingTask(false);
    ChatInterface.setTaskCreationInfo(null);
    
  } catch (error) {
    console.error('更新任务失败:', error);
    ChatInterface.updateToCompleteMessage(loadingMsgId, '任务创建失败: ' + (error as Error).message, true);
    
    // 任务创建失败后也需要重置创建状态
    ChatInterface.setIsCreatingTask(false);
    ChatInterface.setTaskCreationInfo(null);
  }
};

// 静态状态管理部分
export class ChatInterface extends React.Component<ChatInterfaceProps> {
  private static _task: string = '';
  private static _taskDescription: string = '';
  private static _projectName: string = '';
  private static _currentEditingFile: string = '';
  private static _demonstrationQuestion: string = '';
  private static _isCreatingTask: boolean = false;
  private static _taskCreationInfo: {
    useAiForTaskName: boolean;
    taskShortName: string;
    projectName: string;
    taskFileName?: string;
  } | null = null;
  private static _addChatMessageCallback?: (message: ChatMessage) => void;
  private static _resetDemoStatesCallback?: () => void;
  private static _updateMessageCallback?: (messageId: string, content: string, isComplete: boolean, isError?: boolean) => void;
  private static _getAllMessagesCallback?: () => ChatMessage[];
  private static _setAllMessagesCallback?: (messages: ChatMessage[]) => void;
  private static _onTaskChangeCallback?: (newTask: string) => void;
  private static _onProjectNameChangeCallback?: (newProjectName: string) => void;
  private static _onProjectCreatedCallback?: (projectName: string) => void;
  private static _onEditingFileChangeCallback?: (fileName: string) => void;
  private static _onShowTableCallback?: (data: any) => void;
  private static _onShowImageCallback?: (data: any) => void;
  private static _processTaskCreationCallback?: (message: string) => Promise<void>;

  static get task(): string {
    return this._task;
  }

  static get taskDescription(): string {
    return this._taskDescription;
  }

  static get projectName(): string {
    return this._projectName;
  }

  static get currentEditingFile(): string {
    return this._currentEditingFile;
  }

  static get demonstrationQuestion(): string {
    return this._demonstrationQuestion;
  }

  static get isCreatingTask(): boolean {
    return this._isCreatingTask;
  }

  static get taskCreationInfo() {
    return this._taskCreationInfo;
  }

  static setTask(value: string) {
    this._task = value;
    if (ChatInterface._onTaskChangeCallback) {
      ChatInterface._onTaskChangeCallback(value);
    }
  }

  static setTaskDescription(value: string) {
    this._taskDescription = value;
  }

  static setProjectName(value: string) {
    this._projectName = value;
    if (ChatInterface._onProjectNameChangeCallback) {
      ChatInterface._onProjectNameChangeCallback(value);
    }
  }

  static setCurrentEditingFile(value: string) {
    // Save current chat messages before switching files
    if (this._currentEditingFile && this._currentEditingFile !== value && this._currentEditingFile.endsWith('.json')) {
      this.saveCurrentChatMessages();
    }
    
    const previousFile = this._currentEditingFile;
    this._currentEditingFile = value;
    
    // Load messages for the new file if it's a JSON file and different from previous
    if (value && value.endsWith('.json') && value !== previousFile) {
      console.log(`[ChatInterface] 切换文件: ${previousFile} -> ${value}, 加载新文件的聊天消息`);
      this.loadChatMessagesForTask(this._projectName, value);
    }
    
    if (ChatInterface._onEditingFileChangeCallback) {
      ChatInterface._onEditingFileChangeCallback(value);
    }
  }

  static setDemonstrationQuestion(value: string) {
    this._demonstrationQuestion = value;
  }

  static setIsCreatingTask(value: boolean) {
    this._isCreatingTask = value;
  }

  static setTaskCreationInfo(value: { useAiForTaskName: boolean; taskShortName: string; projectName: string; taskFileName?: string; } | null) {
    this._taskCreationInfo = value;
  }

  static setAddChatMessageCallback(callback: (message: ChatMessage) => void) {
    this._addChatMessageCallback = callback;
  }

  /**
   * 保存当前聊天消息到当前任务文件
   */
  static async saveCurrentChatMessages() {
    if (!this._projectName || !this._currentEditingFile || !this._getAllMessagesCallback) {
      console.log('[ChatInterface] 无法保存聊天消息：项目名称或当前文件为空');
      return;
    }
    
    const messages = this._getAllMessagesCallback();
    if (!messages || messages.length === 0) {
      console.log('[ChatInterface] 没有聊天消息需要保存');
      return;
    }
    
    console.log(`[ChatInterface] 保存聊天消息到任务文件: ${this._projectName}/${this._currentEditingFile}`);
    
    try {
      const result = await window.electronAPI.saveTaskChatMessages({
        taskName: this._projectName,
        fileName: this._currentEditingFile,
        messages: messages
      });
      
      if (result.success) {
        console.log('[ChatInterface] 聊天消息保存成功');
      } else {
        console.error('[ChatInterface] 聊天消息保存失败:', result.message);
      }
    } catch (error) {
      console.error('[ChatInterface] 保存聊天消息时出错:', error);
    }
  }
  
  /**
   * 加载指定任务的聊天消息
   * @param taskName 任务名称
   * @param fileName 文件名称
   */
  static async loadChatMessagesForTask(taskName: string, fileName: string) {
    if (!taskName || !fileName || !this._setAllMessagesCallback) {
      console.log('[ChatInterface] 无法加载聊天消息：任务名称或文件名为空');
      return;
    }
    
    console.log(`[ChatInterface] 加载任务聊天消息: ${taskName}/${fileName}`);
    
    try {
      const result = await window.electronAPI.loadTaskChatMessages({
        taskName: taskName,
        fileName: fileName
      });
      
      if (result.success && result.messages) {
        console.log('[ChatInterface] 聊天消息加载成功, 消息数量:', result.messages.length);
        this._setAllMessagesCallback(result.messages);
      } else {
        console.log('[ChatInterface] 没有找到聊天消息或加载失败:', result.message);
        // 如果没有找到消息，则设置为空数组
        this._setAllMessagesCallback([]);
      }
    } catch (error) {
      console.error('[ChatInterface] 加载聊天消息时出错:', error);
      // 出错时也设置为空数组
      this._setAllMessagesCallback([]);
    }
  }

  static addChatMessage(sender: string, content: string, isQuestion: boolean = false): string {
    const id = generateUniqueId();
    this._addChatMessageCallback?.({
      id,
      sender,
      content,
      isQuestion,
      isDemoing: false,
      isLoading: false,
      isComplete: false,
      isError: false,
      messageType: 'text',
    });
    return id;
  }

  static addAskQuestionMessage(question: string, requestId: string) {
    const id = generateUniqueId();
    this._addChatMessageCallback?.({
      id,
      sender: 'assistant',
      content: question,
      messageType: 'ask_question',
      payload: { requestId },
    });
  }

  static addNotifyMessage(message: string) {
    const id = generateUniqueId();
    this._addChatMessageCallback?.({
      id,
      sender: 'assistant',
      content: message,
      messageType: 'notify_message',
    });
  }

  static addNotifyTableMessage(table: any) {
    const id = generateUniqueId();
    this._addChatMessageCallback?.({
      id,
      sender: 'assistant',
      content: 'Table: ' + (table.name || 'Untitled'),
      messageType: 'notify_table',
      payload: table,
    });
  }

  static addNotifyImageMessage(image: any) {
    const id = generateUniqueId();
    this._addChatMessageCallback?.({
      id,
      sender: 'assistant',
      content: '',
      messageType: 'notify_image',
      payload: image,
    });
  }

  static addLoadingMessage(content: string, sender: string = 'system'): string {
    const id = generateUniqueId();
    this._addChatMessageCallback?.({
      id,
      sender,
      content,
      isQuestion: false,
      isLoading: true,
      isComplete: false,
      isError: false
    });
    return id;
  }

  static updateToCompleteMessage(messageId: string, content: string, isError: boolean = false) {
    this._updateMessageCallback?.(messageId, content, true, isError);
  }

  static setUpdateMessageCallback(callback: (messageId: string, content: string, isComplete: boolean, isError?: boolean) => void) {
    this._updateMessageCallback = callback;
  }

  static setResetDemoStatesCallback(callback: () => void) {
    this._resetDemoStatesCallback = callback;
  }

  static resetAllDemoStates() {
    this._resetDemoStatesCallback?.();
  }

  static setGetAllMessagesCallback(callback: () => ChatMessage[]) {
    this._getAllMessagesCallback = callback;
  }

  static getAllMessages(): ChatMessage[] {
    console.log("getAllMessages", this._getAllMessagesCallback?.());
    return this._getAllMessagesCallback?.() || [];
  }

  static setSetAllMessagesCallback(callback: (messages: ChatMessage[]) => void) {
    this._setAllMessagesCallback = callback;
  }

  static setAllMessages(messages: ChatMessage[]) {
    this._setAllMessagesCallback?.(messages);
  }

  static setOnTaskChangeCallback(callback?: (newTask: string) => void) {
    ChatInterface._onTaskChangeCallback = callback;
  }

  static setOnProjectNameChangeCallback(callback?: (newProjectName: string) => void) {
    ChatInterface._onProjectNameChangeCallback = callback;
  }

  static setOnProjectCreatedCallback(callback?: (projectName: string) => void) {
    this._onProjectCreatedCallback = callback;
  }

  static setOnEditingFileChangeCallback(callback?: (fileName: string) => void) {
    this._onEditingFileChangeCallback = callback;
  }

  static setOnShowTableCallback(callback?: (data: any) => void) {
    this._onShowTableCallback = callback;
  }

  static setOnShowImageCallback(callback?: (data: any) => void) {
    this._onShowImageCallback = callback;
  }

  static showTable(data: any) {
    this._onShowTableCallback?.(data);
  }

  static showImage(data: any) {
    this._onShowImageCallback?.(data);
  }

  static notifyProjectCreated(projectName: string) {
    if (this._onProjectCreatedCallback) {
      this._onProjectCreatedCallback(projectName);
    }
  }

  static async processTaskCreationMessage(message: string) {
    console.log('[ChatInterface] processTaskCreationMessage 被调用, message:', message);
    console.log('[ChatInterface] _processTaskCreationCallback:', !!this._processTaskCreationCallback);
    // 这个方法专门用于处理任务创建消息
    // 它会直接调用组件实例的处理逻辑
    if (this._processTaskCreationCallback) {
      await this._processTaskCreationCallback(message);
    }
  }

  static setProcessTaskCreationCallback(callback?: (message: string) => Promise<void>) {
    this._processTaskCreationCallback = callback;
  }

  render() {
    return <ChatInterfaceComponent {...this.props} />;
  }
}

const QuoteHint: React.FC<QuoteHintProps> = ({ message, onClose }) => (
  <div className="chat-quote-hint">
    <span className="chat-quote-content">
      {I18nUtils.getText('replyingTo')} {message}
    </span>
    <X className="chat-quote-close" size={16} onClick={onClose} />
  </div>
);


const ChatInterfaceComponent: React.FC<ChatInterfaceProps> = ({ height, onHeightChange, onDragStateChange }) => {
  const { language } = useLanguage();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [quoteMessage, setQuoteMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [exampleTasks, setExampleTasks] = useState<any[]>([]);
  const [hasTask, setHasTask] = useState(false);  // 在 handleLoadExampleTask 中设置任务后，用此钩子来强制触发组件重新渲染。避免输入框检测 ChatInterface.task 不及时，从而不能立即显示输入框。
  const [showWelcome, setShowWelcome] = useState(true);  // 在 handleLoadExampleTask 中设置任务后，用此钩子来关闭 Welcome 组件，因为在示例任务重，默认没有任何消息，所以不设置的话，Welcome 组件会一直显示。
  const [replyingToRequest, setReplyingToRequest] = useState<string | null>(null);
  const [isCreatingTask, setIsCreatingTask] = useState(false);  // 跟踪是否正在创建任务
  const [taskCreationProjectName, setTaskCreationProjectName] = useState('');  // 正在创建任务的项目名称
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Add resizing state management
  const [isDragging, setIsDragging] = useState(false);
  const dragStartY = useRef<number>(0);
  const dragStartHeight = useRef<number>(0);

  // Add state to track if system prompts are being viewed
  const [isViewingSystemPrompt, setIsViewingSystemPrompt] = useState(false);

  // 获取示例任务
  useEffect(() => {
    const fetchExampleTasks = async (retryCount = 0) => {
      try {
        const exampleTasks = await flaskApi.getExampleTasks();
        console.log("fetchExampleTasks exampleTasks", exampleTasks);
        
        if (exampleTasks && exampleTasks.length > 0) {
          setExampleTasks(exampleTasks);
        } else if (retryCount < 100) {  // 最多重试20次
          console.log("fetchExampleTasks retryCount", retryCount);
          setTimeout(() => fetchExampleTasks(retryCount + 1), 100);  // 500毫秒后重试
        }
      } catch (error) {
        console.error('获取示例任务失败:', error);
        if (retryCount < 100) {  // 最多重试20次
          console.log("fetchExampleTasks retryCount", retryCount);
          setTimeout(() => fetchExampleTasks(retryCount + 1), 100);  // 500毫秒后重试
        }
      }
    };

    // 立即执行 fetchExampleTasks
    fetchExampleTasks();

    const handleFlaskMessage = (message: { type: string, payload: any }) => {
      console.log('Received flask message:', message);
      if (message.type === 'ask_question') {
        ChatInterface.addAskQuestionMessage(message.payload.question, message.payload.request_id);
      } else if (message.type === 'notify_message') {
        ChatInterface.addNotifyMessage(message.payload.message);
      } else if (message.type === 'notify_table') {
        ChatInterface.addNotifyTableMessage(message.payload.table);
      } else if (message.type === 'notify_image') {
        console.log('Received notify_image, payload:', message.payload);
        console.log('Image data from payload.image:', message.payload.image);
        console.log('Image data structure check:');
        console.log('- payload.image.image_data:', !!message.payload.image?.image_data);
        console.log('- payload.image.metadata:', message.payload.image?.metadata);
        console.log('- Full image object keys:', Object.keys(message.payload.image || {}));
        ChatInterface.addNotifyImageMessage(message.payload.image);
      }
    };

    window.electronAPI.onFlaskMessage(handleFlaskMessage);

    return () => {
      if (window.electronAPI.offFlaskMessage) {
        window.electronAPI.offFlaskMessage();
      }
    };
  }, []);

  // 将示例任务转换为 Prompts 组件需要的格式
  const promptItems = exampleTasks.map((exampleTask, index) => ({
    key: index.toString(),
    icon: index % 3 === 0
      ? <Flame color="#FF4D4F" size={18} />
      : index % 3 === 1
      ? <Rocket color="#722ED1" size={18} />
      : <Smile color="#52C41A" size={18} />,
    label: exampleTask.task_name,
    description: exampleTask.task_description,
    disabled: false,
  }));

  useEffect(() => {
    // 注册消息回调
    ChatInterface.setAddChatMessageCallback((message: ChatMessage) => {
      setMessages(prev => [...prev, message]);
    });

    // 注册更新消息回调
    ChatInterface.setUpdateMessageCallback((messageId, content, isComplete, isError) => {
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, content, isLoading: false, isComplete, isError }
            : msg
        )
      );

      // If user is near bottom and a message was completed, scroll to show the update
      if (isComplete && isNearBottom()) {
        setTimeout(() => {
          scrollToBottom();
        }, 50);
      }
    });

    // 注册重置演示状态的回调
    ChatInterface.setResetDemoStatesCallback(() => {
      setMessages(prev => prev.map(msg => ({ ...msg, isDemoing: false })));
    });

    // 添加设置所有消息的回调
    ChatInterface.setSetAllMessagesCallback((messages: ChatMessage[]) => {
      setMessages(messages);
      // When loading messages from storage, scroll to bottom after a delay
      if (messages.length > 0) {
        setTimeout(() => {
          scrollToBottom(true); // Force immediate scroll for loaded messages
        }, 100);
      }
    });

    // 注册任务创建处理回调
    ChatInterface.setProcessTaskCreationCallback(async (message: string) => {
      await optimizeScriptThroughChat(message);
    });

    // 监听任务创建状态变化
    const checkTaskCreationStatus = () => {
      setIsCreatingTask(ChatInterface.isCreatingTask);
      if (ChatInterface.isCreatingTask && ChatInterface.taskCreationInfo) {
        setTaskCreationProjectName(ChatInterface.taskCreationInfo.projectName || '');
      } else {
        setTaskCreationProjectName('');
      }
    };

    // 定期检查状态变化
    const statusCheckInterval = setInterval(checkTaskCreationStatus, 100);

    // 清理函数
    return () => {
      clearInterval(statusCheckInterval);
      ChatInterface.setAddChatMessageCallback(() => {});
      ChatInterface.setResetDemoStatesCallback(() => {});
      ChatInterface.setUpdateMessageCallback(() => {});
      ChatInterface.setSetAllMessagesCallback(() => {});
      ChatInterface.setProcessTaskCreationCallback(undefined);
      
      // 组件卸载时保存当前聊天消息
      if (ChatInterface.currentEditingFile && ChatInterface.projectName) {
        ChatInterface.saveCurrentChatMessages();
      }
    };
  }, []);

  useEffect(() => {
    console.log('Language changed, updating ChatInterface UI');
  }, [language]);

  const scrollToBottom = (force: boolean = false) => {
    // Use requestAnimationFrame to ensure DOM has updated
    requestAnimationFrame(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({
          behavior: force ? 'auto' : 'smooth',
          block: 'end'
        });
      }
    });
  };

  // Check if user is near the bottom of the chat
  const isNearBottom = () => {
    const chatContainer = messagesEndRef.current?.parentElement;
    if (!chatContainer) return true;

    const { scrollTop, scrollHeight, clientHeight } = chatContainer;
    const threshold = 100; // pixels from bottom
    return scrollHeight - scrollTop - clientHeight < threshold;
  };

  // Track previous message count to detect new messages
  const prevMessageCountRef = useRef(0);

  useEffect(() => {
    // 注册获取所有消息的回调
    ChatInterface.setGetAllMessagesCallback(() => messages);

    // Check if new messages were added (not just updated)
    const currentMessageCount = messages.length;
    const hadNewMessage = currentMessageCount > prevMessageCountRef.current;

    // Update the ref for next comparison
    prevMessageCountRef.current = currentMessageCount;

    // Only auto-scroll when new messages are added
    if (hadNewMessage && currentMessageCount > 0) {
      // Use a small delay to ensure the new message is fully rendered
      setTimeout(() => {
        scrollToBottom();
      }, 50);
    }

    // 自动保存消息（使用防抖）
    if (ChatInterface.currentEditingFile && ChatInterface.projectName && messages.length > 0) {
      const saveTimeout = setTimeout(() => {
        ChatInterface.saveCurrentChatMessages();
      }, 1000); // 1秒防抖

      return () => {
        clearTimeout(saveTimeout);
        ChatInterface.setGetAllMessagesCallback(() => []);
      };
    }

    // 清理函数
    return () => {
      ChatInterface.setGetAllMessagesCallback(() => []);
    }
  }, [messages]);

  useEffect(() => {
    const lastMessage = messages.at(-1);
    if (lastMessage && lastMessage.messageType === 'ask_question') {
      setReplyingToRequest(lastMessage.payload.requestId);
      setQuoteMessage(lastMessage.content);
    }
  }, [messages]);

  const addChatMessage = (sender: string, content: string, isQuestion: boolean = false): string => {
    return ChatInterface.addChatMessage(sender, content, isQuestion);
  };

  const handleSend = async () => {
    const messageToSend = inputValue;
    setInputValue('');
    setQuoteMessage('');
    setReplyingToRequest(null);

    if (!messageToSend.trim()) return;

    addChatMessage('user', messageToSend);

    if (replyingToRequest) {
      if (window.electronAPI.answerQuestion) {
        window.electronAPI.answerQuestion({
          request_id: replyingToRequest,
          answer: messageToSend,
        });
      }
      return;
    }

    // 检查是否没有打开任何任务文件
    const hasOpenTask = ChatInterface.task && ChatInterface.currentEditingFile;
    
    if (!hasOpenTask) {
      // 检查是否已经有项目名称（从 NavBar 或 TaskFilesList 加载的项目）
      let projectName = ChatInterface.projectName;
      
      // 如果没有项目名称，创建新的项目
      if (!projectName) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        projectName = `Task_${timestamp}`;
        // 设置项目名称
        ChatInterface.setProjectName(projectName);
        console.log('[ChatInterface] 没有现有项目，创建新项目:', projectName);
      } else {
        console.log('[ChatInterface] 使用现有项目:', projectName);
      }
      
      // 设置任务创建状态
      ChatInterface.setIsCreatingTask(true);
      ChatInterface.setTaskCreationInfo({
        useAiForTaskName: true, // 使用 AI 生成任务名称
        taskShortName: '', // 没有简称，让 AI 生成
        projectName: projectName
      });
      
      console.log('[ChatInterface] 检测到没有打开任务文件，开始在项目中创建新任务');
      console.log('[ChatInterface] 项目名称:', projectName);
      console.log('[ChatInterface] 任务描述:', messageToSend);
    }

    await optimizeScriptThroughChat(messageToSend);
  };

  const optimizeScriptThroughChat = async (message: string) => {
    console.log('[ChatInterface] optimizeScriptThroughChat 被调用, message:', message);
    console.log('[ChatInterface] isCreatingTask:', ChatInterface.isCreatingTask);
    console.log('[ChatInterface] taskCreationInfo:', ChatInterface.taskCreationInfo);

    // 新增：处理任务创建时，如果编辑器处于查看知识库模式，则退出该模式以启用聊天
    if (ChatInterface.isCreatingTask) {
      if (CodeEditorUtils.viewAppKnowledge || CodeEditorUtils.viewProjectKnowledge) {
        console.log('[ChatInterface] 检测到正在查看知识库，在创建新任务时自动隐藏知识库视图。');
        await CodeEditorUtils.hideKnowledgeViewOnly();
      }
    }
    
    // 根据是否是任务创建场景使用不同的加载提示词
    const loadingText = ChatInterface.isCreatingTask 
      ? I18nUtils.getText('taskCreationProcessing')
      : I18nUtils.getText('taskOptimizationProcessing');
    const loadingMsgId = ChatInterface.addLoadingMessage(loadingText);
    
    try {
      // 检查是否是任务创建请求
      if (ChatInterface.isCreatingTask && ChatInterface.taskCreationInfo) {
        console.log('[ChatInterface] 检测到任务创建请求，开始处理...');
        const { useAiForTaskName, taskShortName, projectName } = ChatInterface.taskCreationInfo;
        
        // 获取应用知识库
        let appKnowledge = '';
        try {
          const knowledgeFileResult = await window.electronAPI.readTaskFile({
            taskName: projectName,
            fileName: 'Knowledge.json',
          });
          if (knowledgeFileResult.success && knowledgeFileResult.content !== undefined) {
            const knowledgeData = JSON.parse(knowledgeFileResult.content);
            if (knowledgeData.knowledge) {
              appKnowledge = knowledgeData.knowledge;
            }
          }
        } catch (error) {
          console.warn('Failed to load Knowledge.json:', error);
        }

        // 构建对话历史
        const conversationHistory = messages
          .filter(msg => msg.messageType === 'text' && (msg.sender === 'user' || msg.sender === 'assistant'))
          .map(msg => {
            const senderLabel = msg.sender === 'user' ? 'User' : 'Assistant';
            return `${senderLabel}: ${msg.content}`;
          })
          .join('\n');

        console.log('[ChatInterface] 准备调用 generateWorkflow API (任务创建模式)');
        console.log('[ChatInterface] conversationHistory:', conversationHistory);
        console.log('[ChatInterface] message:', message.trim());
        console.log('[ChatInterface] currentWorkflow: (空字符串 - 任务创建中)', '');
        console.log('[ChatInterface] appKnowledge:', appKnowledge);
        console.log('[ChatInterface] useAiForTaskName:', useAiForTaskName);

        const appLanguage = await window.electronAPI.getAppLanguageConfig();
        
        // 调用 generateWorkflow API
        // 注意：在任务创建模式下，currentWorkflow 始终传入空字符串，
        // 因为此时还没有完整的工作流，即使是多轮对话也应该保持为空
        const workflowResponse = await promptApi.generateWorkflow(
          conversationHistory,
          message.trim(),
          '', // currentWorkflow 为空（新任务创建中）
          appKnowledge,
          useAiForTaskName, // needTaskName
          appLanguage
        );

        console.log('[ChatInterface] generateWorkflow API 响应:', workflowResponse);
        
              // 解析 XML 响应
      const parsedResponse = parseWorkflowResponse(workflowResponse);
      
      // 如果有 thought，先显示到对话区
      if (parsedResponse.thought) {
        ChatInterface.addChatMessage('assistant', parsedResponse.thought);
      }
      
      if (parsedResponse.question) {
        // 显示问题，继续保持任务创建状态进行多轮对话
        console.log('[ChatInterface] 收到问题，保持任务创建模式继续对话');
        ChatInterface.addChatMessage('assistant', parsedResponse.question);
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('taskCreationReplyComplete'));
        // 注意：不重置任务创建状态，继续保持创建模式
        return;
      } else if (parsedResponse.workflow) {
        // 更新任务文件
        await updateTaskWithWorkflow(
          parsedResponse.workflow,
          parsedResponse.taskName || taskShortName || '新任务',
          message.trim(),
          projectName,
          loadingMsgId
        );
        // 注意：updateTaskWithWorkflow 函数内部会重置任务创建状态
        return;
      } else {
        // 响应格式不正确，重置任务创建状态
        ChatInterface.setIsCreatingTask(false);
        ChatInterface.setTaskCreationInfo(null);
        throw new Error('生成的响应格式不正确');
      }
      }

      // 原有的脚本优化逻辑 - 使用 generateWorkflow API
      const conversationHistory = messages
        .filter(msg => msg.messageType === 'text' && (msg.sender === 'user' || msg.sender === 'assistant'))
        .map(msg => {
          const senderLabel = msg.sender === 'user' ? 'User' : 'Assistant (you)';
          return `${senderLabel}: ${msg.content}`;
        })
        .join('\n');
      console.log('conversationHistory', conversationHistory);

      // 获取应用知识库
      let appKnowledge = '';
      try {
        const knowledgeFileResult = await window.electronAPI.readTaskFile({
          taskName: ChatInterface.projectName,
          fileName: 'Knowledge.json',
        });
        if (knowledgeFileResult.success && knowledgeFileResult.content !== undefined) {
          const knowledgeData = JSON.parse(knowledgeFileResult.content);
          if (knowledgeData.knowledge) {
            appKnowledge = knowledgeData.knowledge;
          }
        }
      } catch (error) {
        console.warn('Failed to load Knowledge.json:', error);
      }

      const appLanguage = await window.electronAPI.getAppLanguageConfig();
      const currentWorkflow = CodeEditorUtils.getNLScript() || '';
      
      console.log('[ChatInterface] 准备调用 generateWorkflow API (任务优化模式)');
      console.log('[ChatInterface] currentWorkflow: (当前NL脚本)', currentWorkflow);
      console.log('[ChatInterface] needTaskName: false (已有任务)');
      
      const workflowResponse = await promptApi.generateWorkflow(
        conversationHistory,
        message.trim(),
        currentWorkflow, // currentWorkflow 使用当前的 NL Script
        appKnowledge,
        false, // needTaskName 为 false，因为已经有任务了
        appLanguage
      );

      console.log('[ChatInterface] generateWorkflow API 响应:', workflowResponse);
      
      // 解析 XML 响应
      const parsedResponse = parseWorkflowResponse(workflowResponse);
      
      // 如果有 thought，先显示到对话区
      if (parsedResponse.thought) {
        ChatInterface.addChatMessage('assistant', parsedResponse.thought);
      }
      
      if (parsedResponse.question) {
        // 显示问题
        ChatInterface.addChatMessage('assistant', parsedResponse.question);
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('conversationReplyComplete'));
      } else if (parsedResponse.workflow) {
        // 更新工作流（NL Script），并设置NL脚本版本为1
        CodeEditorUtils.setNLScript(parsedResponse.workflow, true);
        
        // 延迟触发自动保存，避免与转换过程冲突
        setTimeout(() => {
          CodeEditorUtils.triggerAutoSave();
        }, 2000);
        
        // ChatInterface.addChatMessage('assistant', '工作流已更新！');
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('taskOptimizationComplete'));
      } else {
        // 如果既没有问题也没有工作流，显示默认回复
        if (!parsedResponse.thought) {
          ChatInterface.addChatMessage('assistant', '已处理您的请求。');
        }
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('conversationReplyComplete'));
      }
    } catch (error) {
      if (error instanceof Error && error.message === 'Insufficient Token') {
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('insufficientTokenReply'), true);
      } else {
        console.error('Error:', error);
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('replyError'), true);
      }
      
      // 如果是任务创建失败，重置状态
      if (ChatInterface.isCreatingTask) {
        ChatInterface.setIsCreatingTask(false);
        ChatInterface.setTaskCreationInfo(null);
      }
    }
  };

  const handleDemoStart = async (messageId: string) => {
    const loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('startingDemo'));
    try {
      const response = await flaskApi.startDemo(
        ChatInterface.task,
        CodeEditorUtils.getCodeScript() || '',
        CodeEditorUtils.getNLScript() || '',
        ChatInterface.demonstrationQuestion
      );

      if (response.ok) {
        // 更新消息状态，设置当前 question 消息的 isDemoing 属性为 true
        setMessages(prevMessages => prevMessages.map(msg => 
          msg.id === messageId ? { ...msg, isDemoing: true } : msg
        ));
        // 同步更新工具栏按钮状态
        ToolbarState.updateDemoButtonText(I18nUtils.getText('endDemo'));
        ToolbarState.updateDemoButtonState(true);
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('startDemoComplete'));
      } else {
        const errorData = await response.json();
        if (errorData.error === 'WebSocket connection error') {
          message.error(I18nUtils.getText('websocketConnectionError'));
          throw new Error(I18nUtils.getText('websocketConnectionError'));
        }
        throw new Error('请求失败，状态码: ' + response.status);
      }
    }
    catch (error) {
      console.error('Demo start failed:', error);
      ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('startDemoError'), true);
    }
  };

  const handleDemoEnd = async (messageId: string) => {
    const loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('endingDemo'));
    try {
      const appLanguage = await window.electronAPI.getAppLanguageConfig();
      const response = await flaskApi.endDemo(
        appLanguage
      );

      if (response.ok) {
        const result = await response.json();
        CodeEditorUtils.setNLScript(result.NL_script);
        CodeEditorUtils.setCodeScript(result.code_script);
        // 延迟触发自动保存，避免与转换过程冲突
        setTimeout(() => {
          CodeEditorUtils.triggerAutoSave();
        }, 2000);
        // 同步更新工具栏按钮状态
        ToolbarState.updateDemoButtonText(I18nUtils.getText('startDemo'));
        ToolbarState.updateDemoButtonState(false);
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('endDemoComplete'));
        ChatInterface.resetAllDemoStates?.();
      } else {
        throw new Error('结束演示失败');
      }
    } catch (error) {
      console.error('Demo end failed:', error);
      ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('endDemoError'), true);
    }
  };

  const askUserDemonstration = async (questions: string[]) => {
    addChatMessage('assistant', 
      I18nUtils.getText('needExplanation') + "\n" + 
      questions.map((q, i) => `${i + 1}. ${q}`).join("\n")
    );
    addChatMessage('assistant', I18nUtils.getText('demoInstruction'));

    for (const question of questions) {
      ChatInterface.setDemonstrationQuestion(question);
      addChatMessage('assistant', question, true);
    }
  };

  const handleLoadExampleTask = async (taskName: any) => {
    // 根据 taskName 找到对应的 exampleTask
    const exampleTask = exampleTasks.find(task => task.task_name === taskName);
    if (!exampleTask) return;

    console.log("handleLoadExampleTask", exampleTask);

    // 更新 ChatInterface 的数据
    ChatInterface.setTask(exampleTask.task_name);
    ChatInterface.setTaskDescription(exampleTask.task_description);
    // 注意：示例任务的项目名称通常与任务名称相同，但可以根据需要调整
    ChatInterface.setProjectName(exampleTask.task_name);
    setHasTask(true);
    // setShowWelcome(false);  // 加载完示例任务之后，关闭 Welcome 组件，临时先不关闭了，更美观一些。
    
    // 更新 CodeEditor 的数据
    // 示例任务通常没有带标号的版本，所以只设置去标号的版本
    CodeEditorUtils.setCodeScript(exampleTask.code_script);
    CodeEditorUtils.setNLScript(exampleTask.NL_script);
    
    // 清空带标号的版本（因为示例任务没有带标号版本）
    CodeEditorUtils.labeledCodeScript = '';
    CodeEditorUtils.labeledNLScript = '';
    
    // 根据当前模式设置 lastScript
    if (CodeEditorUtils.scriptMode === "NL") {
      CodeEditorUtils.lastScript = exampleTask.NL_script;
    } else {
      CodeEditorUtils.lastScript = exampleTask.code_script;
    }

    // 延迟触发自动保存，确保示例任务脚本被保存
    setTimeout(() => {
      CodeEditorUtils.triggerAutoSave();
    }, 2000);

    message.success(I18nUtils.getText('loadExampleTaskSuccess'));
  };

  // 取消任务创建
  const handleCancelTaskCreation = () => {
    // 重置任务创建状态
    ChatInterface.setIsCreatingTask(false);
    ChatInterface.setTaskCreationInfo(null);
    
    // 清空聊天消息
    ChatInterface.setAllMessages([]);
    
    // 更新本地状态
    setIsCreatingTask(false);
    setTaskCreationProjectName('');
    
    message.success(I18nUtils.getText('taskCreationCancelled'));
  };

  // Add resize handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    if (onHeightChange) {
      setIsDragging(true);
      onDragStateChange?.(true);
      dragStartY.current = e.clientY;
      dragStartHeight.current = height || 300;
    }
  };

  useEffect(() => {
    let animationFrameId: number;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !onHeightChange) return;

      // Use requestAnimationFrame for smoother updates
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }

      animationFrameId = requestAnimationFrame(() => {
        const deltaY = dragStartY.current - e.clientY;
        // Apply constraints directly here like LogPanel does
        const newHeight = Math.min(Math.max(200, dragStartHeight.current + deltaY), window.innerHeight - 300);
        onHeightChange(newHeight);
      });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      onDragStateChange?.(false);
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [isDragging, onHeightChange]);

  // 监听系统提示词视图变化，及时更新输入框禁用状态
  useEffect(() => {
    const handleSystemPromptViewChange = (event: CustomEvent) => {
      const { isViewing } = (event.detail || {}) as { isViewing?: boolean };
      setIsViewingSystemPrompt(!!isViewing);
    };

    // 初始化状态
    setIsViewingSystemPrompt(CodeEditorUtils.viewAppKnowledge || CodeEditorUtils.viewProjectKnowledge);

    // 监听自定义事件
    window.addEventListener('systemPromptViewChange', handleSystemPromptViewChange as EventListener);

    return () => {
      window.removeEventListener('systemPromptViewChange', handleSystemPromptViewChange as EventListener);
    };
  }, []);

  return (
    <div className={`chat-interface-container ${isDragging ? 'dragging' : ''} ${isCreatingTask ? 'task-creation-mode' : ''}`}>
      <div
        className="chat-interface-header"
        onMouseDown={handleMouseDown}
        style={{ cursor: onHeightChange ? 'ns-resize' : 'default' }}
      >
        <div className="chat-interface-header-left">
          <MessageCircle size={20} color="#6366f1" />
          <span className="chat-interface-title">
            {I18nUtils.getText('dialogueArea')}
          </span>
        </div>
        
        {isCreatingTask && (
          <div className="chat-interface-header-right">
            <div className="task-creation-indicator">
              <span className="task-creation-text">
                {I18nUtils.getText('creatingNewTask')}
              </span>
              <Button 
                size="small" 
                type="text" 
                onClick={handleCancelTaskCreation}
                className="task-creation-cancel-btn"
              >
                <X size={16} />
                {I18nUtils.getText('cancelTaskCreation')}
              </Button>
            </div>
          </div>
        )}
      </div>
      <div className="chat-messages">
        {messages.map((msg) => (
          <div key={msg.id} className="chat-message">
            <Bubble
              placement={msg.sender === 'user' ? 'end' : 'start'}
              content={
                <>
                  {msg.isLoading && <Loader2 style={{ marginRight: 8 }} color="#1890ff" size={18} className="spin" />}
                  {msg.isComplete && !msg.isError && <CheckCircle style={{ marginRight: 8 }} color="#52c41a" size={18} />}
                  {msg.isComplete && msg.isError && <XCircle style={{ marginRight: 8 }} color="#ff4d4f" size={18} />}
                  {/* <strong>{I18nUtils.getText(msg.sender as any)}:</strong> {msg.content} */}
                  {msg.messageType !== 'notify_image' && msg.messageType !== 'notify_table' && msg.content}
                  {msg.messageType === 'notify_table' && (
                    <div className="chat-table-container">
                      <div className="chat-table-header">
                        <span>{I18nUtils.getText('agentSentTable')}</span>
                      </div>
                      <Card
                        size="small"
                        className="chat-card"
                        hoverable
                        onClick={() => ChatInterface.showTable(msg.payload)}
                      >
                        <div className="chat-card-content">
                          <Table size={18} />
                          <strong>{msg.payload.name}</strong>
                        </div>
                      </Card>
                    </div>
                  )}
                  {msg.messageType === 'notify_image' && msg.payload && (
                    <div className="chat-image-container">
                      <div className="chat-image-header">
                        <span>{I18nUtils.getText('agentSentImage')}</span>
                      </div>
                      <Card
                        size="small"
                        className="chat-image-card"
                        hoverable
                        onClick={() => ChatInterface.showImage(msg.payload)}
                        style={{ marginTop: '8px' }}
                      >
                        <div style={{ padding: '0px', textAlign: 'center' }}>
                          {(msg.payload.image_data || msg.payload.image?.image_data) ? (
                            <img
                              src={msg.payload.image_data || msg.payload.image?.image_data || ''}
                              alt={msg.payload?.metadata?.name || msg.payload?.image?.metadata?.name || 'image'}
                              style={{ 
                                maxWidth: '100%', 
                                maxHeight: '150px', 
                                borderRadius: '4px',
                                objectFit: 'contain'
                              }}
                              onError={(e) => {
                                console.error('Image load error:', e);
                                console.log('Message payload:', msg.payload);
                                console.log('Image data type:', typeof msg.payload.image_data);
                                console.log('Image data preview:', msg.payload.image_data?.substring(0, 100));
                                console.log('Nested image data type:', typeof msg.payload.image?.image_data);
                                console.log('Nested image data preview:', msg.payload.image?.image_data?.substring(0, 100));
                              }}
                              onLoad={() => {
                                console.log('Image loaded successfully');
                              }}
                            />
                          ) : (
                            <div style={{ 
                              padding: '20px', 
                              textAlign: 'center', 
                              color: '#999',
                              border: '1px dashed #ddd',
                              borderRadius: '4px'
                            }}>
                              图片加载失败
                            </div>
                          )}
                        </div>
                      </Card>
                    </div>
                  )}
                </>
              }
              avatar={{
                icon: msg.sender === 'user' ? <User size={20} /> :
                      msg.sender === 'system' ? <Settings size={20} /> :
                      <Bot size={20} />,
              }}
              header={
                msg.messageType === 'ask_question' 
                  ? I18nUtils.getText('assistantQuestion') 
                  : I18nUtils.getText(msg.sender as any)
              }
              footer={
                (msg.isQuestion && (
                  <div className="chat-demo-buttons-container">
                    {msg.isDemoing ? (
                      <Button onClick={() => handleDemoEnd(msg.id)} className="chat-end-demo-button">
                        {I18nUtils.getText('endDemo')}
                      </Button>
                    ) : (
                      <Button onClick={() => handleDemoStart(msg.id)} className="chat-demo-button">
                        {I18nUtils.getText('startDemo')}
                      </Button>
                    )}
                    <Button onClick={() => setQuoteMessage(msg.content)} className="chat-text-reply-button">
                      {I18nUtils.getText('replyText')}
                    </Button>
                  </div>
                ))
              }
            />
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      
      <div className="chat-input-area">
        {quoteMessage && (
          <QuoteHint 
            message={quoteMessage}
            onClose={() => {
              setQuoteMessage('');
              setReplyingToRequest(null);
            }}
          />
        )}
        <Sender
          className="chat-sender"
          loading={loading}
          value={inputValue}
          onChange={(v) => setInputValue(v)}
          onSubmit={() => {
            handleSend();
          }}
          onCancel={() => {
            setInputValue('');
          }}
          disabled={isViewingSystemPrompt}
          placeholder={
            isViewingSystemPrompt
              ? I18nUtils.getText('chatDisabledDuringSystemPromptView')
              : replyingToRequest 
                ? I18nUtils.getText('replyToQuestionPlaceholder') 
                : isCreatingTask
                  ? I18nUtils.getText('taskCreationInProgress')
                  : (!ChatInterface.task && !ChatInterface.currentEditingFile)
                    ? '描述您想要完成的任务...'
                    : I18nUtils.getText('chatInputPlaceholder')
          }
        />
      </div>
    </div>
  );
};

export default ChatInterfaceComponent;