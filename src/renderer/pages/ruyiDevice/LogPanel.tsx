import React, { useState, useEffect, useRef } from 'react';
import { flaskApi } from '../../../services/FlaskApiService';
import { I18nUtils } from './languageSupport/i18nUtils';
import '../styles/LogPanel.css';
import { useLanguage } from './languageSupport/LanguageContext';
import { ClearOutlined, DownloadOutlined, ExpandAltOutlined, ShrinkOutlined } from '@ant-design/icons';
import { Cctv, Monitor } from 'lucide-react';
import { Tooltip } from 'antd';

interface LogPanelProps {
  visible: boolean;
  height: number;
  onHeightChange: (height: number) => void;
}

export const LogPanel: React.FC<LogPanelProps> = ({ visible, height, onHeightChange }) => {
  const { language } = useLanguage();
  const [logs, setLogs] = useState<string[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const dragStartY = useRef<number>(0);
  const dragStartHeight = useRef<number>(0);
  const logContainerRef = useRef<HTMLTextAreaElement>(null);
  const previousHeight = useRef<number>(height);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    
    if (visible) {
      // 立即获取一次日志
      fetchLogs();
      
      // 设置定时器定期获取日志
      intervalId = setInterval(fetchLogs, 500);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [visible]);

  const fetchLogs = async () => {
    try {
      const response = await flaskApi.getRuyiAgentLog();
      // console.log('flaskApi getRuyiAgentLog response', response);
      if (response.ruyi_agent_log) {
        setLogs(prevLogs => [...prevLogs, response.ruyi_agent_log]);  // 将新日志追加到现有日志后面，直接追加字符串
        // 自动滚动到底部
        if (logContainerRef.current) {
          logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
        }
      }
    } catch (error) {
      console.error('获取日志失败:', error);
    }
  };

  // 在 useEffect 中监听 language 变化
  useEffect(() => {
    console.log('Language changed, updating LogPanel UI');
  }, [language]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    dragStartY.current = e.clientY;
    dragStartHeight.current = height;
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      
      const deltaY = dragStartY.current - e.clientY;
      // 最小高度为 52px
      const newHeight = Math.min(Math.max(0, dragStartHeight.current + deltaY), window.innerHeight - 200);
      onHeightChange(newHeight);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, onHeightChange]);

  const handleClearLogs = () => {
    setLogs([]);
  };

  const handleDownloadLogs = () => {
    const logText = logs.join('\n');
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ruyi-logs-${new Date().toISOString().replace(/[:.]/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleToggleExpand = () => {
    if (isExpanded) {
      // 恢复到之前的高度
      onHeightChange(previousHeight.current);
    } else {
      // 保存当前高度并展开
      previousHeight.current = height;
      onHeightChange(window.innerHeight / 2);
    }
    setIsExpanded(!isExpanded);
  };

  if (!visible) return null;

  return (
    <div className={`log-panel ${height <= 40 ? 'minimized' : ''}`} style={{ height: `${height}px` }}>
      <div className="log-panel-header" onMouseDown={handleMouseDown}>
        <span>
          <Cctv size={16} style={{ marginRight: 8, color: '#6366f1' }} />
          {I18nUtils.getText('executionLog')}
        </span>
        <div className="controls">
          <Tooltip title={I18nUtils.getText('clearLogs')}>
            <div className="control-button" onClick={handleClearLogs}>
              <ClearOutlined />
            </div>
          </Tooltip>
          <Tooltip title={I18nUtils.getText('downloadLogs')}>
            <div className="control-button" onClick={handleDownloadLogs}>
              <DownloadOutlined />
            </div>
          </Tooltip>
          <Tooltip title={isExpanded ? I18nUtils.getText('shrinkPanel') : I18nUtils.getText('expandPanel')}>
            <div className="control-button" onClick={handleToggleExpand}>
              {isExpanded ? <ShrinkOutlined /> : <ExpandAltOutlined />}
            </div>
          </Tooltip>
        </div>
      </div>
      <textarea
        className="log-panel-content"
        ref={logContainerRef}
        value={logs.join('\n')}
        readOnly
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          resize: 'none'
        }}
      />
    </div>
  );
};
