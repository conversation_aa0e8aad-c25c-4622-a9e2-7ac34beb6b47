import React, { useState, useEffect, useContext } from 'react';
import { Button, message, Modal } from 'antd';
import {
  PlayCircleOutlined,
  BookOutlined,
  SendOutlined,
  FileTextOutlined,
  OrderedListOutlined,
  ThunderboltOutlined,
  StopOutlined,
  BugOutlined
} from '@ant-design/icons';
import { CodeEditorUtils } from './CodeEditor';
import { ChatInterface } from './ChatInterface';
import { I18nUtils } from './languageSupport/i18nUtils';
import { flaskApi } from '../../../services/FlaskApiService';
import { useLanguage } from './languageSupport/LanguageContext';
import { promptApi } from '../../../services/api';
import { userApi } from '../../../services/api';
import { Device } from './Select';  // 导入 Device 类型
import { useDevices } from './DeviceContext';  // 导入 DeviceContext hook
import '../styles/Toolbar.css';

// 工具函数：移除脚本中的标号
const removeLabelFromScript = (labeledScript: string): string => {
  if (!labeledScript) return '';
  
  return labeledScript
    .split('\n')
    .map(line => {
      // 移除行首的 [数字] 标号，但保留标号后的缩进空格 (NL script格式)
      const lineWithoutPrefixLabel = line.replace(/^\[\d+\] ?/, '');
      
      // 移除行尾的注释标号 # [数字] (Code script格式)
      const lineWithoutSuffixLabel = lineWithoutPrefixLabel.replace(/\s*#\s*\[\d+\]\s*$/, '');
      
      return lineWithoutSuffixLabel;
    })
    .join('\n');
};

// 工具函数：获取当前任务文件中的标号版本脚本
const getLabeledScriptsFromCurrentTask = async (): Promise<{
  labeledNLScript: string;
  labeledCodeScript: string;
}> => {
  // 首先尝试从 CodeEditorUtils 中获取最新的带标号脚本
  const currentLabeledNL = CodeEditorUtils.labeledNLScript;
  const currentLabeledCode = CodeEditorUtils.labeledCodeScript;

  // 如果 CodeEditorUtils 中有带标号脚本，则使用它们
  if (currentLabeledNL && currentLabeledCode) {
    return {
      labeledNLScript: currentLabeledNL,
      labeledCodeScript: currentLabeledCode
    };
  }

  // 否则从文件中读取
  const projectName = ChatInterface.projectName;
  const currentEditingFile = ChatInterface.currentEditingFile;

  if (!projectName || !currentEditingFile || !currentEditingFile.endsWith('.json') || currentEditingFile === 'Knowledge.json') {
    return { labeledNLScript: '', labeledCodeScript: '' };
  }

  try {
    const result = await window.electronAPI.readTaskFile({
      taskName: projectName,
      fileName: currentEditingFile
    });

    if (result.success && result.content) {
      const taskData = JSON.parse(result.content);
      return {
        labeledNLScript: taskData.NL_script_labeled || '',
        labeledCodeScript: taskData.code_script_labeled || ''
      };
    }
  } catch (error) {
    console.warn('Failed to get labeled scripts from current task:', error);
  }

  return { labeledNLScript: '', labeledCodeScript: '' };
};

// 工具函数：根据行号从带标号的NL脚本中提取对应的带标号文本
const extractLabeledNLScriptByLines = (labeledNLScript: string, startLine: number, endLine: number): string => {
  if (!labeledNLScript || startLine < 0 || endLine < 0) {
    return '';
  }

  try {
    const labeledLines = labeledNLScript.split('\n');
    const selectedLines: string[] = [];
    
    // 根据编辑器中的行号提取对应的带标号行
    let currentEditorLineNumber = 1;
    
    for (const labeledLine of labeledLines) {
      const trimmedLine = labeledLine.trim();
      
      // 跳过空行
      if (!trimmedLine) {
        continue;
      }
      
      // 检查是否是带标号的行
      const lineMatch = trimmedLine.match(/^\[(\d+)\]/);
      if (lineMatch) {
        // 如果当前编辑器行号在选中范围内，添加到结果中
        if (currentEditorLineNumber >= startLine && currentEditorLineNumber <= endLine) {
          selectedLines.push(labeledLine);
        }
        currentEditorLineNumber++;
      }
    }
    
    return selectedLines.join('\n');
  } catch (error) {
    console.warn('Failed to extract labeled NL script by lines:', error);
    return '';
  }
};

// 工具函数：从标号版本脚本中提取选中部分对应的标号代码
const getSelectedCodeFromLabeled = (labeledCodeScript: string, selectedNLScript: string, NLScript: string): string => {
  if (!labeledCodeScript || !selectedNLScript || !NLScript) {
    return '';
  }

  try {
    // 找到选中的NL脚本在完整NL脚本中的行号范围
    const nlLines = NLScript.split('\n');
    const selectedLines = selectedNLScript.split('\n');
    
    // 简单的匹配逻辑：找到选中文本在原文中的位置
    const startIndex = NLScript.indexOf(selectedNLScript);
    if (startIndex === -1) return '';
    
    const beforeText = NLScript.substring(0, startIndex);
    const startLineNumber = beforeText.split('\n').length;
    const endLineNumber = startLineNumber + selectedLines.length - 1;
    
    // 从标号版本的代码脚本中找到对应行号的代码
    const codeLines = labeledCodeScript.split('\n');
    const selectedCodeLines: string[] = [];
    
    for (const line of codeLines) {
      let lineNumber = -1;
      
      // 检查行首格式 [数字] (旧格式兼容)
      const prefixMatch = line.match(/^\[(\d+)\]/);
      if (prefixMatch) {
        lineNumber = parseInt(prefixMatch[1]);
      } else {
        // 检查行尾注释格式 # [数字] (新格式)
        const suffixMatch = line.match(/#\s*\[(\d+)\]\s*$/);
        if (suffixMatch) {
          lineNumber = parseInt(suffixMatch[1]);
        }
      }
      
      if (lineNumber !== -1 && lineNumber >= startLineNumber && lineNumber <= endLineNumber) {
        selectedCodeLines.push(line);
      }
    }
    
    return selectedCodeLines.join('\n');
  } catch (error) {
    console.warn('Failed to extract selected code from labeled:', error);
    return '';
  }
};

// 修改为函数，延迟获取文本
export const ToolbarState = {
  getDemoButtonText: () => I18nUtils.getText('startDemo'),
  demoButtonText: '',
  isDemoing: false,
  isChangingScript: false,
  updateDemoButtonText: (text: string) => {
    ToolbarState.demoButtonText = text;
  },
  updateDemoButtonState: (isDemo: boolean) => {
    ToolbarState.isDemoing = isDemo;
  },
  updateChangingScriptState: (isChanging: boolean) => {
    ToolbarState.isChangingScript = isChanging;
  }
};

const parseWorkflowToCodeResponse = (response: string): { thought: string; labeled_python_script: string } => {
  const thoughtMatch = response.match(/<thought>([\s\S]*?)<\/thought>/);
  const pythonScriptMatch = response.match(/<labeled_python_script>([\s\S]*?)<\/labeled_python_script>/);
  
  let thought = thoughtMatch ? thoughtMatch[1].trim() : '';
  let labeledPythonScript = pythonScriptMatch ? pythonScriptMatch[1].trim() : '';

  // 检查并移除 Python 脚本中的 ``` 包裹
  if (labeledPythonScript.startsWith('```') && labeledPythonScript.endsWith('```')) {
    // 将脚本按行分割
    const lines = labeledPythonScript.split('\n');
    // 移除第一行和最后一行的 ```
    labeledPythonScript = lines.slice(1, -1).join('\n').trim();
  }

  return {
    thought,
    labeled_python_script: labeledPythonScript
  };
};

/**
 * 执行代码按钮处理函数
 * @param selected 是否是选中部分的 script 执行
 */
export const handleExecuteCode = async (selected = false) => {
  let loadingMsgId = '';
  try {
    // 先检查用户的 token 余额
    const userInfoResponse = await userApi.getUserInfo();
    if (userInfoResponse.success && userInfoResponse.data.quota === 0) {
      message.error(I18nUtils.getText('insufficientTokenExecute'));
      return;
    }

    // 先更新设备映射配置到 RuyiAgent
    try {
      // 尝试获取设备信息
      let devicesFromContext: Device[] = [];
      
      // 尝试从全局获取设备状态（如果在 DeviceProvider 上下文中）
      try {
        const deviceContext = (window as any).__deviceContext;
        if (deviceContext?.devices) {
          devicesFromContext = deviceContext.devices;
        }
      } catch (error) {
        console.warn('无法从 Context 获取设备信息，跳过设备映射更新:', error);
      }

      if (devicesFromContext.length > 0) {
        // 过滤出已连接的设备并生成映射
        const connectedDevices = devicesFromContext.filter((device: Device) => {
          // 排除未连接或未授权的设备
          const isConnected = device.status === I18nUtils.getText('connected') || 
                             device.status === I18nUtils.getText('ready') ||
                             (device.status === I18nUtils.getText('connected') && !('isSdkUnavailable' in device && device.isSdkUnavailable));
          return isConnected;
        });

        // 生成设备映射对象
        const deviceMappings: Record<string, string> = {};
        connectedDevices.forEach((device: Device) => {
          const deviceName = device.deviceNumber || device.id;
          deviceMappings[deviceName] = device.id;
        });

        // 更新到 RuyiAgent 配置
        const success = await window.electronAPI.setDeviceMappingsConfig(deviceMappings);
        if (success) {
          console.log('设备映射配置已更新:', deviceMappings);
        } else {
          console.warn('设备映射配置更新失败');
        }

        // 设置 RuyiAgent 的 device_name 和 device_type
        try {
          // 检查用户是否选择了某个设备进行查看
          const selectedDevice = (window as any).__selectedDevice;
          
          let targetDevice: Device | null = null;
          let deviceName = '';
          let deviceType = '';
          
          if (selectedDevice) {
            // 如果用户选择了设备，验证该设备是否在已连接设备列表中
            const deviceInList = connectedDevices.find(device => device.id === selectedDevice.id);
            if (deviceInList) {
              targetDevice = deviceInList;
            }
          }
          
          // 如果没有选择设备或选择的设备未连接，使用第一个已连接的设备
          if (!targetDevice && connectedDevices.length > 0) {
            targetDevice = connectedDevices[0];
          }
          
          if (targetDevice) {
            // 设置设备名称
            deviceName = targetDevice.deviceNumber || targetDevice.id;
            
            // 根据设备类型设置 device_type
            if ('type' in targetDevice && targetDevice.type === 'browser') {
              deviceType = 'browser';
            } else {
              deviceType = 'websocket';
            }
            
            // 更新 RuyiAgent 配置
            const configSuccess = await window.electronAPI.setRuyiAgentDeviceConfig(deviceName, deviceType);
            if (configSuccess) {
              console.log(`RuyiAgent 设备配置已更新: device_name=${deviceName}, device_type=${deviceType}`);
            } else {
              console.warn('RuyiAgent 设备配置更新失败');
            }
          } else {
            console.warn('没有可用的已连接设备，无法设置 RuyiAgent 设备配置');
          }
        } catch (error) {
          console.error('设置 RuyiAgent 设备配置时出错:', error);
        }
      }
    } catch (error) {
      console.error('更新设备映射配置时出错:', error);
    }

    // CodeEditorUtils.checkAndUpdateAppKnowledge();
    await window.electronAPI.createAnnotationDataDir();

    // 先获取选中的脚本，因为更新了脚本之后，就无法获取选中的脚本了
    const selectedCodeScript = CodeEditorUtils.getSelectedCodeScript();
    const selectedNLScript = CodeEditorUtils.getSelectedNLScript();
    const startLine = CodeEditorUtils.getSelectedStartLine();
    const endLine = CodeEditorUtils.getSelectedEndLine();

    // 如果当前是 Code 模式，执行之前就不用同步更新 NL 脚本了
    if (CodeEditorUtils.scriptMode === "NL") {
      await CodeEditorUtils.updateScriptBeforeExecute();
    }
    
    // 等待下一个事件循环，确保脚本更新完成
    await new Promise(resolve => setTimeout(resolve, 100));

    // loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('executing'));

    // 获取所有需要发送的脚本内容
    const codeScript = CodeEditorUtils.getCodeScript() || '';
    const NLScript = CodeEditorUtils.getNLScript() || '';
    
    // 直接从 CodeEditorUtils 获取最新的带标号脚本，避免从文件重新读取旧数据
    let labeledNLScript = CodeEditorUtils.labeledNLScript || '';
    let labeledCodeScript = CodeEditorUtils.labeledCodeScript || '';
    
    // 如果 CodeEditorUtils 中没有带标号脚本，则尝试从任务文件获取
    if (!labeledNLScript || !labeledCodeScript) {
      console.log('[Toolbar] CodeEditorUtils中缺少带标号脚本，从任务文件获取');
      const { labeledNLScript: fileLabeledNL, labeledCodeScript: fileLabeledCode } = await getLabeledScriptsFromCurrentTask();
      labeledNLScript = labeledNLScript || fileLabeledNL;
      labeledCodeScript = labeledCodeScript || fileLabeledCode;
    }

    console.log('===== handleExecuteCode =====');
    console.log('CodeEditorUtils.scriptMode:', CodeEditorUtils.scriptMode);
    console.log('selected:', selected);
    console.log('selectedCodeScript:', selectedCodeScript);
    console.log('selectedNLScript:', selectedNLScript);
    console.log('startLine:', startLine);
    console.log('endLine:', endLine);
    console.log('codeScript:', codeScript);
    console.log('NLScript:', NLScript);
    console.log('labeledNLScript:', labeledNLScript);
    console.log('labeledCodeScript:', labeledCodeScript);

    // 获取标号版本的脚本

    // 检查是否需要先生成代码脚本
    let finalCodeScript = codeScript;
    let finalLabeledCodeScript = labeledCodeScript;
    
    if (!codeScript && NLScript) {
      console.log('[Toolbar] 检测到任务还没有代码脚本，正在从工作流生成...');
      const transferLoadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('executionNeedsCodeScript'));
      
      try {
        const appLanguage = await window.electronAPI.getAppLanguageConfig();
        const response = await promptApi.transferWorkflowToCode(
          ChatInterface.taskDescription || ChatInterface.task,
          '', // currentLabeledPythonScript 为空，因为还没有代码脚本
          '', // oldLabeledWorkflow 为空，因为是新任务
          labeledNLScript, // currentLabeledWorkflow 使用当前的带标号 NL 脚本
          appLanguage
        );

        console.log('[Toolbar] transferWorkflowToCode response:', response);
        
        // 解析响应
        const parsedResponse = parseWorkflowToCodeResponse(response);
        const generatedCodeScript = parsedResponse.labeled_python_script;
        
        // 移除标号得到去标号的代码脚本
        const cleanCodeScript = removeLabelFromScript(generatedCodeScript);
        
        // 更新脚本内容
        finalCodeScript = cleanCodeScript;
        finalLabeledCodeScript = generatedCodeScript;
        
        // 更新 CodeEditor 中的脚本
        CodeEditorUtils.setCodeScript(cleanCodeScript);
        CodeEditorUtils.setLabeledCodeScript(generatedCodeScript, cleanCodeScript);
        
        // 触发自动保存
        setTimeout(() => {
          CodeEditorUtils.triggerAutoSave();
        }, 1000);
        
        ChatInterface.updateToCompleteMessage(transferLoadingMsgId, I18nUtils.getText('generateCodeScriptBeforeExecutionComplete'));
        console.log('[Toolbar] 代码脚本生成完成，继续执行...');
        
      } catch (error) {
        console.error('[Toolbar] 代码脚本生成失败:', error);
        if (error instanceof Error && error.message === 'Insufficient Token') {
          ChatInterface.updateToCompleteMessage(transferLoadingMsgId, I18nUtils.getText('insufficientTokenExecute'), true);
        } else {
          ChatInterface.updateToCompleteMessage(transferLoadingMsgId, I18nUtils.getText('generateCodeScriptBeforeExecutionError'), true);
        }
        throw error;
      }
    }

    // 执行代码
    let response;

    if (!selected) {
      // 没用选中，发送所有代码执行
      loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('executing'));
      response = await flaskApi.executeCode(
        ChatInterface.task,
        finalCodeScript,
        finalLabeledCodeScript,
        labeledNLScript
      );
    } else {
      if (selectedCodeScript) {
        // 选中 code script，需要获取对应的标号版本选中代码
        // const selectedCodeLabeled = getSelectedCodeFromLabeled(labeledCodeScript, selectedNLScript || '', NLScript);
        
        // 当在 Code 模式下选择代码时，selectedNLScript 会为空，getSelectedCodeFromLabeled 设计上会返回空
        // 此时应该根据代码编辑器的行号来截取 finalLabeledCodeScript
        const labeledCodeLines = finalLabeledCodeScript.split('\n');
        const labeledSelectedCode = labeledCodeLines.slice(startLine - 1, endLine).join('\n');
        
        console.log('[Toolbar] labeledSelectedCode:', labeledSelectedCode);
        console.log('[Toolbar] labeledNLScript:', labeledNLScript);

        loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('executing'));
        response = await flaskApi.executeCode(
          ChatInterface.task,
          selectedCodeScript,
          labeledSelectedCode,
          labeledNLScript  // 传入完整的NL标号脚本
        );
      } else {
        // 选中 NL script，先调用 transferSelectedWorkflowToCode 转换为 code script 再执行
        const transferLoadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('transferSelectedScriptFromNLToCode'));
        let transferResponse;
        try {
          // 根据选中的行号从 labeledNLScript 中提取对应的带标号文本
          const labeledSelectedNLScript = extractLabeledNLScriptByLines(labeledNLScript, startLine, endLine);
          
          console.log('[Toolbar] 选中的NL脚本 (不带标号):', selectedNLScript);
          console.log('[Toolbar] 选中的NL脚本 (带标号):', labeledSelectedNLScript);
          console.log('[Toolbar] 选中行号范围:', `${startLine}-${endLine}`);
          
          const appLanguage = await window.electronAPI.getAppLanguageConfig();
          const response = await promptApi.transferSelectedWorkflowToCode(
            ChatInterface.task,
            labeledNLScript,
            labeledCodeScript,
            labeledSelectedNLScript,  // 使用带标号的选中文本
            appLanguage
          );

          // 使用 CodeEditor 的方法解析响应
          transferResponse = {
            code_script: parseWorkflowToCodeResponse(response).labeled_python_script,
            NL_script: labeledSelectedNLScript // 保持原有的 NL script
          };
        } catch (error) {
          if (error instanceof Error && error.message === 'Insufficient Token') {
            ChatInterface.updateToCompleteMessage(transferLoadingMsgId, I18nUtils.getText('insufficientTokenTransferSelectedScript'), true);
          } else {
            ChatInterface.updateToCompleteMessage(transferLoadingMsgId, I18nUtils.getText('transferSelectedScriptFromNLToCodeError'), true);
          }
          throw new Error(I18nUtils.getText('transferSelectedScriptFromNLToCodeError'));
        }

        const selectedLabeledNLScript = transferResponse.NL_script;
        const transferLabeledCodeScript = transferResponse.code_script;
        
        // 移除标号得到真正的脚本
        const cleanTransferCodeScript = transferLabeledCodeScript
          .split('\n')
          .map((line: string) => line.replace(/^\[\d+\] ?/, ''))
          .join('\n');

          ChatInterface.updateToCompleteMessage(transferLoadingMsgId, I18nUtils.getText('transferSelectedScriptFromNLToCodeComplete'));


        loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('executing'));
        response = await flaskApi.executeCode(
          ChatInterface.task,
          cleanTransferCodeScript, // 使用清理后的脚本
          transferLabeledCodeScript, // 转换得到的代码没有标号版本
          selectedLabeledNLScript  // 传入完整的NL标号脚本
        );
      }
    }

    if (!response.ok) {
      const errorData = await response.json();
      if (errorData.error === 'WebSocket connection error') {
        message.error(I18nUtils.getText('websocketConnectionError'));
        throw new Error(I18nUtils.getText('websocketConnectionError'));
      }
      throw new Error('请求失败，状态码: ' + response.status);
    }

    const result = await response.text();
    console.log(result);
    ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('executeComplete'));
  }
  catch (error) {
    console.error('handleExecuteCode 执行过程中出错:', error);
    // updateScriptBeforeExecute 中的报错逻辑中，就包含 loadingMsgId 的更新
    // 所以下面的更新语句，在 updateScriptBeforeExecute 中报错的时候，不会被调用
    ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('executeError'), true);
  }
};

/**
 * 开始演示，演示用户选中的代码
 */
export const handleStartDemo = async () => {

  // 获取所有需要发送的脚本内容
  const codeScript = CodeEditorUtils.getCodeScript() || '';
  const NLScript = CodeEditorUtils.getNLScript() || '';
  const selectedCodeScript = CodeEditorUtils.getSelectedCodeScript();
  const selectedNLScript = CodeEditorUtils.getSelectedNLScript();
  const startLine = CodeEditorUtils.getSelectedStartLine();
  const endLine = CodeEditorUtils.getSelectedEndLine();
      

  const loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('startingDemo'));
  try {
    const appLanguage = await window.electronAPI.getAppLanguageConfig();
    const response = await flaskApi.startDemo(
      ChatInterface.task,
      appLanguage,
      codeScript,
      NLScript,
      "",
      selectedCodeScript,
      selectedNLScript,
      startLine,
      endLine
    );

    if (response.ok) {
      // 同步更新工具栏按钮状态
      // ToolbarState.updateDemoButtonText(I18nUtils.getText('endDemo'));
      ToolbarState.updateDemoButtonState(true);
      ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('startDemoComplete'));
    } else {
      const errorData = await response.json();
      if (errorData.error === 'WebSocket connection error') {
        message.error(I18nUtils.getText('websocketConnectionError'));
        throw new Error(I18nUtils.getText('websocketConnectionError'));
      }
      throw new Error('请求失败，状态码: ' + response.status);
    }
  } catch (error) {
    console.error('Demo start failed:', error);
    ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('startDemoError'), true);
  }
};

/**
 * 结束演示
 */
export const handleEndDemo = async () => {
  const loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('endingDemo'));
  try {
    const appLanguage = await window.electronAPI.getAppLanguageConfig();
    const response = await flaskApi.endDemo(appLanguage);

    if (response.ok) {
      const result = await response.json();
      CodeEditorUtils.setNLScript(result.NL_script);
      CodeEditorUtils.setCodeScript(result.code_script);
      // 延迟触发自动保存，避免与转换过程冲突
      setTimeout(() => {
        CodeEditorUtils.triggerAutoSave();
      }, 2000);
      // 同步更新工具栏按钮状态
      // ToolbarState.updateDemoButtonText(I18nUtils.getText('startDemo'));
      ToolbarState.updateDemoButtonState(false);
      ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('endDemoComplete'));
      // ChatInterface.resetAllDemoStates?.();
    } else {
      throw new Error('结束演示失败');
    }
  } catch (error) {
    console.error('Demo end failed:', error);
    ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('endDemoError'), true);
  }
};

// 添加停止执行按钮处理函数
export const handleStopExecute = async () => {
  let loadingMsgId = '';
  try {
    loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('stoppingExecute'));
    const response = await flaskApi.stopExecute();
    
    if (!response.ok) {
      throw new Error('请求失败，状态码: ' + response.status);
    }

    // 清除执行行高亮
    CodeEditorUtils.clearExecutingLineHighlight();

    ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('stopExecuteComplete'));
  } catch (error) {
    console.error('停止执行过程中出错:', error);
    
    // 即使出错也要清除高亮
    CodeEditorUtils.clearExecutingLineHighlight();
    
    ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('stopExecuteError'), true);
  }
};

// 自动调试按钮处理函数
export const handleAutoDebug = async () => {
  console.log('自动调试');
};

interface ToolbarProps {
  // 可以在这里添加其他需要的 props
}

export const Toolbar: React.FC<ToolbarProps> = () => {
  const { language } = useLanguage();
  const { devices } = useDevices();  // 获取设备信息
  
  // 状态定义
  const [demoButtonText, setDemoButtonText] = useState(ToolbarState.getDemoButtonText());
  const [appKnowledgeButtonText, setAppKnowledgeButtonText] = useState(I18nUtils.getText('viewAppKnowledge'));
  const [isChangingScript, setIsChangingScript] = useState(false);
  const [isBatchModalVisible, setIsBatchModalVisible] = useState(false);
  const [isDemoing, setIsDemoing] = useState(false);

  // IMPORTANT: 此处不能将 scriptMode 和 viewAppKnowledge 设置为 state 进行使用, 因为他们无法自动更新
  // const [scriptMode, setScriptMode] = useState(CodeEditorUtils.scriptMode);
  // const [viewAppKnowledge, setViewAppKnowledge] = useState(CodeEditorUtils.viewAppKnowledge);

  // 根据 scriptMode 动态设置 scriptModeButtonText
  const [scriptModeButtonText, setScriptModeButtonText] = useState(
    CodeEditorUtils.scriptMode === "Code" ? I18nUtils.getText('viewSteps') : I18nUtils.getText('viewScript')
  );

  // 使用 useEffect 监听 ToolbarState 的变化
  useEffect(() => {
    const originalUpdateDemoButtonText = ToolbarState.updateDemoButtonText;
    const originalUpdateDemoButtonState = ToolbarState.updateDemoButtonState;
    const originalUpdateChangingScriptState = ToolbarState.updateChangingScriptState;

    ToolbarState.updateDemoButtonText = (text: string) => {
      originalUpdateDemoButtonText(text);
      setDemoButtonText(text);
    };

    ToolbarState.updateDemoButtonState = (isDemo: boolean) => {
      originalUpdateDemoButtonState(isDemo);
      setIsDemoing(isDemo);
    };

    ToolbarState.updateChangingScriptState = (isChanging: boolean) => {
      originalUpdateChangingScriptState(isChanging);
      setIsChangingScript(isChanging);
    };

    return () => {
      ToolbarState.updateDemoButtonText = originalUpdateDemoButtonText;
      ToolbarState.updateDemoButtonState = originalUpdateDemoButtonState;
      ToolbarState.updateChangingScriptState = originalUpdateChangingScriptState;
    };
  }, []);

  // 添加快捷键监听
  useEffect(() => {
    let isMounted = true;  // 添加挂载状态标识

    const handleToggleScriptLanguage = () => {
      // 如果当前正在显示知识库，忽略快捷键
      if (CodeEditorUtils.viewAppKnowledge || CodeEditorUtils.viewProjectKnowledge) {
        return;
      }
      
      if (!ToolbarState.isChangingScript && isMounted) {  // 使用全局状态检查
        handleChangeScriptLanguage();
      }
    };

    window.electronAPI.onToggleScriptLanguage(handleToggleScriptLanguage);

    // 清理函数
    return () => {
      isMounted = false;  // 组件卸载时设置为false
      // 移除事件监听器
      window.electronAPI.offToggleScriptLanguage?.();
    };
  }, []);

  // 演示按钮处理函数
  const handleDemo = async () => {
    const isStarting = demoButtonText === I18nUtils.getText('startDemo');
    // const endpoint = isStarting ? 'start_demo' : 'end_demo';
    const endpoint = isStarting ? 'startDemo' : 'endDemo';
    const loadingText = isStarting ? 'startingDemo' : 'endingDemo';
    const completeText = isStarting ? 'startDemoComplete' : 'endDemoComplete';
    const errorText = isStarting ? 'startDemoError' : 'endDemoError';

    const loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText(loadingText));

    try {
      const response = await flaskApi[endpoint](
        ChatInterface.task,
        CodeEditorUtils.getCodeScript() || '',
        CodeEditorUtils.getNLScript() || '',
        ChatInterface.demonstrationQuestion
      );
      
      if (!response.ok) {
        if (isStarting) {
          const errorData = await response.json();
          if (errorData.error === 'WebSocket connection error') {
              message.error(I18nUtils.getText('websocketConnectionError'));
              throw new Error(I18nUtils.getText('websocketConnectionError'));
            }
        }
        throw new Error('请求失败，状态码: ' + response.status);
      }

      if (isStarting) {
        const result = await response.text();
        console.log(result);
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText(completeText));
        ToolbarState.updateDemoButtonState(true);
        ToolbarState.updateDemoButtonText(I18nUtils.getText('endDemo'));
        // StreamClientScrcpy.startDemonstrationCount++;
      } else {
        const result = await response.json();
        CodeEditorUtils.setNLScript(result.NL_script);
        CodeEditorUtils.setCodeScript(result.code_script);
        // 延迟触发自动保存，避免与转换过程冲突
        setTimeout(() => {
          CodeEditorUtils.triggerAutoSave();
        }, 2000);
        // ChatInterface.addChatMessage('assistant', result.response);
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText(completeText));
        // StreamClientScrcpy.endDemonstrationCount++;
        ToolbarState.updateDemoButtonState(false);
        ToolbarState.updateDemoButtonText(I18nUtils.getText('startDemo'));
        // 重置所有聊天消息的演示状态
        ChatInterface.resetAllDemoStates?.();
      }
    } catch (error) {
      console.error('演示操作失败:', error);
      ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText(errorText), true);
    }
  };

  // 切换脚本语言钮处理函数
  const handleChangeScriptLanguage = async () => {
    if (ToolbarState.isChangingScript) {
      return; // 如果正在转换，忽略点击
    }

    if (CodeEditorUtils.viewAppKnowledge) {
      // 如果在查看知识库，直接隐藏并更新按钮文本
      await CodeEditorUtils.hideAppKnowledge();
      setAppKnowledgeButtonText(I18nUtils.getText('viewAppKnowledge'));
      
      // 等待状态更新后再设置按钮文本
      setTimeout(() => {
        setScriptModeButtonText(CodeEditorUtils.scriptMode === "Code"
          ? I18nUtils.getText('viewSteps')
          : I18nUtils.getText('viewScript'));
      }, 0);
    } else {
      // 执行脚本语言转换
      try {
        await CodeEditorUtils.changeScriptLanguage();
        
        // 等待状态更新后再设置按钮文本
        setTimeout(() => {
          setScriptModeButtonText(CodeEditorUtils.scriptMode === "Code"
            ? I18nUtils.getText('viewSteps')
            : I18nUtils.getText('viewScript'));
        }, 100); // 稍微延长等待时间确保状态更新完成
      } catch (error) {
        console.error('脚本语言转换失败:', error);
        // 转换失败时确保状态被重置
        ToolbarState.updateChangingScriptState(false);
      }
    }
  };

  // 应用知识库按钮处理函数
  const handleAppKnowledge = () => {
    if (CodeEditorUtils.viewAppKnowledge) {
      CodeEditorUtils.hideAppKnowledge();
      setAppKnowledgeButtonText(I18nUtils.getText('viewAppKnowledge'));
    } else {
      CodeEditorUtils.showAppKnowledge();
      setAppKnowledgeButtonText(CodeEditorUtils.scriptMode === "Code"
        ? I18nUtils.getText('viewScript')
        : I18nUtils.getText('viewSteps'));
    }
  };

  // 获取知识库按钮的图标
  const getAppKnowledgeIcon = () => {
    if (appKnowledgeButtonText === I18nUtils.getText('viewAppKnowledge')) {
      return <BookOutlined />;  // 查看知识库
    } else if (CodeEditorUtils.scriptMode === "Code") {
      return <FileTextOutlined />;  // 查看脚本
    } else {
      return <OrderedListOutlined />;  // 查看步骤
    }
  };

  // 获取脚本/步骤按钮的图标
  const getScriptModeIcon = () => {
    return CodeEditorUtils.scriptMode === "Code"
      ? <OrderedListOutlined />  // 查看步骤
      : <FileTextOutlined />;    // 查看脚本
  };

  // 在 useEffect 中监听 language 变化
  useEffect(() => {
    // 更新工具栏按钮等需要翻译的内容
    console.log('Language changed, updating Toolbar UI');
    if (CodeEditorUtils.viewAppKnowledge) {
      setAppKnowledgeButtonText(CodeEditorUtils.scriptMode === "Code"
        ? I18nUtils.getText('viewScript')
        : I18nUtils.getText('viewSteps'));
    } else {
      setAppKnowledgeButtonText(I18nUtils.getText('viewAppKnowledge'));
    }
  }, [language]);

  return (
    <>
      <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', padding: '0 16px' }}>
        {/* 左侧功能按钮 - 移除执行和停止执行按钮 */}
        <div style={{ display: 'flex', gap: '20px' }}>
          {/* 执行和停止执行按钮已迁移到navbar */}

          {isDemoing && (
            <Button
              type="primary"
              danger
              icon={<StopOutlined />}
              onClick={handleEndDemo}
            >
              {I18nUtils.getText('endDemo')}
            </Button>
          )}

          {/* 演示按钮 -> 暂时关闭 */}
          {/* <Button
            type="primary"
            danger={demoButtonText !== I18nUtils.getText('startDemo')}
            icon={<PlayCircleOutlined />}
            onClick={handleDemo}
          >
            {demoButtonText}
          </Button> */}
        </div>

        {/* 右侧功能按钮 */}
        {/* 切换脚本语言按钮 -> 暂时关闭 */}
        {/* <Button
          icon={getScriptModeIcon()}
          onClick={handleChangeScriptLanguage}
          disabled={isChangingScript}
        >
          {scriptModeButtonText}
        </Button> */}

        {/* 知识库按钮 -> 暂时关闭, 因为知识库功能 server 暂时没有支持 */}
        {/* <Button
          icon={getAppKnowledgeIcon()}
          onClick={handleAppKnowledge}
          disabled={isChangingScript}
        >
          {appKnowledgeButtonText}
        </Button> */}
      </div>
    </>
  );
};