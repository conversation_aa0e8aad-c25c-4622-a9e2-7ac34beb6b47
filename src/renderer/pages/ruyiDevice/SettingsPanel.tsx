import React, { useEffect } from 'react';
import { Menu, Input, Select, Radio, Button, message } from 'antd';
import { I18nUtils } from './languageSupport/i18nUtils';
import '../styles/SettingsPanel.css';
import { useLanguage } from './languageSupport/LanguageContext';
import { userApi, tokenApi } from '../../../services/api';
interface SettingsPanelProps {
  modelSettings: {
    modelType: string;
    apiKey: string;
  };
  portSettings: {
    flask_port: string;
    scrcpy_port: string;
    device_port: string;
    electron_http_port: string;
  };
  browserSettings: {
    homepage: string;
    searchEngine: string;
  };
  language: string;
  appLanguage: string;
  onModelSettingsChange: (settings: any) => void;
  onPortSettingsChange: (settings: any) => void;
  onBrowserSettingsChange: (settings: any) => void;
  onLanguageChange: (language: string) => void;
  onAppLanguageChange: (language: string) => void;
  onPortChange: (e: React.ChangeEvent<HTMLInputElement>, field: 'flask_port' | 'scrcpy_port' | 'device_port' | 'electron_http_port') => void;
  onBrowserHomepageChange: (homepage: string) => void;
  onBrowserSearchEngineChange: (searchEngine: string) => void;
  onPortSettingsSave: () => void;
  onModelSettingsSave: () => void;
  onBrowserSettingsSave: () => void;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  modelSettings,
  portSettings,
  browserSettings,
  language,
  appLanguage,
  onModelSettingsChange,
  onPortSettingsChange,
  onBrowserSettingsChange,
  onLanguageChange,
  onAppLanguageChange,
  onPortChange,
  onBrowserHomepageChange,
  onBrowserSearchEngineChange,
  onPortSettingsSave,
  onModelSettingsSave,
  onBrowserSettingsSave
}) => {
  const [selectedMenuItem, setSelectedMenuItem] = React.useState('language');
  const { language: contextLanguage, setLanguage } = useLanguage();
  const [tokenBalance, setTokenBalance] = React.useState<number | null>(null);
  const [tokenUsed, setTokenUsed] = React.useState<number | null>(null);
  const [exchangeAmount, setExchangeAmount] = React.useState<string>('');

  useEffect(() => {
    console.log('Language changed, updating SettingsPanel UI');
  }, [contextLanguage]);

  useEffect(() => {
    if (selectedMenuItem === 'token') {
      fetchTokenBalance();
    }
  }, [selectedMenuItem]);

  const fetchTokenBalance = async () => {
    try {
      const response = await userApi.getUserInfo();
      if (response.success) {
        setTokenBalance(response.data.quota);
        setTokenUsed(response.data.used_quota);
      }
    } catch (error) {
      console.error('Failed to fetch token balance:', error);
    }
  };

  const handleAppLanguageChange = (e: any) => {
    const newLanguage = e.target.value;
    onAppLanguageChange(newLanguage);
    
    // 触发自定义事件
    const event = new CustomEvent('appLanguageChanged', { detail: newLanguage });
    window.dispatchEvent(event);
  };

  const handleExchange = async () => {
    if (!exchangeAmount) {
      message.error(I18nUtils.getText('tokenExchangePlaceholder'));
      return;
    }

    try {
      const response = await tokenApi.exchangeToken(exchangeAmount);
      if (response.success) {
        message.success(I18nUtils.getText('tokenExchangeSuccess'));
        setExchangeAmount('');
        fetchTokenBalance(); // 刷新 token 余量
      } else {
        message.error(response.message || I18nUtils.getText('tokenExchangeFailed'));
      }
    } catch (error) {
        console.error('Failed to exchange token:', error);
      message.error(I18nUtils.getText('tokenExchangeFailed'));
    }
  };

  const renderContent = () => {
    switch (selectedMenuItem) {
      case 'language':
        return (
          <div className="settings-content">
            <h3>{I18nUtils.getText('languageSettings')}</h3>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('ideLanguage')}</label>
              <Radio.Group 
                value={language} 
                onChange={(e) => onLanguageChange(e.target.value)}
              >
                <Radio.Button value="zh">中文</Radio.Button>
                <Radio.Button value="en">English</Radio.Button>
              </Radio.Group>
            </div>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('appLanguage')}</label>
              <Radio.Group 
                value={appLanguage}
                onChange={handleAppLanguageChange}
              >
                <Radio.Button value="zh">中文</Radio.Button>
                <Radio.Button value="en">English</Radio.Button>
              </Radio.Group>
            </div>
          </div>
        );
      case 'model':
        return (
          <div className="settings-content">
            <h3>{I18nUtils.getText('modelSettings')}</h3>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('modelType')}</label>
              <Select
                value={modelSettings.modelType}
                onChange={(value) => onModelSettingsChange({ ...modelSettings, modelType: value })}
                style={{ width: '100%' }}
              >
                <Select.Option value="gpt-4" children="GPT-4" />
                <Select.Option value="gpt-4-turbo" children="GPT-4 Turbo" />
                <Select.Option value="claude-3-sonnet" children="Claude 3.5 Sonnet" />
              </Select>
            </div>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('apiKey')}</label>
              <Input.Password
                value={modelSettings.apiKey}
                onChange={(e) => onModelSettingsChange({ ...modelSettings, apiKey: e.target.value })}
              />
            </div>
            <div className="settings-save-button">
              <Button type="primary" onClick={onModelSettingsSave}>
                {I18nUtils.getText('saveSettings')}
              </Button>
            </div>
          </div>
        );
      case 'port':
        return (
          <div className="settings-content">
            <h3>{I18nUtils.getText('portSettings')}</h3>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('backendPort')}</label>
              <Input
                value={portSettings.flask_port}
                onChange={(e) => onPortChange(e, 'flask_port')}
                type="text"
                maxLength={5}
              />
            </div>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('screencastPort')}</label>
              <Input
                value={portSettings.scrcpy_port}
                onChange={(e) => onPortChange(e, 'scrcpy_port')}
                type="text"
                maxLength={5}
              />
            </div>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('taskPort')}</label>
              <Input
                value={portSettings.device_port}
                onChange={(e) => onPortChange(e, 'device_port')}
                type="text"
                maxLength={5}
              />
            </div>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('electronHttpPort')}</label>
              <Input
                value={portSettings.electron_http_port}
                onChange={(e) => onPortChange(e, 'electron_http_port')}
                type="text"
                maxLength={5}
              />
            </div>
            <div className="settings-save-button">
              <Button type="primary" onClick={onPortSettingsSave}>
                {I18nUtils.getText('saveSettings')}
              </Button>
            </div>
          </div>
        );
      case 'browser':
        return (
          <div className="settings-content">
            <h3>{I18nUtils.getText('browserSettings')}</h3>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('browserHomepage')}</label>
              <Input
                value={browserSettings.homepage}
                onChange={(e) => onBrowserHomepageChange(e.target.value)}
                placeholder={I18nUtils.getText('browserHomepagePlaceholder')}
                type="url"
              />
            </div>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('defaultSearchEngine')}</label>
              <Select
                value={browserSettings.searchEngine}
                onChange={(value) => onBrowserSearchEngineChange(value)}
                style={{ width: '100%' }}
              >
                <Select.Option value="google">{I18nUtils.getText('searchEngineGoogle')}</Select.Option>
                <Select.Option value="baidu">{I18nUtils.getText('searchEngineBaidu')}</Select.Option>
                <Select.Option value="bing">{I18nUtils.getText('searchEngineBing')}</Select.Option>
                <Select.Option value="sougou">{I18nUtils.getText('searchEngineSougou')}</Select.Option>
              </Select>
            </div>
            <div className="settings-save-button">
              <Button type="primary" onClick={onBrowserSettingsSave}>
                {I18nUtils.getText('saveSettings')}
              </Button>
            </div>
          </div>
        );
      case 'token':
        return (
          <div className="settings-content">
            <h3>{I18nUtils.getText('tokenBalance')}</h3>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('tokenUsed')}</label>
              <div className="token-display">
                {tokenUsed !== null ? tokenUsed.toString() : 'Loading...'}
              </div>
            </div>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('tokenRemaining')}</label>
              <div className="token-display">
                {tokenBalance !== null ? tokenBalance.toString() : 'Loading...'}
              </div>
            </div>
            <div className="settings-form-item">
              <label>{I18nUtils.getText('tokenExchange')}</label>
              <Input
                value={exchangeAmount}
                onChange={(e) => setExchangeAmount(e.target.value)}
                placeholder={I18nUtils.getText('tokenExchangePlaceholder')}
              />
            </div>
            <div className="settings-save-button">
              <Button type="primary" onClick={handleExchange}>
                {I18nUtils.getText('tokenExchangeButton')}
              </Button>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="settings-panel">
      <div className="settings-menu">
        <Menu
          selectedKeys={[selectedMenuItem]}
          mode="vertical"
          onClick={({ key }) => setSelectedMenuItem(key)}
          items={[
            { key: 'language', label: I18nUtils.getText('languageSettings') },
            // { key: 'model', label: I18nUtils.getText('modelSettings') },
            { key: 'port', label: I18nUtils.getText('portSettings') },
            { key: 'browser', label: I18nUtils.getText('browserSettings') },
            { key: 'token', label: I18nUtils.getText('tokenBalance') },
          ]}
        />
      </div>
      <div className="settings-content-container">
        {renderContent()}
      </div>
    </div>
  );
};

export default SettingsPanel;
