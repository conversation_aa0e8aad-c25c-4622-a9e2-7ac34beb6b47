import { Spin } from 'antd';
import { I18nUtils } from './languageSupport/i18nUtils';
import '../styles/RuyiClientGuide.css';
import { InfoCircleOutlined, RightOutlined, ExclamationCircleOutlined, SafetyOutlined, KeyOutlined, QuestionCircleOutlined } from '@ant-design/icons';

export const InstallationProgress = () => (
  <div className="installation-container">
    <Spin size="large" />
    <div style={{ marginTop: '24px' }}>
      <h3 className="installation-title">
        {I18nUtils.getText('ruyiClientInstallationTitle')}
      </h3>
      <p className="installation-text">
        {I18nUtils.getText('ruyiClientInstallationDesc')}
        <br />
        <span className="installation-required">
          {I18nUtils.getText('ruyiClientInstallationRequired')}
        </span>
      </p>
      <div className="installation-status">
        <p className="installation-status-text">
          {I18nUtils.getText('ruyiClientInstalling')}
        </p>
        <p className="installation-text" style={{ margin: 0 }}>
          {I18nUtils.getText('ruyiClientInstallConfirm')}
          <br />
          {I18nUtils.getText('ruyiClientInstallWait')}
        </p>
      </div>
    </div>
  </div>
);

export const ConfigurationGuide = () => {
  const configDesc = I18nUtils.getArrayText('ruyiClientConfigDesc');
  const overlaySteps = I18nUtils.getArrayText('ruyiClientOverlaySteps');
  const accessibilitySteps = I18nUtils.getArrayText('ruyiClientAccessibilitySteps');

  return (
    <div className="guide-container">
      <h3 className="guide-title">
        <InfoCircleOutlined style={{ marginRight: '10px', color: '#1890ff' }} />
        {I18nUtils.getText('ruyiClientConfigTitle')}
      </h3>
      
      <div className="guide-desc">
        <ul>
          {configDesc.map((item: string, index: number) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
      </div>

      <p className="guide-note">
        <ExclamationCircleOutlined style={{ marginRight: '8px' }} />
        {I18nUtils.getText('ruyiClientConfigNote')}
      </p>

      <div className="permissions-section">
        <h4 className="permissions-title">
          <SafetyOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
          {I18nUtils.getText('ruyiClientNormalPermissions')}
        </h4>
        <ul className="permissions-list">
          <li className="permission-item">
            <span className="permission-label">{I18nUtils.getText('ruyiClientPermissionMic')}</span>
            <span className="permission-arrow"><RightOutlined /></span>
            <span className="permission-action">{I18nUtils.getText('ruyiClientPermissionAllow')}</span>
          </li>
          <li className="permission-item">
            <span className="permission-label">{I18nUtils.getText('ruyiClientPermissionMedia')}</span>
            <span className="permission-arrow"><RightOutlined /></span>
            <span className="permission-action">{I18nUtils.getText('ruyiClientPermissionAllow')}</span>
          </li>
          <li className="permission-item">
            <span className="permission-label">{I18nUtils.getText('ruyiClientPermissionNotification')}</span>
            <span className="permission-arrow"><RightOutlined /></span>
            <span className="permission-action">{I18nUtils.getText('ruyiClientPermissionAllow')}</span>
          </li>
          <li className="permission-item">
            <span className="permission-label">{I18nUtils.getText('ruyiClientPermissionScreen')}</span>
            <span className="permission-arrow"><RightOutlined /></span>
            <span className="permission-action">{I18nUtils.getText('ruyiClientPermissionAllow')}</span>
          </li>
        </ul>
      </div>

      <div className="permissions-section">
        <h4 className="permissions-title">
          <SafetyOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
          {I18nUtils.getText('ruyiClientSpecialPermissions')}
        </h4>
        <div className="special-permissions">
          <p className="special-permission-title">
            {I18nUtils.getText('ruyiClientOverlayTitle')}
          </p>
          <ol className="special-permission-steps">
            {overlaySteps.map((step: string, index: number) => (
              <li key={index}>{step}</li>
            ))}
          </ol>

          <p className="special-permission-title">
            {I18nUtils.getText('ruyiClientAccessibilityTitle')}
          </p>
          <ol className="special-permission-steps">
            {accessibilitySteps.map((step: string, index: number) => (
              <li key={index}>{step}</li>
            ))}
          </ol>
        </div>
      </div>
    </div>
  );
};

export const DeviceConnectionGuide = () => {
  return (
    <div className="device-connection-guide-tooltip">
      <div className="guide-title">
        <QuestionCircleOutlined className="guide-icon" />
        {I18nUtils.getText('deviceConnectionGuideTitle')}
      </div>
      <div className="guide-content">
        {/* 基本步骤 */}
        <div className="guide-section">
          <div className="guide-section-content">
            <div className="guide-intro">
              {I18nUtils.getText('deviceConnectionGuideDesc')}
            </div>
            <ul className="guide-steps numbered-list">
              {I18nUtils.getArrayText('deviceConnectionGuideSteps').map((step: string, index: number) => (
                <li key={index}><strong>{step}</strong></li>
              ))}
            </ul>
            <div className="guide-note">
              {I18nUtils.getText('deviceConnectionGuideNote')}
            </div>
          </div>
        </div>
        {/* 连接到电脑 */}
        <div className="guide-section">
          <div className="guide-section-title">
            {I18nUtils.getText('connectToComputerTitle')}
          </div>
          <div className="guide-section-content">
            <div className="guide-intro">
              {I18nUtils.getText('connectToComputerDesc')}
            </div>
            <ul className="guide-steps numbered-list">
              {I18nUtils.getArrayText('connectToComputerSteps').map((step: string, index: number) => (
                <li key={index}>{step}</li>
              ))}
            </ul>
            <div className="guide-note">
              {I18nUtils.getText('connectToComputerNote')}
            </div>
            {/* 开发者模式子部分 */}
            <div className="guide-sub-section">
              <div className="guide-sub-section-title">
                {I18nUtils.getText('enableDevModeTitle')}
              </div>
              <ul className="guide-steps">
                {I18nUtils.getArrayText('enableDevModeSteps').map((step: string, index: number) => (
                  <li key={index}>
                    <strong>{step}</strong>
                    {index === 1 && (
                      <div className="guide-sub-steps">
                        {I18nUtils.getObjectText('enableDevModeSubSteps').aboutPhone}
                      </div>
                    )}
                    {index === 2 && (
                      <div className="guide-sub-steps">
                        {I18nUtils.getObjectText('enableDevModeSubSteps').versionNumber}
                      </div>
                    )}
                    {index === 3 && (
                      <div className="guide-sub-steps">
                        {I18nUtils.getObjectText('enableDevModeSubSteps').activate}
                      </div>
                    )}
                  </li>
                ))}
              </ul>
            </div>
            {/* USB 调试子部分 */}
            <div className="guide-sub-section">
              <div className="guide-sub-section-title">
                {I18nUtils.getText('enableUsbDebugTitle')}
              </div>
              <ul className="guide-steps">
                {I18nUtils.getArrayText('enableUsbDebugSteps').map((step: string, index: number) => (
                  <li key={index}>
                    <strong>{step}</strong>
                    {index === 1 && (
                      <div className="guide-sub-steps">
                        {I18nUtils.getObjectText('enableUsbDebugSubSteps').findSwitch}
                        {I18nUtils.getObjectText('enableUsbDebugSubSteps').confirm}
                        {I18nUtils.getObjectText('enableUsbDebugSubSteps').allow}
                      </div>
                    )}
                  </li>
                ))}
              </ul>
            </div>
            {/* 连线并允许调试子部分 */}
            <div className="guide-sub-section">
              <div className="guide-sub-section-title">
                {I18nUtils.getText('connectAndAllowTitle')}
              </div>
              <ul className="guide-steps">
                {I18nUtils.getArrayText('connectAndAllowSteps').map((step: string, index: number) => (
                  <li key={index}>{step}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
        {/* 连接到 Ruyi IDE */}
        <div className="guide-section">
          <div className="guide-section-title">
            {I18nUtils.getText('connectToRuyiTitle')}
          </div>
          <div className="guide-section-content">
            <ul className="guide-steps">
              {I18nUtils.getArrayText('connectToRuyiDesc').map((desc: string, index: number) => (
                <li key={index}>{desc}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
