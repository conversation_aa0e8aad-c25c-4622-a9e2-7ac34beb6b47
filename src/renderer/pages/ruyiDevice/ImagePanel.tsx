import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON>, message } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { I18nUtils } from './languageSupport/i18nUtils';
import { useBrowserViewControl } from '../../hooks/useBrowserViewControl';

interface ImagePanelProps {
  visible: boolean;
  onClose: () => void;
  imageData: { metadata: any; image_data: string } | null;
}

export const ImagePanel: React.FC<ImagePanelProps> = ({ visible, onClose, imageData }) => {
  useBrowserViewControl(visible);
  const [drawerWidth, setDrawerWidth] = useState(400);

  useEffect(() => {
    if (visible && imageData && imageData.image_data) {
      const img = new Image();
      img.onload = () => {
        const naturalWidth = img.naturalWidth;
        const naturalHeight = img.naturalHeight;

        // Drawer header height is ~55px, body padding top/bottom is 24px each.
        const availableHeight = window.innerHeight - 55 - 48;
        
        // Calculate the ideal width that would make the image height fit the available space
        const idealImageWidth = (availableHeight * naturalWidth) / naturalHeight;

        // Drawer body has 24px padding on left and right
        const newDrawerWidth = idealImageWidth + 48;
        
        const minWidth = 400;
        const maxWidth = window.innerWidth * 0.9;
        
        setDrawerWidth(Math.max(minWidth, Math.min(newDrawerWidth, maxWidth)));
      };
      img.src = imageData.image_data;
    }
  }, [visible, imageData]);

  const handleDownload = async () => {
    if (!imageData) return;

    try {
      const link = document.createElement('a');
      link.href = imageData.image_data;
      link.download = imageData.metadata?.name || 'image';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success(I18nUtils.getText('downloadSuccess'));
    } catch (error) {
      message.error(I18nUtils.getText('downloadFailed'));
    }
  };

  if (!imageData) {
    return null;
  }

  const imageName = imageData.metadata?.name || I18nUtils.getText('receivedImage');

  return (
    <Drawer
      title={imageName}
      placement="left"
      onClose={onClose}
      open={visible}
      width={drawerWidth}
      maskClosable={false}
      extra={
        <Button
          icon={<DownloadOutlined />}
          onClick={handleDownload}
        >
          {I18nUtils.getText('download')}
        </Button>
      }
    >
      <img src={imageData.image_data} alt={imageName} style={{ maxWidth: '100%' }} />
    </Drawer>
  );
}; 