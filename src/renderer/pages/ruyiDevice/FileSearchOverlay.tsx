import React, { useState, useEffect, useRef } from 'react';
import { List, Spin } from 'antd';
import { File, Folder, FileCode, Database, Book, Search } from 'lucide-react';
import { I18nUtils } from './languageSupport/i18nUtils';
import { ChatInterface } from './ChatInterface';
// Browser view control is now handled in NavBar component
import '../styles/FileSearchOverlay.css';

interface SearchResult {
  name: string;
  displayName: string; // User-friendly display name
  path: string;
  type: 'file' | 'folder';
  fileType?: 'knowledge' | 'task' | 'data';
  matches: SearchMatch[];
  content?: string; // For task files to extract task names
  score: number; // For sorting results by relevance
}

interface SearchMatch {
  type: 'filename' | 'displayName' | 'content' | 'metadata';
  field?: string; // Specific field that matched (e.g., 'NL_script', 'task_description')
  snippet?: string; // Preview of the matched content
}

interface FileSearchOverlayProps {
  visible: boolean;
  searchQuery: string;
  onClose: () => void;
  onFileSelect: (filePath: string) => void;
  position: { top: number; left: number; width: number };
}

export const FileSearchOverlay: React.FC<FileSearchOverlayProps> = ({
  visible,
  searchQuery,
  onClose,
  onFileSelect,
  position
}) => {
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const overlayRef = useRef<HTMLDivElement>(null);

  // Browser view control is now handled in NavBar component using focus-aware approach
  // This prevents conflicts between native BrowserView and web-based overlay

  // Get file icon based on type
  const getFileIcon = (type: string, fileType?: string) => {
    if (type === 'folder') return <Folder size={16} color="#6366f1" />;
    if (fileType === 'knowledge') return <Book size={16} color="#10b981" />;
    if (fileType === 'task') return <FileCode size={16} color="#f59e0b" />;
    if (fileType === 'data') return <Database size={16} color="#8b5cf6" />;
    return <File size={16} color="#6b7280" />;
  };

  // Load file contents for searching and display names
  const loadFileContents = async (files: any[], projectName: string): Promise<any[]> => {
    const filesWithContent = [...files];
    
    for (let i = 0; i < filesWithContent.length; i++) {
      const file = filesWithContent[i];
      
      // Load content for all files (not just task files) to enable content searching
      if (!file.isDirectory) {
        try {
          const result = await window.electronAPI.readTaskFile({
            taskName: projectName,
            fileName: file.name
          });
          
          if (result.success && result.content !== undefined) {
            filesWithContent[i] = {
              ...file,
              content: result.content
            };
          }
        } catch (error) {
          console.warn(`Failed to load content for file ${file.name}:`, error);
        }
      }
      
      // Recursively process child files
      if (file.children && file.children.length > 0) {
        filesWithContent[i] = {
          ...file,
          children: await loadFileContents(file.children, projectName)
        };
      }
    }
    
    return filesWithContent;
  };

  // Perform file search with three search types
  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    const projectName = ChatInterface.projectName;
    if (!projectName) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    try {
      // Get file tree from the existing API
      const result = await window.electronAPI.getTaskFileTree(projectName);
      if (result.success && result.tree) {
        // Load all file contents for comprehensive searching
        const filesWithContent = await loadFileContents(result.tree, projectName);
        const results = searchInFileTree(filesWithContent, query.toLowerCase());
        setSearchResults(results);
        setSelectedIndex(0);
      }
    } catch (error) {
      console.error('Search failed:', error);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Get user-friendly display name for files (same logic as TaskFilesList)
  const getFileDisplayName = (fileName: string, fileType?: string, fileContent?: string): string => {
    if (fileName === 'Knowledge.json') {
      return I18nUtils.getText('knowledgeFileDisplayName');
    }
    
    // If it's a task file with content, try to parse and show task name
    if (fileType === 'task' && fileContent) {
      try {
        const taskData = JSON.parse(fileContent);
        if (taskData.task_name) {
          return taskData.task_name;
        }
      } catch (error) {
        console.warn('Failed to parse task file for display name:', error);
      }
    }
    
    // Default behavior: remove file extension
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1 || lastDotIndex === 0) {
      return fileName;
    }
    return fileName.substring(0, lastDotIndex);
  };

  // Create content snippet for display
  const createSnippet = (content: string, query: string, maxLength: number = 100): string => {
    const lowerContent = content.toLowerCase();
    const lowerQuery = query.toLowerCase();
    const index = lowerContent.indexOf(lowerQuery);
    
    if (index === -1) return '';
    
    const start = Math.max(0, index - 20);
    const end = Math.min(content.length, index + query.length + 20);
    let snippet = content.substring(start, end);
    
    if (start > 0) snippet = '...' + snippet;
    if (end < content.length) snippet = snippet + '...';
    
    return snippet;
  };

  // Search through file tree with three search types
  const searchInFileTree = (files: any[], query: string): SearchResult[] => {
    const results: SearchResult[] = [];

    const searchRecursive = (fileList: any[], currentPath = '') => {
      fileList.forEach(file => {
        const fullPath = currentPath ? `${currentPath}/${file.name}` : file.name;
        const fileName = file.name.toLowerCase();
        const displayName = getFileDisplayName(file.name, file.fileType, file.content);
        const displayNameLower = displayName.toLowerCase();
        
        const matches: SearchMatch[] = [];
        let score = 0;

        // 1. File name searching (filename and localized display name)
        // For task files, only search the display name (task name), not the real filename
        if (file.fileType !== 'task' && fileName.includes(query)) {
          matches.push({ type: 'filename' });
          score += fileName.startsWith(query) ? 100 : 50; // Boost for prefix matches
        }
        
        // Search display name for all files (but avoid duplicate matches)
        if (displayNameLower.includes(query)) {
          // For task files, always add displayName match
          // For other files, only add if different from filename to avoid duplicates
          if (file.fileType === 'task' || displayNameLower !== fileName) {
            matches.push({ type: 'displayName' });
            score += displayNameLower.startsWith(query) ? 100 : 50;
          }
        }

        // 2. File content searching (only for files, not folders)
        if (!file.isDirectory && file.content) {
          if (file.fileType === 'knowledge') {
            // For Knowledge.json, search in the knowledge field
            try {
              const knowledgeData = JSON.parse(file.content);
              if (knowledgeData.knowledge && knowledgeData.knowledge.toLowerCase().includes(query)) {
                const snippet = createSnippet(knowledgeData.knowledge, query);
                matches.push({ 
                  type: 'content', 
                  field: 'knowledge',
                  snippet: snippet
                });
                score += 30;
              }
            } catch (error) {
              // If JSON parsing fails, search in raw content
              if (file.content.toLowerCase().includes(query)) {
                const snippet = createSnippet(file.content, query);
                matches.push({ 
                  type: 'content', 
                  field: 'raw',
                  snippet: snippet
                });
                score += 20;
              }
            }
          } else if (file.fileType === 'task') {
            // For task files, search in NL_script and code_script
            try {
              const taskData = JSON.parse(file.content);
              
              if (taskData.NL_script && taskData.NL_script.toLowerCase().includes(query)) {
                const snippet = createSnippet(taskData.NL_script, query);
                matches.push({ 
                  type: 'content', 
                  field: 'NL_script',
                  snippet: snippet
                });
                score += 30;
              }
              
              if (taskData.code_script && taskData.code_script.toLowerCase().includes(query)) {
                const snippet = createSnippet(taskData.code_script, query);
                matches.push({ 
                  type: 'content', 
                  field: 'code_script',
                  snippet: snippet
                });
                score += 30;
              }
            } catch (error) {
              // If JSON parsing fails, search in raw content
              if (file.content.toLowerCase().includes(query)) {
                const snippet = createSnippet(file.content, query);
                matches.push({ 
                  type: 'content', 
                  field: 'raw',
                  snippet: snippet
                });
                score += 20;
              }
            }
          } else {
            // For other files, search in raw content
            if (file.content.toLowerCase().includes(query)) {
              const snippet = createSnippet(file.content, query);
              matches.push({ 
                type: 'content', 
                field: 'raw',
                snippet: snippet
              });
              score += 20;
            }
          }
        }

        // 3. File metadata searching (only for task files)
        if (file.fileType === 'task' && file.content) {
          try {
            const taskData = JSON.parse(file.content);
            if (taskData.task_description && taskData.task_description.toLowerCase().includes(query)) {
              const snippet = createSnippet(taskData.task_description, query);
              matches.push({ 
                type: 'metadata', 
                field: 'task_description',
                snippet: snippet
              });
              score += 25;
            }
          } catch (error) {
            // Ignore JSON parsing errors for metadata search
          }
        }
        
        if (matches.length > 0) {
          results.push({
            name: file.name,
            displayName: displayName,
            path: fullPath,
            type: file.isDirectory ? 'folder' : 'file',
            fileType: file.fileType,
            content: file.content,
            matches: matches,
            score: score
          });
        }

        // Search in children if it's a directory
        if (file.children && file.children.length > 0) {
          searchRecursive(file.children, fullPath);
        }
      });
    };

    searchRecursive(files);
    
    // Sort results by score (higher is better) and then alphabetically
    return results.sort((a, b) => {
      if (b.score !== a.score) {
        return b.score - a.score;
      }
      return a.displayName.localeCompare(b.displayName);
    });
  };

  // Simple fuzzy matching (kept for backward compatibility but not used in main search)
  const fuzzyMatch = (text: string, query: string): boolean => {
    let queryIndex = 0;
    for (let i = 0; i < text.length && queryIndex < query.length; i++) {
      if (text[i] === query[queryIndex]) {
        queryIndex++;
      }
    }
    return queryIndex === query.length;
  };

  // Highlight matching text
  const highlightMatch = (text: string, query: string) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} className="search-highlight">{part}</span>
      ) : (
        part
      )
    );
  };

  // Get display path for search results
  const getDisplayPath = (result: SearchResult): string => {
    // For root level files, just show the display name
    if (!result.path.includes('/')) {
      return result.displayName;
    }
    
    // For nested files, show the path but replace the filename with display name
    const pathParts = result.path.split('/');
    pathParts[pathParts.length - 1] = result.displayName;
    return pathParts.join('/');
  };

  // Get match type description for display
  const getMatchDescription = (matches: SearchMatch[]): string => {
    const descriptions: string[] = [];
    
    matches.forEach(match => {
      switch (match.type) {
        case 'filename':
          descriptions.push(I18nUtils.getText('searchMatchFilename'));
          break;
        case 'displayName':
          descriptions.push(I18nUtils.getText('searchMatchDisplayName'));
          break;
        case 'content':
          if (match.field === 'knowledge') {
            descriptions.push(I18nUtils.getText('searchMatchKnowledge'));
          } else if (match.field === 'NL_script') {
            descriptions.push(I18nUtils.getText('searchMatchNLScript'));
          } else if (match.field === 'code_script') {
            descriptions.push(I18nUtils.getText('searchMatchCodeScript'));
          } else {
            descriptions.push(I18nUtils.getText('searchMatchFileContent'));
          }
          break;
        case 'metadata':
          if (match.field === 'task_description') {
            descriptions.push(I18nUtils.getText('searchMatchTaskDescription'));
          }
          break;
      }
    });
    
    return descriptions.join(', ');
  };

  // Get content snippet to display
  const getContentSnippet = (matches: SearchMatch[]): string => {
    // Find the first match with a snippet
    const matchWithSnippet = matches.find(match => match.snippet);
    return matchWithSnippet?.snippet || '';
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!visible) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < searchResults.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : prev);
          break;
        case 'Enter':
          e.preventDefault();
          if (searchResults[selectedIndex]) {
            onFileSelect(searchResults[selectedIndex].path);
            onClose();
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [visible, searchResults, selectedIndex, onFileSelect, onClose]);

  // Perform search when query changes
  useEffect(() => {
    if (visible) {
      performSearch(searchQuery);
    }
  }, [searchQuery, visible]);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (overlayRef.current && !overlayRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [visible, onClose]);

  if (!visible) return null;

  return (
    <div
      ref={overlayRef}
      className="file-search-overlay"
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        width: position.width,
        zIndex: 1500  // High z-index to ensure visibility above all other components including modals
      }}
    >
      <div className="search-overlay-content">
        {loading ? (
          <div className="search-loading">
            <Spin size="small" />
            <span>{I18nUtils.getText('loading')}</span>
          </div>
        ) : searchResults.length > 0 ? (
          <div className="search-results">
            {searchResults.map((result, index) => (
              <div
                key={result.path}
                className={`search-result-item ${index === selectedIndex ? 'selected' : ''}`}
                onClick={() => {
                  onFileSelect(result.path);
                  onClose();
                }}
              >
                <div className="search-result-icon">
                  {getFileIcon(result.type, result.fileType)}
                </div>
                <div className="search-result-content">
                  <div className="search-result-name">
                    {highlightMatch(result.displayName, searchQuery)}
                  </div>
                  <div className="search-result-path">
                    {getDisplayPath(result)}
                  </div>
                  <div className="search-result-matches">
                    {I18nUtils.getText('searchMatchedIn')}: {getMatchDescription(result.matches)}
                  </div>
                  {getContentSnippet(result.matches) && (
                    <div className="search-result-snippet">
                      {highlightMatch(getContentSnippet(result.matches), searchQuery)}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : searchQuery.trim() ? (
          <div className="search-no-results">
            <Search size={16} />
            <span>{I18nUtils.getText('searchNoResults')}</span>
          </div>
        ) : (
          <div className="search-placeholder">
            <Search size={16} />
            <span>{I18nUtils.getText('searchTypeToSearchFiles')}</span>
          </div>
        )}
      </div>
    </div>
  );
}; 