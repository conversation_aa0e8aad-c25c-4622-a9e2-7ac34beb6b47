import React, { useEffect, useState, useRef } from 'react';
import { List, Card, Typography, Spin, Empty, message, Modal, Button, Tooltip, Drawer, Row, Col, Popconfirm } from 'antd';
import { Smartphone, Clock, XCircle, Rocket, HelpCircle, Plus, Globe, Cloud, Monitor, Trash2 } from 'lucide-react';
import { useLanguage } from './languageSupport/LanguageContext';
import { systemApi } from '../../../services/api';
import { DeviceConnectionGuide } from './RuyiClientGuide';
import { useBrowserViewControl } from '../../../renderer/hooks/useBrowserViewControl';
import { useDevices } from './DeviceContext';
import { flaskApi } from '../../../services/FlaskApiService';
import PhysicalDeviceScanner from './PhysicalDeviceScanner';
import '../styles/Select.css';
import { I18nUtils } from './languageSupport/i18nUtils';

const { Title, Paragraph } = Typography;

// 设备类型定义
export type PhysicalDevice = {
  id: string;
  brand: string;
  model: string;
  product: string;
  sdk: string | null;
  sdkStatus: string | null;
  isSdkUnavailable: boolean;
  status: string;
  description?: string;
  deviceNumber?: string;
  ruyiClientStatus?: 'checking' | 'ok' | 'updating' | 'error';
  ruyiClientMessage?: string;
};

export type VirtualDevice = {
  id: string;
  brand: string;
  model: string;
  product: string;
  status: string;
  description: string;
  sdk?: string | null;
  sdkStatus?: string | null;
  isSdkUnavailable?: boolean;
  deviceNumber?: string;
};

export type BrowserDevice = {
  id: string;
  brand: string;
  model: string;
  product: string;
  status: string;
  type: 'browser';
  url: string;
  description?: string;
  deviceNumber?: string;
};

export type Device = PhysicalDevice | VirtualDevice | BrowserDevice;

interface SelectProps {
  onDeviceSelect: (device: Device) => void;
  manualDevices: Device[];
  setManualDevices: React.Dispatch<React.SetStateAction<Device[]>>;
}

const Select: React.FC<SelectProps> = ({ onDeviceSelect, manualDevices, setManualDevices }) => {
  const { language } = useLanguage();
  const [devices, setDevices] = useState<Device[]>([]);
  const { setDevices: setContextDevices } = useDevices();  // 获取 Context 中的 setDevices
  const [loading, setLoading] = useState<boolean>(true);
  const [countdown, setCountdown] = useState<number | null>(null);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [currentDevice, setCurrentDevice] = useState<Device | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const redirectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const previousBrowserDeviceCount = useRef<number>(0);
  const previousCloudPhoneCount = useRef<number>(0);
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [isAddDeviceModalVisible, setIsAddDeviceModalVisible] = useState<boolean>(false);
  const [isPhysicalDeviceScannerVisible, setIsPhysicalDeviceScannerVisible] = useState<boolean>(false);
  const [browserDeviceCount, setBrowserDeviceCount] = useState<number>(0);
  const [cloudPhoneCount, setCloudPhoneCount] = useState<number>(0);
  const [browserHomepage, setBrowserHomepage] = useState<string>('https://www.baidu.com');

  // 用于记录已检查过的设备，避免重复检查
  const [checkedDevices, setCheckedDevices] = useState<Set<string>>(new Set());
  
  // 用于记录已删除的设备ID，避免被重新添加
  const [deletedDeviceIds, setDeletedDeviceIds] = useState<Set<string>>(new Set());
  
  // 监控deletedDeviceIds的变化，用于调试
  useEffect(() => {
    console.log('deletedDeviceIds更新:', Array.from(deletedDeviceIds));
  }, [deletedDeviceIds]);

  // Add the hook to control browser view visibility
  useBrowserViewControl(isModalVisible || drawerVisible);

  const getDeviceIcon = (device: Device) => {
    const style = { width: 24, height: 24 };
    if ('type' in device && device.type === 'browser') {
      return <Globe style={style} />;
    }
    if (device.id.startsWith('cloud-phone-')) {
      return <Cloud style={style} />;
    }
    return <Smartphone style={style} />;
  };

  // 生成设备号码的辅助函数
  const generateDeviceNumber = (devices: Device[], deviceType: 'phone' | 'cloud' | 'batch' | 'browser', index: number): string => {
    switch (deviceType) {
      case 'phone':
        return `phone${index + 1}`;
      case 'cloud':
        return `cloud${index + 1}`;
      case 'batch':
        return `batch${index + 1}`;
      case 'browser':
        return `browser${index + 1}`;
      default:
        return `device${index + 1}`;
    }
  };

  // 检查并更新 Ruyi Client
  const checkAndUpdateRuyiClient = async (deviceId: string) => {
    // 如果已经检查过这个设备，跳过
    if (checkedDevices.has(deviceId)) {
      return;
    }

    try {
      console.log(`开始检查设备 ${deviceId} 的 Ruyi Client 版本`);
      
      // 更新设备状态为检查中
      setDevices(prevDevices => 
        prevDevices.map(device => {
          if (device.id === deviceId && 'ruyiClientStatus' in device) {
            return {
              ...device,
              ruyiClientStatus: 'checking' as const,
              ruyiClientMessage: I18nUtils.getText('checkingRuyiClient')
            };
          }
          return device;
        })
      );

      // 调用后端API检查并更新
      const result = await flaskApi.checkAndUpdateRuyiClient(deviceId);
      
      // 打印当前安装的 Ruyi Client 版本信息
      console.log(`设备 ${deviceId} 当前安装的 Ruyi Client 版本: ${result.installed_version || '未获取到版本信息'}`);
      console.log(`设备 ${deviceId} 目标 Ruyi Client 版本: ${result.target_version || '未获取到目标版本'}`);
      console.log(`设备 ${deviceId} 是否需要更新: ${result.needs_update ? '是' : '否'}`);
      
      // 记录已检查的设备
      setCheckedDevices(prev => new Set(prev).add(deviceId));
      
      // 更新设备状态
      setDevices(prevDevices => 
        prevDevices.map(device => {
          if (device.id === deviceId && 'ruyiClientStatus' in device) {
            let statusMessage = result.message;
            
            // 如果权限授予成功，添加权限状态信息
            if (result.permissions_granted) {
              statusMessage += ' - ' + I18nUtils.getText('permissionsGranted');
            } else if (result.permissions_granted === false) {
              statusMessage += ' - ' + I18nUtils.getText('permissionsGrantFailed');
            }
            
            return {
              ...device,
              ruyiClientStatus: result.update_success ? 'ok' as const : 'error' as const,
              ruyiClientMessage: statusMessage
            };
          }
          return device;
        })
      );

      if (result.needs_update && result.update_success) {
        console.log(`设备 ${deviceId} Ruyi Client 更新成功: ${result.message}`);
      } else if (result.needs_update && !result.update_success) {
        console.error(`设备 ${deviceId} Ruyi Client 更新失败: ${result.message}`);
      } else {
        console.log(`设备 ${deviceId} Ruyi Client 版本正常: ${result.message}`);
      }
      
      // 权限授予结果日志
      if (result.permissions_granted !== undefined) {
        if (result.permissions_granted) {
          console.log(`设备 ${deviceId} 权限授予成功`);
        } else {
          console.warn(`设备 ${deviceId} 权限授予失败或部分失败`);
        }
      }
    } catch (error) {
      console.error(`设备 ${deviceId} Ruyi Client 检查失败:`, error);
      
      // 更新设备状态为错误
      setDevices(prevDevices => 
        prevDevices.map(device => {
          if (device.id === deviceId && 'ruyiClientStatus' in device) {
            return {
              ...device,
              ruyiClientStatus: 'error' as const,
              ruyiClientMessage: I18nUtils.getText('ruyiClientCheckFailed')
            };
          }
          return device;
        })
      );
    }
  };

  const loadDeviceList = async () => {
    try {
      // 添加虚拟设备
      const virtualDevices: VirtualDevice[] = Array.from({ length: cloudPhoneCount }).map((_, index) => ({
        id: `cloud-phone-${String(index + 1).padStart(3, '0')}`,
        brand: 'Ruyi',
        model: I18nUtils.getText('cloudPhone'),
        product: 'RuyiCloud',
        status: I18nUtils.getText('disconnected'),
        description: I18nUtils.getText('cloudPhoneDescription'),
        deviceNumber: generateDeviceNumber([], 'cloud', index)
      }));

      // 添加浏览器设备
      const browserDevices: BrowserDevice[] = Array.from({ length: browserDeviceCount }).map((_, index) => ({
        id: `browser-${String(index + 1).padStart(3, '0')}`,
        brand: 'Ruyi',
        model: 'Web Browser',
        product: 'Browser',
        status: I18nUtils.getText('connected'),
        type: 'browser',
        url: browserHomepage,
        description: I18nUtils.getText('browserDeviceDescription'),
        deviceNumber: generateDeviceNumber([], 'browser', index)
      }));

      // 组合所有设备：手动添加的物理设备 + 浏览器设备 + 虚拟设备
      const allDevices = [...manualDevices, ...browserDevices, ...virtualDevices];
      
      // 过滤掉已删除的设备
      const filteredDevices = allDevices.filter(device => !deletedDeviceIds.has(device.id));
      
      setDevices(filteredDevices);
      setContextDevices(filteredDevices);
    } catch (error) {
      console.error('Failed to load device list:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDeviceList();
  }, [manualDevices, browserDeviceCount, cloudPhoneCount, deletedDeviceIds, browserHomepage]);
  
  // 当设备数量从0变为1时，确保清除第一个设备的删除标记
  useEffect(() => {
    // 处理浏览器设备
    if (browserDeviceCount === 1 && previousBrowserDeviceCount.current === 0) {
      const firstDeviceId = 'browser-001';
      console.log('浏览器设备数量从0变为1，清除已删除ID:', firstDeviceId);
      setDeletedDeviceIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(firstDeviceId);
        return newSet;
      });
    }
    previousBrowserDeviceCount.current = browserDeviceCount;
    
    // 处理云手机设备
    if (cloudPhoneCount === 1 && previousCloudPhoneCount.current === 0) {
      const firstDeviceId = 'cloud-phone-001';
      console.log('云手机设备数量从0变为1，清除已删除ID:', firstDeviceId);
      setDeletedDeviceIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(firstDeviceId);
        return newSet;
      });
    }
    previousCloudPhoneCount.current = cloudPhoneCount;
  }, [browserDeviceCount, cloudPhoneCount]);

  // 在组件挂载时读取设备数量配置
  useEffect(() => {
    const loadDeviceCountConfig = async () => {
      try {
        const deviceCounts = await window.electronAPI.getDeviceCountConfig();
        setBrowserDeviceCount(deviceCounts.browserDeviceCount ?? 1);
        setCloudPhoneCount(deviceCounts.cloudPhoneCount ?? 1);
        
        // 在应用启动时，清除所有可能的浏览器和云手机设备ID的删除标记
        // 这确保了应用重启后不会因为之前的删除记录而导致设备不显示
        setDeletedDeviceIds(new Set());
        console.log('应用启动，重置deletedDeviceIds');
      } catch (error) {
        console.error('Failed to load device count config:', error);
        setBrowserDeviceCount(1);
        setCloudPhoneCount(1);
      }
    };

    const loadBrowserHomepageConfig = async () => {
      try {
        const homepage = await window.electronAPI.getBrowserHomepageConfig();
        setBrowserHomepage(homepage || 'https://www.baidu.com');
      } catch (error) {
        console.warn('Failed to get browser homepage config, using default:', error);
        setBrowserHomepage('https://www.baidu.com');
      }
    };
    
    loadDeviceCountConfig();
    loadBrowserHomepageConfig();
  }, []);

  // 在 useEffect 中监听 language 变化
  useEffect(() => {
    // 更新设备选择界面等需要翻译的内容
    console.log('Language changed, updating Select UI');
  }, [language]);

  const handleVirtualDeviceClick = async (device: Device) => {
    try {
      // 记录虚拟设备点击
      await systemApi.recordVirtualDeviceClick();
      
      // 设置当前设备并显示通知面板
      setCurrentDevice(device);
      setIsModalVisible(true);
      
      // 开始3秒倒计时
      setCountdown(3);
      timerRef.current = setInterval(() => {
        setCountdown(prev => (prev !== null ? prev - 1 : 0));
      }, 1000);

      // 3秒后跳转
      redirectTimeoutRef.current = setTimeout(() => {
        handleRedirect();
      }, 3000);
    } catch (error) {
      message.error(I18nUtils.getText('virtualDeviceUnsupported'));
    }
  };

  const handleCancel = () => {
    // 清除定时器
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (redirectTimeoutRef.current) {
      clearTimeout(redirectTimeoutRef.current);
      redirectTimeoutRef.current = null;
    }
    
    // 重置状态
    setCountdown(null);
    setIsModalVisible(false);
    setCurrentDevice(null);
  };

  const handleRedirect = () => {
    handleCancel();
    // @ts-ignore
    (window.electronAPI as any).openExternalUrl('https://wisewk.com/contacts');
  };

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
      }
    };
  }, []);

  const getDeviceTitle = (deviceId: string) => {
    if (deviceId.startsWith('cloud-phone-')) {
      return I18nUtils.getText('ruyiCloudPhone');
    }
    return I18nUtils.getText('ruyiBatchTestDevice');
  };

  const handleDeviceClick = (device: Device) => {
    if (device.id.startsWith('cloud-phone-') || device.id.startsWith('batch-test-')) {
      handleVirtualDeviceClick(device);
      return;
    }
    if ('isSdkUnavailable' in device && device.isSdkUnavailable) {
      Modal.error({
        title: I18nUtils.getText('status'),
        content: 'sdkStatus' in device ? device.sdkStatus : '',
      });
      return;
    }
    // 检查 Ruyi Client 状态 - 移除检查中和更新中的提示，让用户可以正常选择设备
    if ('ruyiClientStatus' in device && device.ruyiClientStatus === 'error') {
      message.error(device.ruyiClientMessage || I18nUtils.getText('ruyiClientError'));
      return;
    }
    // 如果是浏览器设备，直接选择
    if ('type' in device && device.type === 'browser') {
      onDeviceSelect(device);
      return;
    }
    onDeviceSelect(device);
  };

  const handleAddDevice = async (type: 'browser' | 'cloud') => {
    try {
      let newBrowserCount = browserDeviceCount;
      let newCloudPhoneCount = cloudPhoneCount;
      
      if (type === 'browser') {
        newBrowserCount = browserDeviceCount + 1;
        setBrowserDeviceCount(newBrowserCount);
        
        // 清除可能存在的已删除浏览器设备ID，确保新添加的设备能够显示
        // 计算新设备的ID，与loadDeviceList中的逻辑保持一致
        const newDeviceId = `browser-${String(browserDeviceCount + 1).padStart(3, '0')}`;
        console.log('添加浏览器设备，清除已删除ID:', newDeviceId);
        setDeletedDeviceIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(newDeviceId);
          return newSet;
        });
      } else {
        newCloudPhoneCount = cloudPhoneCount + 1;
        setCloudPhoneCount(newCloudPhoneCount);
        
        // 清除可能存在的已删除云手机设备ID
        const newDeviceId = `cloud-phone-${String(cloudPhoneCount + 1).padStart(3, '0')}`;
        console.log('添加云手机设备，清除已删除ID:', newDeviceId);
        setDeletedDeviceIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(newDeviceId);
          return newSet;
        });
      }
      
      // 保存设备数量配置到配置文件
      try {
        await window.electronAPI.setDeviceCountConfig({
          browserDeviceCount: newBrowserCount,
          cloudPhoneCount: newCloudPhoneCount
        });
      } catch (error) {
        console.error('Failed to save device count config:', error);
      }
      
      message.success(I18nUtils.getText('deviceAdded'));
    } catch (error) {
      message.error(I18nUtils.getText('deviceAddFailed'));
    }
    setIsAddDeviceModalVisible(false);
  };

  // 处理物理设备添加
  const handleAddPhysicalDevice = () => {
    setIsAddDeviceModalVisible(false);
    setIsPhysicalDeviceScannerVisible(true);
  };

  // 处理物理设备选择完成
  const handlePhysicalDevicesSelected = async (selectedDevices: PhysicalDevice[]) => {
    // 添加选中的设备到手动设备列表
    setManualDevices(prev => [...prev, ...selectedDevices]);
    setIsPhysicalDeviceScannerVisible(false);
    
    // 更新配置文件中的设备映射
    try {
      // 等待设备列表更新
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 获取更新后的设备列表
      const updatedDevices = [...manualDevices, ...selectedDevices];
      
      // 过滤出已连接的设备并生成映射
      const connectedDevices = updatedDevices.filter((device: Device) => {
        const isConnected = device.status === I18nUtils.getText('connected') || 
                           device.status === I18nUtils.getText('ready') ||
                           (device.status === I18nUtils.getText('connected') && !('isSdkUnavailable' in device && device.isSdkUnavailable));
        return isConnected;
      });

      // 生成新的设备映射对象
      const deviceMappings: Record<string, string> = {};
      connectedDevices.forEach((device: Device) => {
        const deviceName = device.deviceNumber || device.id;
        deviceMappings[deviceName] = device.id;
      });

      // 更新到 RuyiAgent 配置
      const success = await window.electronAPI.setDeviceMappingsConfig(deviceMappings);
      if (success) {
        console.log('设备添加后，设备映射配置已更新:', deviceMappings);
      } else {
        console.warn('设备添加后，设备映射配置更新失败');
      }
    } catch (error) {
      console.error('更新设备映射配置时出错:', error);
    }
    
    message.success(`${I18nUtils.getText('deviceAdded')} (${selectedDevices.length})`);
  };

  // 获取已存在的设备ID列表
  const getExistingDeviceIds = (): string[] => {
    return devices.map(device => device.id);
  };

  // 删除设备处理函数
  const handleDeleteDevice = async (device: Device, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发设备选择
    
    // 添加调试日志
    console.log('删除设备 - 设备对象:', device);
    console.log('删除设备 - 设备ID:', device.id);
    console.log('删除设备 - 设备类型:', 'type' in device ? device.type : 'physical/virtual');
    
    // 将设备ID添加到已删除列表中
    setDeletedDeviceIds(prev => new Set(prev).add(device.id));
    
    // 从设备列表中移除
    setDevices(prevDevices => prevDevices.filter(d => d.id !== device.id));
    
    // 从手动设备列表中移除
    setManualDevices(prevDevices => prevDevices.filter(d => d.id !== device.id));
    
    // 更新Context中的设备列表
    setContextDevices(prevDevices => prevDevices.filter(d => d.id !== device.id));
    
    // 更新配置文件中的设备映射
    try {
      // 获取当前设备列表（排除已删除的设备）
      const currentDevices = devices.filter(d => d.id !== device.id);
      
      // 过滤出已连接的设备并生成映射
      const connectedDevices = currentDevices.filter((d: Device) => {
        const isConnected = d.status === I18nUtils.getText('connected') || 
                           d.status === I18nUtils.getText('ready') ||
                           (d.status === I18nUtils.getText('connected') && !('isSdkUnavailable' in d && d.isSdkUnavailable));
        return isConnected;
      });

      // 生成新的设备映射对象
      const deviceMappings: Record<string, string> = {};
      connectedDevices.forEach((d: Device) => {
        const deviceName = d.deviceNumber || d.id;
        deviceMappings[deviceName] = d.id;
      });

      // 更新到 RuyiAgent 配置
      const success = await window.electronAPI.setDeviceMappingsConfig(deviceMappings);
      if (success) {
        console.log('设备删除后，设备映射配置已更新:', deviceMappings);
      } else {
        console.warn('设备删除后，设备映射配置更新失败');
      }
      
      // 如果是浏览器设备，需要更新deviceCount配置
      if ('type' in device && device.type === 'browser') {
        const newBrowserCount = Math.max(0, browserDeviceCount - 1);
        setBrowserDeviceCount(newBrowserCount);
        
        // 更新deviceCount配置文件
        try {
          await window.electronAPI.setDeviceCountConfig({
            browserDeviceCount: newBrowserCount,
            cloudPhoneCount: cloudPhoneCount
          });
          console.log('设备删除后，deviceCount配置已更新:', { browserDeviceCount: newBrowserCount, cloudPhoneCount });
        } catch (error) {
          console.error('更新deviceCount配置失败:', error);
        }
        
        // 删除浏览器设备状态
        try {
          console.log('准备删除浏览器设备状态，设备ID:', device.id);
          const result = await window.electronAPI.removeBrowserDeviceState(device.id);
          if (result.success) {
            console.log('浏览器设备状态删除成功:', device.id);
          } else {
            console.warn('浏览器设备状态删除失败:', result.message);
          }
        } catch (error) {
          console.error('删除浏览器设备状态失败:', error);
        }
      }
      
      // 如果是云手机设备，需要更新cloudPhoneCount配置
      if (device.id.startsWith('cloud-phone-')) {
        const newCloudPhoneCount = Math.max(0, cloudPhoneCount - 1);
        setCloudPhoneCount(newCloudPhoneCount);
        
        // 更新deviceCount配置文件
        try {
          await window.electronAPI.setDeviceCountConfig({
            browserDeviceCount: browserDeviceCount,
            cloudPhoneCount: newCloudPhoneCount
          });
          console.log('设备删除后，deviceCount配置已更新:', { browserDeviceCount, cloudPhoneCount: newCloudPhoneCount });
        } catch (error) {
          console.error('更新deviceCount配置失败:', error);
        }
      }
      
    } catch (error) {
      console.error('更新设备映射配置时出错:', error);
    }
    
    // 显示成功消息
    message.success(`设备已删除: ${device.brand} ${device.model}`);
  };

  // 获取设备状态显示文本
  const getDeviceStatusText = (device: Device) => {
    if ('ruyiClientStatus' in device) {
      switch (device.ruyiClientStatus) {
        case 'checking':
          return device.status; // 不显示检查中状态，直接显示设备状态
        case 'updating':
          return device.status; // 不显示更新中状态，直接显示设备状态
        case 'error':
          return device.ruyiClientMessage || I18nUtils.getText('ruyiClientError');
        case 'ok':
          return device.status;
        default:
          return device.status;
      }
    }
    return device.status;
  };

  return (
    <div className="device-list-container">
      <div className="device-list-header">
        <Title level={3} style={{ marginBottom: 0 }}>
          <Monitor style={{ marginRight: 8, width: 20, height: 20, color: '#6366f1' }} />
          {I18nUtils.getText('selectDevice')}
        </Title>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            type="primary"
            icon={<Plus size={16} />}
            onClick={() => setIsAddDeviceModalVisible(true)}
            className="add-device-button"
            title={I18nUtils.getText('addDevice')}
          />
          <Button
            type="link"
            className="device-connection-guide-drawer-btn"
            icon={<HelpCircle size={16} />}
            onClick={() => setDrawerVisible(true)}
            title={I18nUtils.getText('deviceConnectionGuideTitle')}
          />
        </div>
      </div>

      {/* Add Device Modal */}
      <Modal
        title={I18nUtils.getText('addDeviceModalTitle')}
        open={isAddDeviceModalVisible}
        onCancel={() => setIsAddDeviceModalVisible(false)}
        footer={null}
        width={720}
      >
        <Row gutter={16}>
          <Col span={8}>
            <Card
              className="device-type-card"
              onClick={handleAddPhysicalDevice}
            >
              <Smartphone className="device-type-icon" size={40} />
              <p className="device-type-title">物理设备</p>
            </Card>
          </Col>
          <Col span={8}>
            <Card
              className="device-type-card"
              onClick={() => handleAddDevice('browser')}
            >
              <Globe className="device-type-icon" size={40} />
              <p className="device-type-title">{I18nUtils.getText('browserDevice')}</p>
            </Card>
          </Col>
          <Col span={8}>
            <Card
              className="device-type-card"
              onClick={() => handleAddDevice('cloud')}
            >
              <Cloud className="device-type-icon" size={40} />
              <p className="device-type-title">{I18nUtils.getText('ruyiCloudDevice')}</p>
            </Card>
          </Col>
        </Row>
      </Modal>

      {/* Drawer 组件 */}
      <Drawer
        title={I18nUtils.getText('deviceConnectionGuideTitle')}
        placement="left"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        mask={false}
        maskClosable={false}
        width={480}
        bodyStyle={{ padding: 0 }}
        className="device-connection-guide-drawer"
      >
        <DeviceConnectionGuide />
      </Drawer>

      <div className="device-list-content">
        <Spin spinning={loading}>
          {devices.length > 0 ? (
            <List
              grid={{ gutter: 16, column: 1 }}
              dataSource={devices}
              renderItem={device => (
                <List.Item>
                  <Card 
                    hoverable={!('isSdkUnavailable' in device && device.isSdkUnavailable) && 
                              !('ruyiClientStatus' in device && (device.ruyiClientStatus === 'checking' || device.ruyiClientStatus === 'updating'))}
                    onClick={() => handleDeviceClick(device)}
                    className={`device-card ${
                      'type' in device && device.type === 'browser' && device.status === I18nUtils.getText('ready')
                        ? 'device-card-connected'
                        : getDeviceStatusText(device) === I18nUtils.getText('connected') || 
                          ('ruyiClientStatus' in device && device.ruyiClientStatus === 'ok')
                          ? 'device-card-connected'
                          : 'device-card-disconnected'
                    }`}
                  >
                    <Card.Meta
                      avatar={getDeviceIcon(device)}
                      title={
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span>
                            {`${device.brand.charAt(0).toUpperCase() + device.brand.slice(1)} ${device.model.charAt(0).toUpperCase() + device.model.slice(1)}`}
                            {device.deviceNumber && (
                              <span className="device-number">
                                {device.deviceNumber}
                              </span>
                            )}
                          </span>
                          <Popconfirm
                            title="确认删除"
                            description={`确定要删除设备 "${device.brand} ${device.model}" 吗？`}
                            onConfirm={(e) => handleDeleteDevice(device, e as any)}
                            okText="确认"
                            cancelText="取消"
                            placement="topRight"
                          >
                            <Button
                              type="text"
                              icon={<Trash2 size={16} />}
                              className="device-delete-button"
                              size="small"
                              onClick={(e) => e.stopPropagation()}
                            />
                          </Popconfirm>
                        </div>
                      }
                      description={
                        <div>
                          {/* <p>{I18nUtils.getText('deviceId')}: {device.id}</p> */}
                          {/* <p>{I18nUtils.getText('productName')}: {device.product}</p> */}
                          <p>{I18nUtils.getText('status')}: {getDeviceStatusText(device)}</p>
                          {'sdkStatus' in device && device.sdkStatus && (
                            <p style={{ color: '#ff4d4f', fontWeight: 600 }}>{device.sdkStatus}</p>
                          )}
                          {'ruyiClientStatus' in device && device.ruyiClientStatus === 'error' && (
                            <p style={{ color: '#ff4d4f', fontWeight: 600 }}>{device.ruyiClientMessage}</p>
                          )}
                          {/* {'description' in device && device.description && <p>{I18nUtils.getText('description')}: {device.description}</p>} */}
                        </div>
                      }
                    />
                  </Card>
                </List.Item>
              )}
            />
          ) : (
            <Empty 
              description={I18nUtils.getText('noDevicesFound')}
              className="empty-container"
            />
          )}
        </Spin>
      </div>

      <Modal
        title={
          <div className="modal-title-container">
            <Title level={4} className="modal-title">
              <Rocket className="modal-title-icon" size={20} />
              {I18nUtils.getText('comingSoon')}{currentDevice && getDeviceTitle(currentDevice.id)}
            </Title>
          </div>
        }
        open={isModalVisible}
        footer={
          <Button 
            icon={<XCircle size={16} />}
            onClick={handleCancel}
            size="large"
            className="cancel-button"
          >
            {I18nUtils.getText('cancelRedirect')}
          </Button>
        }
        closable={true}
        onCancel={handleCancel}
        maskClosable={true}
        centered
        width={480}
        bodyStyle={{ padding: '24px' }}
      >
        <div className="modal-content">
          <Paragraph className="modal-description">
            {I18nUtils.getText('contactPrompt')}
          </Paragraph>
          <div className="countdown-container">
            <Clock className="countdown-icon" size={16} />
            {countdown 
              ? `${countdown} ${I18nUtils.getText('redirectingToWebsite')}` 
              : I18nUtils.getText('redirecting')}
          </div>
        </div>
      </Modal>

      {/* Physical Device Scanner */}
      <PhysicalDeviceScanner
        visible={isPhysicalDeviceScannerVisible}
        onCancel={() => setIsPhysicalDeviceScannerVisible(false)}
        onDevicesSelected={handlePhysicalDevicesSelected}
        existingDeviceIds={getExistingDeviceIds()}
        checkAndUpdateRuyiClient={checkAndUpdateRuyiClient}
      />
    </div>
  );
};

export default Select;
