import React, { useEffect, useRef, useState } from 'react';
import { Modal, Input, Button, Space, List, Radio, Tag, message, Tooltip } from 'antd';
import type { InputRef } from 'antd/es/input';
import { I18nUtils } from './languageSupport/i18nUtils';
import { flaskApi } from '../../../services/FlaskApiService';
import { QuestionCircleOutlined, PlusOutlined, DeleteOutlined, CloseOutlined } from '@ant-design/icons';
import '../styles/AnnotationModal.css';
import { useBrowserViewControl } from '../../../renderer/hooks/useBrowserViewControl';

interface Annotation {
  x: number;
  y: number;
  width: number;
  height: number;
  description: string[]; // 支持多条描述
  mode?: string;
}

// 扩展接口以支持不同的标注数据
interface EnumerateAnnotation extends Annotation {
  groupTitle?: string[]; // 用于存储总括标题
}

interface CheckAnnotation {
  description: string[];
  value: boolean;
  mode: string;
}

interface EnsureAnnotation {
  description: string[];
  x: number;
  y: number;
  width: number;
  height: number;
  mode: string;
}

interface AnnotationSaveResult {
  message: string;
  status: string;
  unit_id?: string;
}

interface AnnotationModalProps {
  visible: boolean;
  screenshot: string;
  onClose: () => void;
  onSave: (annotations: (Annotation | CheckAnnotation | EnsureAnnotation)[]) => void;
}

export const AnnotationModal: React.FC<AnnotationModalProps> = ({
  visible,
  screenshot,
  onClose,
  onSave,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  // 为每种模式创建单独的标注列表
  const [groundingAnnotations, setGroundingAnnotations] = useState<Annotation[]>([]);
  const [enumerateAnnotations, setEnumerateAnnotations] = useState<EnumerateAnnotation[]>([]);
  const [checkAnnotations, setCheckAnnotations] = useState<CheckAnnotation[]>([]);
  const [ensureAnnotations, setEnsureAnnotations] = useState<EnsureAnnotation[]>([]);
  
  const [currentAnnotation, setCurrentAnnotation] = useState<Partial<Annotation> | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentLabel, setCurrentLabel] = useState('');
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [scale, setScale] = useState({ x: 1, y: 1 });
  const [annotationMode, setAnnotationMode] = useState<'grounding' | 'enumerate' | 'check' | 'ensure'>('grounding');
  const [groupTitle, setGroupTitle] = useState(''); // 用于enumerate模式的总标题
  const [checkDescription, setCheckDescription] = useState(''); // 用于check模式的描述
  const [checkValue, setCheckValue] = useState<boolean>(true); // 用于check模式的True/False值
  const [ensureDescription, setEnsureDescription] = useState(''); // 用于ensure模式的描述
  const [ensureWarning, setEnsureWarning] = useState(''); // 新增：用于存储警告信息
  const [groundingDescriptions, setGroundingDescriptions] = useState<string[]>([]);
  const [enumerateDescriptions, setEnumerateDescriptions] = useState<string[]>([]);
  const [ensureDescriptions, setEnsureDescriptions] = useState<string[]>([]); // 新增：ensure模式多描述
  const [checkDescriptions, setCheckDescriptions] = useState<string[]>([]);
  const [additionalGroupTitles, setAdditionalGroupTitles] = useState<string[]>([]);

  // 1. 定义 ref 数组
  const groundingDescRefs = useRef<Array<InputRef | null>>([]);
  const enumerateDescRefs = useRef<Array<InputRef | null>>([]);
  const checkDescRefs = useRef<Array<InputRef | null>>([]);
  const ensureDescRefs = useRef<Array<InputRef | null>>([]);
  const groupTitleRefs = useRef<Array<InputRef | null>>([]);

  // Add the hook to control browser view visibility
  useBrowserViewControl(visible);

  // 获取当前模式下的标注列表
  const getCurrentAnnotations = () => {
    switch (annotationMode) {
      case 'grounding':
        return groundingAnnotations;
      case 'enumerate':
        return enumerateAnnotations;
      case 'check':
        return checkAnnotations;
      case 'ensure':
        return ensureAnnotations;
      default:
        return groundingAnnotations;
    }
  };

  // 设置当前模式下的标注列表
  const setCurrentAnnotations = (annotations: any[]) => {
    switch (annotationMode) {
      case 'grounding':
        setGroundingAnnotations(annotations);
        break;
      case 'enumerate':
        setEnumerateAnnotations(annotations);
        break;
      case 'check':
        setCheckAnnotations(annotations);
        break;
      case 'ensure':
        setEnsureAnnotations(annotations);
        break;
    }
  };

  // 加载图片
  useEffect(() => {
    if (screenshot) {
      const img = new Image();
      img.src = screenshot;
      img.onload = () => {
        setImage(img);
        if (canvasRef.current) {
          const canvas = canvasRef.current;
          const containerWidth = canvas.parentElement?.clientWidth || canvas.width;
          
          // 计算缩放比例
          const scaleX = img.width / containerWidth;
          const scaleY = scaleX; // 保持宽高比
          
          // 设置Canvas的显示尺寸
          canvas.style.width = `${containerWidth}px`;
          canvas.style.height = `${img.height / scaleX}px`;
          
          // 设置Canvas的实际尺寸
          canvas.width = img.width;
          canvas.height = img.height;
          
          setScale({ x: scaleX, y: scaleY });
          
          const ctx = canvas.getContext('2d');
          ctx?.drawImage(img, 0, 0);
        }
      };
    }
  }, [screenshot]);

  // 根据不同模式绘制标注
  const drawAnnotations = () => {
    if (!canvasRef.current || !image) return;
    const ctx = canvasRef.current.getContext('2d');
    if (!ctx) return;

    // 清除画布并重新绘制图片
    ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
    ctx.drawImage(image, 0, 0);

    // 获取当前模式的标注
    const currentModeAnnotations = getCurrentAnnotations();

    // 绘制当前模式的标注框（不是check模式的才需要绘制）
    if (annotationMode !== 'check') {
      currentModeAnnotations.forEach((annotation) => {
        if ('x' in annotation && 'width' in annotation) {
          ctx.strokeStyle = '#ff0000';
          ctx.lineWidth = 4;
          ctx.strokeRect(annotation.x, annotation.y, annotation.width || 0, annotation.height || 0);
        }
      });

      // 绘制当前正在创建的标注
      if (currentAnnotation && 'x' in currentAnnotation) {
        ctx.strokeStyle = '#ff0000';
        ctx.lineWidth = 4;
        ctx.strokeRect(
          currentAnnotation.x || 0,
          currentAnnotation.y || 0,
          currentAnnotation.width || 0,
          currentAnnotation.height || 0
        );
      }
    }
  };

  useEffect(() => {
    drawAnnotations();
  }, [groundingAnnotations, enumerateAnnotations, ensureAnnotations, currentAnnotation, annotationMode]);

  // 鼠标事件处理
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left) * scale.x;
    const y = (e.clientY - rect.top) * scale.y;
    setIsDrawing(true);
    setCurrentAnnotation({ x, y });
    setEnsureWarning(''); // 清除警告信息
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !currentAnnotation || !canvasRef.current) return;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left) * scale.x;
    const y = (e.clientY - rect.top) * scale.y;
    
    setCurrentAnnotation({
      ...currentAnnotation,
      width: currentAnnotation.x !== undefined ? x - currentAnnotation.x : 0,
      height: currentAnnotation.y !== undefined ? y - currentAnnotation.y : 0,
    });
  };

  const handleMouseUp = () => {
    if (!currentAnnotation) return;
    setIsDrawing(false);
    if (currentAnnotation.width && currentAnnotation.height) {
      setCurrentAnnotation({ ...currentAnnotation });
    } else {
      setCurrentAnnotation(null);
    }
  };

  const handleSave = async () => {
    // 只获取当前模式的标注数据
    let currentModeAnnotations: (Annotation | CheckAnnotation | EnsureAnnotation)[] = [];
    
    switch (annotationMode) {
      case 'grounding':
        currentModeAnnotations = groundingAnnotations.map(a => ({ ...a, mode: 'grounding' }));
        break;
      case 'enumerate':
        const allGroupTitles = [groupTitle, ...additionalGroupTitles].filter(Boolean);
        currentModeAnnotations = enumerateAnnotations.map(a => ({ 
          ...a, 
          groupTitle: allGroupTitles,
          mode: 'enumerate' 
        }));
        break;
      case 'check':
        currentModeAnnotations = [...checkAnnotations];
        break;
      case 'ensure':
        currentModeAnnotations = [...ensureAnnotations];
        break;
      default:
        currentModeAnnotations = [];
    }

    try {
      // 保存当前模式的标注数据，并获取保存路径
      const uploadResult = await saveToLocal(currentModeAnnotations);
      console.log('Upload result:', uploadResult);

      if (uploadResult.status !== 'success') {
        const errorMessage = I18nUtils.getText('saveAnnotationToCloudFailed');
        message.error(errorMessage);
        console.error(errorMessage, uploadResult);
        return;
      }

      // 调用父组件的保存方法，但不关闭模态框
      onSave(currentModeAnnotations);
      
      message.success(`${annotationMode} ${I18nUtils.getText('annotationMode')}${I18nUtils.getText('annotationSuccess')}`);
    } catch (error) {
      // 错误提示
      message.error(I18nUtils.getText('saveFailed') + ': ' + (error instanceof Error ? error.message : 'Unknown error'));
      console.error('保存标注数据失败:', error);
    }
  };

  const handleAddAnnotation = () => {
    if (annotationMode === 'grounding') {
      if (
        groundingDescriptions.length > 0 &&
        currentAnnotation?.width &&
        currentAnnotation?.height
      ) {
        setGroundingAnnotations([
          ...groundingAnnotations,
          { ...currentAnnotation as Annotation, description: groundingDescriptions, mode: 'grounding' }
        ]);
        setGroundingDescriptions([]);
        setCurrentAnnotation(null);
      }
    } else if (annotationMode === 'enumerate') {
      if (
        enumerateDescriptions.length > 0 &&
        currentAnnotation?.width &&
        currentAnnotation?.height
      ) {
        setEnumerateAnnotations([
          ...enumerateAnnotations,
          { ...currentAnnotation as Annotation, description: enumerateDescriptions, mode: 'enumerate' }
        ]);
        setEnumerateDescriptions([]);
        setCurrentAnnotation(null);
      }
    } else if (annotationMode === 'check') {
      if (checkDescription) {
        const checkAnnotation: CheckAnnotation = {
          description: [checkDescription],
          value: checkValue,
          mode: 'check'
        };
        setCheckAnnotations([...checkAnnotations, checkAnnotation]);
        setCheckDescription('');
        setCheckValue(true);
      }
    } else if (annotationMode === 'ensure') {
      if (
        ([ensureDescription, ...ensureDescriptions].filter(Boolean).length > 0) &&
        currentAnnotation?.width &&
        currentAnnotation?.height
      ) {
        const all = [ensureDescription, ...ensureDescriptions].filter(Boolean);
        const ensureAnnotation: EnsureAnnotation = {
          description: all,
          x: currentAnnotation.x || 0,
          y: currentAnnotation.y || 0,
          width: currentAnnotation.width || 0,
          height: currentAnnotation.height || 0,
          mode: 'ensure'
        };
        setEnsureAnnotations([...ensureAnnotations, ensureAnnotation]);
        setEnsureDescription('');
        setEnsureDescriptions([]);
        setCurrentAnnotation(null);
      }
    }
  };

  const handleDeleteAnnotation = (index: number) => {
    const currentModeAnnotations = [...getCurrentAnnotations()];
    currentModeAnnotations.splice(index, 1);
    setCurrentAnnotations(currentModeAnnotations);
    
    // 如果是 ensure 模式，清除警告信息
    if (annotationMode === 'ensure') {
      setEnsureWarning('');
    }
  };

  const saveToLocal = async (annotations: (Annotation | CheckAnnotation | EnsureAnnotation)[]): Promise<AnnotationSaveResult> => {
    if (!canvasRef.current) throw new Error("Canvas reference is null");

    // 获取标注后的图片
    const annotatedImage = canvasRef.current.toDataURL('image/png');

    // 获取 VH 数据
    let viewHierarchy = {};
    try {
      viewHierarchy = await flaskApi.getVH();
    } catch (error) {
      console.error('[AnnotationModal] SAVE-ANNOTATION-DATA-VIEW-HIERARCHY-ERROR', error);
    }

    console.log('[AnnotationModal] SAVE-ANNOTATION-DATA-VIEW-HIERARCHY', viewHierarchy);

    console.log('[AnnotationModal] SAVE-ANNOTATION-DATA', annotations);

    // 保存数据
    const result = await window.electronAPI.saveAnnotationData({
      screenshot, // 原始截图
      annotatedImage, // 标注后的图片
      viewHierarchy: JSON.stringify(viewHierarchy), // 使用从 Flask API 获取的 VH 数据
      annotations, // 当前模式的标注数据
    });

    console.log('[AnnotationModal] SAVE-ANNOTATION-DATA-SAVE DOWN');

    // The result from electron API might be a JSON string.
    if (typeof result === 'string') {
      try {
        return JSON.parse(result);
      } catch (e) {
        console.error("Failed to parse annotation save result:", e);
        // Fallback or error handling
        return { status: 'error', message: 'Failed to parse server response' };
      }
    }

    return result;
  };

  return (
    <Modal
      title={
        <div className="annotation-modal-title">
          <div className="annotation-modal-title-wrapper">
            {I18nUtils.getText('annotationModalTitle')}
            <div className="annotation-modal-title-divider" />
            <Tooltip
              placement="right"
              overlayClassName="annotation-tip-tooltip"
              title={
                <div>
                  <div className="tip-title">
                    <QuestionCircleOutlined className="tip-icon" />
                    {I18nUtils.getText('manualPatchTitle')}
                  </div>
                  <div className="tip-content">
                    {(() => {
                      const tip = I18nUtils.getText('manualPatchTip');
                      const lines = tip.split(/\n/).filter(Boolean);
                      type Section = { title: string; content: string[] };
                      const sections: Section[] = [];
                      let current: Section | null = null;
                      lines.forEach((line: string) => {
                        // 修改正则表达式：匹配以中文冒号、英文冒号、中文问号或英文问号结尾的行
                        const match = line.match(/^(.*?)([：:？\?])$/);
                        if (match) {
                          if (current) sections.push(current);
                          current = { title: match[1], content: [] };
                        } else if (current) {
                          current.content.push(line);
                        }
                      });
                      if (current) sections.push(current);
                      if (sections.length === 0) {
                        // 如果没有分组成功，直接按行显示
                        return lines.map((line, index) => <p key={index}>{line}</p>);
                      }
                      return sections.map((sec: Section, idx: number) => (
                        <div className="tip-section" key={idx}>
                          <div className="tip-section-title">
                            {sec.title}
                          </div>
                          <div className="tip-section-content">
                            <ul style={{ margin: 0, padding: 0 }}>
                              {sec.content.map((c: string, i: number) => (
                                <li key={i} dangerouslySetInnerHTML={{ 
                                  __html: c.replace(/^-/,'').trim()
                                         .replace(/`([^`]+)`/g, '<code>$1</code>')
                                         .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                                }} />
                              ))}
                            </ul>
                          </div>
                        </div>
                      ));
                    })()}
                  </div>
                </div>
              }
            >
              <QuestionCircleOutlined 
                style={{ 
                  fontSize: '16px',
                  cursor: 'pointer',
                  // color: '#8c8c8c',
                  transition: 'color 0.3s',
                  display: 'flex',
                  alignItems: 'center'
                }}
                className="help-icon-hover"
              />
            </Tooltip>
          </div>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={900}
      footer={null}
      maskClosable={false}
    >
      <div style={{ marginBottom: 16 }}>
        <div style={{ 
          marginBottom: 8,
          display: 'flex',
          alignItems: 'center',
          gap: '12px'  // 添加间距
        }}>
          {I18nUtils.getText('annotationMode')}:
          <Radio.Group 
            value={annotationMode} 
            onChange={(e) => {
              setAnnotationMode(e.target.value);
              setCurrentAnnotation(null);
              setCurrentLabel('');
            }}
          >
            <Radio.Button value="grounding">Grounding</Radio.Button>
            {/* <Radio.Button value="enumerate">Enumerate</Radio.Button> */}
            <Radio.Button value="check">Check</Radio.Button>
            {/* <Radio.Button value="ensure">Ensure</Radio.Button> */}
          </Radio.Group>
        </div>
      </div>

      <div style={{ display: 'flex', gap: 20 }}>
        <div style={{ flex: 1, overflow: 'auto' }}>
          <canvas
            ref={canvasRef}
            style={{
              border: '1px solid #ccc',
              maxWidth: '100%',
              display: 'block',
            }}
            onMouseDown={annotationMode !== 'check' ? handleMouseDown : undefined}
            onMouseMove={annotationMode !== 'check' ? handleMouseMove : undefined}
            onMouseUp={annotationMode !== 'check' ? handleMouseUp : undefined}
            onMouseLeave={() => setIsDrawing(false)}
          />
        </div>
        <div style={{ width: 500 }}>
          {/* Grounding模式的UI */}
          {annotationMode === 'grounding' && (
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4, gap: 8 }}>
                {I18nUtils.getText('annotationLabelTitle')}
                <button
                  className="annotation-add-desc-btn"
                  type="button"
                  onClick={() => {
                    setGroundingDescriptions([...groundingDescriptions, '']);
                    setTimeout(() => {
                      const lastIdx = groundingDescriptions.length;
                      groundingDescRefs.current[lastIdx]?.focus();
                    }, 0);
                  }}
                  style={{ display: 'flex', alignItems: 'center', gap: 4, border: 'none', background: 'none', padding: 0, cursor: 'pointer', color: '#1890ff' }}
                >
                  <PlusOutlined />
                  <span style={{ fontSize: 13 }}>{I18nUtils.getText('annotationLabelAddOther')}</span>
                </button>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                <Input
                  value={currentLabel}
                  onChange={e => setCurrentLabel(e.target.value)}
                  placeholder={I18nUtils.getText('annotationLabelPlaceholder')}
                />
                <DeleteOutlined
                  style={{ color: '#ff4d4f', cursor: 'pointer' }}
                  onClick={() => {
                    if (groundingDescriptions.length > 0) {
                      setCurrentLabel(groundingDescriptions[0]);
                      setGroundingDescriptions(groundingDescriptions.slice(1));
                    } else {
                      setCurrentLabel('');
                    }
                  }}
                />
              </div>
              {groundingDescriptions.map((desc, idx) => (
                <div key={idx} style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                  <Input
                    ref={el => groundingDescRefs.current[idx] = el}
                    value={desc}
                    onChange={e => {
                      const newArr = [...groundingDescriptions];
                      newArr[idx] = e.target.value;
                      setGroundingDescriptions(newArr);
                    }}
                    placeholder={I18nUtils.getText('annotationLabelAddOtherPlaceholder')}
                  />
                  <DeleteOutlined
                    style={{ color: '#ff4d4f', cursor: 'pointer' }}
                    onClick={() => setGroundingDescriptions(groundingDescriptions.filter((_, i) => i !== idx))}
                  />
                </div>
              ))}
              <Button
                type="primary"
                style={{ marginTop: 8 }}
                onClick={() => {
                  // 合并currentLabel和groundingDescriptions
                  const all = [currentLabel, ...groundingDescriptions].filter(Boolean);
                  if (all.length > 0 && currentAnnotation?.width && currentAnnotation?.height) {
                    setGroundingAnnotations([
                      ...groundingAnnotations,
                      { ...currentAnnotation as Annotation, description: all, mode: 'grounding' }
                    ]);
                    setCurrentLabel('');
                    setGroundingDescriptions([]);
                    setCurrentAnnotation(null);
                  }
                }}
                disabled={([currentLabel, ...groundingDescriptions].filter(Boolean).length === 0 || !currentAnnotation || !currentAnnotation.width || !currentAnnotation.height)}
              >
                {I18nUtils.getText('add')}
              </Button>
            </div>
          )}

          {/* Enumerate模式的UI */}
          {annotationMode === 'enumerate' && (
            <>
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4, gap: 8 }}>
                  {I18nUtils.getText('annotationGroupTitle')}
                  <button
                    className="annotation-add-desc-btn"
                    type="button"
                    onClick={() => {
                      setAdditionalGroupTitles([...additionalGroupTitles, '']);
                      setTimeout(() => {
                        const lastIdx = additionalGroupTitles.length;
                        groupTitleRefs.current[lastIdx]?.focus();
                      }, 0);
                    }}
                    style={{ display: 'flex', alignItems: 'center', gap: 4, border: 'none', background: 'none', padding: 0, cursor: 'pointer', color: '#1890ff' }}
                  >
                    <PlusOutlined />
                    <span style={{ fontSize: 13 }}>{I18nUtils.getText('annotationLabelAddOther')}</span>
                  </button>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                  <Input 
                    value={groupTitle}
                    onChange={(e) => setGroupTitle(e.target.value)}
                    placeholder={I18nUtils.getText('annotationGroupTitlePlaceholder')}
                  />
                  <DeleteOutlined
                    style={{ color: '#ff4d4f', cursor: 'pointer' }}
                    onClick={() => {
                      if (additionalGroupTitles.length > 0) {
                        setGroupTitle(additionalGroupTitles[0]);
                        setAdditionalGroupTitles(additionalGroupTitles.slice(1));
                      } else {
                        setGroupTitle('');
                      }
                    }}
                  />
                </div>
                {additionalGroupTitles.map((desc, idx) => (
                  <div key={idx} style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                    <Input
                      ref={el => groupTitleRefs.current[idx] = el}
                      value={desc}
                      onChange={e => {
                        const newArr = [...additionalGroupTitles];
                        newArr[idx] = e.target.value;
                        setAdditionalGroupTitles(newArr);
                      }}
                      placeholder={I18nUtils.getText('annotationLabelAddOtherPlaceholder')}
                    />
                    <DeleteOutlined
                      style={{ color: '#ff4d4f', cursor: 'pointer' }}
                      onClick={() => setAdditionalGroupTitles(additionalGroupTitles.filter((_, i) => i !== idx))}
                    />
                  </div>
                ))}
              </div>
              {/* 单项描述输入区域始终显示，不再依赖 currentAnnotation */}
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4, gap: 8 }}>
                  {I18nUtils.getText('annotationItemLabelTitle')}
                  <button
                    className="annotation-add-desc-btn"
                    type="button"
                    onClick={() => {
                      setEnumerateDescriptions([...enumerateDescriptions, '']);
                      setTimeout(() => {
                        const lastIdx = enumerateDescriptions.length;
                        enumerateDescRefs.current[lastIdx]?.focus();
                      }, 0);
                    }}
                    style={{ display: 'flex', alignItems: 'center', gap: 4, border: 'none', background: 'none', padding: 0, cursor: 'pointer', color: '#1890ff' }}
                  >
                    <PlusOutlined />
                    <span style={{ fontSize: 13 }}>{I18nUtils.getText('annotationLabelAddOther')}</span>
                  </button>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                  <Input
                    value={currentLabel}
                    onChange={e => setCurrentLabel(e.target.value)}
                    placeholder={I18nUtils.getText('annotationItemLabelPlaceholder')}
                  />
                  <DeleteOutlined
                    style={{ color: '#ff4d4f', cursor: 'pointer' }}
                    onClick={() => {
                      if (enumerateDescriptions.length > 0) {
                        setCurrentLabel(enumerateDescriptions[0]);
                        setEnumerateDescriptions(enumerateDescriptions.slice(1));
                      } else {
                        setCurrentLabel('');
                      }
                    }}
                  />
                </div>
                {enumerateDescriptions.map((desc, idx) => (
                  <div key={idx} style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                    <Input
                      ref={el => enumerateDescRefs.current[idx] = el}
                      value={desc}
                      onChange={e => {
                        const newArr = [...enumerateDescriptions];
                        newArr[idx] = e.target.value;
                        setEnumerateDescriptions(newArr);
                      }}
                      placeholder={I18nUtils.getText('annotationLabelAddOtherPlaceholder')}
                    />
                    <DeleteOutlined
                      style={{ color: '#ff4d4f', cursor: 'pointer' }}
                      onClick={() => setEnumerateDescriptions(enumerateDescriptions.filter((_, i) => i !== idx))}
                    />
                  </div>
                ))}
                <Button
                  type="primary"
                  style={{ marginTop: 8 }}
                  onClick={() => {
                    // 合并currentLabel和enumerateDescriptions
                    const all = [currentLabel, ...enumerateDescriptions].filter(Boolean);
                    if (all.length > 0 && currentAnnotation?.width && currentAnnotation?.height) {
                      setEnumerateAnnotations([
                        ...enumerateAnnotations,
                        { ...currentAnnotation as Annotation, description: all, mode: 'enumerate' }
                      ]);
                      setCurrentLabel('');
                      setEnumerateDescriptions([]);
                      setCurrentAnnotation(null);
                    }
                  }}
                  disabled={([currentLabel, ...enumerateDescriptions].filter(Boolean).length === 0 || !currentAnnotation || !currentAnnotation.width || !currentAnnotation.height)}
                >
                  {I18nUtils.getText('add')}
                </Button>
              </div>
            </>
          )}

          {/* Check模式的UI */}
          {annotationMode === 'check' && (
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4, gap: 8 }}>
                {I18nUtils.getText('checkDescriptionTitle')}
                <button
                  className="annotation-add-desc-btn"
                  type="button"
                  onClick={() => {
                    setCheckDescriptions([...checkDescriptions, '']);
                    setTimeout(() => {
                      const lastIdx = checkDescriptions.length;
                      checkDescRefs.current[lastIdx]?.focus();
                    }, 0);
                  }}
                  style={{ display: 'flex', alignItems: 'center', gap: 4, border: 'none', background: 'none', padding: 0, cursor: 'pointer', color: '#1890ff' }}
                >
                  <PlusOutlined />
                  <span style={{ fontSize: 13 }}>{I18nUtils.getText('annotationLabelAddOther')}</span>
                </button>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                <Input
                  value={checkDescription}
                  onChange={e => setCheckDescription(e.target.value)}
                  placeholder={I18nUtils.getText('checkDescriptionPlaceholder')}
                />
                <DeleteOutlined
                  style={{ color: '#ff4d4f', cursor: 'pointer' }}
                  onClick={() => {
                    if (checkDescriptions.length > 0) {
                      setCheckDescription(checkDescriptions[0]);
                      setCheckDescriptions(checkDescriptions.slice(1));
                    } else {
                      setCheckDescription('');
                    }
                  }}
                />
              </div>
              {checkDescriptions.map((desc, idx) => (
                <div key={idx} style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                  <Input
                    ref={el => checkDescRefs.current[idx] = el}
                    value={desc}
                    onChange={e => {
                      const newArr = [...checkDescriptions];
                      newArr[idx] = e.target.value;
                      setCheckDescriptions(newArr);
                    }}
                    placeholder={I18nUtils.getText('annotationCheckAddOtherPlaceholder')}
                  />
                  <DeleteOutlined
                    style={{ color: '#ff4d4f', cursor: 'pointer' }}
                    onClick={() => setCheckDescriptions(checkDescriptions.filter((_, i) => i !== idx))}
                  />
                </div>
              ))}
              <div style={{ marginTop: 8 }}>
                <div style={{ marginBottom: 4 }}>{I18nUtils.getText('checkValueTitle')}</div>
                <Radio.Group 
                  value={checkValue} 
                  onChange={(e) => setCheckValue(e.target.value)}
                >
                  <Radio value={true}>True</Radio>
                  <Radio value={false}>False</Radio>
                </Radio.Group>
              </div>
              <Button 
                type="primary" 
                style={{ marginTop: 8 }} 
                onClick={() => {
                  const all = [checkDescription, ...checkDescriptions].filter(Boolean);
                  if (all.length > 0) {
                    const checkAnnotation: CheckAnnotation = {
                      description: all,
                      value: checkValue,
                      mode: 'check'
                    };
                    setCheckAnnotations([...checkAnnotations, checkAnnotation]);
                    setCheckDescription('');
                    setCheckDescriptions([]);
                    setCheckValue(true);
                  }
                }}
                disabled={([checkDescription, ...checkDescriptions].filter(Boolean).length === 0)}
              >
                {I18nUtils.getText('add')}
              </Button>
            </div>
          )}

          {/* Ensure模式的UI */}
          {annotationMode === 'ensure' && (
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4, gap: 8 }}>
                {I18nUtils.getText('ensureDescriptionTitle')}
                <button
                  className="annotation-add-desc-btn"
                  type="button"
                  onClick={() => {
                    setEnsureDescriptions([...ensureDescriptions, '']);
                    setTimeout(() => {
                      const lastIdx = ensureDescriptions.length;
                      ensureDescRefs.current[lastIdx]?.focus();
                    }, 0);
                  }}
                  style={{ display: 'flex', alignItems: 'center', gap: 4, border: 'none', background: 'none', padding: 0, cursor: 'pointer', color: '#1890ff' }}
                >
                  <PlusOutlined />
                  <span style={{ fontSize: 13 }}>{I18nUtils.getText('annotationLabelAddOther')}</span>
                </button>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                <Input
                  value={ensureDescription}
                  onChange={e => setEnsureDescription(e.target.value)}
                  placeholder={I18nUtils.getText('ensureDescriptionPlaceholder')}
                />
                <DeleteOutlined
                  style={{ color: '#ff4d4f', cursor: 'pointer' }}
                  onClick={() => {
                    if (ensureDescriptions.length > 0) {
                      setEnsureDescription(ensureDescriptions[0]);
                      setEnsureDescriptions(ensureDescriptions.slice(1));
                    } else {
                      setEnsureDescription('');
                    }
                  }}
                />
              </div>
              {ensureDescriptions.map((desc, idx) => (
                <div key={idx} style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                  <Input
                    ref={el => ensureDescRefs.current[idx] = el}
                    value={desc}
                    onChange={e => {
                      const newArr = [...ensureDescriptions];
                      newArr[idx] = e.target.value;
                      setEnsureDescriptions(newArr);
                    }}
                    placeholder={I18nUtils.getText('annotationEnsureAddOtherPlaceholder')}
                  />
                  <DeleteOutlined
                    style={{ color: '#ff4d4f', cursor: 'pointer' }}
                    onClick={() => setEnsureDescriptions(ensureDescriptions.filter((_, i) => i !== idx))}
                  />
                </div>
              ))}
              {ensureWarning && (
                <div style={{ color: '#ff4d4f', marginTop: 8 }}>
                  {ensureWarning}
                </div>
              )}
              <Button
                style={{ marginTop: 8, marginRight: 8 }}
                onClick={() => {
                  // 允许添加多个 x/y/width/height 都为-1的ensure标注
                  const all = [ensureDescription, ...ensureDescriptions].filter(Boolean);
                  if (all.length > 0) {
                    setEnsureAnnotations([
                      ...ensureAnnotations,
                      {
                        description: all,
                        x: -1,
                        y: -1,
                        width: -1,
                        height: -1,
                        mode: 'ensure',
                      },
                    ]);
                    setEnsureDescription('');
                    setEnsureDescriptions([]);
                    setCurrentAnnotation(null);
                  }
                }}
                disabled={([ensureDescription, ...ensureDescriptions].filter(Boolean).length === 0 || !!currentAnnotation)}
              >
                {I18nUtils.getText('markCurrentScreenAsTarget')}
              </Button>
              <Button
                type="primary"
                style={{ marginTop: 8 }}
                onClick={handleAddAnnotation}
                disabled={([ensureDescription, ...ensureDescriptions].filter(Boolean).length === 0 || !currentAnnotation)}
              >
                {I18nUtils.getText('addEnsureAction')}
              </Button>
            </div>
          )}

          <List
            header={<div>{I18nUtils.getText('annotationsList')}</div>}
            bordered
            dataSource={getCurrentAnnotations() as any[]}
            renderItem={(item, index) => (
              <List.Item
                actions={[
                  <Button 
                    size="small" 
                    danger 
                    onClick={() => handleDeleteAnnotation(index)}
                  >
                    {I18nUtils.getText('delete')}
                  </Button>
                ]}
              >
                <div>
                  {'label' in item && <div>{item.label as string}</div>}
                  {'description' in item && Array.isArray(item.description)
                    ? (item.description as string[]).map((desc: string, i: number) => <div key={i}>{desc}</div>)
                    : <div>{item.description as string}</div>}
                  {'value' in item && <Tag color={item.value ? 'green' : 'red'}>{item.value ? 'True' : 'False'}</Tag>}
                  {'x' in item && 'width' in item && (
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      X: {Math.round(item.x)}, Y: {Math.round(item.y)}, 
                      W: {Math.round(item.width)}, H: {Math.round(item.height)}
                    </div>
                  )}
                </div>
              </List.Item>
            )}
          />
          <div style={{ marginTop: 16 }}>
            <Space>
              <Button onClick={onClose}>{I18nUtils.getText('cancel')}</Button>
              <Button type="primary" onClick={handleSave} disabled={
                (annotationMode === 'grounding' && groundingAnnotations.length === 0) ||
                (annotationMode === 'enumerate' && (enumerateAnnotations.length < 2 || [groupTitle, ...additionalGroupTitles].filter(Boolean).length === 0)) ||
                (annotationMode === 'check' && checkAnnotations.length === 0) ||
                (annotationMode === 'ensure' && ensureAnnotations.length === 0)
              }>
                {I18nUtils.getText('save')}
              </Button>
            </Space>
          </div>
        </div>
      </div>
    </Modal>
  );
};