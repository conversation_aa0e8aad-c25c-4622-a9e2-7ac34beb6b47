import React, { useState, useEffect } from 'react';
import { Modal, <PERSON>, <PERSON>, <PERSON><PERSON>, Spin, message, Checkbox, Empty, Typography } from 'antd';
import { MobileOutlined, ReloadOutlined, CheckCircleOutlined, SearchOutlined, WarningOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Device, PhysicalDevice } from './Select';
import { I18nUtils } from './languageSupport/i18nUtils';
import '../styles/PhysicalDeviceScanner.css';

const { Text } = Typography;

interface PhysicalDeviceScannerProps {
  visible: boolean;
  onCancel: () => void;
  onDevicesSelected: (devices: PhysicalDevice[]) => void;
  existingDeviceIds: string[]; // 已存在的设备ID列表，用于过滤
  checkAndUpdateRuyiClient?: (deviceId: string) => Promise<void>; // Ruyi Client检查函数
}

interface ScanState {
  isScanning: boolean;
  availableDevices: PhysicalDevice[];
  selectedDeviceIds: string[];
  error: string | null;
}

const generateDeviceNumber = (devices: Device[], deviceType: 'phone' | 'cloud' | 'batch' | 'browser', index: number): string => {
    switch (deviceType) {
      case 'phone':
        return `phone${index + 1}`;
      case 'cloud':
        return `cloud${index + 1}`;
      case 'batch':
        return `batch${index + 1}`;
      case 'browser':
        return `browser${index + 1}`;
      default:
        return `device${index + 1}`;
    }
};

const PhysicalDeviceScanner: React.FC<PhysicalDeviceScannerProps> = ({
  visible,
  onCancel,
  onDevicesSelected,
  existingDeviceIds,
  checkAndUpdateRuyiClient
}) => {
  const [scanState, setScanState] = useState<ScanState>({
    isScanning: false,
    availableDevices: [],
    selectedDeviceIds: [],
    error: null
  });

  // 扫描物理设备
  const scanDevices = async () => {
    setScanState(prev => ({ ...prev, isScanning: true, error: null }));
    
    try {
      const result = await window.electronAPI.runAdbCommand('adb devices -l');
      const lines = result.split('\n').filter(line => line.trim() !== '');
      
      // 跳过第一行（List of devices attached）
      const deviceLines = lines.slice(1);
      
      if (deviceLines.length === 0) {
        setScanState(prev => ({ 
          ...prev, 
          isScanning: false, 
          availableDevices: [],
          selectedDeviceIds: []
        }));
        return;
      }

      // 并行获取所有设备的详细信息
      const parsedDevices: PhysicalDevice[] = await Promise.all(
        deviceLines.map(async (line, index) => {
          const [id, ...rest] = line.split(/\s+/);
          const deviceInfo = rest.join(' ');
          
          // 获取设备品牌
          let brand = I18nUtils.getText('unknownBrand');
          try {
            brand = await window.electronAPI.runAdbCommand(`adb -s ${id} shell getprop ro.product.brand`);
            brand = brand.trim();
          } catch (error) {
            console.error('Failed to get device brand:', error);
          }
          
          // 获取 SDK 版本
          let sdk: string | null = null;
          try {
            sdk = await window.electronAPI.runAdbCommand(`adb -s ${id} shell getprop ro.build.version.sdk`);
            sdk = sdk.trim();
          } catch (error) {
            console.error('Failed to get device SDK:', error);
          }
          
          // 解析设备信息
          const model = deviceInfo.match(/model:([^\s]+)/)?.[1] || I18nUtils.getText('unknownModel');
          const product = deviceInfo.match(/product:([^\s]+)/)?.[1] || I18nUtils.getText('unknownProduct');
          
          // 判断 SDK 是否可用
          let sdkStatus: string | null = null;
          let isSdkUnavailable = false;
          if (sdk && !isNaN(Number(sdk))) {
            const sdkNum = Number(sdk);
            if (sdkNum < 26) {
              sdkStatus = I18nUtils.getText('sdkTooLow').replace('{sdk}', String(sdkNum));
              isSdkUnavailable = true;
            }
          }

          const device: PhysicalDevice = {
            id,
            brand,
            model,
            product,
            sdk,
            sdkStatus,
            isSdkUnavailable,
            status: deviceInfo.includes('unauthorized') ? 
                I18nUtils.getText('unauthorized') : 
                I18nUtils.getText('connected'),
            deviceNumber: generateDeviceNumber([], 'phone', index),
            ruyiClientStatus: 'checking',
            ruyiClientMessage: I18nUtils.getText('checkingRuyiClient')
          };
  
          // 对于新检测到的物理设备，自动检查 Ruyi Client 版本
          if (device.status === I18nUtils.getText('connected') && !device.isSdkUnavailable && checkAndUpdateRuyiClient) {
            // 异步检查，不阻塞设备列表显示
            setTimeout(() => checkAndUpdateRuyiClient(device.id), 100);
          }

          return device;
        })
      );

      // 过滤掉已存在的设备
      const newDevices = parsedDevices.filter(device => !existingDeviceIds.includes(device.id));
      
      setScanState(prev => ({ 
        ...prev, 
        isScanning: false, 
        availableDevices: newDevices,
        selectedDeviceIds: []
      }));

      if (newDevices.length === 0) {
        message.info('未发现新设备');
      }

    } catch (error) {
      console.error('Failed to scan devices:', error);
      setScanState(prev => ({ 
        ...prev, 
        isScanning: false, 
        error: error instanceof Error ? error.message : String(error)
      }));
      message.error(I18nUtils.getText('scanDevicesFailed'));
    }
  };

  // 自动扫描当对话框打开时
  useEffect(() => {
    if (visible) {
      scanDevices();
    } else {
      // 重置状态
      setScanState({
        isScanning: false,
        availableDevices: [],
        selectedDeviceIds: [],
        error: null
      });
    }
  }, [visible]);

  // 处理设备选择
  const handleDeviceSelect = (deviceId: string, checked: boolean) => {
    setScanState(prev => ({
      ...prev,
      selectedDeviceIds: checked 
        ? [...prev.selectedDeviceIds, deviceId]
        : prev.selectedDeviceIds.filter(id => id !== deviceId)
    }));
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    setScanState(prev => ({
      ...prev,
      selectedDeviceIds: checked 
        ? prev.availableDevices.map(device => device.id)
        : []
    }));
  };

  // 确认添加设备
  const handleConfirm = () => {
    const selectedDevices = scanState.availableDevices.filter(
      device => scanState.selectedDeviceIds.includes(device.id)
    );
    
    if (selectedDevices.length === 0) {
      message.warning(I18nUtils.getText('pleaseSelectDevices'));
      return;
    }

    onDevicesSelected(selectedDevices);
  };

  // 获取设备状态颜色
  const getStatusColor = (device: PhysicalDevice) => {
    if (device.status === I18nUtils.getText('unauthorized')) {
      return '#ff4d4f';
    }
    if (device.isSdkUnavailable) {
      return '#faad14';
    }
    return '#52c41a';
  };

  return (
    <Modal
      title={
        <div className="scanner-modal-title">
          <MobileOutlined />
          <span>{I18nUtils.getText('scanPhysicalDevices')}</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={1000}
      style={{ top: '20px' }}
      bodyStyle={{ padding: 0 }}
      footer={[
        <Button 
          key="rescan" 
          icon={<ReloadOutlined />} 
          onClick={scanDevices} 
          disabled={scanState.isScanning}
          className="scanner-button scanner-button-default"
        >
          {I18nUtils.getText('rescan')}
        </Button>,
        <Button 
          key="cancel" 
          onClick={onCancel}
          className="scanner-button scanner-button-default"
        >
          {I18nUtils.getText('cancel')}
        </Button>,
        <Button 
          key="confirm" 
          type="primary" 
          onClick={handleConfirm}
          disabled={scanState.selectedDeviceIds.length === 0}
          icon={<CheckCircleOutlined />}
          className="scanner-button scanner-button-primary"
        >
          {I18nUtils.getText('addSelectedDevices')}
        </Button>
      ]}
      className="device-scanner-modal"
    >
      <div className="scanner-content">
        <div className="scanner-info-banner">
          <InfoCircleOutlined className="info-icon" />
          <Text className="info-text">
            {I18nUtils.getText('scanDevicesDescription')}
          </Text>
        </div>

        <Spin spinning={scanState.isScanning} tip={I18nUtils.getText('scanningDevices')} className="scanner-spinner">
          {scanState.error ? (
            <div className="scanner-error-container">
              <WarningOutlined className="error-icon" />
              <Text type="danger" className="error-text">{scanState.error}</Text>
              <Button 
                type="primary" 
                onClick={scanDevices} 
                className="scanner-retry-button"
                icon={<ReloadOutlined />}
              >
                {I18nUtils.getText('retry')}
              </Button>
            </div>
          ) : scanState.availableDevices.length > 0 ? (
            <>
              <div className="scanner-header">
                <div className="scanner-select-all">
                  <Checkbox
                    checked={scanState.selectedDeviceIds.length === scanState.availableDevices.length}
                    indeterminate={scanState.selectedDeviceIds.length > 0 && scanState.selectedDeviceIds.length < scanState.availableDevices.length}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  >
                    {I18nUtils.getText('selectAll')}
                  </Checkbox>
                </div>
                <div className="scanner-stats">
                  <div className="stat-item">
                    <span className="stat-label">{I18nUtils.getText('foundDevices')}:</span>
                    <span className="stat-value found">{scanState.availableDevices.length}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">已选择:</span>
                    <span className="stat-value selected">{scanState.selectedDeviceIds.length}</span>
                  </div>
                </div>
              </div>

              <List
                dataSource={scanState.availableDevices}
                className="scanner-device-list"
                renderItem={(device) => (
                  <List.Item className="scanner-list-item">
                    <Card 
                      size="small" 
                      className={`scanner-device-card ${scanState.selectedDeviceIds.includes(device.id) ? 'selected' : ''}`}
                    >
                      <div className="scanner-device-content">
                        <Checkbox
                          checked={scanState.selectedDeviceIds.includes(device.id)}
                          onChange={(e) => handleDeviceSelect(device.id, e.target.checked)}
                          disabled={device.status === I18nUtils.getText('unauthorized')}
                          className="scanner-checkbox"
                        />
                        
                        <div className="scanner-device-icon" style={{ backgroundColor: getStatusColor(device) + '15', borderColor: getStatusColor(device) + '30' }}>
                          <MobileOutlined style={{ color: getStatusColor(device) }} />
                        </div>
                        
                        <div className="scanner-device-details">
                          {/* 设备名称和型号 */}
                          <div className="detail-column device-name">
                            <div className="device-title">
                              {`${device.brand.charAt(0).toUpperCase() + device.brand.slice(1)} ${device.model.charAt(0).toUpperCase() + device.model.slice(1)}`}
                            </div>
                            <div className="device-id">
                              {device.id}
                            </div>
                          </div>

                          {/* SDK版本 */}
                          <div className="detail-column">
                            <div className="detail-label">SDK版本</div>
                            <div className={`detail-value ${device.isSdkUnavailable ? 'error' : ''}`}>
                              {device.sdk || 'Unknown'}
                            </div>
                          </div>

                          {/* 连接状态 */}
                          <div className="detail-column">
                            <div className="detail-label">连接状态</div>
                            <div className="detail-value status">
                              <div className="status-indicator" style={{ backgroundColor: getStatusColor(device) }}></div>
                              <span style={{ color: getStatusColor(device) }}>
                                {device.status}
                              </span>
                            </div>
                          </div>

                          {/* 产品信息 */}
                          <div className="detail-column">
                            <div className="detail-label">产品型号</div>
                            <div className="detail-value">
                              {device.product}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* SDK警告信息 */}
                      {device.sdkStatus && (
                        <div className="scanner-warning-banner">
                          <div className="warning-indicator"></div>
                          <span className="warning-text">
                            {device.sdkStatus}
                          </span>
                        </div>
                      )}
                    </Card>
                  </List.Item>
                )}
              />
            </>
          ) : !scanState.isScanning ? (
            <div className="scanner-empty-container">
              <Empty 
                description={I18nUtils.getText('noDevicesFound')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
              <div className="empty-hint">
                <SearchOutlined className="search-icon" />
                <span>请确保设备已连接到电脑，并且已启用USB调试模式</span>
              </div>
            </div>
          ) : null}
        </Spin>
      </div>
    </Modal>
  );
};

export default PhysicalDeviceScanner; 