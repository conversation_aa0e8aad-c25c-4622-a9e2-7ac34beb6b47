import React, { useEffect, useState } from 'react';
import { Card, Button, Tag, message, Drawer, Tooltip, Input } from 'antd';
import { I18nUtils } from './languageSupport/i18nUtils';
import { useLanguage } from './languageSupport/LanguageContext';
import { PencilLine, FlaskConical, ChevronLeft, ChevronRight, RefreshCw, Home, HelpCircle } from 'lucide-react';
import { flaskApi } from '../../../services/FlaskApiService';
import { AnnotationModal } from './AnnotationModal';
import { TestModelModal } from './TestModelModal';
import { createRoot } from 'react-dom/client';
import '../styles/DeviceState.css';
import { ConfigurationGuide } from './RuyiClientGuide';

interface DeviceStateProps {
  deviceId: string;
  brand: string;
  productName: string;
  isConnected: boolean;
  isAuthorized: boolean;
  isInstalled: boolean;
  onSelectDevice: () => void;
  isBrowser?: boolean;
  browserState?: {
    isLoading: boolean;
    canGoBack: boolean;
    canGoForward: boolean;
    currentUrl: string;
    error: {
      errorCode: number;
      errorDescription: string;
    } | null;
  };
}

export const DeviceState: React.FC<DeviceStateProps> = ({
  productName,
  isConnected,
  isAuthorized,
  isInstalled,
  onSelectDevice,
  isBrowser = false,
  browserState
}) => {
  const { language } = useLanguage();
  const deviceTitle = productName || I18nUtils.getText('unknownModel');
  const [isTestModalVisible, setIsTestModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [url, setUrl] = useState('');
  const [browserHomepage, setBrowserHomepage] = useState('https://www.baidu.com');

  useEffect(() => {
    console.log('Language changed, updating DeviceState UI');
  }, [language]);

  useEffect(() => {
    if (browserState?.currentUrl) {
      setUrl(browserState.currentUrl);
    } else if (isBrowser && browserHomepage) {
      // If no current URL but we have a homepage config, use it as default
      setUrl(browserHomepage);
    }
  }, [browserState?.currentUrl, isBrowser, browserHomepage]);

  useEffect(() => {
    const loadBrowserHomepageConfig = async () => {
      try {
        const homepage = await window.electronAPI.getBrowserHomepageConfig();
        setBrowserHomepage(homepage || 'https://www.baidu.com');
      } catch (error) {
        console.warn('Failed to get browser homepage config, using default:', error);
        setBrowserHomepage('https://www.baidu.com');
      }
    };
    
    if (isBrowser) {
      loadBrowserHomepageConfig();
    }
  }, [isBrowser]);

  // 下载 VH 的处理函数
  const handleDownloadVH = async () => {
    try {
      const response = await flaskApi.getVH();
      const vh = response;
      
      // 创建 Blob 对象
      const blob = new Blob([JSON.stringify(vh, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      
      // 创建临时下载链接
      const link = document.createElement('a');
      link.href = url;
      link.download = `vh_${new Date().getTime()}.json`;
      document.body.appendChild(link);
      link.click();
      
      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      message.success(I18nUtils.getText('downloadVHSuccess'));
    } catch (error) {
      console.error('下载 VH 失败:', error);
      message.error(I18nUtils.getText('downloadVHFailed'));
    }
  };

  // 下载截图的处理函数
  const handleDownloadScreenshot = async () => {
    try {
      const response = await flaskApi.getScreenshot();
      const screenshot = response.screenshot;
      
      // Base64 字符串转换为 Blob
      const byteString = atob(screenshot.split(',')[1]);
      const mimeString = 'image/png';
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([ab], { type: mimeString });
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `screenshot_${new Date().getTime()}.png`;
      document.body.appendChild(link);
      link.click();
      
      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      message.success(I18nUtils.getText('downloadScreenshotSuccess'));
    } catch (error) {
      console.error('下载截图失败:', error);
      message.error(I18nUtils.getText('downloadScreenshotFailed'));
    }
  };

  // 添加新的处理函数
  const handleAnnotateScreenshot = async () => {
    try {
      // 显示加载中提示
      const loadingMessage = message.loading({
        content: I18nUtils.getText('loadingScreenshot'),
        duration: 0, // 不自动关闭
      });
      
      // 获取截图
      const response = await flaskApi.getScreenshot();
      const screenshot = response.screenshot;
      
      // 关闭加载提示
      loadingMessage();
      
      // 打开标注界面，传入截图数据
      const annotationResult = await openAnnotationModal(screenshot);
      
      if (annotationResult) {
        console.log('标注结果:', annotationResult);
        // 这里可以添加处理标注结果的逻辑
      }
    } catch (error) {
      // 确保出错时也关闭加载提示
      message.destroy();
      console.error('截图标注失败:', error);
      message.error(I18nUtils.getText('annotationFailed'));
    }
  };

  const openAnnotationModal = (screenshot: string): Promise<any> => {
    return new Promise((resolve) => {
      const div = document.createElement('div');
      document.body.appendChild(div);
      const root = createRoot(div);

      const destroy = () => {
        root.unmount();
        if (div.parentNode) {
          div.parentNode.removeChild(div);
        }
      };

      root.render(
        <AnnotationModal
          visible={true}
          screenshot={screenshot}
          onClose={() => {
            destroy();
            resolve(null);
          }}
          onSave={(annotations) => {
            destroy();
            resolve(annotations);
          }}
        />
      );
    });
  };

  const showTestModal = () => {
    setIsTestModalVisible(true);
  };

  const showGuide = () => {
    setDrawerVisible(true);
  };

  const needsHelp = !isBrowser && (!isConnected || !isAuthorized);

  // 添加浏览器控制函数
  const handleGoBack = async () => {
    await window.electronAPI.goBack();
  };

  const handleGoForward = async () => {
    await window.electronAPI.goForward();
  };

  const handleReload = async () => {
    await window.electronAPI.reload();
  };

  const handleGoHome = async () => {
    await window.electronAPI.loadURL(browserHomepage);
  };

  const handleUrlSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (url.trim()) {
      await window.electronAPI.loadURL(url.trim());
    }
  };

  return (
    <Card 
      className="device-state-card"
      title={deviceTitle}
      size="small"
      extra={
        <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
          {!needsHelp ? (
            <>
                <Button 
                  icon={<PencilLine size={16} />}
                  onClick={handleAnnotateScreenshot}
                  disabled={!isConnected}
                >
                  {I18nUtils.getText('manualPatch')}
                </Button>
                <Button 
                  icon={<FlaskConical size={16} />}
                  onClick={showTestModal}
                  disabled={!isConnected}
                >
                  {I18nUtils.getText('testModel')}
                </Button>
            </>
          ) : (
            <Button type="link" className="help-link" onClick={showGuide}>
              <HelpCircle size={16} style={{marginRight: 4}} />
              {I18nUtils.getText('howToConnect')}
            </Button>
          )}
            <Button 
              icon={<ChevronLeft size={16} />}
              onClick={onSelectDevice}
            >
              {I18nUtils.getText('switchDevice')}
            </Button>
        </div>
      }
    >
      {isBrowser ? (
        <div className="browser-controls">
          <Button 
            icon={<ChevronLeft size={16} />}
            onClick={handleGoBack}
            disabled={!browserState?.canGoBack}
          />
          <Button 
            icon={<ChevronRight size={16} />}
            onClick={handleGoForward}
            disabled={!browserState?.canGoForward}
          />
          <Button 
            icon={<RefreshCw size={16} />}
            onClick={handleReload}
            loading={browserState?.isLoading}
          />
                      <Button 
              icon={<Home size={16} />}
              onClick={handleGoHome}
              title="主页"
            />
          <form onSubmit={handleUrlSubmit} style={{ flex: 1 }}>
            <Input
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder={I18nUtils.getText('enterUrl')}
              style={{ width: '100%' }}
            />
          </form>
        </div>
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 6 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ fontSize: '13px', color: '#64748b' }}>{I18nUtils.getText('ruyiAssistantStatus')}:</span>
            <div style={{ display: 'flex', gap: 6, alignItems: 'center' }}>
              <Tag color={isInstalled ? 'green' : 'blue'} className="device-status-tag">
                {isInstalled ? I18nUtils.getText('installed') : I18nUtils.getText('notInstalled')}
              </Tag>
              <Tag color={isConnected ? 'green' : 'red'} className="device-status-tag">
                {isConnected ? I18nUtils.getText('connected') : I18nUtils.getText('disconnected')}
              </Tag>
              <Tag color={isAuthorized ? 'green' : 'orange'} className="device-status-tag">
                {isAuthorized ? I18nUtils.getText('authorized') : I18nUtils.getText('unauthorized')}
              </Tag>
            </div>
          </div>
        </div>
      )}

      <TestModelModal
        visible={isTestModalVisible}
        onClose={() => setIsTestModalVisible(false)}
      />

      <Drawer
        title={I18nUtils.getText('ruyiClientConfigTitle')}
        placement="left"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        mask={false}
        maskClosable={false}
        width={480}
        bodyStyle={{ padding: 0 }}
        className="device-connection-guide-drawer"
      >
        <ConfigurationGuide />
      </Drawer>
    </Card>
  );
};
