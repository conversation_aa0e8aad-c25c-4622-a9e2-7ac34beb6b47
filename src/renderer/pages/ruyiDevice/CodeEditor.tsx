import React, { useEffect, useRef, createContext, useState } from 'react';
import * as monaco from 'monaco-editor';
import { I18nUtils } from './languageSupport/i18nUtils';
import { ChatInterface } from './ChatInterface';
import { HelpCircle, Info, Play, Square } from 'lucide-react';
import { Tooltip, Modal, Button } from 'antd';
import { handleExecuteCode, handleStartDemo, handleEndDemo, handleStopExecute } from './Toolbar';
import { flaskApi } from '../../../services/FlaskApiService';
import { promptApi, repoApi, userApi } from '../../../services/api';
import { useLanguage } from './languageSupport/LanguageContext';
import { ToolbarState } from './Toolbar';
import { registerCompletionProvider, updateCompletionProviderLanguage } from './languageSupport/completionProvider';
import { registerCompletionProviderNL, updateCompletionProviderLanguageNL } from './languageSupport/completionProviderNL';
import '../styles/CodeEditor.css';
import '../styles/TaskDetailsModal.css';

// 工具函数：移除脚本中的标号
const removeLabelFromScript = (labeledScript: string): string => {
  if (!labeledScript) return '';
  
  return labeledScript
    .split('\n')
    .map(line => {
      // 移除行首的 [数字] 标号，但保留标号后的缩进空格 (NL script格式)
      const lineWithoutPrefixLabel = line.replace(/^\[\d+\] ?/, '');
      
      // 移除行尾的注释标号 # [数字] (Code script格式)
      const lineWithoutSuffixLabel = lineWithoutPrefixLabel.replace(/\s*#\s*\[\d+\]\s*$/, '');
      
      return lineWithoutSuffixLabel;
    })
    .join('\n');
};

// 工具函数：为脚本添加标号
const addLabelToScript = (script: string): string => {
  if (!script) return '';
  
  const lines = script.split('\n');
  let lineNumber = 1;
  
  return lines.map(line => {
    // 空行不添加标号
    if (line.trim() === '') {
      return line;
    }
    // 非空行添加标号
    return `[${lineNumber++}]${line}`;
  }).join('\n');
};

// 创建并导出 CodeEditor Context，用于在组件树中共享编辑器状态
export const CodeEditorContext = createContext<any>(null);

/**
 * CodeEditor 组件
 * 集成了 Monaco 编辑器，提供代码编辑和自然语言指令编辑功能
 * 包含了编辑器的状态管理和 Context Provider
 */
export const CodeEditor = () => {
  const { language } = useLanguage();
  // Context 状态定义
  const [monacoEditor, setMonacoEditor] = useState<monaco.editor.IStandaloneCodeEditor | null>(null);  // Monaco 编辑器实例
  const [decorationsCollection, setDecorationsCollection] = useState<monaco.editor.IEditorDecorationsCollection | null>(null);  // 编辑器装饰器集合
  const [highlightDecorationsCollection, setHighlightDecorationsCollection] = useState<monaco.editor.IEditorDecorationsCollection | null>(null);  // 执行高亮装饰器集合
  const [readonlyDecorationsCollection, setReadonlyDecorationsCollection] = useState<monaco.editor.IEditorDecorationsCollection | null>(null);  // 只读行装饰器集合
  const [scriptMode, setScriptMode] = useState<"NL" | "Code">("NL");  // 编辑器模式：自然语言(NL)或代码(Code)
  // const [originalNLScript, setOriginalNLScript] = useState("# Natural language instructions of task steps\n");  // 原始自然语言脚本
  const [originalNLScript, setOriginalNLScript] = useState("");  // 原始自然语言脚本
  // const [originalCodeScript, setOriginalCodeScript] = useState("device, ui, data, fm, tool = agent.device, agent.ui, agent.data, agent.fm, agent.tools\n");  // 原始代码脚本
  const [originalCodeScript, setOriginalCodeScript] = useState("");  // 原始代码脚本
  const [labeledNLScript, setLabeledNLScript] = useState("");  // 带标号的自然语言脚本
  const [labeledCodeScript, setLabeledCodeScript] = useState("");  // 带标号的代码脚本
  const [originalAppKnowledge, setOriginalAppKnowledge] = useState("");  // 原始应用知识库内容
  const [lastScript, setLastScript] = useState("");  // 最后编辑的脚本内容
  const [viewAppKnowledge, setViewAppKnowledge] = useState(false);  // 是否显示应用知识库
  const [viewProjectKnowledge, setViewProjectKnowledge] = useState(false);  // 是否显示项目知识库
  const [hasErrorHighlight, setHasErrorHighlight] = useState(false);  // 是否有错误高亮显示
  const [NLScriptVersion, setNLScriptVersion] = useState(0);  // NL脚本版本标志位，0或1
  const [CodeScriptVersion, setCodeScriptVersion] = useState(0);  // Code脚本版本标志位，0或1
  const [selectedRange, setSelectedRange] = useState<{
    position: { top: number; left: number };
    visible: boolean;
  }>({
    position: { top: 0, left: 0 },
    visible: false,
  });

  // 添加引用来检测文本是否被省略
  const mainTitleRef = useRef<HTMLDivElement>(null);
  const subTitleRef = useRef<HTMLDivElement>(null);
  const [mainTitleTruncated, setMainTitleTruncated] = useState(false);
  const [subTitleTruncated, setSubTitleTruncated] = useState(false);

  // 编辑器容器的 ref
  const editorRef = useRef<HTMLDivElement>(null);
  
  // 自动保存相关的状态
  const [userInfo, setUserInfo] = useState({
    username: '',
    displayName: '',
    email: '',
    githubId: '',
    wechatId: ''
  });
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);
  const autoSyncTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Task details modal state
  const [isTaskDetailsModalVisible, setIsTaskDetailsModalVisible] = useState(false);
  const [editingTaskName, setEditingTaskName] = useState('');
  const [editingTaskDescription, setEditingTaskDescription] = useState('');
  const [isSavingTaskDetails, setIsSavingTaskDetails] = useState(false);
  
  // Execution state
  const [isChangingScript, setIsChangingScript] = useState(false);

  // 包装执行函数，在执行前清除错误高亮
  const handleExecuteCodeWithClearError = (isSelectedCode: boolean) => {
    // 在执行前清除错误高亮
    CodeEditorUtils.clearErrorHighlight();
    // 调用原始执行函数
    handleExecuteCode(isSelectedCode);
  };

  // 包装停止执行函数，在停止时清除错误高亮
  const handleStopExecuteWithClearError = () => {
    // 在停止执行时清除错误高亮
    CodeEditorUtils.clearErrorHighlight();
    // 调用原始停止执行函数
    handleStopExecute();
  };

  // Handle opening task details modal
  const handleOpenTaskDetails = () => {
    setEditingTaskName(ChatInterface.task || '');
    setEditingTaskDescription(ChatInterface.taskDescription || '');
    setIsTaskDetailsModalVisible(true);
  };

  // Handle saving task details
  const handleSaveTaskDetails = async () => {
    if (!editingTaskName.trim()) {
      // Show error message
      return;
    }

    try {
      setIsSavingTaskDetails(true);
      
      const projectName = ChatInterface.projectName;
      const currentEditingFile = ChatInterface.currentEditingFile;
      
      if (!projectName || !currentEditingFile) {
        console.error('No project or file selected');
        return;
      }

      // Read current file content from backend
      const readResult = await window.electronAPI.readTaskFile({
        taskName: projectName,
        fileName: currentEditingFile
      });

      if (!readResult.success || readResult.content === undefined) {
        console.error('Failed to read current file:', readResult.message);
        return;
      }

      // Parse current JSON
      let taskData;
      try {
        taskData = JSON.parse(readResult.content);
      } catch (error) {
        console.error('Failed to parse current task file:', error);
        return;
      }

      // Update task name and description
      taskData.task_name = editingTaskName.trim();
      taskData.task_description = editingTaskDescription.trim();

      // Save updated content back to file
      const updatedContent = JSON.stringify(taskData, null, 2);
      const updateResult = await window.electronAPI.updateTaskFile({
        taskName: projectName,
        fileName: currentEditingFile,
        content: updatedContent
      });

      if (!updateResult.success) {
        console.error('Failed to update task file:', updateResult.message);
        return;
      }

      // Update ChatInterface state
      ChatInterface.setTask(editingTaskName.trim());
      ChatInterface.setTaskDescription(editingTaskDescription.trim());

      // Trigger git sync
      try {
        // Get repo token
        let repoToken = '';
        const tokenResponse = await repoApi.getRepoToken(projectName);
        if (tokenResponse && tokenResponse.token) {
          repoToken = tokenResponse.token;
        }

        if (repoToken) {
          // Trigger git sync
          const syncResult = await window.electronAPI.autoSaveAndSync({
            taskName: projectName,
            fileName: currentEditingFile,
            content: updatedContent,
            repoToken: repoToken,
            userInfo: userInfo
          });

          if (syncResult.success) {
            console.log('Task details synced to remote repository');
          } else {
            console.warn('Failed to sync task details to remote:', syncResult.message);
          }
        } else {
          console.warn('No repo token available, skipping git sync');
        }
      } catch (error) {
        console.warn('Git sync failed:', error);
      }

      // Trigger TaskFilesList refresh by dispatching a custom event
      window.dispatchEvent(new CustomEvent('taskDetailsUpdated', {
        detail: { projectName, fileName: currentEditingFile }
      }));

      // Close modal
      setIsTaskDetailsModalVisible(false);
      
      // Show success message
      console.log('Task details saved successfully');
    } catch (error) {
      console.error('Failed to save task details:', error);
    } finally {
      setIsSavingTaskDetails(false);
    }
  };

  /**
   * 初始化 Monaco 编辑器
   * 在组件挂载时创建编辑器实例并配置基本选项
   */
  useEffect(() => {
    if (!editorRef.current) return;

    // 创建 Monaco 编辑器实例
    const editor = monaco.editor.create(editorRef.current, {
      automaticLayout: true,  // 自动调整布局大小
      language: 'python',     // 设置编程语言为 Python
      value: scriptMode === "NL" ? originalNLScript : originalCodeScript,  // 根据当前模式设置初始内容
      wordWrap: 'off',        // 禁用自动换行，允许水平滚动
      theme: 'vs',           // 使用默认亮色主题
      minimap: { enabled: false },  // 禁用代码小地图
      scrollbar: {           // 配置滚动条样式
        vertical: 'visible', // 始终显示垂直滚动条
        horizontal: 'visible', // 始终显示水平滚动条
        useShadows: true,    // 启用滚动条阴影效果
        verticalScrollbarSize: 8,   // 减小垂直滚动条宽度
        horizontalScrollbarSize: 8, // 减小水平滚动条高度
        verticalSliderSize: 6,      // 垂直滑块大小
        horizontalSliderSize: 6,    // 水平滑块大小
        arrowSize: 0,               // 隐藏滚动箭头
        scrollByPage: false         // 禁用按页滚动
      },
      lineNumbers: 'on',     // 显示行号
      glyphMargin: false,    // 禁用装饰器边距，取消行号左侧的行符号
      fontSize: 13,          // 设置字体大小为13px
      fontFamily: 'HackNerdFont, MapleCNFont, Menlo, Monaco, Courier New, monospace',
      
      // 添加自动补全相关配置
      suggestOnTriggerCharacters: true, // 在触发字符时显示建议
      quickSuggestions: {
        other: true,
        comments: true,
        strings: true
      },
      parameterHints: {
        enabled: true
      },
      
      // 滚动行为优化
      scrollBeyondLastLine: false,  // 禁止滚动到最后一行之后
      scrollBeyondLastColumn: 0,    // 禁止滚动到最后一列之后
      smoothScrolling: true,        // 启用平滑滚动
    });

    // 更新状态
    setMonacoEditor(editor);
    setDecorationsCollection(editor.createDecorationsCollection());
    setHighlightDecorationsCollection(editor.createDecorationsCollection());
    setReadonlyDecorationsCollection(editor.createDecorationsCollection());

    // 添加只读行保护监听器
    editor.onDidChangeModelContent((e) => {
      const readonlyLines = (editor as any)._readonlyLines || [];
      if (readonlyLines.length === 0) return;

      // 检查变更是否影响只读行
      for (const change of e.changes) {
        const startLine = change.range.startLineNumber;
        const endLine = change.range.endLineNumber;
        
        // 如果变更影响只读行，则撤销变更
        for (let line = startLine; line <= endLine; line++) {
          if (readonlyLines.includes(line)) {
            // 延迟恢复，避免无限循环
            setTimeout(() => {
              const model = editor.getModel();
              if (model) {
                const lineContent = model.getLineContent(line);
                                 const expectedContent = lineContent.includes('设备信息') ? '# 设备信息' : '# 操作指南';
                if (lineContent !== expectedContent) {
                  editor.executeEdits('readonly-protection', [{
                    range: new monaco.Range(line, 1, line, lineContent.length + 1),
                    text: expectedContent,
                    forceMoveMarkers: true
                  }]);
                }
              }
            }, 0);
            break;
          }
        }
      }
    });

    // 添加键盘事件监听，阻止在只读行的输入
    editor.onKeyDown((e) => {
      const readonlyLines = (editor as any)._readonlyLines || [];
      if (readonlyLines.length === 0) return;

      const position = editor.getPosition();
      if (position && readonlyLines.includes(position.lineNumber)) {
        // 阻止所有可能改变内容的键盘事件
        if (e.keyCode !== monaco.KeyCode.UpArrow &&
            e.keyCode !== monaco.KeyCode.DownArrow &&
            e.keyCode !== monaco.KeyCode.LeftArrow &&
            e.keyCode !== monaco.KeyCode.RightArrow &&
            e.keyCode !== monaco.KeyCode.Home &&
            e.keyCode !== monaco.KeyCode.End &&
            e.keyCode !== monaco.KeyCode.PageUp &&
            e.keyCode !== monaco.KeyCode.PageDown &&
            e.keyCode !== monaco.KeyCode.Tab) {
          e.preventDefault();
          e.stopPropagation();
        }
      }
    });

    // 组件卸载时清理编辑器实例
    return () => {
      editor.dispose();
    };
  }, []);

  // 获取用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const response = await userApi.getUserInfo();
        const userInfo = response.data;
        setUserInfo({
          username: userInfo.username,
          displayName: userInfo.display_name,
          email: userInfo.email,
          githubId: userInfo.github_id,
          wechatId: userInfo.wechat_id
        });
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    };
    fetchUserInfo();
  }, []);

  // 监听 ToolbarState.isChangingScript 的变化
  useEffect(() => {
    // 初始化状态
    setIsChangingScript(ToolbarState.isChangingScript);

    // 设置轮询检查状态变化
    const pollInterval = setInterval(() => {
      setIsChangingScript(ToolbarState.isChangingScript);
    }, 100); // 每100ms检查一次状态变化

    return () => {
      clearInterval(pollInterval);
    };
  }, []);

  // 获取当前 App 语言设置并注册/更新补全提供者
  useEffect(() => {
    let disposable: monaco.IDisposable;

    const initializeCompletionProvider = async () => {
      try {
        const appLanguage = await window.electronAPI.getAppLanguageConfig();
        // 如果已经存在 disposable，先清理掉
        if (disposable) {
          disposable.dispose();
        }
        // 根据 scriptMode 注册不同的补全提供者
        if (scriptMode === "NL") {
          disposable = registerCompletionProviderNL(appLanguage);
        } else {
          disposable = registerCompletionProvider(appLanguage);
        }
      } catch (error) {
        console.error('Failed to initialize completion provider:', error);
      }
    };

    initializeCompletionProvider();

    // 监听 App Language 变化事件
    const handleAppLanguageChange = (event: CustomEvent) => {
      const newLanguage = event.detail;
      if (disposable) {
        disposable.dispose();
      }
      // 根据 scriptMode 注册不同的补全提供者
      if (scriptMode === "NL") {
        disposable = registerCompletionProviderNL(newLanguage);
      } else {
        disposable = registerCompletionProvider(newLanguage);
      }
    };

    // 添加事件监听器
    window.addEventListener('appLanguageChanged', handleAppLanguageChange as EventListener);

    // 清理函数
    return () => {
      if (disposable) {
        disposable.dispose();
      }
      window.removeEventListener('appLanguageChanged', handleAppLanguageChange as EventListener);
    };
  }, [scriptMode]); // 添加 scriptMode 作为依赖项

  // 1. 先声明 contextValue
  /**
   * 创建 Context 值
   * 包含所有需要共享的状态和设置函数，使用 useMemo 优化性能
   */
  const contextValue = React.useMemo(() => ({
    monacoEditor,
    setMonacoEditor,
    decorationsCollection, 
    setDecorationsCollection,
    highlightDecorationsCollection,
    setHighlightDecorationsCollection,
    readonlyDecorationsCollection,
    setReadonlyDecorationsCollection,
    scriptMode,
    setScriptMode,
    originalNLScript,
    setOriginalNLScript,
    originalCodeScript,
    setOriginalCodeScript,
    labeledNLScript,
    setLabeledNLScript,
    labeledCodeScript,
    setLabeledCodeScript,
    originalAppKnowledge,
    setOriginalAppKnowledge,
    lastScript,
    setLastScript,
    viewAppKnowledge,
    setViewAppKnowledge,
    viewProjectKnowledge,
    setViewProjectKnowledge,
    hasErrorHighlight,
    setHasErrorHighlight,
    NLScriptVersion,
    setNLScriptVersion,
    CodeScriptVersion,
    setCodeScriptVersion
  }), [
    monacoEditor,
    decorationsCollection,
    highlightDecorationsCollection,
    readonlyDecorationsCollection,
    scriptMode,
    originalNLScript,
    originalCodeScript,
    labeledNLScript,
    labeledCodeScript,
    originalAppKnowledge,
    lastScript,
    viewAppKnowledge,
    viewProjectKnowledge,
    hasErrorHighlight,
    NLScriptVersion,
    CodeScriptVersion
  ]);

  // 2. 再使用 contextValue 的 useEffect
  /**
   * 监听编辑器内容变化，更新装饰器
   * 当编辑器内容发生变化时，重新计算并更新行执行按钮装饰器
   */
  useEffect(() => {
    if (!monacoEditor) return;
    
    // 确保全局 context 先设置
    (window as any).__codeEditorContext = contextValue;
    
    // 初始化装饰器（此时全局 context 已正确设置）
    CodeEditorUtils.updateLineExecuteDecorations();

    // 监听编辑器内容变化，更新装饰器
    const disposable = monacoEditor.onDidChangeModelContent(() => {
      CodeEditorUtils.updateLineExecuteDecorations();
      // 当用户修改编辑器内容时，清除错误高亮
      if (hasErrorHighlight) {
        CodeEditorUtils.clearErrorHighlight();
      }
      
      // 更新只读行装饰器
      CodeEditorUtils.updateReadonlyLineDecorations();
      
      // 检查是否应该设置版本标志位
      // 跳过以下情况：
      // 1. 正在查看知识库
      // 2. 正在转换脚本
      // 3. 编辑器内容变化是由于模式切换导致的（内容与对应模式的原始脚本相同）
      if (!viewAppKnowledge && !viewProjectKnowledge && !isChangingScript) {
        const currentContent = monacoEditor.getValue() || "";
        const expectedContent = scriptMode === "NL" ? originalNLScript : originalCodeScript;
        
        // 只有当前内容与预期内容不同时，才认为是用户手动编辑
        if (currentContent !== expectedContent) {
          // 根据当前脚本模式设置相应的版本标志位
          if (scriptMode === "NL") {
            CodeEditorUtils.NLScriptVersion = 1;
            CodeEditorUtils.CodeScriptVersion = 0;
          } else if (scriptMode === "Code") {
            CodeEditorUtils.CodeScriptVersion = 1;
            CodeEditorUtils.NLScriptVersion = 0;
          }
        }
      }
    });

    return () => {
      disposable.dispose();
    };
  }, [monacoEditor, contextValue, hasErrorHighlight, scriptMode, originalNLScript, originalCodeScript, viewAppKnowledge, viewProjectKnowledge, isChangingScript]);

  /**
   * 监听 scriptMode, 脚本, 自然语言指令, 知识库 的变化，更新编辑器的内容
   */
  useEffect(() => {
    if (!monacoEditor) return;

    let targetContent = '';
    
    if (viewAppKnowledge || viewProjectKnowledge) {
      targetContent = originalAppKnowledge;
    } else if (scriptMode === "NL") {
      targetContent = originalNLScript;
    } else {
      targetContent = originalCodeScript;
    }

    updateEditorContentSafely(monacoEditor, targetContent, 'updateContent');
  }, [scriptMode, originalNLScript, originalCodeScript, monacoEditor, originalAppKnowledge, viewAppKnowledge, viewProjectKnowledge]);

  useEffect(() => {
    console.log('CodeEditor: UseEffect Script Mode:', scriptMode);
  }, [scriptMode]);

  // 处理选中代码的函数
  const handleEditorSelectionChange = () => {
    if (!monacoEditor || viewAppKnowledge) return;

    const selection = monacoEditor.getSelection();
    if (!selection || selection.isEmpty()) {
      setSelectedRange({ ...selectedRange, visible: false });
      return;
    }

    // 获取选中文本的坐标
    const selectedText = monacoEditor.getModel()?.getValueInRange(selection);
    if (!selectedText) return;

    // 获取选中区域的位置信息 - 使用相对于视口的位置
    const coordinates = monacoEditor.getScrolledVisiblePosition(
      selection.getStartPosition()
    );

    if (coordinates) {
      // 获取编辑器DOM元素的位置
      const editorElement = monacoEditor.getDomNode();
      if (!editorElement) return;

      const editorRect = editorElement.getBoundingClientRect();
      
      // 计算相对于视口的正确位置
      // coordinates.top 和 coordinates.left 是相对于编辑器内容区域的位置
      // 需要加上编辑器容器的位置偏移
      const top = coordinates.top + editorRect.top;
      const left = coordinates.left + editorRect.left;
      
      // 确保悬浮窗不会超出视口边界
      const buttonHeight = 40; // 按钮高度
      const buttonWidth = 120; // 按钮宽度
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      
      // 调整垂直位置，确保按钮完全可见
      let adjustedTop = top - buttonHeight - 10; // 在选中文本上方显示
      if (adjustedTop < 0) {
        adjustedTop = top + 20; // 如果上方空间不够，显示在下方
      }
      if (adjustedTop + buttonHeight > viewportHeight) {
        adjustedTop = viewportHeight - buttonHeight - 10; // 确保不超出底部
      }
      
      // 调整水平位置，确保按钮完全可见
      let adjustedLeft = left;
      if (adjustedLeft + buttonWidth > viewportWidth) {
        adjustedLeft = viewportWidth - buttonWidth - 10; // 确保不超出右边界
      }
      if (adjustedLeft < 0) {
        adjustedLeft = 10; // 确保不超出左边界
      }
      
      setSelectedRange({
        position: {
          top: adjustedTop,
          left: adjustedLeft,
        },
        visible: true,
      });
    }
  };

  useEffect(() => {
    if (!monacoEditor) return;

    // 添加选中文本变化的监听器
    const disposable = monacoEditor.onDidChangeCursorSelection(handleEditorSelectionChange);

    return () => {
      disposable.dispose();
    };
  }, [monacoEditor]);

  // 监听代码执行高亮事件
  useEffect(() => {
    const handleHighlightExecutingLine = (data: { labeled_code_script: string; labeled_NL_script: string; error?: boolean }) => {
      CodeEditorUtils.highlightExecutingLine(data.labeled_code_script, data.labeled_NL_script, data.error);
    };

    const handleClearExecutingLineHighlight = () => {
      CodeEditorUtils.clearExecutingLineHighlight();
    };

    window.electronAPI.onHighlightExecutingLine(handleHighlightExecutingLine);
    window.electronAPI.onClearExecutingLineHighlight(handleClearExecutingLineHighlight);

    return () => {
      window.electronAPI.offHighlightExecutingLine();
      window.electronAPI.offClearExecutingLineHighlight();
    };
  }, []);

  // 自动保存功能
  useEffect(() => {
    if (!monacoEditor) return;

    // 本地保存函数
    const localSave = async (content: string) => {
      // 检查是否正在转换脚本，如果是则跳过自动保存
      if (ToolbarState.isChangingScript) {
        console.log('[LocalSave] 正在转换脚本中，跳过自动保存');
        return;
      }

      const projectName = ChatInterface.projectName;
      const currentEditingFile = ChatInterface.currentEditingFile;
      
      // 检查是否有项目和文件
      if (!projectName || !currentEditingFile) {
        return;
      }

      // 如果是知识库文件且内容未变，则跳过保存
      if (currentEditingFile === 'Knowledge.json') {
        if (content === CodeEditorUtils.originalAppKnowledge) {
          return; // 内容无变化，不执行任何操作
        }
      }

      // 通知开始保存
      CodeEditorUtils.notifyAutoSaveStatus('saving');

            // 检查是否是知识库文件，如果是，特殊处理
      if (currentEditingFile === 'Knowledge.json') {
        try {
          // 保存知识库文件到本地
          const result = await window.electronAPI.updateKnowledgeFile({
            taskName: projectName,
            knowledgeContent: content
          });
          
          if (result.success) {
            console.log('[LocalSave] 知识库文件已保存到本地');
            // 触发Git同步防抖
            scheduleGitSync(projectName, currentEditingFile, content);
          } else {
            CodeEditorUtils.notifyAutoSaveStatus('error');
            setTimeout(() => {
              CodeEditorUtils.notifyAutoSaveStatus('idle');
            }, 3000);
          }
        } catch (error) {
          console.error('[LocalSave] 保存知识库文件失败:', error);
          CodeEditorUtils.notifyAutoSaveStatus('error');
          setTimeout(() => {
            CodeEditorUtils.notifyAutoSaveStatus('idle');
          }, 3000);
        }
        return;
      }

      // 检查是否是任务JSON文件，如果是，需要更新其中的脚本内容
      let finalContent = content;
      if (currentEditingFile.endsWith('.json') && currentEditingFile !== 'Knowledge.json') {
        try {
          // 读取现有的JSON文件
          const readResult = await window.electronAPI.readTaskFile({
            taskName: projectName,
            fileName: currentEditingFile
          });
          
          if (readResult.success && readResult.content) {
            const taskData = JSON.parse(readResult.content);
            
            // 获取最新的NL和Code脚本以及版本标志位
            const newNLScript = CodeEditorUtils.getNLScript() || "";
            const newCodeScript = CodeEditorUtils.getCodeScript() || "";
            const newLabeledNLScript = CodeEditorUtils.labeledNLScript || "";
            const newLabeledCodeScript = CodeEditorUtils.labeledCodeScript || "";
            const newNLVersion = CodeEditorUtils.NLScriptVersion;
            const newCodeVersion = CodeEditorUtils.CodeScriptVersion;

            // 如果脚本和版本标志位都没有变化，则跳过保存
            if (taskData.NL_script === newNLScript && taskData.code_script === newCodeScript && 
                taskData.NL_script_labeled === newLabeledNLScript && taskData.code_script_labeled === newLabeledCodeScript &&
                (taskData.NL_script_version || 0) === newNLVersion && (taskData.code_script_version || 0) === newCodeVersion) {
              CodeEditorUtils.notifyAutoSaveStatus('idle');
              return;
            }
            
            // 更新对应的脚本字段
            taskData.NL_script = newNLScript;
            taskData.code_script = newCodeScript;
            // 同时更新带标号的版本
            taskData.NL_script_labeled = newLabeledNLScript;
            taskData.code_script_labeled = newLabeledCodeScript;
            // 更新版本标志位
            taskData.NL_script_version = newNLVersion;
            taskData.code_script_version = newCodeVersion;
            
            // 保存更新后的JSON文件
            finalContent = JSON.stringify(taskData, null, 2);
          }
        } catch (error) {
          console.warn('[LocalSave] 解析任务JSON文件失败，将保存原始内容:', error);
        }
      }

      try {
        // 仅保存到本地文件
        const result = await window.electronAPI.updateTaskFile({
          taskName: projectName,
          fileName: currentEditingFile,
          content: finalContent
        });
        
        if (result.success) {
          console.log(`[LocalSave] ${currentEditingFile} 已保存到本地`);
          // 触发Git同步防抖
          scheduleGitSync(projectName, currentEditingFile, finalContent);
        } else {
          console.warn(`[LocalSave] ${currentEditingFile} 本地保存失败:`, result.message);
          CodeEditorUtils.notifyAutoSaveStatus('error');
          setTimeout(() => {
            CodeEditorUtils.notifyAutoSaveStatus('idle');
          }, 3000);
        }
      } catch (error) {
        console.error('[LocalSave] 本地保存失败:', error);
        CodeEditorUtils.notifyAutoSaveStatus('error');
        setTimeout(() => {
          CodeEditorUtils.notifyAutoSaveStatus('idle');
        }, 3000);
      }
    };

    // Git同步调度函数（带防抖）
    const scheduleGitSync = async (projectName: string, fileName: string, content: string) => {
      // 清除之前的Git同步定时器
      if (autoSyncTimerRef.current) {
        clearTimeout(autoSyncTimerRef.current);
      }

      // 设置新的Git同步定时器（3秒防抖）
      autoSyncTimerRef.current = setTimeout(async () => {
        try {
          // 通知开始同步
          CodeEditorUtils.notifyAutoSaveStatus('syncing');
          
          // 获取repo token
          let repoToken = '';
          const tokenResponse = await repoApi.getRepoToken(projectName);
          if (tokenResponse && tokenResponse.token) {
            repoToken = tokenResponse.token;
          } else {
            console.warn('[GitSync] 无法获取repo token，跳过Git同步');
            CodeEditorUtils.notifyAutoSaveStatus('idle');
            return;
          }
          
          // 调用Git同步API（不再重新保存本地文件）
          const result = await window.electronAPI.autoSaveAndSync({
            taskName: projectName,
            fileName: fileName,
            content: content,
            repoToken: repoToken,
            userInfo: userInfo
          });
          
          if (result.success) {
            console.log(`[GitSync] ${fileName} 已同步到云端`);
            CodeEditorUtils.notifyAutoSaveStatus('completed');
            setTimeout(() => {
              CodeEditorUtils.notifyAutoSaveStatus('idle');
            }, 2000);
          } else {
            console.warn(`[GitSync] ${fileName} 同步失败:`, result.message);
            CodeEditorUtils.notifyAutoSaveStatus('error');
            setTimeout(() => {
              CodeEditorUtils.notifyAutoSaveStatus('idle');
            }, 3000);
          }
        } catch (error) {
          console.error('[GitSync] Git同步失败:', error);
          CodeEditorUtils.notifyAutoSaveStatus('error');
          setTimeout(() => {
            CodeEditorUtils.notifyAutoSaveStatus('idle');
          }, 3000);
        }
      }, 3000); // 3秒防抖
    };

    // 监听编辑器内容变化
    const disposable = monacoEditor.onDidChangeModelContent(() => {
      // 清除之前的定时器
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }

      // 设置新的定时器，防抖1秒
      autoSaveTimerRef.current = setTimeout(() => {
        const content = monacoEditor.getValue();
        localSave(content);
      }, 1000); // 1秒防抖
    });

    // 从外部触发自动保存的实现
    CodeEditorUtils._triggerAutoSave = async () => {
      if (monacoEditor) {
        const content = monacoEditor.getValue();
        await localSave(content);
      }
    };

    return () => {
      disposable.dispose();
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }
      if (autoSyncTimerRef.current) {
        clearTimeout(autoSyncTimerRef.current);
      }
      CodeEditorUtils._triggerAutoSave = async () => {}; // 清理
    };
  }, [monacoEditor, userInfo]);

  // 监听编辑文件变化，清除自动保存和Git同步定时器
  useEffect(() => {
    const handleEditingFileChange = (fileName: string) => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
        autoSaveTimerRef.current = null;
      }
      if (autoSyncTimerRef.current) {
        clearTimeout(autoSyncTimerRef.current);
        autoSyncTimerRef.current = null;
      }
    };

    ChatInterface.setOnEditingFileChangeCallback(handleEditingFileChange);

    return () => {
      ChatInterface.setOnEditingFileChangeCallback(undefined);
    };
  }, []);

  // 在 useEffect 中监听 language 变化
  useEffect(() => {
    // 更新编辑器标题等需要翻译的内容
    console.log('Language changed, updating CodeEditor UI');
  }, [language]);

  // 添加检测文本是否被省略的效果
  useEffect(() => {
    const checkIfTruncated = () => {
      // 对主标题的检测
      if (mainTitleRef.current) {
        // 使用更可靠的方法检测文本是否被截断
        const element = mainTitleRef.current;
        
        // 创建一个临时的克隆元素，用于计算完整文本的尺寸
        const clone = element.cloneNode(true) as HTMLElement;
        clone.style.position = 'absolute';
        clone.style.visibility = 'hidden';
        clone.style.height = 'auto';
        clone.style.width = element.clientWidth + 'px';
        clone.style.whiteSpace = 'normal';
        // 移除 -webkit-line-clamp 限制
        clone.style.webkitLineClamp = 'unset';
        clone.style.display = 'block';
        
        // 移除可能的省略号元素
        const ellipsis = clone.querySelector('.ellipsis');
        if (ellipsis) {
          ellipsis.remove();
        }
        
        document.body.appendChild(clone);
        
        // 检查是否有溢出，并且文本长度超过一定阈值才显示省略号
        const hasOverflow = clone.scrollHeight > element.clientHeight;
        const textLongEnough = ChatInterface.task ? ChatInterface.task.length > 30 : false;
        const isOverflowing = hasOverflow && textLongEnough;
        
        // 清理克隆元素
        document.body.removeChild(clone);
        
        // 设置主标题是否被截断的状态
        setMainTitleTruncated(isOverflowing);
        
        // 调试信息
        console.log('Main title check:', {
          originalHeight: element.clientHeight,
          fullTextHeight: clone.scrollHeight,
          textLength: ChatInterface.task?.length,
          isOverflowing
        });
      }
      
      // 对副标题的检测
      if (subTitleRef.current) {
        // 使用更可靠的方法检测文本是否被截断
        const element = subTitleRef.current;
        
        // 创建一个临时的克隆元素，用于计算完整文本的尺寸
        const clone = element.cloneNode(true) as HTMLElement;
        clone.style.position = 'absolute';
        clone.style.visibility = 'hidden';
        clone.style.height = 'auto';
        clone.style.width = element.clientWidth + 'px';
        clone.style.whiteSpace = 'normal';
        // 移除 -webkit-line-clamp 限制
        clone.style.webkitLineClamp = 'unset';
        clone.style.display = 'block';
        
        // 移除可能的省略号元素
        const ellipsis = clone.querySelector('.ellipsis');
        if (ellipsis) {
          ellipsis.remove();
        }
        
        document.body.appendChild(clone);
        
        // 检查是否有溢出，并且文本长度超过一定阈值才显示省略号
        const hasOverflow = clone.scrollHeight > element.clientHeight;
        const textLongEnough = ChatInterface.taskDescription ? ChatInterface.taskDescription.length > 60 : false;
        const isOverflowing = hasOverflow && textLongEnough;
        
        // 清理克隆元素
        document.body.removeChild(clone);
        
        // 设置副标题是否被截断的状态
        setSubTitleTruncated(isOverflowing);
        
        // 调试信息
        console.log('Sub title check:', {
          originalHeight: element.clientHeight,
          fullTextHeight: clone.scrollHeight,
          textLength: ChatInterface.taskDescription?.length,
          isOverflowing
        });
      }
    };

    // 初始检测需要等待渲染完成，使用多个时间点检测以确保准确性
    const timers = [
      setTimeout(checkIfTruncated, 0),
      setTimeout(checkIfTruncated, 100),
      setTimeout(checkIfTruncated, 500)
    ];
    
    // 添加窗口大小变化的监听器，以便在调整大小时重新检查
    window.addEventListener('resize', checkIfTruncated);
    
    // 在组件卸载时清理
    return () => {
      timers.forEach(timer => clearTimeout(timer));
      window.removeEventListener('resize', checkIfTruncated);
    };
  }, [ChatInterface.task, ChatInterface.taskDescription, scriptMode, language]);

  // 渲染编辑器组件
  return (
    <>
      <>
        <div className="code-editor-title">
          {/* 左侧执行按钮，仅在 code/NL 模式下显示 */}
          {!viewAppKnowledge && !viewProjectKnowledge ? (
            <div className="execution-buttons">
              <Tooltip title={I18nUtils.getText('execute')}>
                <Button
                  icon={<Play size={14} />}
                  onClick={() => handleExecuteCodeWithClearError(false)}
                  disabled={isChangingScript}
                  className="editor-execute-button"
                  size="small"
                />
              </Tooltip>
              <Tooltip title={I18nUtils.getText('stopExecute')}>
                <Button
                  icon={<Square size={14} />}
                  onClick={handleStopExecuteWithClearError}
                  className="editor-stop-button"
                  size="small"
                />
              </Tooltip>
            </div>
          ) : (
            <div className="execution-buttons-placeholder"></div>
          )}

          {/* 中央标题区域 */}
          <div className="code-editor-title-wrapper">
            {viewAppKnowledge || viewProjectKnowledge ? (
              <div className="code-editor-knowledge-title-center">
                <span style={{ fontWeight: 600, fontSize: 16, color: '#262626', letterSpacing: 0.5 }}>
                  {I18nUtils.getText(viewAppKnowledge ? 'appKnowledgeTitle' : 'projectKnowledgeTitle')}
                </span>
              </div>
            ) : (
              ChatInterface.task ? (
                <div className="code-editor-title-content" style={{ padding: '8px 0' }}>
                  <div className="code-editor-title-row">
                    <span className="code-editor-script-type">
                      {scriptMode === "Code" ? (
                        <>
                          <svg viewBox="0 0 24 24" width="18" height="18" stroke="#262626" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '4px' }}>
                            <path d="M8 3L2 12L8 21"></path>
                            <path d="M16 3L22 12L16 21"></path>
                          </svg>
                          <span style={{ color: '#262626' }}>{I18nUtils.getText('codeScriptTitle')}</span>
                        </>
                      ) : (
                        <>
                          <svg viewBox="0 0 24 24" width="18" height="18" stroke="#262626" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '4px' }}>
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14 2 14 8 20 8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10 9 9 9 8 9"></polyline>
                          </svg>
                          <span style={{ color: '#262626' }}>{I18nUtils.getText('NLInstructionTitle')}</span>
                        </>
                      )}
                      <span style={{ margin: '0 4px' }}>-</span>
                      <span style={{ minHeight: '24px', padding: '4px 0' }}>{ChatInterface.task}</span>
                    </span>
                  </div>
                </div>
              ) : (
                <div className="code-editor-no-task-title">
                  {scriptMode === "Code" ? (
                    <>
                      <svg viewBox="0 0 24 24" width="20" height="20" stroke="#262626" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '6px' }}>
                        <path d="M8 3L2 12L8 21"></path>
                        <path d="M16 3L22 12L16 21"></path>
                      </svg>
                      <span className="script-type-part" style={{ color: '#262626' }}>{I18nUtils.getText('codeScriptTitle')}</span>
                      <span className="separator-part" style={{ color: '#262626' }}>&nbsp;-&nbsp;</span>
                      <span className="editor-title-part" style={{ color: '#262626' }}>{I18nUtils.getText('editorTitle')}</span>
                    </>
                  ) : (
                    <>
                      <svg viewBox="0 0 24 24" width="20" height="20" stroke="#262626" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '6px' }}>
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                      </svg>
                      <span className="script-type-part" style={{ color: '#262626' }}>{I18nUtils.getText('NLInstructionTitle')}</span>
                      <span className="separator-part" style={{ color: '#262626' }}>&nbsp;-&nbsp;</span>
                      <span className="editor-title-part" style={{ color: '#262626' }}>{I18nUtils.getText('editorTitle')}</span>
                    </>
                  )}
                </div>
              )
            )}
          </div>

          {/* 右侧帮助图标，仅在 code/NL 模式下显示 */}
          {!viewAppKnowledge && !viewProjectKnowledge ? (
            <div className="help-icon-container">
              {/* Task details button - only show when editing a task */}
              {ChatInterface.task && (
                <Tooltip
                  placement="bottomRight"
                  title={I18nUtils.getText('viewTaskDetails')}
                >
                  <Info 
                    style={{ 
                      fontSize: 20, 
                      color: '#8c8c8c', 
                      cursor: 'pointer',
                      marginRight: 8
                    }} 
                    className="help-icon-hover"
                    onClick={handleOpenTaskDetails}
                  />
                </Tooltip>
              )}
              <Tooltip
                placement="bottomRight"
                color="#fff"
                overlayClassName="editor-usage-tip-tooltip"
                title={
                  <div>
                    <div className="tip-title">
                      <HelpCircle className="tip-icon" />
                      {I18nUtils.getText('editorUsageTitle')}
                    </div>
                    <div className="tip-content">
                      {(() => {
                        const tip = I18nUtils.getText(scriptMode === "Code" ? 'editorUsageTipCode' : 'editorUsageTipNL');
                        // 按换行分组
                        const lines = tip.split(/\n|\n/).filter(Boolean);
                        // 以冒号分组
                        type Section = { title: string; content: string[] };
                        const sections: Section[] = [];
                        let current: Section | null = null;
                        lines.forEach((line: string) => {
                          // 修改正则表达式，只匹配以冒号或中文冒号结尾的行作为标题
                          const match = line.match(/^(.*?)([：:])$/);
                          if (match) {
                            if (current) sections.push(current);
                            current = { title: match[1], content: [] };
                          } else if (current) {
                            current.content.push(line);
                          }
                        });
                        if (current) sections.push(current);
                        return sections.map((sec: Section, idx: number) => (
                          <div className="tip-section" key={idx}>
                            <div className="tip-section-title">
                              {sec.title}
                            </div>
                            <div className="tip-section-content">
                              <ul style={{ margin: 0, padding: 0 }}>
                                {sec.content.map((c: string, i: number) => (
                                  <li key={i} dangerouslySetInnerHTML={{ 
                                    __html: c.replace(/^-\s+/, '')
                                           .replace(/`([^`]+)`/g, '<code>$1</code>')
                                           .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                                  }} />
                                ))}
                              </ul>
                            </div>
                          </div>
                        ));
                      })()}
                    </div>
                  </div>
                }
              >
                <HelpCircle style={{ fontSize: 20, color: '#8c8c8c', cursor: 'pointer' }} className="help-icon-hover" />
              </Tooltip>
            </div>
          ) : (
            <div className="help-icon-container">
              <Tooltip 
                title={
                  <div className="tooltip-container">
                    <h4 className="tooltip-title">
                      {I18nUtils.getText('appKnowledgeFormatTitle')}
                    </h4>
                    <div className="tooltip-desc">
                      {I18nUtils.getText('appKnowledgeFormatDesc')}
                    </div>
                    <div>
                      <div className="tooltip-example-title">
                        {I18nUtils.getText('appKnowledgeExample')}
                      </div>
                      <pre className="tooltip-example-content">
                        {I18nUtils.getText('appKnowledgeExampleContent')}
                      </pre>
                    </div>
                  </div>
                }
                placement="right"
                color="#fff"
                overlayStyle={{ maxWidth: '500px' }}
                overlayInnerStyle={{ padding: 0 }}
              >
                <HelpCircle 
                  style={{ 
                    fontSize: 20,
                    cursor: 'pointer',
                    color: '#8c8c8c',
                    transition: 'color 0.3s',
                    display: 'flex',
                    alignItems: 'center'
                  }}
                  className="help-icon-hover" 
                />
              </Tooltip>
            </div>
          )}
        </div>
        <div ref={editorRef} className="monaco-editor-wrapper" />
      </>
      {selectedRange.visible && !viewAppKnowledge && !viewProjectKnowledge && (
        <div
          className="selected-code-actions"
          style={{
            position: 'fixed',
            top: `${selectedRange.position.top - 10}px`,
            left: `${selectedRange.position.left - 100}px`,
          }}
        >
          <button
            className="selected-code-button"
            onClick={() => handleExecuteCodeWithClearError(true)}
          >
            {I18nUtils.getText('executeSelectedCode')}
          </button>
          {/* <button
            className="selected-code-button"
            onClick={handleStartDemo}
          >
            {I18nUtils.getText('demoSelectedCode')}
          </button> */}
        </div>
      )}
      {/* Task Details Modal */}
      <Modal
        title={
          <div className="task-modal-title">
            <Info size={18} />
            {I18nUtils.getText('editTaskDetails')}
          </div>
        }
        open={isTaskDetailsModalVisible}
        onCancel={() => setIsTaskDetailsModalVisible(false)}
        footer={[
          <button
            key="cancel"
            onClick={() => setIsTaskDetailsModalVisible(false)}
            className="task-modal-button task-modal-button-default"
          >
            {I18nUtils.getText('cancel')}
          </button>,
          <button
            key="save"
            onClick={handleSaveTaskDetails}
            disabled={!editingTaskName.trim() || isSavingTaskDetails}
            className="task-modal-button task-modal-button-primary"
          >
            {isSavingTaskDetails ? I18nUtils.getText('saving') : I18nUtils.getText('save')}
          </button>
        ]}
        width={600}
        className="task-details-modal"
      >
        <div className={`task-form-content ${isSavingTaskDetails ? 'task-form-loading' : ''}`}>
          {/* Info Banner */}
          <div className="task-info-banner">
            <Info className="task-info-icon" size={16} />
            <span className="task-info-text">
              {I18nUtils.getText('taskDetailsInfo')}
            </span>
          </div>
          
          {/* Task Name Section */}
          <div className="task-form-section">
            <label className="task-form-label required">
              {I18nUtils.getText('taskName')}
            </label>
            <input
              type="text"
              value={editingTaskName}
              onChange={(e) => setEditingTaskName(e.target.value)}
              placeholder={I18nUtils.getText('taskNamePlaceholder')}
              className="task-form-input"
            />
          </div>
          
          {/* Task Description Section */}
          <div className="task-form-section">
            <label className="task-form-label">
              {I18nUtils.getText('taskDescription')}
            </label>
            <textarea
              value={editingTaskDescription}
              onChange={(e) => setEditingTaskDescription(e.target.value)}
              placeholder={I18nUtils.getText('taskDescriptionPlaceholder')}
              className="task-form-textarea"
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

/**
 * 获取全局存储的 CodeEditor Context
 * @returns CodeEditor Context 对象
 */
const getContext = () => {
  return (window as any).__codeEditorContext;
};

/**
 * CodeEditorUtils
 * 提供编辑器相关的工具函数和状态管理
 */
/**
 * 安全地更新Monaco编辑器内容，保持撤销历史
 * @param monacoEditor Monaco编辑器实例
 * @param newContent 要设置的新内容
 * @param editId 编辑操作的ID，用于标识操作
 */
const updateEditorContentSafely = (
  monacoEditor: monaco.editor.IStandaloneCodeEditor,
  newContent: string,
  editId: string = 'updateContent'
) => {
  const currentValue = monacoEditor.getValue();
  
  // 如果内容相同，则不需要更新
  if (currentValue === newContent) return;
  
  // 使用 executeEdits 替换全部内容，这样可以保持撤销历史
  const model = monacoEditor.getModel();
  if (model) {
    const fullRange = model.getFullModelRange();
    monacoEditor.executeEdits(editId, [{
      range: fullRange,
      text: newContent,
      forceMoveMarkers: true
    }]);
  } else {
    // 如果没有model，则回退到setValue
    monacoEditor.setValue(newContent);
  }
};

export const CodeEditorUtils = {
  /**
   * 自动保存状态回调函数
   */
  _autoSaveStatusCallback: undefined as ((status: 'idle' | 'saving' | 'syncing' | 'completed' | 'error') => void) | undefined,
  /**
   * 用于从外部触发自动保存的内部函数
   * @private
   */
  _triggerAutoSave: async () => {},
  /**
   * 获取当前脚本模式
   * @returns 当前脚本模式 ("NL" | "Code")
   */
  get scriptMode(): "NL" | "Code" {
    const context = getContext();
    return context?.scriptMode || "NL";
  },

  /**
   * 获取原始自然语言脚本
   * @returns 原始自然语言脚本内容
   */
  get originalNLScript(): string {
    const context = getContext();
    // return context?.originalNLScript || "# Natural language instructions of task steps\n";
    return context?.originalNLScript || "";
  },

  /**
   * 获取原始代码脚本
   * @returns 原始代码脚本内容
   */
  get originalCodeScript(): string {
    const context = getContext();
    // return context?.originalCodeScript || "device, ui, data, fm, tool = agent.device, agent.ui, agent.data, agent.fm, agent.tools\n";
    return context?.originalCodeScript || "";
  },

  /**
   * 获取原始应用知识库内容
   * @returns 原始应用知识库内容
   */
  get originalAppKnowledge(): string {
    const context = getContext();
    return context?.originalAppKnowledge || "";
  },

  /**
   * 获取带标号的自然语言脚本
   * @returns 带标号的自然语言脚本内容
   */
  get labeledNLScript(): string {
    const context = getContext();
    return context?.labeledNLScript || "";
  },

  /**
   * 获取带标号的代码脚本
   * @returns 带标号的代码脚本内容
   */
  get labeledCodeScript(): string {
    const context = getContext();
    return context?.labeledCodeScript || "";
  },

  /**
   * 获取最后编辑的脚本内容
   * @returns 最后编辑的脚本内容
   */
  get lastScript(): string {
    const context = getContext();
    return context?.lastScript || "";
  },

  /**
   * 获取是否显示应用知识库状态
   * @returns 是否显示应用知识库
   */
  get viewAppKnowledge(): boolean {
    const context = getContext();
    return context?.viewAppKnowledge || false;
  },

  /**
   * 获取是否显示项目知识库状态
   * @returns 是否显示项目知识库
   */
  get viewProjectKnowledge(): boolean {
    const context = getContext();
    return context?.viewProjectKnowledge || false;
  },

  /**
   * 获取NL脚本版本标志位
   * @returns NL脚本版本标志位 (0或1)
   */
  get NLScriptVersion(): number {
    const context = getContext();
    return context?.NLScriptVersion || 0;
  },

  /**
   * 获取Code脚本版本标志位
   * @returns Code脚本版本标志位 (0或1)
   */
  get CodeScriptVersion(): number {
    const context = getContext();
    return context?.CodeScriptVersion || 0;
  },

  /**
   * 设置脚本模式
   * @param value 要设置的脚本模式 ("NL" | "Code")
   */
  set scriptMode(value: "NL" | "Code") {
    const context = getContext();
    if (context?.setScriptMode) {
      context.setScriptMode(value);
    }
  },

  /**
   * 设置原始自然语言脚本
   * @param value 要设置的脚本内容
   */
  set originalNLScript(value: string) {
    const context = getContext();
    if (context?.setOriginalNLScript) {
      context.setOriginalNLScript(value);
    }
  },

  /**
   * 设置原始代码脚本
   * @param value 要设置的脚本内容
   */
  set originalCodeScript(value: string) {
    const context = getContext();
    if (context?.setOriginalCodeScript) {
      context.setOriginalCodeScript(value);
    }
  },

  /**
   * 设置原始应用知识库内容
   * @param value 要设置的知识库内容
   */
  set originalAppKnowledge(value: string) {
    const context = getContext();
    if (context?.setOriginalAppKnowledge) {
      context.setOriginalAppKnowledge(value);
    }
  },

  /**
   * 设置最后编辑的脚本内容
   * @param value 要设置的脚本内容
   */
  set lastScript(value: string) {
    const context = getContext();
    if (context?.setLastScript) {
      context.setLastScript(value);
    }
  },

  /**
   * 设置是否显示应用知识库
   * @param value 是否显示知识库
   */
  set viewAppKnowledge(value: boolean) {
    const context = getContext();
    if (context?.setViewAppKnowledge) {
      context.setViewAppKnowledge(value);
    }
  },

  /**
   * 设置是否显示项目知识库
   * @param value 是否显示项目知识库
   */
  set viewProjectKnowledge(value: boolean) {
    const context = getContext();
    if (context?.setViewProjectKnowledge) {
      context.setViewProjectKnowledge(value);
    }
  },

  /**
   * 设置NL脚本版本标志位
   * @param value NL脚本版本标志位 (0或1)
   */
  set NLScriptVersion(value: number) {
    const context = getContext();
    if (context?.setNLScriptVersion) {
      context.setNLScriptVersion(value);
    }
  },

  /**
   * 设置Code脚本版本标志位
   * @param value Code脚本版本标志位 (0或1)
   */
  set CodeScriptVersion(value: number) {
    const context = getContext();
    if (context?.setCodeScriptVersion) {
      context.setCodeScriptVersion(value);
    }
  },

  /**
   * 重置版本标志位（在调用转换 API 时使用）
   */
  resetVersionFlags(): void {
    this.NLScriptVersion = 0;
    this.CodeScriptVersion = 0;
  },

  /**
   * 设置版本标志位
   * @param NLVersion NL脚本版本标志位
   * @param codeVersion Code脚本版本标志位
   */
  setVersionFlags(NLVersion: number, codeVersion: number): void {
    this.NLScriptVersion = NLVersion;
    this.CodeScriptVersion = codeVersion;
  },

  /**
   * 设置带标号的自然语言脚本
   * @param value 要设置的带标号脚本内容
   */
  set labeledNLScript(value: string) {
    const context = getContext();
    if (context?.setLabeledNLScript) {
      context.setLabeledNLScript(value);
    }
  },

  /**
   * 设置带标号的代码脚本
   * @param value 要设置的带标号脚本内容
   */
  set labeledCodeScript(value: string) {
    const context = getContext();
    if (context?.setLabeledCodeScript) {
      context.setLabeledCodeScript(value);
    }
  },


  /**
   * 获取最新的脚本内容
   * 如果在查看应用知识库，则返回最后编辑的脚本
   * @returns 当前脚本内容
   */
  getScript(): string | undefined {
    const context = getContext();
    if (!context) return undefined;

    const { monacoEditor } = context;
    // 如果正在查看知识库，返回最后编辑的脚本内容
    if (this.viewAppKnowledge || this.viewProjectKnowledge) return this.lastScript;
    // 否则返回编辑器当前内容
    return monacoEditor?.getValue();
  },

  /**
   * 设置编辑器中的代码内容
   * @param code 要设置的代码内容
   */
  setScript(code: string): void {
    const context = getContext();
    if (!context) return;

    const { monacoEditor } = context;
    if (!monacoEditor) return;
    
    updateEditorContentSafely(monacoEditor, code, 'setScript');
  },

  /**
   * 获取当前最新的代码脚本
   * @returns 代码脚本内容
   */
  getCodeScript(): string | undefined {
    const context = getContext();
    if (!context) return undefined;

    // 如果正在查看应用知识库，返回保存的原始代码脚本
    if (this.viewAppKnowledge || this.viewProjectKnowledge) {
      return this.originalCodeScript;
    }

    const { scriptMode, monacoEditor } = context;
    // 如果当前是代码模式，返回编辑器当前内容
    if (scriptMode === "Code") {
      return monacoEditor?.getValue();
    }
    // 否则返回保存的原始代码脚本
    return this.originalCodeScript;
  },

  /**
   * 获取当前最新的自然语言脚本
   * @returns 自然语言脚本内容
   */
  getNLScript(): string | undefined {
    const context = getContext();
    if (!context) return undefined;

    // 如果正在查看应用知识库，返回保存的原始自然语言脚本
    if (this.viewAppKnowledge || this.viewProjectKnowledge) {
      return this.originalNLScript;
    }
  
    const { scriptMode, monacoEditor } = context;
    // 如果当前是自然语言模式，返回编辑器当前内容
    if (scriptMode === "NL") {
      return monacoEditor?.getValue();
    }
    // 否则返回保存的原始自然语言脚本
    return this.originalNLScript;
  },

  /**
   * 设置自然语言脚本
   * 同时更新编辑器内容（如果当前处于 NL 模式）
   * @param code 要设置的自然语言脚本
   * @param updateVersion 是否更新版本标志位，默认为false（用于程序内部同步时不更新版本）
   */
  setNLScript(code: string, updateVersion: boolean = false): void {
    const context = getContext();
    if (!context) return;

    // 更新原始自然语言脚本
    this.originalNLScript = code;
    
    // 如果需要更新版本标志位，则设置NL脚本版本为1，Code脚本版本为0
    if (updateVersion) {
      this.NLScriptVersion = 1;
      this.CodeScriptVersion = 0;
    }
    
    // 如果当前是自然语言模式且不在查看知识库，更新编辑器内容（移动到钩子函数中处理）
    // const { scriptMode, monacoEditor } = context;
    // if (!this.viewAppKnowledge && scriptMode === "NL") {
    //   monacoEditor?.setValue(code);
    // }
  },

  /**
   * 设置代码脚本
   * 同时更新编辑器内容（如果当前处于 Code 模式）
   * @param code 要设置的代码脚本
   * @param updateVersion 是否更新版本标志位，默认为false（用于程序内部同步时不更新版本）
   */
  setCodeScript(code: string, updateVersion: boolean = false): void {
    const context = getContext();
    if (!context) return;

    // 更新原始代码脚本
    this.originalCodeScript = code;
    
    // 如果需要更新版本标志位，则设置Code脚本版本为1，NL脚本版本为0
    if (updateVersion) {
      this.CodeScriptVersion = 1;
      this.NLScriptVersion = 0;
    }
    
    // 如果当前是代码模式且不在查看知识库，更新编辑器内容（移动到钩子函数中处理）
    // const { scriptMode, monacoEditor } = context;
    // if (!this.viewAppKnowledge && scriptMode === "Code") {
    //   monacoEditor?.setValue(code);
    // }
  },

  /**
   * 设置带标号的自然语言脚本和去标号的自然语言脚本
   * @param labeledScript 带标号的脚本
   * @param cleanScript 去标号的脚本（可选，如果不提供将自动去除标号）
   */
  setLabeledNLScript(labeledScript: string, cleanScript?: string): void {
    const context = getContext();
    if (!context) return;

    // 保存带标号版本
    this.labeledNLScript = labeledScript;
    
    // 设置去标号版本
    const clean = cleanScript || removeLabelFromScript(labeledScript);
    this.setNLScript(clean);
  },

  /**
   * 设置带标号的代码脚本和去标号的代码脚本
   * @param labeledScript 带标号的脚本
   * @param cleanScript 去标号的脚本（可选，如果不提供将自动去除标号）
   */
  setLabeledCodeScript(labeledScript: string, cleanScript?: string): void {
    const context = getContext();
    if (!context) return;

    // 保存带标号版本
    this.labeledCodeScript = labeledScript;
    
    // 设置去标号版本
    const clean = cleanScript || removeLabelFromScript(labeledScript);
    this.setCodeScript(clean);
  },

  /**
   * 获取编辑器中选中的代码
   * @returns 选中的代码内容
   */
  getSelectedCode(): string | undefined {
    const context = getContext();
    if (!context) return undefined;

    const { monacoEditor } = context;
    if (!monacoEditor) return undefined;

    // 获取所有选中区域
    const selections = monacoEditor.getSelections();
    let selectedCode = '';
    if (selections) {
      // 遍历所有选中区域，拼接选中的代码
      for (const selection of selections) {
        const model = monacoEditor.getModel();
        if (model) {
          const range = new monaco.Range(
            selection.startLineNumber,
            selection.startColumn,
            selection.endLineNumber,
            selection.endColumn
          );
          selectedCode += model.getValueInRange(range) + '\n';
        }
      }
    }
    return selectedCode.trim();
  },

  /**
   * 检查用户是否修改了脚本内容
   * 通过规范化处理后比较当前内容和原始内容
   * @returns 是否有修改
   */
  checkScriptUpdate(): boolean {
    const context = getContext();
    if (!context) return false;

    const { monacoEditor, scriptMode, originalNLScript, originalCodeScript, viewAppKnowledge } = context;
    
    // 如果正在查看应用知识库，不认为脚本有更新
    // if (viewAppKnowledge) return false;

    if (!monacoEditor) return false;

    // 获取当前脚本和原始脚本
    // 如果当前在查看应用知识库，那么 currentScript = lastScript
    // 否则 currentScript = monacoEditor.getValue()
    const currentScript = viewAppKnowledge ? this.lastScript : monacoEditor.getValue() || "";
    const originalScript = scriptMode === "Code" ? originalCodeScript : originalNLScript;

    // 规范化处理函数：去除多余空白字符并转为小写
    const normalizeScript = (script: string): string => {
      if (scriptMode === "Code") {
        return script.trim();
      }
      return script
        .replace(/\s+/g, ' ')  // 将多个空白字符替换为单个空格
        .trim()  // 去除首尾空白
        .toLowerCase();  // 转换为小写以忽略大小写差异
    };

    // 比较规范化后的脚本
    return normalizeScript(currentScript) !== normalizeScript(originalScript);
  },

  /**
   * 去掉字符串开头和结尾的 ``` 包裹
   * @param content 要处理的字符串
   * @returns 去掉 ``` 包裹后的字符串
   */
  removeCodeBlockWrappers(content: string): string {
    if (!content) return content;
    
    const lines = content.split('\n');
    
    // 检查是否第一行和最后一行都是 ```
    if (lines.length >= 2 && 
        lines[0].trim().startsWith('```') && 
        lines[lines.length - 1].trim() === '```') {
      // 去掉首尾两行
      return lines.slice(1, -1).join('\n');
    }
    
    return content;
  },

  /**
   * 解析 transferWorkflowToCode API 返回的字符串
   */
  parseWorkflowToCodeResponse(response: string): { thought: string; labeled_python_script: string } {
    const thoughtMatch = response.match(/<thought>([\s\S]*?)<\/thought>/);
    const scriptMatch = response.match(/<labeled_python_script>([\s\S]*?)<\/labeled_python_script>/);
    
    let labeledPythonScript = scriptMatch ? scriptMatch[1].trim() : '';
    // 去掉可能的 ``` 包裹
    labeledPythonScript = this.removeCodeBlockWrappers(labeledPythonScript);
    
    return {
      thought: thoughtMatch ? thoughtMatch[1].trim() : '',
      labeled_python_script: labeledPythonScript
    };
  },

  /**
   * 解析 transferCodeToWorkflow API 返回的字符串
   */
  parseCodeToWorkflowResponse(response: string): { thought: string; labeled_workflow: string; labeled_python_script: string } {
    const thoughtMatch = response.match(/<thought>([\s\S]*?)<\/thought>/);
    const workflowMatch = response.match(/<labeled_workflow>([\s\S]*?)<\/labeled_workflow>/);
    const scriptMatch = response.match(/<labeled_python_script>([\s\S]*?)<\/labeled_python_script>/);
    
    let labeledWorkflow = workflowMatch ? workflowMatch[1].trim() : '';
    let labeledPythonScript = scriptMatch ? scriptMatch[1].trim() : '';
    
    // 去掉可能的 ``` 包裹
    labeledWorkflow = this.removeCodeBlockWrappers(labeledWorkflow);
    labeledPythonScript = this.removeCodeBlockWrappers(labeledPythonScript);
    
    return {
      thought: thoughtMatch ? thoughtMatch[1].trim() : '',
      labeled_workflow: labeledWorkflow,
      labeled_python_script: labeledPythonScript
    };
  },

  /**
   * 执行前检查脚本更新
   * 根据版本标志位决定是否需要同步更新对应的另一种模式的脚本
   */
  async updateScriptBeforeExecute(): Promise<void> {
    const context = getContext();
    if (!context) return;

    // 在执行前清除错误高亮
    this.clearErrorHighlight();

    const { scriptMode, monacoEditor, viewAppKnowledge } = context;
    if (!monacoEditor) return;

    // 保存当前的选择状态
    const currentSelections = monacoEditor.getSelections();
    
    let loadingMsgId: string = '';

    try {
      if (scriptMode === "Code") {
        // 检查Code脚本版本标志位，如果为0则不需要同步
        if (this.CodeScriptVersion === 0) return;
        
        // 在更新后恢复选择状态
        if (currentSelections && currentSelections.length > 0) {
          setTimeout(() => {
            monacoEditor.setSelections(currentSelections);
          }, 0);
        }
        
        // 设置 isChangingScript 为 true
        ToolbarState.updateChangingScriptState(true);

        // 添加加载中消息
        loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('transferringScriptFromScriptToNL'));
        const currentCodeScript = viewAppKnowledge ? this.lastScript : monacoEditor.getValue() || "";

        const appLanguage = await window.electronAPI.getAppLanguageConfig();
        
        const response = await promptApi.transferCodeToWorkflow(
          ChatInterface.taskDescription || ChatInterface.task,
          this.labeledNLScript,
          this.labeledCodeScript,
          currentCodeScript,
          appLanguage
        );
        console.log('CodeEditor: transferCodeToWorkflow response:', response);

        const parsedResponse = this.parseCodeToWorkflowResponse(response);

        // 更新原始代码脚本和自然语言脚本，但不更新版本标志位
        this.originalCodeScript = currentCodeScript;
        this.setLabeledNLScript(parsedResponse.labeled_workflow);
        this.setLabeledCodeScript(parsedResponse.labeled_python_script);
        // 重置版本标志位
        this.resetVersionFlags();

        // 更新完成消息
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('transferScriptFromScriptToNLComplete'));
      } else {
        // 处理自然语言模式下的脚本更新
        // 检查NL脚本版本标志位，如果为0则不需要同步
        if (this.NLScriptVersion === 0) return;

        // 设置 isChangingScript 为 true
        ToolbarState.updateChangingScriptState(true);

        // 添加加载中消息
        loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('transferringScriptFromNLtoScript'));
        
        const currentNLScript = viewAppKnowledge ? this.lastScript : monacoEditor.getValue() || "";
        // 为当前NL脚本添加标号
        const currentLabeledWorkflow = addLabelToScript(currentNLScript);
        
        // 确保第三个参数（oldLabeledWorkflow）也是带标号的版本
        const oldLabeledWorkflow = this.labeledNLScript || addLabelToScript(this.originalNLScript);

        const appLanguage = await window.electronAPI.getAppLanguageConfig();

        const response = await promptApi.transferWorkflowToCode(
          ChatInterface.taskDescription || ChatInterface.task,
          this.labeledCodeScript,
          oldLabeledWorkflow,
          currentLabeledWorkflow,
          appLanguage
        );

        const parsedResponse = this.parseWorkflowToCodeResponse(response);

        // 更新原始自然语言脚本和代码脚本，但不更新版本标志位
        this.originalNLScript = currentNLScript;
        this.setLabeledCodeScript(parsedResponse.labeled_python_script);
        this.setLabeledNLScript(currentLabeledWorkflow); // 使用带标号的当前工作流
        // 重置版本标志位
        this.resetVersionFlags();
        // 延迟触发自动保存，避免与转换过程冲突
        setTimeout(() => {
          this.triggerAutoSave();
        }, 2000);

        // 更新完成消息
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('transferScriptFromNLtoScriptComplete'));
      }
    } catch (error) {
      console.error('[CodeEditor] updateScriptBeforeExecute error:', error);
      if (error instanceof Error && error.message === 'Insufficient Token') {
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('insufficientTokenUpdateScript'), true);
      } else {
        if (loadingMsgId) {
          ChatInterface.updateToCompleteMessage(
          loadingMsgId, 
          I18nUtils.getText(scriptMode === "Code" ? 'transferScriptFromScriptToNLError' : 'transferScriptFromNLtoScriptError'),
          true
          );
        }
      }
      throw error;
    } finally {
      // 无论成功或失败，最终都将 isChangingScript 设置为 false
      ToolbarState.updateChangingScriptState(false);
    }

  },

  /**
   * 强制触发一次自动保存
   */
  async triggerAutoSave(): Promise<void> {
    await this._triggerAutoSave();
  },

  /**
   * 获取最新的 App Knowledge
   * @returns 最新的 App Knowledge
   */
  getAppKnowledge(): string | undefined {
    const context = getContext();
    if (!context) return undefined;

    // 如果正在查看应用知识库，返回编辑器中最新的 App Knowledge
    const { monacoEditor } = context;
    if (this.viewAppKnowledge || this.viewProjectKnowledge) {
      return monacoEditor?.getValue();
    }
    // 否则返回保存的原始 App Knowledge
    return this.originalAppKnowledge;
  },

  /**
   * 设置 App Knowledge
   * @param appKnowledge 要设置的 App Knowledge
   */
  setAppKnowledge(appKnowledge: string): void {
    this.originalAppKnowledge = appKnowledge;
  },

  /**
   * 显示应用知识库
   * 保存当前脚本并切换到知识库视图
   */
  showAppKnowledge(): void {
    console.log('CodeEditor: Show App Knowledge');
    const context = getContext();
    if (!context) return;

    const { monacoEditor, setViewAppKnowledge } = context;
    if (!monacoEditor) return;

    // 保存当前脚本内容
    this.lastScript = monacoEditor.getValue() || "";
    console.log('CodeEditor: lastScript:', this.lastScript);
    this.viewAppKnowledge = true;
    this.viewProjectKnowledge = false; // 确保项目知识库视图关闭
    // 禁用装饰器边距
    monacoEditor.updateOptions({ glyphMargin: false });
    // 显示知识库内容
    monacoEditor.setValue(this.originalAppKnowledge);
    
    // 通知其他组件正在查看系统提示词
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('systemPromptViewChange', { detail: { isViewing: true } }));
    }
    // setViewAppKnowledge(true);
  },

  /**
   * 显示项目知识库
   * 保存当前脚本并切换到项目知识库视图
   */
  showProjectKnowledge(knowledgeContent?: string): void {
    console.log('CodeEditor: Show Project Knowledge');
    const context = getContext();
    if (!context) return;

    const { monacoEditor } = context;
    if (!monacoEditor) return;

    // 保存当前脚本内容
    this.lastScript = monacoEditor.getValue() || "";
    console.log('CodeEditor: lastScript:', this.lastScript);
    this.viewProjectKnowledge = true;
    this.viewAppKnowledge = false; // 确保应用知识库视图关闭
    // 禁用装饰器边距
    monacoEditor.updateOptions({ glyphMargin: false });
    // 显示知识库内容
    monacoEditor.setValue(knowledgeContent !== undefined ? knowledgeContent : this.originalAppKnowledge);
    
    // 延迟更新只读行装饰，确保内容已经渲染
    setTimeout(() => {
      this.updateReadonlyLineDecorations();
    }, 100);
    
    // 通知其他组件正在查看系统提示词
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('systemPromptViewChange', { detail: { isViewing: true } }));
    }
  },

  /**
   * 隐藏应用知识库
   * 恢复到之前的脚本状态
   * @param restoreToLastMode 是否恢复到最后的编辑模式
   */
  // async hideAppKnowledge(restoreToLastMode: boolean = true): Promise<void> {
  async hideAppKnowledge(): Promise<void> {
    console.log('CodeEditor: Hide App Knowledge');
    const context = getContext();
    if (!context) return;

    const { monacoEditor, setViewAppKnowledge, scriptMode, setScriptMode } = context;
    if (!monacoEditor) return;
    
    // 检查并更新知识库内容
    this.checkAndUpdateAppKnowledge();

    // 在隐藏知识库时清除错误高亮
    this.clearErrorHighlight();

    // 恢复到之前的脚本内容
    console.log('CodeEditor: Restore to Last Script:', this.lastScript);
    setTimeout(() => {
      updateEditorContentSafely(monacoEditor, this.lastScript, 'restoreScript');
      // 清除只读行装饰
      this.updateReadonlyLineDecorations();
      // 重置只读行数组，防止后续编辑时仍将知识库标题行视为只读
      (monacoEditor as any)._readonlyLines = [];
    }, 0);

    // 更新视图状态
    this.viewAppKnowledge = false;
    this.viewProjectKnowledge = false;
    // setViewAppKnowledge(false);
    monacoEditor.updateOptions({ glyphMargin: false });
    
    // 通知其他组件已退出系统提示词查看
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('systemPromptViewChange', { detail: { isViewing: false } }));
    }
  },

  /**
   * 仅隐藏知识库视图，不恢复之前的脚本内容
   * 用于加载新任务时切换视图
   */
  async hideKnowledgeViewOnly(): Promise<void> {
    console.log('CodeEditor: Hide Knowledge View Only');
    const context = getContext();
    if (!context) return;

    const { monacoEditor } = context;
    if (!monacoEditor) return;
    
    // 检查并更新知识库内容
    this.checkAndUpdateAppKnowledge();

    // 在隐藏知识库时清除错误高亮
    this.clearErrorHighlight();

    // 更新视图状态
    this.viewAppKnowledge = false;
    this.viewProjectKnowledge = false;
    monacoEditor.updateOptions({ glyphMargin: false });
    
    // 清除只读行装饰
    this.updateReadonlyLineDecorations();
    
    // 重置只读行数组，防止后续编辑时仍将知识库标题行视为只读
    (monacoEditor as any)._readonlyLines = [];
    
    // 通知其他组件已退出系统提示词查看
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('systemPromptViewChange', { detail: { isViewing: false } }));
    }
  },

  /**
   * 检查知识库是否有修改
   * 如果有修改则更新到后端
   */
  checkAndUpdateAppKnowledge(): void {
    const context = getContext();
    if (!context) return;

    const { viewAppKnowledge, viewProjectKnowledge, monacoEditor } = context;
    if ((!viewAppKnowledge && !viewProjectKnowledge) || !monacoEditor) return;

    // 获取当前知识库内容
    const currentAppKnowledge = monacoEditor.getValue() || "";

    // 规范化处理函数
    const normalizeAppKnowledge = (knowledge: string): string => {
      return knowledge
        .replace(/\s+/g, ' ')
        .replace(/^\s+|\s+$/g, '')
        .toLowerCase();
    };

    // 检查是否有实质性修改
    if (normalizeAppKnowledge(currentAppKnowledge) !== normalizeAppKnowledge(this.originalAppKnowledge)) {
      // 更新原始知识库内容, 在 React 中是异步的, 因此不能直接将 this.originalAppKnowledge 发送到后端
      this.originalAppKnowledge = currentAppKnowledge;

      // 如果是项目知识库，需要保存到Knowledge.json文件
      if (viewProjectKnowledge) {
        this.saveProjectKnowledge(currentAppKnowledge);
      }

      // 直接传递最新的知识库内容到后端 -> 知识库不需要 flask 后端保存了
      // this.sendAppKnowledgeToBackend(currentAppKnowledge);
    }
  },

  /**
   * 保存项目知识库到Knowledge.json文件
   * @param knowledgeContent 知识库内容
   */
  async saveProjectKnowledge(knowledgeContent: string): Promise<void> {
    try {
      // 获取当前项目名称，需要从ChatInterface或其他地方获取
      const currentProject = ChatInterface.projectName; // 假设有这个属性
      if (!currentProject) {
        console.warn('当前未选择项目，无法保存项目知识库');
        return;
      }

      const result = await window.electronAPI.updateKnowledgeFile({
        taskName: currentProject,
        knowledgeContent: knowledgeContent
      });

      if (result.success) {
        console.log('项目知识库保存成功');
      } else {
        console.error('项目知识库保存失败:', result.message);
      }
    } catch (error) {
      console.error('保存项目知识库时出错:', error);
    }
  },

  /**
   * 将知识库内容更新到后端 
   * -> 现在不需要了，因为 flask 后端不需要用来生成 script 了
   * @param appKnowledge 最新的知识库内容
   */
  async sendAppKnowledgeToBackend(appKnowledge: string): Promise<void> {
    try {
      // const response = await fetch("http://127.0.0.1:5000/set_app_knowledge", {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Accept': '*/*'
      //   },
      //   body: JSON.stringify({
      //     appKnowledge: appKnowledge
      //   })
      // });

      const response = await flaskApi.setAppKnowledge(appKnowledge);

      if (!response.ok) {
        throw new Error('请求失败，状态码: ' + response.status);
      }
    } catch (error) {
      console.error('发送知识库到后端时出错:', error);
      // 根据需要，可以在这里添加错误处理逻辑，例如通知用户
    }
  },

  /**
   * 切换编辑器模式（Code <--> NL）
   * 根据版本标志位决定是否需要同步更新另一种模式的脚本
   */
  async changeScriptLanguage(): Promise<void> {
    const context = getContext();
    if (!context) return;

    // 在切换脚本模式前清除错误高亮
    this.clearErrorHighlight();

    const { scriptMode, setScriptMode, monacoEditor, originalNLScript, originalCodeScript } = context;
    if (!monacoEditor) return;

    let loadingMsgId: string = '';

    try {
      if (scriptMode === "Code") {
        // 从代码模式切换到自然语言模式
        // 检查Code脚本版本标志位，如果为1则需要转换
        if (this.CodeScriptVersion === 0) {
          // 如果Code脚本版本为0，直接切换
          console.log('CodeEditor: Change Script Language From Code To NL: No Code Update');
          setScriptMode("NL");
          return;
        }

        // 添加加载中消息
        loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('transferringScriptFromScriptToNL'));

        // 设置转换状态
        ToolbarState.updateChangingScriptState(true);

        const appLanguage = await window.electronAPI.getAppLanguageConfig();
        console.log('[CodeEditor] transferCodeToWorkflow originalCodeScript:', this.originalCodeScript);
        const response = await promptApi.transferCodeToWorkflow(
          ChatInterface.taskDescription || ChatInterface.task,
          this.labeledNLScript,
          this.labeledCodeScript,
          monacoEditor.getValue() || "",
          appLanguage
        );
        console.log('[CodeEditor] transferCodeToWorkflow response:', response);

        const parsedResponse = this.parseCodeToWorkflowResponse(response);

        this.originalCodeScript = monacoEditor.getValue() || "";
        console.log('[CodeEditor] changeScriptLanguage originalCodeScript:', this.originalCodeScript);
        // 同时保存带标号和去标号的版本，但不更新版本标志位
        this.setLabeledNLScript(parsedResponse.labeled_workflow);
        this.setLabeledCodeScript(parsedResponse.labeled_python_script);
        // 重置版本标志位
        this.resetVersionFlags();
        setScriptMode("NL");
        // 延迟触发自动保存，避免与转换过程冲突
        setTimeout(() => {
          this.triggerAutoSave();
        }, 2000);
        console.log('[CodeEditor] changeScriptLanguage New Script Mode:', scriptMode);
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('transferScriptFromScriptToNLComplete'));
      } else {
        // 从自然语言模式切换到代码模式
        // 检查NL脚本版本标志位和Code脚本是否为空
        const isCodeScriptEmpty = !this.originalCodeScript || this.originalCodeScript.trim() === "";
        // 检查当前NL脚本内容是否为空
        const currentNLScript = monacoEditor.getValue() || "";
        const isCurrentNLScriptEmpty = !currentNLScript || currentNLScript.trim() === "";
        
        if (this.NLScriptVersion === 0 && !isCodeScriptEmpty) {
          // 如果NL脚本版本为0且Code脚本不为空，直接切换
          console.log('CodeEditor: Change Script Language From NL To Code: No NL Update and Code Not Empty');
          setScriptMode("Code");
          return;
        }

        // 如果当前NL脚本为空，且Code脚本也为空，直接切换不进行转换
        if (isCurrentNLScriptEmpty && isCodeScriptEmpty) {
          console.log('CodeEditor: Change Script Language From NL To Code: Both NL and Code are empty, no conversion needed');
          setScriptMode("Code");
          return;
        }

        // 如果NL有更新或者Code脚本为空（但NL不为空），需要进行转换
        let transferReason = '';
        if (this.NLScriptVersion === 1 && isCodeScriptEmpty) {
          transferReason = 'NL updated and Code empty';
        } else if (this.NLScriptVersion === 1) {
          transferReason = 'NL updated';
        } else if (isCodeScriptEmpty && !isCurrentNLScriptEmpty) {
          transferReason = 'Code empty but NL has content';
        }
        
        console.log(`[CodeEditor] transferWorkflowToCode reason: ${transferReason}`);

        // 如果没有转换理由，直接切换模式
        if (!transferReason) {
          console.log('CodeEditor: Change Script Language From NL To Code: No conversion needed, switching directly');
          setScriptMode("Code");
          return;
        }

        // 添加加载中消息
        loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('transferringScriptFromNLtoScript'));

        // 设置转换状态
        ToolbarState.updateChangingScriptState(true);

        console.log('[CodeEditor] transferWorkflowToCode originalNLScript:', this.originalNLScript);
        const appLanguage = await window.electronAPI.getAppLanguageConfig();
        
        // 为当前NL脚本添加标号
        const currentLabeledWorkflow = addLabelToScript(currentNLScript);
        
        // 确保第三个参数（oldLabeledWorkflow）也是带标号的版本
        const oldLabeledWorkflow = this.labeledNLScript || addLabelToScript(this.originalNLScript);

        const response = await promptApi.transferWorkflowToCode(
          ChatInterface.taskDescription || ChatInterface.task,
          this.labeledCodeScript,
          oldLabeledWorkflow,
          currentLabeledWorkflow,
          appLanguage
        );
        
        const parsedResponse = this.parseWorkflowToCodeResponse(response);
        
        this.originalNLScript = monacoEditor.getValue() || "";
        console.log('CodeEditor: Change Script Language From NL To Code: With Update');
        console.log('CodeEditor: Script Mode:', scriptMode);
        // 同时保存带标号和去标号的版本，但不更新版本标志位
        this.setLabeledCodeScript(parsedResponse.labeled_python_script);
        this.setLabeledNLScript(currentLabeledWorkflow); // 使用带标号的当前工作流
        // 重置版本标志位
        this.resetVersionFlags();
        setScriptMode("Code");
        // 延迟触发自动保存，避免与转换过程冲突
        setTimeout(() => {
          this.triggerAutoSave();
        }, 2000);
        console.log('CodeEditor: New Script Mode:', scriptMode);
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('transferScriptFromNLtoScriptComplete'));
      }
    } catch (error) {
      if (error instanceof Error && error.message === 'Insufficient Token') {
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('insufficientTokenUpdateScript'), true);
      } else {
        console.error('Error in changeScriptLanguage:', error);
        if (loadingMsgId) {
          ChatInterface.updateToCompleteMessage(
            loadingMsgId,
            I18nUtils.getText(scriptMode === "Code" ? 'transferScriptFromScriptToNLError' : 'transferScriptFromNLtoScriptError'),
            true
          );
        }
      }
    } finally {
      // 无论成功或失败，最终都将 isChangingScript 设置为 false
      ToolbarState.updateChangingScriptState(false);
    }
  },

  /**
   * 获取指定行的代码内容
   * @param lineNumber 行号
   * @returns 该行的代码内容
   */
  getLineCode(lineNumber: number): string {
    const context = getContext();
    if (!context) return '';

    const { monacoEditor } = context;
    if (!monacoEditor) return '';

    // 获取指定行的内容
    const model = monacoEditor.getModel();
    if (model) {
      return model.getLineContent(lineNumber);
    }
    return '';
  },


  /**
   * 更新行（执行/演示）按钮装饰器
   * 为每一行添加按钮的装饰器，用于选中该行
   */
  updateLineExecuteDecorations(): void {
    const context = getContext();
    if (!context) return;

    const { monacoEditor, decorationsCollection } = context;
    if (!monacoEditor || !decorationsCollection) return;

    const model = monacoEditor.getModel();
    if (!model) return;

    // 获取总行数
    const lineCount = model.getLineCount();
    const decorations: monaco.editor.IModelDeltaDecoration[] = [];

    // 为每一行添加装饰器
    for (let lineNumber = 1; lineNumber <= lineCount; lineNumber++) {
      decorations.push({
        range: new monaco.Range(lineNumber, 1, lineNumber, 1),
        options: {
          isWholeLine: false,
          glyphMarginClassName: 'execute-button',
          // glyphMarginHoverMessage: { value: I18nUtils.getText('executeLine') },
          stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
        }
      });
    }

    // 更新装饰器集合
    decorationsCollection.set(decorations);

    // 添加执行按钮点击事件监听器（如果尚未添加）
    if (!monacoEditor.hasExecuteButtonListener) {
      const editorNode = monacoEditor.getDomNode();
      if (!editorNode) return;

      // 添加点击事件监听器
      editorNode.addEventListener('click', (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        if (target.classList.contains('execute-button')) {
          // 获取点击位置对应的行号
          const position = monacoEditor.getTargetAtClientPoint(e.clientX, e.clientY);
          if (position?.position?.lineNumber) {
            const lineNumber = position.position.lineNumber;
            const code = this.getLineCode(lineNumber);
            
            // 设置该行为选中状态
            const selection = {
              startLineNumber: lineNumber,
              startColumn: 1,
              endLineNumber: lineNumber,
              endColumn: code.length + 1
            };
            monacoEditor.setSelection(selection);
            
            // handleExecuteCode({
            //   code,
            //   type: this.scriptMode  // 使用当前编辑器模式作为代码类型
            // });
          }
        }
      });

      monacoEditor.hasExecuteButtonListener = true;
    }
  },

  /**
   * 更新只读行装饰器
   * 检测并标记 "# 设备信息" 和 "# 操作指南" 行为只读
   */
  updateReadonlyLineDecorations(): void {
    const context = getContext();
    if (!context) return;

    const { monacoEditor, readonlyDecorationsCollection, viewAppKnowledge, viewProjectKnowledge } = context;
    if (!monacoEditor || !readonlyDecorationsCollection) return;

    // 只在查看项目知识库时应用只读行逻辑
    if (!viewProjectKnowledge) {
      // 退出知识库视图，移除装饰并清空只读行记录，避免残留
      readonlyDecorationsCollection.clear();
      (monacoEditor as any)._readonlyLines = [];
      return;
    }

    const model = monacoEditor.getModel();
    if (!model) return;

    const content = monacoEditor.getValue();
    const lines = content.split('\n');
    const decorations: monaco.editor.IModelDeltaDecoration[] = [];
    const readonlyLines: number[] = [];

    // 查找 "# 设备信息" 和 "# 操作指南" 行
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line === '# 设备信息' || line === '# 操作指南') {
        const lineNumber = i + 1;
        readonlyLines.push(lineNumber);
        
        // 添加只读行装饰器
        decorations.push({
          range: new monaco.Range(lineNumber, 1, lineNumber, 1),
          options: {
            isWholeLine: true,
            className: 'readonly-line',
            lineNumberClassName: 'readonly-line-number',
            marginClassName: 'readonly-line-margin',
            stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
          }
        });
      }
    }

    // 更新装饰器集合
    readonlyDecorationsCollection.set(decorations);

    // 存储只读行号，供其他方法使用
    (monacoEditor as any)._readonlyLines = readonlyLines;
  },

  /**
   * 获取选中的代码脚本
   */
  getSelectedCodeScript(): string {
    const context = getContext();
    if (!context) return '';

    const { scriptMode, viewAppKnowledge } = context;
    if (scriptMode !== "Code" || viewAppKnowledge) return '';

    return this.getSelectedCode() || '';
  },

  /**
   * 获取选中的自然语言脚本
   */
  getSelectedNLScript(): string {
    const context = getContext();
    if (!context) return '';

    const { scriptMode, viewAppKnowledge } = context;
    if (scriptMode !== "NL" || viewAppKnowledge) return '';

    return this.getSelectedCode() || '';
  },

  /**
   * 获取选中的开始行，如果没有任何选中区域，则返回 -1。
   * 假设用户只会选择一段连续的代码。
   */
  getSelectedStartLine(): number {
    const context = getContext();
    if (!context) return -1;

    const { monacoEditor } = context;
    if (!monacoEditor) return -1;

    // 获取所有选中区域
    const selections = monacoEditor.getSelections();
    if (!selections || selections.length === 0) return -1;

    // 返回第一个选中区域的起始行号
    return selections[0].startLineNumber;
  },

  /**
   * 获取选中的结束行，如果没有任何选中区域，则返回 -1。
   * 假设用户只会选择一段连续的代码。
   */
  getSelectedEndLine(): number {
    const context = getContext();
    if (!context) return -1;

    const { monacoEditor } = context;
    if (!monacoEditor) return -1;

    // 获取所有选中区域
    const selections = monacoEditor.getSelections();
    if (!selections || selections.length === 0) return -1;

    // 返回最后一个选中区域的结束行号
    return selections[selections.length - 1].endLineNumber;
  },

  /**
   * 设置自动保存状态回调函数
   * @param callback 状态变化回调函数
   */
  setAutoSaveStatusCallback(callback: ((status: 'idle' | 'saving' | 'syncing' | 'completed' | 'error') => void) | undefined): void {
    this._autoSaveStatusCallback = callback;
  },

  /**
   * 通知自动保存状态变化
   * @param status 新的状态
   */
  notifyAutoSaveStatus(status: 'idle' | 'saving' | 'syncing' | 'completed' | 'error'): void {
    if (this._autoSaveStatusCallback) {
      this._autoSaveStatusCallback(status);
    }
  },

  /**
   * 强制保存当前编辑器中的内容，不触发git同步，用于快速保存防止数据丢失
   */
  async forceSave(): Promise<void> {
    const context = getContext();
    if (!context || !context.monacoEditor) {
      return;
    }

    const { monacoEditor } = context;
    const content = monacoEditor.getValue();
    const projectName = ChatInterface.projectName;
    const currentEditingFile = ChatInterface.currentEditingFile;

    if (!projectName || !currentEditingFile) {
      return;
    }
    
    // 以下逻辑复制自 `localSave` 但移除了UI通知和git同步，以实现快速、静默的保存

    // 检查是否是知识库文件
    if (currentEditingFile === 'Knowledge.json') {
      try {
        await window.electronAPI.updateKnowledgeFile({
          taskName: projectName,
          knowledgeContent: content
        });
      } catch (error) {
        console.error('[ForceSave] 保存知识库文件失败:', error);
      }
      return;
    }

    // 检查是否是任务JSON文件
    let finalContent = content;
    if (currentEditingFile.endsWith('.json') && currentEditingFile !== 'Knowledge.json') {
      try {
        const readResult = await window.electronAPI.readTaskFile({
          taskName: projectName,
          fileName: currentEditingFile
        });
        
        if (readResult.success && readResult.content) {
          const taskData = JSON.parse(readResult.content);
          
          if (CodeEditorUtils.scriptMode === "NL") {
            taskData.NL_script = content;
          } else {
            taskData.code_script = content;
          }
          // 同时更新版本标志位
          taskData.NL_script_version = CodeEditorUtils.NLScriptVersion;
          taskData.code_script_version = CodeEditorUtils.CodeScriptVersion;
          finalContent = JSON.stringify(taskData, null, 2);
        }
      } catch (error) {
        console.warn('[ForceSave] 解析任务JSON文件失败，将保存原始内容:', error);
      }
    }

    try {
      await window.electronAPI.updateTaskFile({
        taskName: projectName,
        fileName: currentEditingFile,
        content: finalContent
      });
    } catch (error) {
      console.error('[ForceSave] 本地保存失败:', error);
    }
  },

  /**
   * 重置知识库视图
   * 用于加载新项目时，强制退出知识库视图并清空内容
   */
  resetKnowledgeView(): void {
    console.log('CodeEditor: Resetting Knowledge View');
    const context = getContext();
    if (!context) return;

    const { monacoEditor } = context;
    if (!monacoEditor) return;
    
    // 清空知识库内容
    this.originalAppKnowledge = "";

    // 更新视图状态
    this.viewAppKnowledge = false;
    this.viewProjectKnowledge = false;
    monacoEditor.updateOptions({ glyphMargin: false });
    
    // 清除只读行装饰
    this.updateReadonlyLineDecorations();
    
    // 重置只读行数组，防止后续编辑时仍将知识库标题行视为只读
    (monacoEditor as any)._readonlyLines = [];
    
    // 通知其他组件已退出系统提示词查看
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('systemPromptViewChange', { detail: { isViewing: false } }));
    }
  },

  /**
   * 重置带标号的脚本
   * 用于处理新任务加载时的状态重置
   */
  resetLabeledScripts(): void {
    console.log('CodeEditor: Resetting Labeled Scripts');
    this.labeledNLScript = '';
    this.labeledCodeScript = '';
  },

  /**
   * 为脚本添加标号
   * @param script 原始脚本
   * @returns 带标号的脚本
   */
  addLabelToScript(script: string): string {
    if (!script) return '';
    
    let lineNumber = 1;
    return script
      .split('\n')
      .map(line => {
        // 空行不添加标号
        if (line.trim() === '') {
          return line;
        }
        // 非空行添加标号
        const labeledLine = `[${lineNumber}]${line}`;
        lineNumber++;
        return labeledLine;
      })
      .join('\n');
  },

  /**
   * 高亮正在执行的代码行
   * @param labeledCodeScript 带标号的代码脚本（当前执行行）
   * @param labeledNLScript 带标号的自然语言脚本（当前执行行）
   * @param error 是否为错误状态
   */
  highlightExecutingLine(labeledCodeScript: string, labeledNLScript: string, error?: boolean): void {
    console.log('=== highlightExecutingLine ===');
    console.log('labeledCodeScript:', labeledCodeScript);
    console.log('labeledNLScript:', labeledNLScript);
    console.log('error:', error);
    
    const context = getContext();
    if (!context) return;

    const { monacoEditor, highlightDecorationsCollection, scriptMode, viewAppKnowledge } = context;
    if (!monacoEditor || !highlightDecorationsCollection || viewAppKnowledge) return;

    console.log('Current script mode:', scriptMode);

    // 检查是否是执行结束标记 [-1]
    if (labeledCodeScript.trim() === '[-1]' && labeledNLScript.trim() === '[-1]') {
      console.log('Execution finished, clearing highlights');
      highlightDecorationsCollection.clear();
      return;
    }

    const model = monacoEditor.getModel();
    if (!model) return;

    // 先清除之前的高亮
    highlightDecorationsCollection.clear();

    // 根据当前模式选择相应的标号脚本
    const currentExecutingLine = scriptMode === "Code" ? labeledCodeScript : labeledNLScript;
    if (!currentExecutingLine) {
      console.log('No current executing line found');
      return;
    }

    console.log('Current executing line:', currentExecutingLine);

    // 提取当前执行行的标号
    let lineNumberMatch = null;
    let executingLabelNumber = -1;
    
    if (scriptMode === "Code") {
      // 对于 Code script，标号在行尾作为注释 # [数字]
      lineNumberMatch = currentExecutingLine.match(/#\s*\[(\d+)\]\s*$/);
    } else {
      // 对于 NL script，标号在行首 [数字]
      lineNumberMatch = currentExecutingLine.match(/^\[(\d+)\]/);
    }
    
    if (!lineNumberMatch) {
      console.log('No line number match found');
      return;
    }

    executingLabelNumber = parseInt(lineNumberMatch[1]);
    console.log('Executing label number:', executingLabelNumber);
    
    // 根据标号在完整的带标号脚本中找到对应的行号
    let targetLineNumber = -1;
    
    if (scriptMode === "Code") {
      // 对于代码模式，使用完整的带标号代码脚本进行映射
      const fullLabeledCodeScript = this.labeledCodeScript;
      if (fullLabeledCodeScript) {
        targetLineNumber = this.findLineNumberByLabel(fullLabeledCodeScript, executingLabelNumber);
        console.log('Found code line by label mapping at:', targetLineNumber);
      }
    } else {
      // 对于NL模式，使用完整的带标号NL脚本进行映射
      const fullLabeledNLScript = this.labeledNLScript;
      if (fullLabeledNLScript) {
        targetLineNumber = this.findLineNumberByLabel(fullLabeledNLScript, executingLabelNumber);
        console.log('Found NL line by label mapping at:', targetLineNumber);
      }
    }

    if (targetLineNumber <= 0) {
      console.log('No target line number found by label mapping');
      return;
    }

    console.log('Highlighting line:', targetLineNumber);
    console.log('Error status:', error);

    // 根据error状态选择不同的样式
    const highlightClass = error ? 'executing-line-error-highlight' : 'executing-line-highlight';
    const lineNumberClass = error ? 'executing-line-error-decoration' : 'executing-line-decoration';

    // 创建高亮装饰器
    const decorations: monaco.editor.IModelDeltaDecoration[] = [{
      range: new monaco.Range(targetLineNumber, 1, targetLineNumber, 1),
      options: {
        isWholeLine: true,
        className: highlightClass,
        lineNumberClassName: lineNumberClass
      }
    }];

    // 应用高亮装饰器
    highlightDecorationsCollection.set(decorations);

    // 如果是错误高亮，设置错误高亮状态
    if (error) {
      this.hasErrorHighlight = true;
    }

    // 滚动到执行行
    monacoEditor.revealLineInCenter(targetLineNumber);

    console.log('=== highlightExecutingLine completed ===');
  },

  /**
   * 根据标号在带标号脚本中找到对应的编辑器行号
   * @param labeledScript 完整的带标号脚本
   * @param labelNumber 要查找的标号
   * @returns 编辑器中的行号，如果未找到返回-1
   */
  findLineNumberByLabel(labeledScript: string, labelNumber: number): number {
    if (!labeledScript) return -1;
    
    const context = getContext();
    if (!context) return -1;
    
    const { monacoEditor } = context;
    if (!monacoEditor) return -1;
    
    const model = monacoEditor.getModel();
    if (!model) return -1;
    
    // 获取编辑器当前内容的所有行
    const editorLines = [];
    const totalLines = model.getLineCount();
    for (let i = 1; i <= totalLines; i++) {
      editorLines.push(model.getLineContent(i));
    }
    
    // 解析带标号脚本，找到目标标号对应的行内容
    const labeledLines = labeledScript.split('\n');
    let targetLineContent = '';
    
    for (const labeledLine of labeledLines) {
      const trimmedLine = labeledLine.trim();
      if (!trimmedLine) continue;
      
      let currentLabel = -1;
      let lineContent = '';
      
      // 检查行首格式 [数字] (NL script)
      const prefixMatch = trimmedLine.match(/^\[(\d+)\]\s*(.*)/);
      if (prefixMatch) {
        currentLabel = parseInt(prefixMatch[1]);
        lineContent = prefixMatch[2];
      } else {
        // 检查行尾注释格式 # [数字] (Code script)
        const suffixMatch = trimmedLine.match(/^(.*?)\s*#\s*\[(\d+)\]\s*$/);
        if (suffixMatch) {
          currentLabel = parseInt(suffixMatch[2]);
          lineContent = suffixMatch[1].trim();
        }
      }
      
      if (currentLabel === labelNumber) {
        targetLineContent = lineContent;
        break;
      }
    }
    
    if (!targetLineContent) {
      console.log(`Label [${labelNumber}] not found in labeled script`);
      return -1;
    }
    
    console.log(`Looking for content: "${targetLineContent}"`);
    
    // 在编辑器内容中查找匹配的行
    for (let i = 0; i < editorLines.length; i++) {
      const editorLineContent = editorLines[i].trim();
      if (editorLineContent === targetLineContent) {
        const editorLineNumber = i + 1; // 编辑器行号从1开始
        console.log(`Found label [${labelNumber}] content "${targetLineContent}" at editor line ${editorLineNumber}`);
        return editorLineNumber;
      }
    }
    
    // 如果精确匹配失败，尝试模糊匹配（去除多余空白字符）
    const normalizeContent = (content: string) => content.replace(/\s+/g, ' ').trim();
    const normalizedTarget = normalizeContent(targetLineContent);
    
    for (let i = 0; i < editorLines.length; i++) {
      const normalizedEditorLine = normalizeContent(editorLines[i]);
      if (normalizedEditorLine === normalizedTarget) {
        const editorLineNumber = i + 1;
        console.log(`Found label [${labelNumber}] content "${targetLineContent}" at editor line ${editorLineNumber} (fuzzy match)`);
        return editorLineNumber;
      }
    }
    
    console.log(`Label [${labelNumber}] content "${targetLineContent}" not found in editor`);
    return -1;
  },

  /**
   * 清除执行行高亮
   */
  clearExecutingLineHighlight(): void {
    const context = getContext();
    if (!context) return;

    const { highlightDecorationsCollection } = context;
    if (!highlightDecorationsCollection) return;

    console.log('Clearing executing line highlight');
    highlightDecorationsCollection.clear();
    
    // 清除错误高亮状态
    this.hasErrorHighlight = false;
  },

  /**
   * 获取是否有错误高亮状态
   * @returns 是否有错误高亮
   */
  get hasErrorHighlight(): boolean {
    const context = getContext();
    return context?.hasErrorHighlight || false;
  },

  /**
   * 设置错误高亮状态
   * @param value 是否有错误高亮
   */
  set hasErrorHighlight(value: boolean) {
    const context = getContext();
    if (context?.setHasErrorHighlight) {
      context.setHasErrorHighlight(value);
    }
  },

  /**
   * 清除错误高亮
   * 只清除错误状态的高亮，不影响正常的执行高亮
   */
  clearErrorHighlight(): void {
    if (this.hasErrorHighlight) {
      console.log('Clearing error highlight');
      this.clearExecutingLineHighlight();
      this.hasErrorHighlight = false;
    }
  },

  /**
   * 清除所有高亮（包括错误高亮和正常高亮）
   */
  clearAllHighlight(): void {
    console.log('Clearing all highlights');
    this.clearExecutingLineHighlight();
    this.hasErrorHighlight = false;
  }
};