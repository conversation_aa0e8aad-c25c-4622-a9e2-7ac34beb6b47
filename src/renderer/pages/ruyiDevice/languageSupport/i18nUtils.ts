import { translations } from './translations';

export class I18nUtils {
  public static currentLanguage: string = 'zh';

  static async initialize() {
    try {
      // 从 electron 获取语言配置
      const configLanguage = await window.electronAPI.getLanguageConfig();
      
      // 如果配置语言为 default，则使用系统语言
      this.currentLanguage = configLanguage === 'default' 
        ? (navigator.language.toLowerCase().startsWith('zh') ? 'zh' : 'en')
        : configLanguage;
    } catch (error) {
      console.error('获取语言配置失败:', error);
      // 如果获取失败，使用系统语言作为默认值
      this.currentLanguage = navigator.language.toLowerCase().startsWith('zh') ? 'zh' : 'en';
    }
  }

  static getText(key: keyof typeof translations.en): string {
    if (!this.currentLanguage) {
      console.warn('Language not initialized yet, using default language');
      const value = translations['zh'][key];
      if (typeof value === 'string') {
        return value;
      }
      return '';
    }
    const value = translations[this.currentLanguage as 'zh' | 'en'][key];
    if (typeof value === 'string') {
      return value;
    }
    return '';
  }

  static getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  static getArrayText(key: keyof typeof translations.en): string[] {
    const value = translations[this.currentLanguage as 'zh' | 'en'][key];
    if (Array.isArray(value)) {
      return value;
    }
    return typeof value === 'string' ? [value] : [];
  }

  static getObjectText<T = any>(key: keyof typeof translations.en): T {
    const value = translations[this.currentLanguage as 'zh' | 'en'][key];
    if (typeof value === 'object' && !Array.isArray(value)) {
      return value as T;
    }
    return {} as T;
  }
}