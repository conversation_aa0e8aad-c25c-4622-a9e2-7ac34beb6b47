import * as monaco from 'monaco-editor';

/**
 * 定义英文版本的代码补全建议项
 */
export const getEnglishCompletionSuggestions = (range: monaco.IRange) => {
  return [
    {
      label: '/ ===== Basic API =====',
      kind: monaco.languages.CompletionItemKind.Folder,
      insertText: '',
      detail: 'Basic API',
      documentation: 'Basic API',
      sortText: '01',
      range: range
    },
    {
      label: '/Open app',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Open the "${1:app name}" app.\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Open app',
      documentation: 'Open app',
      sortText: '01_01',
      range: range
    },
    {
      label: '/Click',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Click on "${1:component description}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Click',
      documentation: 'Click',
      sortText: '01_02',
      range: range
    },
    {
      label: '/Long click',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Long click on "${1:component description}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Long click',
      documentation: 'Long click',
      sortText: '01_03',
      range: range
    },
    {
      label: '/Input',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Input "${1:text}" into "${2:component description}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Input',
      documentation: 'Input',
      sortText: '01_04',
      range: range
    },
    {
      label: '/InputByPasting',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Input "${1:text}" into "${2:component description}" by pasting.\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'InputByPasting',
      documentation: "Input by pasting. Use this API when direct input doesn't work.",
      sortText: '01_04',
      range: range
    },
    {
      label: '/Scroll',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Scroll "${1:direction}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Scroll',
      documentation: 'Scroll',
      sortText: '01_05',
      range: range
    },
    {
      label: '/Scroll on component',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Scroll "${1:direction}" on "${2:component description}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Scroll on component',
      documentation: 'Scroll on component',
      sortText: '01_06',
      range: range
    },
    {
      label: '/Back',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Go back.\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Go back',
      documentation: 'Go back',
      sortText: '01_07',
      range: range
    },
    {
      label: '/Home',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Return to the home screen.\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Return to the home screen',
      documentation: 'Return to the home screen',
      sortText: '01_08',
      range: range
    },
    {
      label: '/ ===== Advanced API =====',
      kind: monaco.languages.CompletionItemKind.Folder,
      insertText: '',
      detail: 'Advanced API',
      documentation: 'Advanced API',
      sortText: '02',
      range: range
    },
    {
      label: '/Check and execute',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Check if "${1:description}" is "${2|true,false|}", then execute:\n\t${3:"operation"}\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Check and execute',
      documentation: 'Check and execute',
      sortText: '02_01',
      range: range
    },
    {
      label: '/Operate components of the same type',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'For each "${1:component description}" in the current interface, up to "${2:number}", execute:\n\t${3:"operation1"}\n\t${4:"operation2"}\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Operate components of the same type',
      documentation: 'Operate components of the same type',
      sortText: '02_02',
      range: range
    },
    {
      label: '/Wait for xxx to appear',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Wait until the interface appears: "${1:interface description}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Wait for xxx to appear',
      documentation: 'Wait for xxx to appear',
      sortText: '02_03',
      range: range
    },
    {
      label: '/Scroll until xxx',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Scroll "${1:direction}" until "${2:interface description}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Scroll until xxx',
      documentation: 'Scroll until xxx',
      sortText: '02_04',
      range: range
    },
    {
      label: '/Back to xxx',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Back to "${1:interface description}", maximum "${2:5}" times.\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Back to xxx',
      documentation: 'Back to xxx interface, maximum x times, default is 5 times',
      sortText: '02_05',
      range: range
    },
    {
      label: '/Ensure no pop-ups or ads on the current interface',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Ensure no pop-ups or ads on the current interface.\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Ensure no pop-ups or ads on the current interface',
      documentation: 'Ensure no pop-ups or ads on the current interface',
      sortText: '02_06',
      range: range
    },
    {
      label: '/Call large model',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Call large model to "${1:requirement description}", save response as "${2:result name}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Call large model, supports text/visual input',
      documentation: 'Call large model, supports text/visual input',
      sortText: '02_07',
      range: range
    },
    // {
    //   label: '/Call LLM for analysis',
    //   kind: monaco.languages.CompletionItemKind.Method,
    //   insertText: 'Use LLM to "${1:requirement description}", save response as "${2:result name}".\n',
    //   insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
    //   detail: 'Call LLM for analysis',
    //   documentation: 'Call LLM for analysis',
    //   sortText: '02_07',
    //   range: range
    // },
    // {
    //   label: '/Call VLM for analysis',
    //   kind: monaco.languages.CompletionItemKind.Method,
    //   insertText: 'Use VLM to "${1:requirement description}", save response as "${2:result name}".\n',
    //   insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
    //   detail: 'Call VLM for analysis',
    //   documentation: 'Call VLM for analysis',
    //   sortText: '02_08',
    //   range: range
    // },
    {
      label: '/Loop execution',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Repeat the following, "${1:times}" times:\n\t"${2:operation1}"\n\t"${3:operation2}"\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Loop execution',
      documentation: 'Loop execution',
      sortText: '02_09',
      range: range
    },
    {
      label: '/Define and call function',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Define function "${1:function name}" with parameters "${2:parameters}":\n\t"${4:operation1}"\n\t"${5:operation2}".\nCall the "${6:function name}" function with parameters "${7:parameters}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Define and call function',
      documentation: 'Define and call function',
      sortText: '02_10',
      range: range
    },
    {
      label: '/ ===== User related =====',
      kind: monaco.languages.CompletionItemKind.Folder,
      insertText: '',
      detail: 'Ask/Notify user',
      documentation: 'Ask/Notify user',
      sortText: '03',
      range: range
    },
    {
      label: '/Ask user',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Ask user: "${1:question}", and save response as "${2:response name}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Ask user a question',
      documentation: 'Ask user a question',
      sortText: '03_01',
      range: range
    },
    {
      label: '/Notify user of a message',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Notify user of a message: "${1:message}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Notify user of a message',
      documentation: 'Notify user of a message',
      sortText: '03_02',
      range: range
    },
    {
      label: '/Notify user of a table',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Notify user of a table: "${1:table name}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Notify user of a table',
      documentation: 'Notify user of a table',
      sortText: '03_03',
      range: range
    },
    {
      label: '/Notify user of an image',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Notify user of an image: "${1:image name}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Notify user of an image',
      documentation: 'Notify user of an image',
      sortText: '03_04',
      range: range
    },
    {
      label: '/ ===== Data processing =====',
      kind: monaco.languages.CompletionItemKind.Folder,
      insertText: '',
      detail: 'Data processing',
      documentation: 'Data processing',
      sortText: '04',
      range: range
    },
    {
      label: '/Create a table',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Create a table named "${1:table name}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Create a table',
      documentation: 'Create a table',
      sortText: '04_01',
      range: range
    },
    {
      label: '/Add a row to the table',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Add a row to "${1:table name}" table, containing: "${2:column1: value1}"、"${3:column2: value2}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Add a row to the table',
      documentation: 'Add a row to the table',
      sortText: '04_02',
      range: range
    },
    {
      label: '/ ===== Other functions =====',
      kind: monaco.languages.CompletionItemKind.Folder,
      insertText: '',
      detail: 'Other functions',
      documentation: 'Other functions',
      sortText: '05',
      range: range
    },

    {
      label: '/Get clipboard content',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Get clipboard content and save it as "${1:result name}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Get clipboard content',
      documentation: 'Get clipboard content',
      sortText: '05_01',
      range: range
    },
    {
      label: '/Set clipboard content',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Set clipboard content to "${1:content}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Set clipboard content',
      documentation: 'Set clipboard content',
      sortText: '05_02',
      range: range
    },
    {
      label: '/Expand notification panel',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Expand notification panel.\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Expand notification panel',
      documentation: 'Expand notification panel',
      sortText: '05_03',
      range: range
    },
    {
      label: '/Get text from input box',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'Get text from "${1:component description}" and save it as "${2:result name}".\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'Get text from input box',
      documentation: 'Get text from input box',
      sortText: '05_04',
      range: range
    },

  ];
};

/** 
 * 定义中文版本的代码补全建议项
 */
export const getChineseCompletionSuggestions = (range: monaco.IRange) => {
  return [
    {
      label: '/ ===== 基本功能 =====',
      kind: monaco.languages.CompletionItemKind.Folder,
      insertText: '',
      detail: '基本功能',
      documentation: '基本功能',
      sortText: '01',
      range: range
    },
    {
      label: '/打开应用',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '打开 "${1:应用名称}" 应用。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '打开应用',
      documentation: '打开应用',
      sortText: '01_01',
      range: range
    },
    {
      label: '/点击',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '点击 "${1:组件描述}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '点击元素',
      documentation: '点击指定的UI元素',
      sortText: '01_02',
      range: range
    },
    {
      label: '/长按',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '长按 "${1:组件描述}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '长按元素',
      documentation: '长按指定的UI元素',
      sortText: '01_03',
      range: range
    },
    {
      label: '/输入',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '输入 "${1:文本}" 到 "${2:组件描述}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '输入文本',
      documentation: '在指定元素中输入文本',
      sortText: '01_04',
      range: range
    },
    {
      label: '/通过粘贴输入',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '通过粘贴输入 "${1:文本}" 到 "${2:组件描述}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '通过输入文本',
      documentation: '通过粘贴在指定元素中输入文本，当直接输入失效时使用该方法',
      sortText: '01_04',
      range: range
    },
    {
      label: '/滑动',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '向 "${1:方向}" 滑动。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '向某个方向滑动',
      documentation: '向某个方向滑动',
      sortText: '01_05',
      range: range
    },
    {
      label: '/滑动组件',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '在 "${1:组件描述}"位置处，向 "${2:方向}" 滑动。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '滑动组件',
      documentation: '滑动组件',
      sortText: '01_06',
      range: range
    },
    {
      label: '/返回',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '返回。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '返回',
      documentation: '返回',
      sortText: '01_07',
      range: range
    },
    {
      label: '/返回桌面',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '返回桌面。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '返回桌面',
      documentation: '返回桌面',
      sortText: '01_08',
      range: range
    },
    {
      label: '/ ===== 高阶用法 =====',
      kind: monaco.languages.CompletionItemKind.Folder,
      insertText: '',
      detail: '高级功能',
      documentation: '高级功能',
      sortText: '02',
      range: range
    },
    {
      label: '/检查然后执行',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '检查 "${1:描述}" 是否成立，如果 "${2|成立,不成立|}"，则执行:\n\t${3:操作}\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '检查然后执行',
      documentation: '检查然后执行',
      sortText: '02_01',
      range: range
    },
    {
      label: '/对同一类元素分别操作',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '对于当前界面中 "${1:组件总体描述}"，最多 "${2:数量}" 个，执行:\n\t"${3:操作1}"\n\t"${4:操作2}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '对当前界面中同一类型的元素分别进行操作',
      documentation: '对当前界面中同一类型的元素分别进行操作',
      sortText: '02_02',
      range: range
    },
    {
      label: '/等待xxx出现',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '等待 "${1:界面描述}" 出现。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '等待界面出现',
      documentation: '等待界面出现',
      sortText: '02_03',
      range: range
    },
    {
      label: '/滚动直到 xxx',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '向 "${1:方向}" 滚动，直到 "${2:界面描述}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '向"${1:方向}"滚动，直到"${2:界面描述}"',
      documentation: '向"${1:方向}"滚动，直到"${2:界面描述}"',
      sortText: '02_04',
      range: range
    },
    {
      label: '/返回直到 xxx',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '返回，直到 "${1:描述}" 出现，最大次数为 "${2:5}" 次。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '返回直到 xxx',
      documentation: '返回直到 xxx 界面出现，最大次数为 5 次',
      sortText: '02_05',
      range: range
    },
    {
      label: '/确保当前界面上没有弹窗或广告',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '确保当前界面上没有弹窗或广告。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '确保当前界面上没有弹窗或广告',
      documentation: '确保当前界面上没有弹窗或广告',
      sortText: '02_06',
      range: range
    },
    {
      label: '/调用大模型',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '调用大模型 "${1:需求描述}"，结果记录为 "${2:结果名称}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '调用大模型进行分析，支持文本/视觉输入',
      documentation: '调用大模型进行分析，支持文本/视觉输入',
      sortText: '02_07',
      range: range
    },
    // {
    //   label: '/调用LLM进行分析',
    //   kind: monaco.languages.CompletionItemKind.Method,
    //   insertText: '使用 LLM "${1:需求描述}"，结果记录为 "${2:结果名称}"。\n',
    //   insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
    //   detail: '调用LLM进行分析',
    //   documentation: '调用LLM进行分析',
    //   sortText: '02_07',
    //   range: range
    // },
    // {
    //   label: '/调用VLM进行分析',
    //   kind: monaco.languages.CompletionItemKind.Method,
    //   insertText: '使用 VLM "${1:需求描述}"，结果记录为 "${2:结果名称}"。\n',
    //   insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
    //   detail: '调用VLM进行分析',
    //   documentation: '调用VLM进行分析',
    //   sortText: '02_08',
    //   range: range
    // },
    {
      label: '/循环执行',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '循环执行以下步骤，"${1:次数}" 次：\n\t"${2:操作1}"\n\t"${3:操作2}"\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '循环执行',
      documentation: '循环执行',
      sortText: '02_09',
      range: range
    },
    {
      label: '/定义、调用函数',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '定义函数 "${1:函数名称}"，参数为 "${2:参数名称}"：\n\t"${4:操作1}"\n\t"${5:操作2}"\n调用 "${6:函数名称}" 函数，参数为 "${7:参数实际值}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '定义、调用函数',
      documentation: '定义、调用函数',
      sortText: '02_10',
      range: range
    },
    {
      label: '/ ===== 用户相关 =====',
      kind: monaco.languages.CompletionItemKind.Folder,
      insertText: '',
      detail: '询问/通知用户',
      documentation: '用户相关',
      sortText: '03',
      range: range
    },
    {
      label: '/询问用户',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '询问用户："${1:问题}"，将回复记录为 "${2:回复名称}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '询问用户一个问题',
      documentation: '询问用户一个问题',
      sortText: '03_01',
      range: range
    },
    {
      label: '/通知用户一条消息',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '通知用户一条消息："${1:消息}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '通知用户一条消息',
      documentation: '通知用户一条消息',
      sortText: '03_02',
      range: range
    },
    {
      label: '/通知用户一张表格',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '通知用户一张表格："${1:表格名称}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '通知用户一张表格',
      documentation: '通知用户一张表格',
      sortText: '03_03',
      range: range
    },
    {
      label: '/通知用户一张图片',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '通知用户一张图片："${1:图片名称}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '通知用户一张图片',
      documentation: '通知用户一张图片',
      sortText: '03_04',
      range: range
    },
    {
      label: '/ ===== 数据处理 =====',
      kind: monaco.languages.CompletionItemKind.Folder,
      insertText: '',
      detail: '数据处理',
      documentation: '数据处理',
      sortText: '04',
      range: range
    },
    {
      label: '/创建一个表格',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '创建一个名为 "${1:表格名称}" 的表格。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '创建一个表格',
      documentation: '创建一个表格',
      sortText: '04_01',
      range: range
    },
    {
      label: '/在表格中添加一行',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '在 "${1:表格名称}" 表格中添加一行，包含: "${2:列名1: 值1}"、"${3:列名2: 值2}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '在表格中添加一行',
      documentation: '在表格中添加一行',
      sortText: '04_02',
      range: range
    },
    {
      label: '/ ===== 其他功能 =====',
      kind: monaco.languages.CompletionItemKind.Folder,
      insertText: '',
      detail: '其他',
      documentation: '其他',
      sortText: '05',
      range: range
    },
    {
      label: '/获取手机粘贴板内容',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '获取手机粘贴板内容，记录为 "${1:结果名称}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '获取手机粘贴板内容',
      documentation: '获取手机粘贴板内容',
      sortText: '05_01',
      range: range
    },
    {
      label: '/设置手机粘贴板内容',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '将 "${1:内容}" 设置到手机粘贴板。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '设置手机粘贴板内容',
      documentation: '设置手机粘贴板内容',
      sortText: '05_02',
      range: range
    },
    {
      label: '/展开手机通知栏',
      kind: monaco.languages.CompletionItemKind.Method, 
      insertText: '展开手机通知栏。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '展开手机通知栏',
      documentation: '展开手机通知栏',
      sortText: '05_03',
      range: range
    },
    {
      label: '/获取某个输入框中的文本',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: '获取 "${1:组件描述}" 中的文本，记录为 "${2:结果名称}"。\n',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '获取输入框中的文本',
      documentation: '获取输入框中的文本',
      sortText: '05_04',
      range: range
    },

  ];
};


let currentDisposable: monaco.IDisposable | null = null;

/**
 * 注册代码补全提供者
 * @param appLanguage 应用语言设置
 */
export const registerCompletionProviderNL = (appLanguage: string = 'zh') => {

  // 确保先清除之前的注册
  if (currentDisposable) {
    currentDisposable.dispose();
    currentDisposable = null;
  }

  // 根据语言注册对应的补全提供者
  currentDisposable = monaco.languages.registerCompletionItemProvider('python', {
    triggerCharacters: ['/'],
    provideCompletionItems: (model: monaco.editor.ITextModel, position: monaco.Position, context: monaco.languages.CompletionContext) => {
      const wordUntilPosition = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: wordUntilPosition.startColumn,
        endColumn: wordUntilPosition.endColumn
      };

      // 如果是通过 '/' 触发，则删除这个触发字符
      if (context.triggerCharacter === '/') {
        range.startColumn = position.column - 1;
        range.endColumn = position.column;
      }

      // 根据语言选择补全建议
      const suggestions = appLanguage === 'zh' 
        ? getChineseCompletionSuggestions(range)
        : getEnglishCompletionSuggestions(range);
      
      return { suggestions };
    }
  });

  return currentDisposable;
};

/**
 * 更新补全提供者的语言
 * @param appLanguage 新的应用语言设置
 */
export const updateCompletionProviderLanguageNL = (appLanguage: string) => {
  return registerCompletionProviderNL(appLanguage);
};