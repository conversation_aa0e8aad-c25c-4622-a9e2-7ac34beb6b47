import * as monaco from 'monaco-editor';

/**
 * 定义英文版本的代码补全建议项
 */
export const getEnglishCompletionSuggestions = (range: monaco.IRange) => {
  return [
    {
      label: '/ui',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'ui',
      detail: 'UI module',
      documentation: 'Interfaces for interacting with device GUI',
      sortText: '01_00',
      range: range
    },
    {
      label: '/device',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'device',
      detail: 'Device module',
      documentation: 'System-level APIs for device control',
      sortText: '02_00',
      range: range
    },
    {
      label: '/fm',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'fm',
      detail: 'FM module',
      documentation: 'File management module',
      sortText: '03_00',
      range: range
    },
    {
      label: '/tools',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'tools',
      detail: 'Tools module',
      documentation: 'Utility tools for the agent',
      sortText: '04_00',
      range: range
    },
    {
      label: '/data',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'data',
      detail: 'Data module',
      documentation: 'Data management module',
      sortText: '05_00',
      range: range
    }
  ];
};

/** 
 * 定义中文版本的代码补全建议项
 */
export const getChineseCompletionSuggestions = (range: monaco.IRange) => {
  return [
    {
      label: '/ui',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'ui',
      detail: 'UI 模块',
      documentation: '用于与设备 GUI 交互的接口',
      sortText: '01_00',
      range: range
    },
    {
      label: '/device',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'device',
      detail: '设备模块',
      documentation: '用于控制设备的系统级 API',
      sortText: '02_00',
      range: range
    },
    {
      label: '/fm',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'fm',
      detail: '模型模块',
      documentation: '用于调用 LLM、VLM 来进行分析',
      sortText: '03_00',
      range: range
    },
    {
      label: '/tools',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'tools',
      detail: '工具模块',
      documentation: '用于实现各种工具调用',
      sortText: '04_00',
      range: range
    },
    {
      label: '/data',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'data',
      detail: '数据模块',
      documentation: '用于创建、管理数据表格',
      sortText: '05_00',
      range: range
    }
  ];
};


let currentDisposable: monaco.IDisposable | null = null;

/**
 * 注册代码补全提供者
 * @param appLanguage 应用语言设置
 */
export const registerCompletionProvider = (appLanguage: string = 'zh') => {

  // 确保先清除之前的注册
  if (currentDisposable) {
    currentDisposable.dispose();
    currentDisposable = null;
  }

  // 根据语言注册对应的补全提供者
  currentDisposable = monaco.languages.registerCompletionItemProvider('python', {
    triggerCharacters: ['/', '.'],
    provideCompletionItems: (model: monaco.editor.ITextModel, position: monaco.Position, context: monaco.languages.CompletionContext) => {
      const wordUntilPosition = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: wordUntilPosition.startColumn,
        endColumn: wordUntilPosition.endColumn
      };

      // 如果是通过 '/' 或 '.' 触发，则调整范围
      if (context.triggerCharacter === '/' || context.triggerCharacter === '.') {
        range.startColumn = position.column - 1;
        range.endColumn = position.column;
      }

      // 获取当前行的文本
      const lineContent = model.getLineContent(position.lineNumber);

      // 'device.' 的补全建议
      if (lineContent.includes('device.') && appLanguage === 'zh') {
        const deviceSuggestions = [
          {
            label: 'device.start_app',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.start_app("${1:应用名称}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '启动一个应用',
            documentation: '启动名为 ${1:应用名称} 的应用',
            sortText: '01_01',
            range: range
          },
          {
            label: 'device.kill_app',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.kill_app("${1:应用名称}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '关闭一个应用',
            documentation: '关闭名为 ${1:应用名称} 的应用',
            sortText: '01_02',
            range: range
          },
          {
            label: 'device.click',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.click(${1:x}, ${2:y})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '点击 (x, y)',
            documentation: '点击 (x, y) 位置，持续 ${1:100} 毫秒',
            sortText: '01_03',
            range: range
          },
          {
            label: 'device.long_click',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.long_click(${1:x}, ${2:y})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '长按 (x, y)',
            documentation: '长按 (x, y) 位置，持续 ${1:100} 毫秒',
            sortText: '01_04',
            range: range
          },
          {
            label: 'device.input',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.input("${1:文本}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '输入文本',
            documentation: '在当前聚焦的输入框中输入 ${1:文本}',
            sortText: '01_05',
            range: range
          },
          {
            label: 'device.clear_and_input',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.clear_and_input("${1:文本}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '清空并输入文本',
            documentation: '清空当前聚焦的输入框，并输入 ${1:文本}',
            sortText: '01_06',
            range: range
          },
          {
            label: 'device.get_input_field_text',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.get_input_field_text()',
            detail: '获取输入框文本',
            documentation: '获取当前聚焦的输入框中的文本',
            sortText: '01_07',
            range: range
          },
          {
            label: 'device.ask_question',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.ask_question("${1:问题}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '询问用户问题',
            documentation: '询问用户一个问题，并返回答案',
            sortText: '01_08',
            range: range
          },
          {
            label: 'device.notify_message',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.notify_message("${1:消息}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '通知用户一条消息',
            documentation: '通知用户一条消息',
            sortText: '01_09',
            range: range
          },
          {
            label: 'device.notify_table',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.notify_table("${1:表格名称}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '通知用户一张表格',
            documentation: '通知用户一张表格',
            sortText: '01_10',
            range: range
          },
          {
            label: 'device.notify_image',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.notify_image("${1:图片名称}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '通知用户一张图片',
            documentation: '通知用户一张图片',
            sortText: '01_11',
            range: range
          },
          // {
          //   label: 'device.show_highlight',
          //   kind: monaco.languages.CompletionItemKind.Method,
          //   insertText: '.show_highlight(${1:x}, ${2:y}, ${3:radius})',
          //   insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          //   detail: '显示高亮',
          //   documentation: '显示一个高亮在 (x, y) 位置，半径为 ${1:100} 像素',
          //   sortText: '01_10',
          //   range: range
          // },
          // {
          //   label: 'device.hide_highlight',
          //   kind: monaco.languages.CompletionItemKind.Method,
          //   insertText: '.hide_highlight()',
          //   insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          //   detail: '隐藏高亮',
          //   documentation: '隐藏所有高亮',
          //   sortText: '01_11',
          //   range: range
          // },
          {
            label: 'device.get_clipboard',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.get_clipboard()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '获取剪贴板文本',
            documentation: '获取剪贴板中的文本',
            sortText: '01_12',
            range: range
          },
          {
            label: 'device.set_clipboard',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.set_clipboard("${1:文本}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '设置剪贴板文本',
            documentation: '将 ${1:文本} 设置到剪贴板',
            sortText: '01_13',
            range: range
          },
          {
            label: 'device.expand_notification_panel',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.expand_notification_panel()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '展开通知面板',
            documentation: '展开通知面板',
            sortText: '01_14',
            range: range
          },
          {
            label: 'device.take_screenshot',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.take_screenshot()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '截屏',
            documentation: '截取当前界面的截图',
            sortText: '01_15',
            range: range
          }
        ];
        return { suggestions: deviceSuggestions };
      }
      else if (lineContent.includes('device.')) {
        const deviceSuggestions = [
          {
            label: 'device.start_app',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.start_app("${1:app_name}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Start an app',
            documentation: 'Open app named app_name',
            sortText: '01_01',
            range: range
          },
          {
            label: 'device.kill_app',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.kill_app("${1:app_name}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Kill an app',
            documentation: 'Kill app named app_name',
            sortText: '01_02',
            range: range
          },
          {
            label: 'device.click',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.click(${1:x}, ${2:y})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Click at (x, y)',
            documentation: 'Click at (x, y) with duration milliseconds',
            sortText: '01_03',
            range: range
          },
          {
            label: 'device.long_click',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.long_click(${1:x}, ${2:y})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Long click at (x, y)',
            documentation: 'Long click at (x, y) with duration milliseconds',
            sortText: '01_04',
            range: range
          },
          {
            label: 'device.input',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.input("${1:text}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Input text',
            documentation: 'Input text into the current focused input field',
            sortText: '01_05',
            range: range
          },
          {
            label: 'device.clear_and_input',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.clear_and_input("${1:text}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Clear and input text',
            documentation: 'Clear the current focused input field and input text into it',
            sortText: '01_06',
            range: range
          },
          {
            label: 'device.get_input_field_text',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.get_input_field_text()',
            detail: 'Get input field text',
            documentation: 'Get the text from the current focused input field',
            sortText: '01_07',
            range: range
          },
          {
            label: 'device.ask_question',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.ask_question("${1:question}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Ask a question',
            documentation: 'Ask user a question and return the answer',
            sortText: '01_08',
            range: range
          },
          {
            label: 'device.notify_message',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.notify_message("${1:message}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Notify user with a message',
            documentation: 'Notify user with a message',
            sortText: '01_09',
            range: range
          },
          {
            label: 'device.notify_table',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.notify_table("${1:table_name}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Notify user with a table',
            documentation: 'Notify user with a table',
            sortText: '01_10',
            range: range
          },
          {
            label: 'device.notify_image',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.notify_image("${1:image_name}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Notify user with an image',
            documentation: 'Notify user with an image',
            sortText: '01_11',
            range: range
          },
          // {
          //   label: 'device.show_highlight',
          //   kind: monaco.languages.CompletionItemKind.Method,
          //   insertText: '.show_highlight(${1:x}, ${2:y}, ${3:radius})',
          //   insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          //   detail: 'Show highlight',
          //   documentation: 'Show a highlight at (x, y) with radius pixels',
          //   sortText: '01_10',
          //   range: range
          // },
          // {
          //   label: 'device.hide_highlight',
          //   kind: monaco.languages.CompletionItemKind.Method,
          //   insertText: '.hide_highlight()',
          //   insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          //   detail: 'Hide highlight',
          //   documentation: 'Hide the highlight',
          //   sortText: '01_11',
          //   range: range
          // },
          {
            label: 'device.get_clipboard',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.get_clipboard()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Get clipboard text',
            documentation: 'Get the text from the clipboard',
            sortText: '01_12',
            range: range
          },
          {
            label: 'device.set_clipboard',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.set_clipboard("${1:text}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Set clipboard text',
            documentation: 'Set the text to the clipboard',
            sortText: '01_13',
            range: range
          },
          {
            label: 'device.expand_notification_panel',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.expand_notification_panel()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Expand notification panel',
            documentation: 'Expand the notification panel',
            sortText: '01_14',
            range: range
          },
          {
            label: 'device.take_screenshot',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.take_screenshot()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Take screenshot',
            documentation: 'Take a screenshot of the current screen',
            sortText: '01_15',
            range: range
          }
        ];
        return { suggestions: deviceSuggestions };
      }
      
      // 'ui.locate_view().' 的补全建议
      if (/ui\.locate_view\(.*\)\./.test(lineContent) && appLanguage === 'zh') {
        const uiLocateViewSuggestions = [
          {
            label: 'ui.locate_view().click()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.click()',
            detail: '点击该组件',
            documentation: '点击该组件',
            sortText: '02_01',
            range: range
          },
          {
            label: 'ui.locate_view().input("文本")',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.input("${1:文本}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '输入文本',
            documentation: '在当前组件中输入 ${1:文本}',
            sortText: '02_02',
            range: range
          },
          {
            label: 'ui.locate_view().input_by_pasting("文本")',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.input_by_pasting("${1:文本}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '通过粘贴输入文本',
            documentation: '在当前组件中粘贴 ${1:文本}',
            sortText: '02_02',
            range: range
          },
          {
            label: 'ui.locate_view().long_click()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.long_click()',
            detail: '长按',
            documentation: '长按当前组件1秒',
            sortText: '02_03',
            range: range
          },
          {
            label: 'ui.locate_view().scroll_up()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_up()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '向上滑动',
            documentation: '向上滑动当前组件',
            sortText: '02_04',
            range: range
          },
          {
            label: 'ui.locate_view().scroll_down()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_down()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '向下滑动',
            documentation: '向下滑动当前组件',
            sortText: '02_05',
            range: range
          },
          {
            label: 'ui.locate_view().scroll_left()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_left()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '向左滑动',
            documentation: '向左滑动当前组件',
            sortText: '02_06',
            range: range
          },
          {
            label: 'ui.locate_view().scroll_right()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_right()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '向右滑动',
            documentation: '向右滑动当前组件',
            sortText: '02_07',
            range: range
          },
          {
            label: 'ui.locate_view().content()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.content()',
            detail: '获取组件内容',
            documentation: '获取当前组件所展示的文本内容',
            sortText: '02_08',
            range: range
          },
          {
            label: 'ui.locate_view().scroll_until("描述", "方向")',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_until("${1:描述}", ${2:direction="down"}, max_retry=${3:10})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '滑动直到',
            documentation: '滑动当前组件直到给定描述出现',
            sortText: '02_09',
            range: range
          },
          {
            label: 'ui.locate_view().get_input_field_text()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.get_input_field_text()',
            detail: '获取输入框文本',
            documentation: '获取当前组件（输入框）中的文本',
            sortText: '02_10',
            range: range
          },
          {
            label: 'ui.locate_view().show_highlight()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.show_highlight()',
            detail: '显示高亮',
            documentation: '将当前组件的高亮显示',
            sortText: '02_11',
            range: range
          }
        ];
        return { suggestions: uiLocateViewSuggestions };
      }
      else if (/ui\.locate_view\(.*\)\./.test(lineContent)) {
        const uiLocateViewSuggestions = [
          {
            label: 'ui.locate_view().click()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.click()',
            detail: 'Click a view',
            documentation: 'Click the view',
            sortText: '02_01',
            range: range
          },
          {
            label: 'ui.locate_view().input("text")',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.input("${1:text}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Input text',
            documentation: 'Input text into this view',
            sortText: '02_02',
            range: range
          },
          {
            label: 'ui.locate_view().input_by_pasting("text")',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.input_by_pasting("${1:text}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Input text by pasting',
            documentation: 'Input text into this view by pasting',
            sortText: '02_02',
            range: range
          },
          {
            label: 'ui.locate_view().long_click()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.long_click()',
            detail: 'Long click',
            documentation: 'Long click the view for 1 second',
            sortText: '02_03',
            range: range
          },
          {
            label: 'ui.locate_view().scroll_up()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_up()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Scroll up',
            documentation: 'Scroll up this view',
            sortText: '02_04',
            range: range
          },
          {
            label: 'ui.locate_view().scroll_down()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_down()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Scroll down',
            documentation: 'Scroll down this view',
            sortText: '02_05',
            range: range
          },
          {
            label: 'ui.locate_view().scroll_left()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_left()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Scroll left',
            documentation: 'Scroll left this view',
            sortText: '02_06',
            range: range
          },
          {
            label: 'ui.locate_view().scroll_right()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_right()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Scroll right',
            documentation: 'Scroll right this view',
            sortText: '02_07',
            range: range
          },
          {
            label: 'ui.locate_view().content()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.content()',
            detail: 'Get view content',
            documentation: 'Fetch content from this view',
            sortText: '02_08',
            range: range
          },
          {
            label: 'ui.locate_view().scroll_until("desc", "direction")',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_until("${1:desc}", direction=${2:"down"}, max_retry=${3:10})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Scroll until',
            documentation: 'Scroll this view until desc is fulfilled',
            sortText: '02_09',
            range: range
          },
          {
            label: 'ui.locate_view().get_input_field_text()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.get_input_field_text()',
            detail: 'Get input field text',
            documentation: 'Get the text from this input field view',
            sortText: '02_10',
            range: range
          },
          {
            label: 'ui.locate_view().show_highlight()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.show_highlight()',
            detail: 'Show highlight',
            documentation: 'Show a highlight on this view with a radius of 100 pixels.',
            sortText: '02_11',
            range: range
          }
        ];
        return { suggestions: uiLocateViewSuggestions };
      }

      // 'ui.' 的补全建议
      if (lineContent.includes('ui.') && appLanguage === 'zh') {
        const uiSuggestions = [
          {
            label: 'ui.locate_view()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.locate_view("${1:组件描述}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '定位一个组件',
            documentation: '根据给定描述，定位一个组件',
            sortText: '02_01',
            range: range
          },
          {
            label: 'ui.iterate_views()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.iterate_views("${1:描述}", limit=${2:-1}, direction=${3:"up"})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '枚举所有相似的元素',
            documentation: '在当前组件中，枚举所有相似的元素。枚举完成后，屏幕会自动滚动，滚动方向由 `direction` 指定（默认向上）。你也可以指定 `direction` 为 "up", "down", "left", "right" 中的一个。`limit` 是枚举的最大数量，默认是 -1（没有限制）。',
            sortText: '02_02',
            range: range
          },
          {
            label: 'ui.back()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.back()',
            detail: '返回上一个界面',
            documentation: '返回上一个界面',
            sortText: '02_03',
            range: range
          },

          {
            label: 'ui.home()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.home()',
            detail: '返回手机主界面',
            documentation: '返回手机主界面',
            sortText: '02_04',
            range: range
          },
          {
            label: 'ui.check()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.check("${1:描述}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '检查当前界面是否包含给定描述',
            documentation: '检查当前界面是否包含给定描述',
            sortText: '02_05',
            range: range
          },
          {
            label: 'ui.ensure()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.ensure("${1:描述}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '确保',
            documentation: '通过一次点击操作，确保当前界面满足描述的内容（用于自动关闭广告、弹窗）',
            sortText: '02_06',
            range: range
          },
          {
            label: 'ui.scroll_up()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_up()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '向上滑动',
            documentation: '向上滑动当前组件',
            sortText: '02_07',
            range: range
          },
          {
            label: 'ui.scroll_down()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_down()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '向下滑动',
            documentation: '向下滑动当前组件',
            sortText: '02_08',
            range: range
          },
          {
            label: 'ui.scroll_left()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_left()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '向左滑动',
            documentation: '向左滑动当前组件',
            sortText: '02_09',
            range: range
          },
          {
            label: 'ui.scroll_right()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_right()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '向右滑动',
            documentation: '向右滑动当前组件',
            sortText: '02_10',
            range: range
          },
          {
            label: 'ui.scroll_until()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_until("${1:描述}", direction=${2:"down"}, max_retry=${3:10})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '滑动直到',
            documentation: '滑动当前组件直到给定描述出现',
            sortText: '02_11',
            range: range
          },
          {
            label: 'ui.back_to()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.back_to("${1:描述}", max_steps=${2:5})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '返回，直到给定描述出现',
            documentation: '不断返回，直到界面中出现了给定描述的组件',
            sortText: '02_12',
            range: range
          },
          {
            label: 'ui.wait_until()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.wait_until("${1:描述}", waitInterval=${2:0.5}, timeout=${3:5})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '等待，直到给定描述出现',
            documentation: '等待，直到界面中出现了给定描述的组件。`waitInterval` 是等待时检查的间隔时间（单位：秒，浮点型），`timeout` 是超时时间（单位：秒，默认是 5 秒）。',
            sortText: '02_13',
            range: range
          },
          {
            label: 'ui.snapshot()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.snapshot()',
            detail: '获取当前组件的截图',
            documentation: '获取当前组件的截图',
            sortText: '02_14',
            range: range
          },
          {
            label: 'ui.hide_highlight()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.hide_highlight()',
            detail: '隐藏所有高亮',
            documentation: '隐藏所有高亮',
            sortText: '02_15',
            range: range
          }
        ];
        return { suggestions: uiSuggestions };
      }
      else if (lineContent.includes('ui.')) {
        const uiSuggestions = [
          {
            label: 'ui.locate_view()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.locate_view("${1:description}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Locate a view',
            documentation: 'Locate the view described by description',
            sortText: '02_01',
            range: range
          },
          {
            label: 'ui.iterate_views()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.iterate_views("${1:description}", limit=${2:-1}, direction=${3:"up"})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Enumerate all similar elements related to the description.',
            documentation: 'Yield the views that match `description` inside the current view. After enumerating all views in the current interface, the screen will automatically scroll in the direction specified by `direction` (default is "up"). You can also specify `direction` as one of "left", "down", or "right". `limit` is the maximum number of views to enumerate, default is -1 (no limit).',
            sortText: '02_02',
            range: range
          },
          {
            label: 'ui.back()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.back()',
            detail: 'Navigate back',
            documentation: 'Navigate back from current screen',
            sortText: '02_03',
            range: range
          },

          {
            label: 'ui.home()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.home()',
            detail: 'Navigate home',
            documentation: 'Navigate to the home screen',
            sortText: '02_04',
            range: range
          },
          {
            label: 'ui.check()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.check("${1:description}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Check screen state',
            documentation: 'Check whether the current screen state matches description',
            sortText: '02_05',
            range: range
          },
          {
            label: 'ui.ensure()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.ensure("${1:description}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Ensure view exists',
            documentation: 'Ensure the view described by description is present',
            sortText: '02_06',
            range: range
          },
          {
            label: 'ui.scroll_up()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_up()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Scroll up',
            documentation: 'Scroll up this view',
            sortText: '02_07',
            range: range
          },
          {
            label: 'ui.scroll_down()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_down()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Scroll down',
            documentation: 'Scroll down this view',
            sortText: '02_08',
            range: range
          },
          {
            label: 'ui.scroll_left()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_left()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Scroll left',
            documentation: 'Scroll left this view',
            sortText: '02_09',
            range: range
          },
          {
            label: 'ui.scroll_right()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_right()',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Scroll right',
            documentation: 'Scroll right this view',
            sortText: '02_10',
            range: range
          },
          {
            label: 'ui.scroll_until()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.scroll_until("${1:desc}", direction=${2:"down"}, max_retry=${3:10})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Scroll until',
            documentation: 'Scroll this view until desc is fulfilled',
            sortText: '02_11',
            range: range
          },
          {
            label: 'ui.back_to()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.back_to("${1:description}", max_steps=${2:5})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Back to',
            documentation: 'Back to the view described by description',
            sortText: '02_12',
            range: range
          },
          {
            label: 'ui.wait_until()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.wait_until("${1:description}", waitInterval=${2:0.5}, timeout=${3:5})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Wait until view appears',
            documentation: 'Wait for a view described by description to appear. `waitInterval` is the interval time (in seconds, float) of checking, `timeout` is the timeout time (in seconds, default is 5 seconds).',
            sortText: '02_13',
            range: range
          },
          {
            label: 'ui.snapshot()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.snapshot()',
            detail: 'Get view snapshot',
            documentation: 'Get snapshot UI_View instance of this view',
            sortText: '02_14',
            range: range
          },
          {
            label: 'ui.hide_highlight()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.hide_highlight()',
            detail: 'Hide all highlights',
            documentation: 'Hide all highlights on the screen.',
            sortText: '02_15',
            range: range
          }
        ];
        return { suggestions: uiSuggestions };
      }


      // 'fm.' 的补全建议
      if (lineContent.includes('fm.') && appLanguage === 'zh') {
        const fmSuggestions = [
          {
            label: 'fm.query',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.query(device.take_screenshot(), "${1:文本提示词}", returns=[("${2:名称}", ${3:数据类型})])',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '调用大模型',
            documentation: '调用大模型，支持文本/视觉输入，returns 为列表类型，其中可以包含多个 python 元组，每个元组包含两个元素，第一个元素为返回值的名称描述，第二个元素为返回值的数据类型。',
            sortText: '03_01',
            range: range
          },
          {
            label: 'fm.llm',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.llm("${1:提示词}", returns=[("${2:名称}", ${3:数据类型})])',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '调用大语言模型',
            documentation: '调用大语言模型，returns 为列表类型，其中可以包含多个 python 元组，每个元组包含两个元素，第一个元素为返回值的名称描述，第二个元素为返回值的数据类型。',
            sortText: '03_02',
            range: range
          },
          {
            label: 'fm.vlm',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.vlm(device.take_screenshot(), "${1:提示词}", returns=[("${2:名称}", ${3:数据类型})])',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '调用视觉语言模型',
            documentation: '调用视觉语言模型，returns 为列表类型，其中可以包含多个 python 元组，每个元组包含两个元素，第一个元素为返回值的名称描述，第二个元素为返回值的数据类型。',
            sortText: '03_03',
            range: range
          }
        ]
        return { suggestions: fmSuggestions };
      }
      else if (lineContent.includes('fm.')) {
        const fmSuggestions = [
          {
            label: 'fm.query',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.query(device.take_screenshot(), "${1:prompt}", returns=[("${2:name}", ${3:type})])',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Query large model',
            documentation: 'Query large model. Supports text/visual input. `returns` is a list type, which can contain multiple python tuples. Each tuple contains two elements, the first element is the name description of the return value, and the second element is the data type of the return value.',
            sortText: '03_01',
            range: range
          },
          {
            label: 'fm.llm',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.llm("${1:prompt}", returns=[("${2:name}", ${3:type})])',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Query large language model',
            documentation: 'Query large language model. `returns` is a list type, which can contain multiple python tuples. Each tuple contains two elements, the first element is the name description of the return value, and the second element is the data type of the return value.',
            sortText: '03_02',
            range: range
          },
          {
            label: 'fm.vlm',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.vlm(device.take_screenshot(), "${1:prompt}", returns=[("${2:name}", ${3:type})])',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Query vision language model',
            documentation: 'Query vision language model. `returns` is a list type, which can contain multiple python tuples. Each tuple contains two elements, the first element is the name description of the return value, and the second element is the data type of the return value.',
            sortText: '03_03',
            range: range
          }
        ]
        return { suggestions: fmSuggestions };
      }

      // 'tools.' 的补全建议
      if (lineContent.includes('tools.') && appLanguage === 'zh') {
        const toolsSuggestions = [
          {
            label: 'tools.time_now',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.time_now()',
            detail: '获取当前时间',
            documentation: '获取当前时间，返回 datetime.now() 对象',
            sortText: '04_01',
            range: range
          },
          {
            label: 'tools.time_tag',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.time_tag(${1:time=None})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '获取当前时间标签',
            documentation: '获取当前时间标签，返回当前时间的时间戳',
            sortText: '04_02',
            range: range
          },
          {
            label: 'tools.get_temp_file_path',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.get_temp_file_path(${1:suffix=\'.png\'})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '获取临时文件路径',
            documentation: '获取临时文件路径，返回临时文件的路径',
            sortText: '04_03',
            range: range
          },
          {
            label: 'tools.open_image_url',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.open_image_url("${1:image_url}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '打开图片',
            documentation: '打开图片，返回图片的 URL',
            sortText: '04_04',
            range: range
          },
          {
            label: 'tools.image_to_base64_url',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.image_to_base64_url()',
            detail: '将图片转换为 base64 URL',
            documentation: '将图片转换为 base64 URL',
            sortText: '04_05',
            range: range
          }
        ];
        return { suggestions: toolsSuggestions };
      }
      else if (lineContent.includes('tools.')) {
        const toolsSuggestions = [
          {
            label: 'tools.time_now',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.time_now()',
            detail: 'Get current time',
            documentation: 'Get the current time as datetime.now()',
            sortText: '04_01',
            range: range
          },
          {
            label: 'tools.time_tag',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.time_tag(${1:time=None})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Get time tag',
            documentation: 'Get the time tag of the current time',
            sortText: '04_02',
            range: range
          },
          {
            label: 'tools.get_temp_file_path',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.get_temp_file_path(${1:suffix=\'.png\'})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Get temp file path',
            documentation: 'Get the path of a temporary file',
            sortText: '04_03',
            range: range
          },
          {
            label: 'tools.open_image_url',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.open_image_url("${1:image_url}")',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Open image from URL',
            documentation: 'Open an image from a URL',
            sortText: '04_04',
            range: range
          },
          {
            label: 'tools.image_to_base64_url',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.image_to_base64_url()',
            detail: 'Convert image to base64 URL',
            documentation: 'Convert an image to a base64 URL',
            sortText: '04_05',
            range: range
          }
        ];
        return { suggestions: toolsSuggestions };
      }

      // 'data.livetable().' 的补全建议
      if (/data\.live_table\(\)\./.test(lineContent) && appLanguage === 'zh') {
        const livetableSuggestions = [
          {
            label: 'data.livetable().add_row()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.add_row({"${1:键}": "${2:值}"})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: '添加一行',
            documentation: '添加一行到实时表格',
            sortText: '05_01',
            range: range
          }
        ];
        return { suggestions: livetableSuggestions };
      }
      else if (/data\.live_table\(\)\./.test(lineContent)) {
        const livetableSuggestions = [
          {
            label: 'data.livetable().add_row()',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: '.add_row({"${1:key}": "${2:value}"})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            detail: 'Add a row',
            documentation: 'Add a row to the live table',
            sortText: '05_01',
            range: range
          }
        ];
        return { suggestions: livetableSuggestions };
      }

      // 'data.' 的补全建议
      if (lineContent.includes('data.') && appLanguage === 'zh') {
        const dataSuggestions = [
          // - `data.live_table`: Storing information as a table.
          //   - `livetable.add_row(row_dict)`: add a row.
          // - `data.image`: Store information as an image.
          {
            label: 'data.live_table',
            kind: monaco.languages.CompletionItemKind.Module,
            insertText: '.live_table()',
            detail: '实时表格',
            documentation: '存储信息为表格',
            sortText: '05_01',
            range: range
          },
          {
            label: 'data.image',
            kind: monaco.languages.CompletionItemKind.Module,
            insertText: '.image()',
            detail: '图片',
            documentation: '存储信息为图片',
            sortText: '05_02',
            range: range
          }
        ]
        return { suggestions: dataSuggestions };
      }
      else if (lineContent.includes('data.')) {
        const dataSuggestions = [
          // - `data.live_table`: Storing information as a table.
          //   - `livetable.add_row(row_dict)`: add a row.
          // - `data.image`: Store information as an image.
          {
            label: 'data.live_table',
            kind: monaco.languages.CompletionItemKind.Module,
            insertText: '.live_table()',
            detail: 'Live table',
            documentation: 'Storing information as a table',
            sortText: '05_01',
            range: range
          },
          {
            label: 'data.image',
            kind: monaco.languages.CompletionItemKind.Module,
            insertText: '.image()',
            detail: 'Image',
            documentation: 'Store information as an image',
            sortText: '05_02',
            range: range
          }
        ]
        return { suggestions: dataSuggestions };
      }


      // 根据语言选择补全建议
      const suggestions = appLanguage === 'zh' 
        ? getChineseCompletionSuggestions(range)
        : getEnglishCompletionSuggestions(range);
      // const suggestions = getEnglishCompletionSuggestions(range);
      
      return { suggestions };
    }
  });

  return currentDisposable;
};

/**
 * 更新补全提供者的语言
 * @param appLanguage 新的应用语言设置
 */
export const updateCompletionProviderLanguage = (appLanguage: string) => {
  return registerCompletionProvider(appLanguage);
};