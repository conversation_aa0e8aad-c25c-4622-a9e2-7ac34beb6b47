export const translations = {
  zh: {
    welcome: "您好，欢迎使用 Ruyi Studio！您可以告诉我您的需求，我来帮您生成执行步骤，请问有什么任务可以帮您？😊",
    welcomeTitle: "您好，我是如意智能助手！",
    welcomeDescription: "我可以帮您在手机上完成任何任务，快点击上方的新建按钮开始探索吧！😊",
    welcomeTaskExample: "✨ 您可能想尝试以下任务：",
    dialogueArea: "对话区",

    inputPlaceholder: "输入消息...",
    chatInputPlaceholder: "大模型帮您智能修改，提出您的需求吧！",
    chatDisabledDuringSystemPromptView: "请手动修改任务提示",

    startDemo: "开始演示",
    startingDemo: "正在启动演示...",
    startDemoComplete: "演示启动成功",
    startDemoError: "演示启动失败，请重试",
    demoLine: "演示此行",

    endDemo: "结束演示",
    endingDemo: "正在基于演示进行优化...",
    endDemoComplete: "基于演示优化完成",
    endDemoError: "基于演示优化失败，请重试",

    replyText: "文本回复",
    replyingTo: "正在回复：",
    replyingText: "正在基于文本进行优化...",
    replyComplete: "基于文本优化完成",
    replyError: "基于文本优化失败，请重试",

    // 新增：区分不同场景的进行中和完成提示
    taskCreationProcessing: "正在处理任务需求...",
    taskOptimizationProcessing: "正在优化任务...",
    conversationProcessing: "正在处理回复...",
    conversationReplyComplete: "回复完成",
    taskCreationReplyComplete: "任务需求处理完成",
    taskOptimizationComplete: "任务优化完成",

    abandonExplanation: "放弃说明",
    abandonExplanationMessage: "您已选择无需说明该场景",
    
    execute: "执行",
    executeLine: "执行/演示此行",
    executing: "正在为您启动执行服务...",
    executeComplete: "已为您启动执行",
    executeError: "执行服务启动失败，请重试",

    viewScript: "查看脚本",
    viewSteps: "查看步骤",
    viewAppKnowledge: "编辑知识库", 

    generatingScript: "正在为您生成任务步骤...",
    taskGenSuccess: "任务步骤生成成功",
    taskGenFailed: "任务步骤生成失败，请重试",
    
    needExplanation: "为了能够更好得实现您的任务，需要您对以下场景进行说明：",
    demoInstruction: "您可以通过在手机上进行演示或者通过对话说明。",
    originalNLScript: "# 自然语言描述的任务步骤",
    scriptSwitchFailed: "脚本语言切换失败，请重试",

    sendButton: "发送",
    sendMessageFailed: "发送消息失败",

    user: "用户",
    system: "系统",
    assistant: "助手",
    
    editorTitle: "编辑器",
    codeScriptTitle: "任务脚本",
    NLInstructionTitle: "任务步骤", 
    appKnowledgeTitle: "任务提示",
    projectKnowledgeTitle: "任务提示",
    editorUsageTitle: "编辑器使用说明",

    transferringScriptFromNLtoScript: "正在将任务步骤转换为执行脚本...",
    transferringScriptFromScriptToNL: "正在将执行脚本转换为任务步骤...",
    transferScriptFromNLtoScriptComplete: "任务步骤转换为执行脚本完成",
    transferScriptFromNLtoScriptError: "任务步骤转换为执行脚本失败，请重试",
    transferScriptFromScriptToNLComplete: "执行脚本转换为任务步骤完成",
    transferScriptFromScriptToNLError: "执行脚本转换为任务步骤失败，请重试",

    transferSelectedScriptFromNLToCode: "正在将任务步骤转换为代码脚本...",
    transferSelectedScriptFromNLToCodeComplete: "任务步骤转换为代码脚本完成",
    transferSelectedScriptFromNLToCodeError: "任务步骤转换为代码脚本失败，请重试",

    // 新增：工作流转换为代码脚本相关翻译
    executionNeedsCodeScript: "正在分析任务步骤，准备启动执行...",
    generateCodeScriptBeforeExecutionComplete: "任务步骤分析完成，准备就绪",
    generateCodeScriptBeforeExecutionError: "任务步骤分析失败，请重试",

    ruyiSpace: "如意项目",
    createNew: "新建项目",
    saveSpace: "保存",
    publishSpace: "发布",
    createNewAgent: "新建项目",
    createNewProjectModalTitle: "创建新项目",
    projectName: "项目名称",
    projectNamePlaceholder: "请输入项目名称",
    projectDescriptionPlaceholder: "请输入项目描述（可选）",
    projectNameRequired: "项目名称不能为空",
    uploadAgent: "保存",
    saveProject: "保存项目",
    savingProject: "正在保存项目...",
    saveProjectSuccess: "项目保存成功并已推送到云端仓库",
    saveProjectFailed: "项目保存失败",
    saveProjectNoChanges: "项目没有变更，无需保存",
    saveProjectNeedLoad: "请先创建或载入一个项目再进行保存",
    loadAgent: "载入",
    publishAgent: "发布",
    load: "载入项目",
    selectAgentToLoad: "选择要载入的如意项目",
    createNewAgentSuccess: "创建新任务成功",
    uploadSuccess: "任务保存成功",
    uploadFailed: "保存任务失败",
    loadSuccess: "如意项目载入成功",
    loadFailed: "载入如意项目失败",
    publishSuccess: "如意项目发布成功",
    publishFailed: "发布如意项目失败",
    getAgentListFailed: "获取如意项目列表失败",

    isPublished: "已发布",
    isNotPublished: "未发布",
    totalPublishedVersions: "已发布版本总数",
    publishedVersions: "已发布版本",
    viewPublishedVersions: "已发布版本",
    viewPublishedVersionsSuccess: "获取发布版本成功",
    viewPublishedVersionsFailed: "获取发布版本失败",
    
    createProjectSuccess: "创建项目成功",
    createProjectFailed: "创建项目失败",
    
    // TaskFilesList 新增翻译键
    fileName: "文件名称",
    fileContent: "文件内容",
    fileNameRequired: "文件名称不能为空",
    noProjectSelected: "请先选择一个项目",
    createFileFailed: "创建文件失败",
    createFileSuccess: "文件创建成功",
    createFilePushFailed: "文件创建成功但推送失败",
    createNewFile: "创建新文件",
    fileNamePlaceholder: "请输入文件名称（如：main.py, index.html）",
    fileContentPlaceholder: "请输入文件内容（可选）",
    newFile: "新建文件",

    appKnowledgeFormatTitle: "知识库编写指南",
    appKnowledgeFormatDesc: "知识库可以帮助 Ruyi 更好得为您生成任务步骤。请按照规定格式编写知识库，包含任务名称和任务步骤。每个任务之间用空行分隔。",
    appKnowledgeExample: "示例格式：",
    appKnowledgeExampleContent: 
`任务: 创建一个名为 "如意" 的联系人
步骤: 
  1. 打开 "联系人" 应用。
  2. 点击 "创建联系人" 按钮。
  3. 在 "姓名" 字段中输入 "如意"。
  4. 点击右上角的 "保存" 按钮。

任务: 在小红书中搜索 "如意"
步骤: 
  1. 点击右上角的 "搜索" 按钮。
  2. 在 "搜索" 字段中输入 "如意"。
  3. 点击 "搜索" 按钮。`,

    websocketConnectionError: "Ruyi Assistant 连接失败，请先启动 Ruyi Assistant 的远程控制模式",

    selectDevice: "设备列表",
    selectDeviceError: "选择该设备失败，请重试",

    // 设备连接状态相关
    deviceDisconnected: "设备已断开连接",
    deviceDisconnectedDesc: "手机已经断开连接，请选择其他设备或重新连接手机",
    deviceReconnected: "设备已重新连接",
    deviceReconnectedDesc: "手机已重新连接，投屏将自动恢复",
    deviceReconnecting: "设备重新连接中",
    deviceReconnectingDesc: "正在重新建立手机投屏连接，请稍候...",
    selectOtherDevice: "选择其他设备",
    reconnectDevice: "重新连接",

    ruyiClientInstallSuccess: 'Ruyi Assistant 安装成功！',
    ruyiClientInstallFailed: 'Ruyi Assistant 安装失败，请重试',

    checkingRuyiClient: '正在检查 Ruyi Assistant...',
    updatingRuyiClient: '正在更新 Ruyi Assistant...',
    ruyiClientReady: 'Ruyi Assistant 就绪',
    ruyiClientChecking: 'Ruyi Assistant 正在检查中，请稍候...',
    ruyiClientUpdating: 'Ruyi Assistant 正在更新中，请稍候...',
    ruyiClientError: 'Ruyi Assistant 检查失败',
    ruyiClientCheckFailed: 'Ruyi Assistant 检查失败，请重试',
    
    grantingPermissions: '正在授予权限...',
    permissionsGranted: '权限已授予',
    permissionsGrantFailed: '权限授予失败',
    permissionsPartiallyGranted: '权限部分授予成功',

    saveHistoryModalTitle: "保存历史记录",
    saveHistoryModalPlaceholder: "请输入保存说明...",
    saveHistorySuccess: "历史记录保存成功",
    saveHistoryFailed: "历史记录保存失败",

    publishFormTitle: "发布任务",
    publishFormName: "任务名称",
    publishFormDescription: "任务描述",
    publishFormVersion: "版本号",
    publishFormReleaseNotes: "发布说明",

    publishNeedSelectAgent: "请先创建或切换到某个项目",
    publishNeedSaveFirst: "请先保存当前项目后再进行发布",

    publishManagement: "发布管理",
    publishNewVersion: "发布 ",
    viewPublishHistory: "发布历史",
    publishHistory: "发布历史",
    releaseNotes: "发布说明",
    publishTime: "发布时间",
    
    publishFormNameRequired: "请填写发布名称",
    publishFormDescriptionRequired: "请填写发布描述",
    publishFormVersionRequired: "请填写版本号",

    viewHistory: "修改历史",
    viewProjectHistory: "查看项目历史",
    projectHistory: "项目历史",
    totalHistory: "历史记录总数",
    viewHistoryNeedSelectAgent: "请先创建或切换到某个任务",
    viewHistoryFailed: "获取历史记录失败",
    projectId: "项目ID",
    messages: "消息",
    messagesCount: "条",

    viewDetail: "查看详情",
    historyDetail: "历史记录详情",
    basicInfo: "基本信息",
    historyId: "历史记录ID",
    comment: "备注",
    createTime: "创建时间",
    scriptInfo: "脚本信息",
    codeScript: "代码脚本",
    NLScript: "自然语言脚本",
    messageHistory: "消息历史",
    complete: "已完成",
    demoing: "演示中",
    error: "错误",
    loading: "加载中",
    question: "问题",

    uploadNeedTask: "当前还没有任务，先在创建一个新任务吧！",

    batchExecute: "批量执行",
    batchExecuteTitle: "批量执行",
    batchExecuteDesc: "批量执行支持在大量的手机设备上并行执行任务，可用于软件测试等广泛领域。",
    batchExecuteContact: "使用此功能，请联系李博士",
    batchExecuteEmail: "邮箱：<EMAIL>",
    batchExecutePhone: "手机号：2345678",

    settings: "Ruyi Studio 设置",
    modelSettings: "模型设置（尚未支持）",
    portSettings: "本地端口设置",
    browserSettings: "浏览器设置",
    browserHomepage: "浏览器默认首页",
    browserHomepagePlaceholder: "请输入URL（如：https://www.baidu.com）",
    browserHomepageInvalid: "请输入有效的URL地址",
    browserSettingsChanged: "浏览器设置已更新",
    browserSettingsChangeFailed: "浏览器设置更新失败，请重试",
    defaultSearchEngine: "默认搜索引擎",
    searchEngineGoogle: "Google",
    searchEngineBaidu: "百度",
    searchEngineBing: "Bing",
    searchEngineSougou: "搜狗",
    defaultSearchEngineChanged: "默认搜索引擎已更新",
    defaultSearchEngineChangeFailed: "默认搜索引擎更新失败，请重试",
    modelType: "模型类型",
    apiKey: "API Key",
    screencastPort: "手机投屏端口",
    backendPort: "后端端口",
    saveSettings: "保存设置",
    portChangeMessage: "成功修改本地端口配置，新配置将在重启 Studio 之后生效",
    settingsSaved: "设置已保存",
    defaultAvatar: "默认头像",
    userProfile: "用户信息",
    username: "用户名",
    displayName: "显示名称",
    email: "邮箱",
    notSet: "未设置",
    githubId: "GitHub ID",
    wechatId: "微信 ID",

    loadPortConfigFailed: '加载端口配置失败',
    setPortConfigFailed: '保存端口配置失败',

    taskPort: "执行任务端口",
    electronHttpPort: "浏览器服务端口",
    portRangeWarning: "端口号必须在 1024-65535 之间",
    invalidPortRange: "请确保所有端口号都在有效范围内（1024-65535）",
    duplicatePorts: "端口不能重复，请检查并重试",

    stopExecute: "停止执行",
    stoppingExecute: "正在停止执行...",
    stopExecuteComplete: "执行已停止",
    stopExecuteError: "停止执行失败，请重试",

    autoDebug: "自动调试",

    deviceList: "设备列表",
    deviceId: "设备ID",
    productName: "产品名",
    status: "状态",
    noDevicesFound: "未检测到设备",
    unknownBrand: "未知品牌",
    unknownModel: "未知型号",
    unknownProduct: "未知产品",
    authorized: '已授权',
    unauthorized: "未授权",
    connected: "已连接",
    disconnected: '未连接',
    getDeviceListFailed: "获取设备列表失败",
    cloudPhone: "云手机",
    batchTestDevice: "批量测试设备",

    virtualDeviceUnsupported: "该设备暂未支持，如有需要，请联系 Ruyi 管理员",
    
    ruyiAssistantStatus: '如意助手 APP 状态',
    installed: '已安装',
    notInstalled: '未安装',
    noDeviceSelected: '未选择设备',
    switchDevice: '切换设备',
    howToConnect: '如何连接如意助手？',

    executeSelectedCode: "执行",
    demoSelectedCode: "演示",

    languageSettings: "语言设置",
    ideLanguage: "Studio 语言",
    ideLanguageChanged: "Studio 语言设置已更新",
    ideLanguageChangeFailed: "Studio 语言设置更新失败，请重试",
    appLanguage: "任务语言",
    appLanguageChanged: "任务语言设置已更新",
    appLanguageChangeFailed: "任务语言设置更新失败，请重试",

    loginTitle: "登录 Ruyi Studio（Beta）",
    registerTitle: "注册 Ruyi Studio 账号",
    usernameRequired: "请输入用户名或邮箱！",
    usernameCannotStartWithNumber: "用户名不能以数字开头",
    passwordRequired: "请输入密码！",
    confirmPasswordRequired: "请确认密码！",
    passwordMinLength: "密码长度不能少于8位！",
    passwordComplexity: "密码必须包含字母和数字",
    passwordMismatch: "两次输入的密码不一致！",
    usernamePlaceholder: "用户名或邮箱",
    usernameOnlyPlaceholder: "用户名",
    passwordPlaceholder: "密码",
    confirmPasswordPlaceholder: "确认密码",
    emailPlaceholder: "邮箱",
    loginButton: "登录",
    registerButton: "注册",
    rememberMe: "记住密码",
    noAccount: "还没有账号？",
    haveAccount: "已有账号？",
    registerNow: "立即注册",
    loginNow: "立即登录",
    backendStartFailed: "后端服务器启动失败：",
    checkBackendFailed: "检查后端服务器状态失败：",
    loginSuccess: "登录成功！",
    loginFailed: "登录失败，请重试！",
    registerSuccess: "注册成功！",
    registerFailed: "注册失败，请重试！",

    logout: "退出登录",
    logoutConfirmTitle: "确认退出",
    logoutConfirmContent: "退出登录会跳转回登录界面，当前未保存内容均会消失，是否确认？",
    logoutSuccess: "已成功退出登录",
    cancel: "取消",
    confirm: "确认",

    executionLog: '执行日志',
    createTaskModalTitle: "创建新任务",
    taskName: "任务名称",
    taskNameRequired: "请输入任务名称",
    taskNamePlaceholder: "任务简称（可选）",
    taskDescriptionPlaceholder: "任务详细描述（必填）",
    taskDescriptionRequired: "请输入任务描述",
    appName: "应用名称",
    appNamePlaceholder: "应用名称（可选）",

    createTask: "创建任务",
    createTaskSuccess: "任务创建成功",
    createTaskFailed: "任务创建失败",
    loadExampleTaskSuccess: "任务加载成功",

    downloadVH: "下载视图层级结构信息（VH）",
    downloadScreenshot: "下载截图",
    downloadSuccess: "下载成功",
    downloadFailed: "下载失败",
    downloadScreenshotSuccess: "截图下载成功",
    downloadScreenshotFailed: "截图下载失败",
    downloadVHSuccess: "VH 下载成功",
    downloadVHFailed: "VH 下载失败",

    delete: "删除",
    deleteSuccess: "删除成功",
    deleteFailed: "删除失败",
    deleteLocalFailed: "本地如意项目删除失败",
    deleteCloudFailed: "云端如意项目删除失败",
    deleteCloudAndLocalFailed: "如意项目删除失败",
    
    // 主进程删除相关消息
    taskNameEmpty: "任务名称不能为空",
    taskDirectoryNotExists: "任务目录不存在",
    taskDeleteSuccess: "任务删除成功",
    taskDeleteFailed: "删除任务失败",
    taskSaveSuccess: "任务保存成功",
    taskSaveFailed: "保存任务失败",
    taskNameAndFileNameEmpty: "任务名称和文件名称不能为空",
    fileExists: "文件已存在",
    fileCreateSuccess: "文件创建成功",
    fileCreateFailed: "创建文件失败",
    fileNotExists: "文件不存在",
    fileDeleteSuccess: "文件删除成功",
    fileDeleteFailed: "删除文件失败",
    fileUpdateSuccess: "文件更新成功",
    fileUpdateFailed: "更新文件失败",
    taskNameAndDirectoryNameEmpty: "任务名称和目录名称不能为空",
    directoryExists: "目录已存在",
    directoryCreateSuccess: "目录创建成功",
    directoryCreateFailed: "创建目录失败",
    taskNameAndDirectoryPathEmpty: "任务名称和目录路径不能为空",
    pathNotDirectory: "指定路径不是目录",
    directoryNotExists: "目录不存在",
    directoryDeleteSuccess: "目录删除成功",
    directoryDeleteFailed: "删除目录失败",
    parametersEmpty: "参数不能为空",
    originalFileNotExists: "原文件或目录不存在",
    newNameExists: "新名称已存在",
    renameSuccess: "重命名成功",
    renameFailed: "重命名失败",

    annotateScreenshot: "标注数据",
    annotationSuccess: "标注保存成功",
    annotationFailed: "未能成功获取手机截图，标注失败",
    saveAnnotationToCloudFailed: '标注数据云端保存失败',

    annotationModalTitle: '手工补丁标注面板',
    annotationLabelPlaceholder: '请输入对该元素的描述',
    annotationsList: '标注列表',
    save: '保存',
    add: '添加',
    addEnsureAction: '添加导航动作',

    saveToLocal: "保存到本地",
    saveSuccess: "保存成功",
    saveFailed: "保存失败",
    savedToPath: "已保存到路径",

    annotationMode: "标注模式",
    annotationItemLabelPlaceholder: "请输入单项描述",
    annotationGroupTitlePlaceholder: "请输入总体描述",
    checkDescriptionPlaceholder: "请输入检查描述",
    ensureDescriptionPlaceholder: "请输入目标描述",

    loadingScreenshot: "正在加载截图...",

    annotationLabelTitle: "元素描述",
    annotationLabelListTitle: "该元素的描述列表",
    annotationLabelAdd: "添加描述",
    annotationLabelDelete: "删除描述",
    annotationGroupTitle: "总体描述",
    annotationItemLabelTitle: "单项描述",
    annotationItemLabelListTitle: "单项描述列表",
    annotationItemLabelAdd: "添加描述",
    annotationItemLabelDelete: "删除描述",
    checkDescriptionTitle: "检查描述",
    checkValueTitle: "检查结果",
    ensureDescriptionTitle: "目标描述",
    position: "位置",

    description: "简介",
    cloudPhoneDescription: "在如意提供的云端手机上测试您的脚本，如果需要请联系 <EMAIL>",
    batchTestDeviceDescription: "在100台不同品牌的设备上测试您的脚本，如果需要请联系 <EMAIL>",

    tokenBalance: "Token 管理",
    tokenUsed: "已使用 Token",
    tokenRemaining: "剩余 Token",
    tokenExchange: "兑换 Token",
    tokenExchangePlaceholder: "请输入兑换码",
    tokenExchangeButton: "兑换",
    tokenExchangeSuccess: "兑换成功",
    tokenExchangeFailed: "兑换失败",

    emailRequired: "请输入邮箱！",
    verificationCodeRequired: "请输入验证码！",
    verificationCodePlaceholder: "验证码",
    sendVerificationCode: "发送验证码",
    verificationCodeSent: "验证码已发送",
    verificationCodeFailed: "验证码发送失败",
    emailFormatInvalid: "邮箱格式不正确",

    viewAnnotations: "补丁管理",
    noAnnotations: "暂无标注内容",
    loadAnnotationsFailed: "加载标注数据失败",
    deleteAll: "删除全部",
    deleteAllConfirmTitle: "确认删除全部标注",
    deleteAllConfirmContent: "此操作将永久删除所有本地标注数据，且无法恢复。是否确认？",
    deleteAllSuccess: "全部标注数据已删除",
    deleteAllFailed: "删除全部标注数据失败",

    screenshotPreview: "截图预览",
    annotationCount: "标注数量",
    checkValue: "检查值",
    groupTitle: "分组标题",
    ensureDescription: "确保描述",

    testModel: "测试模型",
    testMode: "测试模式",
    testModelDescription: "请输入描述文本",
    testModelButton: "测试",
    testModelModalTitle: "模型测试",
    testModelResult: "模型测试输出",
    originalImage: "原始截图",
    predictedImage: "测试结果展示",
    predictedCoordinate: "预测坐标",
    testModelFailed: "测试模型失败",
    getScreenshotFailed: "获取截图失败",
    noTestResults: "暂无测试结果",
    clickImageToDescribe: "点击截图上的任意位置获取描述",
    describeResult: "描述结果",
    clickCoordinate: "点击坐标",
    manualPatch: "手工补丁",

    resetPasswordTitle: "重置密码",
    resetPasswordButton: "发送重置链接",
    resetPasswordSuccess: "重置链接已发送到您的邮箱",
    resetPasswordFailed: "发送重置链接失败",
    forgotPassword: "忘记密码？",
    backToLogin: "返回登录",
    retryInSeconds: "重试（{seconds}秒）",

    tokenBalanceZeroTitle: "Token 余量不足",
    tokenBalanceZeroContent: "当前 Token 余量为 0，生成任务、执行等功能将会失败。请先充值。",
    insufficientTokenGenerateFirstScript: "Token 不足，生成任务失败，请充值后重试",
    insufficientTokenReply: "Token 余量不足，优化脚本失败，请充值后重试",
    insufficientTokenUpdateScript: "Token 余量不足，更新脚本失败，请充值后重试",
    insufficientTokenUpload: "Token 余量不足，更新脚本失败，导致上传失败，请充值后重试",
    insufficientTokenPublish: "Token 余量不足，更新脚本失败，导致发布失败，请充值后重试",
    insufficientTokenTransferSelectedScript: "Token 余量不足，选中脚本转换失败，请充值后重试",
    insufficientTokenTestModel: "Token 余量不足，测试模型失败，请充值后重试",
    insufficientTokenExecute: "Token 余量不足，执行任务失败，请充值后重试",

    newVersionTitle: "新版本已准备就绪",
    newVersionFoundTitle: "发现新版本",
    newVersionFoundContent: "发现新版本，是否立即下载？",
    downloadNow: "立即下载",
    downloadLater: "稍后下载",
    restartNow: "立即重启",
    restartLater: "稍后重启",
    saveWorkReminder: "注意：请先保存您的工作内容，再点击下方按钮重启应用进行更新。",
    updateDownloadedMessage: "更新已下载完成，是否立即重启以安装更新？",
    checkingUpdate: "正在检查更新...",
    updateAvailable: "发现新版本，正在后台下载...",
    updateNotAvailable: "当前已是最新版本",
    updateError: "更新出错",
    checkForUpdates: "检查更新",
    checkUpdateFailed: "检查更新失败",

    // Update notification related translations
    remindMeLater: "稍后提醒",
    remindMeLaterDescription: "选择提醒时间：",
    dismissThisVersion: "忽略此版本",
    remindLaterSet: "提醒已设置",
    remindLater1Hour: "将在1小时后提醒您",
    remindLater1Day: "将在1天后提醒您",
    remindLater1Week: "将在1周后提醒您",
    in1Hour: "1小时后",
    in1Day: "1天后",
    in1Week: "1周后",
    updateProgress: "更新进度",
    downloadProgress: "下载进度",

    serverNotice: "系统通知",
    serverMessage: "系统消息",

    reportIssue: "报告问题",
    reportIssueTitle: "报告问题",
    reportIssueContent: "问题描述",
    reportIssueContentPlaceholder: "请详细描述您遇到的问题...",
    reportIssueImage: "添加截图",
    reportIssueImageTip: "点击或拖拽图片到此处",
    reportIssueCategory: "问题类型",
    reportIssueCategoryIdea: "功能建议",
    reportIssueCategorySmallBug: "小问题",
    reportIssueCategoryUrgentBug: "紧急问题",
    reportIssueCategoryIdeaTip: "对 Ruyi Studio 有新的想法或建议",
    reportIssueCategorySmallBugTip: "不影响主要功能",
    reportIssueCategoryUrgentBugTip: "Ruyi Studio 无法正常使用",
    reportIssueSuccess: "问题报告提交成功",
    reportIssueFailed: "问题报告提交失败",
    reportIssueSubmit: "提交",
    reportIssueCancel: "取消",
    reportIssueContentRequired: "请输入问题描述",

    comingSoon: "即将推出！",
    ruyiCloudPhone: "Ruyi 云手机",
    ruyiBatchTestDevice: "Ruyi 批量测试设备",
    contactPrompt: "请在官网填写联系方式，我们将会在研发完成后第一时间通知您！",
    redirectingToWebsite: "秒后将为您导航到官网...",
    redirecting: "正在跳转...",
    cancelRedirect: "取消跳转",
    sdkTooLow: "该设备不可用（该设备 Android SDK 为 {sdk}，小于支持的最低 SDK 26）",
    sdkTooHigh: "该设备不可用（该设备 Android SDK 为 {sdk}，大于支持的最大 SDK 35）",

    editorUsageTipNL: `\
自动补全：\n\
- 输入 \`/\` ，编辑器会有预定义好的提示词补全信息，通过\`tab\`键可以在自动补全后需要填写的变量之间进行切换。\n\
执行方式：\n\
- **单行执行**：编辑器每行左侧都提供了选中该行的按钮，可选中后点击执行。\n\
- **部分执行**：可以通过在编辑器中选中部分行然后进行执行。\n\
- **全部执行**：点击编辑器上方的"执行"按钮可执行全部内容。
`,

editorUsageTipCode: `\
语言：\n\
支持任务步骤（自然语言）和任务脚本（Python语言）两种编写方式，默认使用任务步骤（自然语言）的编写方式。\n\
通过 \`Command+Shift+L\` 可以切换到自然语言模式。\n\
自动补全：\n\
- 输入 \`/\` ，编辑器会有预定义好的提示词补全信息，通过\`tab\`键可以在自动补全后需要填写的变量之间进行切换。\n\
- 对于任务脚本（Python语言），在 \`ui\`、\`device\`、\`fm\`、\`tools\`、\`data\` 后面输入\`.\`可以显示其提供的API的自动补全信息。\n\
执行方式：\n\
- **单行执行**：编辑器每行左侧都提供了选中该行的按钮，可选中后点击执行。\n\
- **部分执行**：可以通过在编辑器中选中部分行然后进行执行。\n\
- **全部执行**：点击编辑器上方的"执行"按钮可执行全部内容。
接口解释（任务脚本模式）：\n\
- **ui**：用于操作和识别界面元素，如点击、输入、滑动、查找控件、等待界面出现等。\n\
- **device**：用于调用设备系统级功能，如启动/关闭应用、获取剪贴板、通知用户、截图等。\n\
- **fm**：用于调用大模型（如语言模型、视觉模型）进行智能问答或推理。\n\
- **tools**：提供常用工具函数，如获取当前时间、生成临时文件路径、图片处理等。\n\
- **data**：用于存储和管理常用数据结构，如表格、图片等。\n\
`,

    manualPatchTip: `\
何时使用？：\n\
- 当云端大模型输出不准确时，可以手动来为大模型打补丁。\n\
补丁逻辑？：\n\
- 任务执行时，会将脚本中的文本描述和补丁中的文本描述进行匹配。\n\
- 如果匹配成功，则会直接用本地补丁中的值来替代云端大模型调用。\n\
如何打补丁？：\n\
- **Grounding**：在截图对应位置标框 -> 输入元素描述 -> 点击添加 -> 标注更多元素或点击保存。\n\
- **Enumerate**：输入总体描述 -> 在截图对应位置标框 -> 输入元素描述 -> 标注更多元素或点击保存（该模式需要标多个框，并且每个框给出元素描述）。\n\
- **Check**：输入检查描述 -> 选择检查结果 -> 点击添加 -> 添加更多或点击保存。\n\
- **Ensure**：在截图对应位置标框 -> 输入目标描述 -> 点击添加 -> 标注更多元素或点击保存。\n\
- **注意**：单张截图请只对上述四种标注模式的其中一种打补丁。`,
    manualPatchTitle: "标注面板使用说明",

    ruyiClientInstallationDrawerTitle: "Ruyi Assistant 安装",
    ruyiClientConfigurationGuideDrawerTitle: "Ruyi Assistant 连接 Ruyi Studio 教程",
    ruyiClientInstallationTitle: "安装提示",
    ruyiClientInstallationDesc: "您的手机上没有安装 Ruyi Assistant",
    ruyiClientInstallationRequired: "此应用是执行任务所必需的",
    ruyiClientInstalling: "正在为您安装 Ruyi Assistant...",
    ruyiClientInstallConfirm: "请在手机上确认安装",
    ruyiClientInstallWait: "这可能需要一些时间，请耐心等待",
    ruyiClientConfigTitle: "Ruyi Assistant 连接 Ruyi Studio 教程",
    ruyiClientConfigDesc: [
      "第一次安装 Ruyi Assistant 之后，需要授予相关权限。",
      "授予这些权限之后，Ruyi Studio 便会自动连接到 Ruyi Assistant。",
      "这些权限请求会自动弹出，按照教程点击允许即可。"
    ],
    ruyiClientConfigNote: "注意：在每次重启 Ruyi Assistant 之后，还会提示需要启动无障碍服务权限，请按照教程进行同意。",
    ruyiClientNormalPermissions: "普通权限",
    ruyiClientSpecialPermissions: "特殊权限",
    ruyiClientPermissionMic: "【访问麦克风】",
    ruyiClientPermissionMedia: "【访问媒体】",
    ruyiClientPermissionNotification: "【发送通知】",
    ruyiClientPermissionScreen: "【录制/投射屏幕】",
    ruyiClientPermissionAllow: "允许按钮",
    ruyiClientOverlayTitle: "显示在其他应用上层",
    ruyiClientOverlaySteps: [
      "搜索或下滑找到【Ruyi Assistant】",
      "点击【Ruyi Assistant】图标",
      "点击\"在其他应用上层显示\"按钮"
    ],
    ruyiClientAccessibilityTitle: "无障碍服务",
    ruyiClientAccessibilitySteps: [
      "下滑找到【已安装的服务】",
      "下滑找到【Ruyi Assistant】",
      "点击【Ruyi Assistant】图标",
      "点击打开功能按钮"
    ],
    deviceConnectionGuideTitle: "设备连接教程",
    deviceConnectionGuideDesc: "为了能够在设备上执行任务，需要进行以下两步：",
    deviceConnectionGuideSteps: [
      "将 Android 设备连接到电脑；",
      "将 Ruyi Assistant 连接到 Ruyi Studio。"
    ],
    deviceConnectionGuideNote: "注意：目前仅支持 Android 设备，且 Android SDK 需在 24～35 之间。使用非支持范围的 Android 设备会提示无法连接。",
    connectToComputerTitle: "如何将 Android 设备连接到电脑？",
    connectToComputerDesc: "需要进行以下四步的配置：",
    connectToComputerSteps: [
      "在手机端上启动开发者模式；",
      "在手机端上允许 USB 调试；",
      "通过 USB 数据线将手机连接到电脑；",
      "在手机端允许电脑进行调试。"
    ],
    connectToComputerNote: "完成上述配置后，Studio 中会显示对应的设备卡片，表示连接成功。",
    enableDevModeTitle: "如何启动开发者模式？",
    enableDevModeSteps: [
      "打开手机中的设置 APP。",
      "进入关于手机：",
      "找到版本号：",
      "激活开发者模式："
    ],
    enableDevModeSubSteps: {
      aboutPhone: '在设置菜单中，找到类似 "关于手机"、"关于设备"、"系统" 或者 "我的设备" 这样的选项（不同品牌手机名字可能稍有不同），点击进入。',
      versionNumber: '找到 "版本号" 或 "软件版本号" 或 "内部版本号" 等选项（不同品牌手机名字可能稍有不同）。',
      activate: '连续点击 "版本号" 选项，通常需要点击 7 次左右。直到出现 "您已成为开发者" 或 "开发者模式已启用" 等类似提示，表示开发者模式已激活。'
    },
    enableUsbDebugTitle: "如何允许 USB 调试？",
    enableUsbDebugSteps: [
      '进入 "开发者选项" 设置页面。',
      '找到并开启 "USB 调试"'
    ],
    enableUsbDebugSubSteps: {
      findSwitch: '在开发者选项中，找到 USB 调试 开关（通常在顶部或搜索 USB）。',
      confirm: '点击开关打开 USB 调试，会弹出警告提示：',
      allow: '点击 "确定" 或 "允许" 继续。'
    },
    connectAndAllowTitle: "连线并允许调试",
    connectAndAllowSteps: [
      "通过 USB 数据线将 Android 设备连接到电脑上。",
      "如果是第一次连接，手机端会弹窗询问是否允许该电脑设备进行调试，点击允许。"
    ],
    connectToRuyiTitle: "如何将 Ruyi Assistant 连接到 Ruyi Studio？",
    connectToRuyiDesc: [
      "点击了某个设备之后，Ruyi Studio 会为该设备自动安装 Ruyi Assistant。", 
      "然后需要手动为 Ruyi Assistant 授予相关的权限。授权后，Ruyi Studio 便会自动连接到 Ruyi Assistant。",
      '具体的授权方法，请在选择设备后，点击 "如何连接？" 进行查看。'
    ],
    markCurrentScreenAsTarget: "将当前界面标注为目标界面",
    annotationLabelAddOtherPlaceholder: "添加对该元素的其他描述",
    annotationLabelAddOther: "额外添加一项描述",
    annotationCheckAddOtherPlaceholder: "添加对该检查的其他描述",
    annotationEnsureAddOtherPlaceholder: "添加对该目标的其他描述",
    ready: "就绪",
    browserDeviceDescription: "在应用内打开网页浏览器",
    enterUrl: "请输入 URL",

    assistantQuestion: "助手提问",
    answerPlaceholder: "请在此输入您的回答...",
    agentSentMessage: '如意助手发来一条消息',
    tablePanelTitle: '表格',
    download: '下载',
    agentSentTable: '如意助手发来一张表格',
    agentSentImage: '如意助手发来一张图片',
    receivedImage: '收到的图片',
    receivedImageNotificationMessage: "请查看左侧的图片面板",
    usePatchInExecution: "在执行任务时使用补丁",
    patchEnabledSuccess: "补丁功能已开启",
    patchDisabledSuccess: "补丁功能已关闭",
    loadPatchConfigFailed: "加载补丁配置失败",
    updatePatchConfigFailed: "更新补丁配置失败",

    // Git 操作相关翻译
    gitInitSuccess: "Git 仓库初始化并推送成功",
    gitInitFailed: "Git 仓库初始化失败",
    gitPushSuccess: "成功推送至云端仓库",
    gitPushFailed: "推送至云端仓库失败",
    createTaskAndPushSuccess: "创建任务并成功推送至云端仓库（使用内置 Git）",
    createTaskPushFailed: "创建任务成功，但推送至云端仓库失败",
    uploadAndPushSuccess: "上传成功并成功推送至云端仓库（使用内置 Git）",
    uploadPushFailed: "上传成功，但推送至云端仓库失败",

    // 文件管理器相关翻译
    fileManager: "文件管理区",
    newTask: "创建新任务",
    createNewTask: "创建新任务",
    fileTaskName: "任务名称",
    fileTaskDescription: "任务描述",
    fileTaskNamePlaceholder: "输入任务名称（可选）",
    fileTaskDescriptionPlaceholder: "请详细描述您想要完成的任务...",
    fileTaskCreateSuccess: "任务创建成功",
    fileTaskCreateFailed: "任务创建失败",
    loadFileSuccess: "文件加载成功",
    loadFileFailed: "文件加载失败",
    deleteFileSuccess: "文件删除成功",
    deleteFileFailed: "文件删除失败",
    confirmDeleteFile: "确定要删除这个文件吗？",
    noTaskSelected: "请先选择一个任务",
    noFilesInProject: "当前项目中没有文件",
    getTaskFilesFailed: "获取任务文件列表失败",
    createAction: "创建",

    // 新建任务相关翻译
    taskShortName: "任务简称",
    taskDetailedDescription: "任务详细描述",
    taskShortNamePlaceholder: "请输入任务简称（可选）",
    taskDetailedDescriptionPlaceholder: "请详细描述您想要完成的任务...",
    taskDetailedDescriptionRequired: "任务详细描述不能为空",
    aiHelpWrite: "AI帮我填",
    newTaskButton: "创建任务",
    
    // Knowledge.json 文件显示名称
    knowledgeFileDisplayName: "任务提示",
    projectKnowledgeLoadedToEditor: "任务提示已加载到编辑器",
    createSpaceFirst: "请先创建一个如意项目",
    taskLoadedToEditor: "任务已加载到编辑器",
    taskFilePreview: "任务文件预览",
    loadThisTask: "加载此任务",
    refreshFileList: "刷新文件列表",
    naturalLanguageScript: "自然语言脚本",

    // Git 同步相关翻译
    gitSyncProgress: "正在同步项目",
    gitSyncSuccess: "项目同步成功",
    gitSyncFailed: "项目同步失败",
    gitCloneSuccess: "项目克隆成功",
    gitCloneFailed: "项目克隆失败",
    gitPullSuccess: "项目更新成功",
    gitPullFailed: "项目更新失败",
    gitConflictTitle: "项目同步冲突",
    gitConflictMessage: "的本地版本与云端版本存在冲突。请选择要使用的版本：",
    useRemoteVersion: "使用云端版本",
    useLocalVersion: "使用本地版本",
    applyingRemoteVersion: "正在应用云端版本...",
    applyingLocalVersion: "正在推送本地版本...",
    applyRemoteVersionSuccess: "已使用云端版本",
    applyRemoteVersionFailed: "应用云端版本失败",
    applyLocalVersionSuccess: "已使用本地版本",
    applyLocalVersionFailed: "推送本地版本失败",
    gitTokenMissing: "无法获取项目访问令牌，将加载项目但跳过Git同步",
    gitSyncException: "Git同步操作异常，将继续加载项目",
    
    // 自动保存相关翻译
    autoSaveStart: "开始自动保存",
    autoSaveInProgress: "正在自动保存",
    autoSaveCompleted: "自动保存完成",
    autoSaveFailed: "自动保存失败",
    autoSyncStart: "开始自动同步",
    autoSyncInProgress: "正在自动同步到云端",
    autoSyncCompleted: "自动同步完成",
    autoSyncFailed: "自动同步失败",
    
    // 文件置顶相关翻译
    pinFile: "置顶文件",
    unpinFile: "取消置顶",
    filePinned: "文件已置顶",
    fileUnpinned: "文件已取消置顶",
    togglePinnedFailed: "切换置顶状态失败",
    
    // 文件导入功能
    importLocalFile: "导入本地数据文件",
    importFileSuccess: "文件导入成功",
    importFileFailed: "文件导入失败",
    selectFileToImport: "选择要导入的文件",
    importingFile: "正在导入文件...",
    dataFileTag: "数据",
    setDataDirPathFailed: '设置 RuyiAgent 数据目录路径失败。',
    loadingAndParsingFile: "正在加载和解析文件...",
    addDevice: "添加设备",
    addDeviceModalTitle: "选择设备类型",
    browserDevice: "浏览器设备",
    ruyiCloudDevice: "Ruyi 云手机",
    deviceAdded: "设备已添加",
    deviceAddFailed: "添加设备失败",
    scanPhysicalDevices: "扫描物理设备",
    scanDevicesFailed: "扫描设备失败",
    scanningDevices: "正在扫描设备...",
    foundDevices: "发现设备",
    selectAll: "全选",
    rescan: "重新扫描",
    addSelectedDevices: "添加选中设备",
    scanDevicesDescription: "扫描当前连接的物理设备，选择要添加到设备列表的设备。",
    retry: "重试",
    pleaseSelectDevices: "请选择要添加的设备",
    
    // 目录管理相关翻译
    createNewFolder: "创建新目录",
    folderName: "目录名称",
    folderNamePlaceholder: "请输入目录名称",
    folderNameRequired: "目录名称不能为空",
    createFolderSuccess: "目录创建成功",
    createFolderFailed: "目录创建失败",
    deleteFolderSuccess: "目录删除成功",
    deleteFolderFailed: "目录删除失败",
    confirmDeleteFolder: "确定要删除这个目录吗？此操作会删除目录下的所有文件。",
    renameItem: "重命名",
    renameItemSuccess: "重命名成功",
    renameItemFailed: "重命名失败",
    newItemName: "新名称",
    newItemNamePlaceholder: "请输入新名称",
    newItemNameRequired: "名称不能为空",
    createFileInFolder: "在此目录中创建文件",
    createFolderInFolder: "在此目录中创建子目录",
    expandFolder: "展开目录",
    collapseFolder: "收起目录",
    folder: "目录",
    knowledgeFile: "提示词",
    taskFile: "任务",
    dataFile: "数据",
    jsonFile: "JSON文件",
    
    // 可见性相关翻译
    private: "私有",
    public: "公开",
    setToPrivateSuccess: "设为私有成功", 
    setToPublicSuccess: "设为公开成功",
    changeVisibilityFailed: "切换可见性失败",
    changeVisibility: "切换可见性",
    makePublic: "设为公开",
    makePrivate: "设为私有",

    // 分页相关翻译
    currentPage: "当前页",
    totalItems: "共 {total} 项",
    pageSize: "每页显示",
    itemsPerPage: "条",
    firstPage: "首页",
    lastPage: "末页",
    previousPage: "上一页",
    nextPage: "下一页",
    jumpToPage: "跳转至",
    pageOf: "第 {current} 页，共 {total} 页",

    viewTable: "查看表格",
    viewImage: "查看图片",
    replyToQuestionPlaceholder: "回复上方问题...",
    onlyMoveFileToFolder: "只能将文件拖动到文件夹内",
    moveFileSuccess: "文件移动成功",
    moveFileFailed: "文件移动失败",

    // Add the new translation keys for LogPanel
    clearLogs: "清除日志",
    downloadLogs: "下载日志",
    shrinkPanel: "收起面板",
    expandPanel: "展开面板",
    viewTaskDetails: "查看任务详情",
    taskDetails: "任务详情",
    close: "关闭",
    taskDescription: "任务描述",
    noDescription: "暂无描述",

    renameTask: "重命名任务",
    newTaskName: "新任务名称",
    newTaskNamePlaceholder: "请输入新任务名称",
    newTaskNameRequired: "任务名称不能为空",
    editTaskDetails: "编辑任务详情",
    saving: "保存中",
    taskDetailsInfo: "编辑任务名称和描述，更改将自动同步到云端",

    // File search functionality translations
    searchMatchedIn: "匹配",
    searchMatchFilename: "文件名",
    searchMatchDisplayName: "显示名",
    searchMatchKnowledge: "知识库内容",
    searchMatchNLScript: "任务步骤",
    searchMatchCodeScript: "代码脚本",
    searchMatchFileContent: "文件内容",
    searchMatchTaskDescription: "任务描述",
    searchNoResults: "未找到结果",
    searchPlaceholder: "输入以搜索文件...",
    searchTypeToSearchFiles: "输入以搜索文件名、内容和描述",
    
    // 任务创建模式相关翻译
    creatingNewTask: "正在创建新任务",
    creatingTaskFor: "正在创建新任务...",
    cancelTaskCreation: "取消创建",
    taskCreationCancelled: "任务创建已取消",
    taskCreationInProgress: "任务创建中，请继续对话完善任务需求",
    taskCreationMultiRound: "系统正在通过多轮对话完善任务需求",
    taskCreationGeneratingWorkflow: "正在基于您的描述生成完整的任务工作流",
  },
  en: {
    welcome: "Hello, welcome to Ruyi Studio! You can tell me your needs, and I will help you generate execution steps. What task can I help you with? 😊",
    welcomeTitle: "Hello, I'm Ruyi Intelligent Assistant!",
    welcomeDescription: "I can help you complete any task on your phone. Click the New button above to start exploring! 😊",
    welcomeTaskExample: "✨ You may want to try the following tasks:",
    dialogueArea: "Dialogue Area",

    inputPlaceholder: "Enter message...",
    chatInputPlaceholder: "Describe your needs, LLM will help!",
    chatDisabledDuringSystemPromptView: "Please modify task prompt manually",

    startDemo: "Start Demo",
    startingDemo: "Starting demonstration...",
    startDemoComplete: "Demonstration started successfully",
    startDemoError: "Failed to start demonstration, please try again",
    demoLine: "Demonstrate this line",

    endDemo: "End Demo",
    endingDemo: "Ending demonstration...",
    endDemoComplete: "Demonstration optimization completed",
    endDemoError: "Failed to end demonstration, please try again",

    replyText: "Text Reply",
    replyingTo: "Replying to: ",
    replyingText: "Optimizing based on text...",
    replyComplete: "Text-based optimization completed",
    replyError: "Text-based optimization failed, please try again",

    // 新增：区分不同场景的进行中和完成提示
    taskCreationProcessing: "Processing task requirements...",
    taskOptimizationProcessing: "Optimizing task...",
    conversationProcessing: "Processing reply...",
    conversationReplyComplete: "Reply completed",
    taskCreationReplyComplete: "Task requirements processed successfully",
    taskOptimizationComplete: "Task optimization completed",

    abandonExplanation: "Abandon Explanation",
    abandonExplanationMessage: "You have chosen not to explain this scenario",
    
    execute: "Execute",
    executeLine: "Execute/Demonstrate this line",
    executing: "Starting execution service...",
    executeComplete: "Execution started successfully",
    executeError: "Execution service failed to start, please try again",

    viewScript: "View Script",
    viewSteps: "View Steps",
    viewAppKnowledge: "Edit Knowledge", 

    generatingScript: "Generating task steps for you...",
    taskGenSuccess: "Task steps generated successfully",
    taskGenFailed: "Task steps generation failed, please try again",
    
    needExplanation: "To better accomplish your task, please explain the following scenarios:",
    demoInstruction: "You can demonstrate on the phone or explain through dialogue.",
    originalNLScript: "# Natural language instructions of task steps",
    scriptSwitchFailed: "Script language switch failed, please try again",

    sendButton: "Send",
    sendMessageFailed: "Send message failed",

    user: "User",
    system: "System",
    assistant: "Assistant",
    
    editorTitle: "Editor",
    codeScriptTitle: "Task Script",
    NLInstructionTitle: "Task Steps", 
    appKnowledgeTitle: "Task Prompt",
    projectKnowledgeTitle: "Task Prompt",
    editorUsageTitle: "Editor Usage Guide",

    transferringScriptFromNLtoScript: "Transferring task steps to execution script...",
    transferringScriptFromScriptToNL: "Transferring execution script to task steps...",
    transferScriptFromNLtoScriptComplete: "Task steps converted to execution script successfully",
    transferScriptFromNLtoScriptError: "Failed to convert task steps to execution script, please try again",
    transferScriptFromScriptToNLComplete: "Execution script converted to task steps successfully",
    transferScriptFromScriptToNLError: "Failed to convert execution script to task steps, please try again",

    transferSelectedScriptFromNLToCode: "Transferring task steps to code script...",
    transferSelectedScriptFromNLToCodeComplete: "Task steps converted to code script successfully",
    transferSelectedScriptFromNLToCodeError: "Failed to convert task steps to code script, please try again",

    // New: Workflow to code script conversion related translations
    executionNeedsCodeScript: "Analyzing task steps, preparing for execution...",
    generateCodeScriptBeforeExecutionComplete: "Task steps analysis completed, ready to proceed",
    generateCodeScriptBeforeExecutionError: "Task steps analysis failed, please try again",

    ruyiSpace: "Ruyi Project",
    createNew: "New Project",
    saveSpace: "Save",
    publishSpace: "Publish",
    createNewAgent: "New Project",
    createNewProjectModalTitle: "Create New Project",
    projectName: "Project Name",
    projectNamePlaceholder: "Please enter project name",
    projectDescriptionPlaceholder: "Please enter project description (optional)",
    projectNameRequired: "Project name cannot be empty",
    uploadAgent: "Save",
    saveProject: "Save Project",
    savingProject: "Saving Project...",
    saveProjectSuccess: "Project saved successfully and pushed to cloud repository",
    saveProjectFailed: "Project save failed",
    saveProjectNoChanges: "No changes to project, no need to save",
    saveProjectNeedLoad: "Please create or load a project first before saving",
    loadAgent: "Load",
    publishAgent: "Publish",
    load: "Load Project",
    selectAgentToLoad: "Select Ruyi Project to Load",
    createNewAgentSuccess: "New task created successfully",
    uploadSuccess: "Task saved successfully",
    uploadFailed: "Failed to save task",
    loadSuccess: "Ruyi project loaded successfully",
    loadFailed: "Failed to load task",
    publishSuccess: "Ruyi project published successfully",
    publishFailed: "Failed to publish task",
    getAgentListFailed: "Failed to get task list",

    isPublished: "Published",
    isNotPublished: "Not Published",
    totalPublishedVersions: "Total Published Versions",
    publishedVersions: "Published Versions",
    viewPublishedVersions: "Published Versions",
    viewPublishedVersionsSuccess: "Get published versions successfully",
    viewPublishedVersionsFailed: "Failed to get published versions",
    
    createProjectSuccess: "Project created successfully",
    createProjectFailed: "Failed to create project",
    
    // TaskFilesList new translation keys
    fileName: "File Name",
    fileContent: "File Content",
    fileNameRequired: "File name cannot be empty",
    noProjectSelected: "Please select a project first",
    createFileFailed: "Failed to create file",
    createFileSuccess: "File created successfully",
    createFilePushFailed: "File created successfully but push failed",
    createNewFile: "Create New File",
    fileNamePlaceholder: "Please enter file name (e.g.: main.py, index.html)",
    fileContentPlaceholder: "Please enter file content (optional)",
    newFile: "New File",

    appKnowledgeFormatTitle: "App Knowledge Guide",
    appKnowledgeFormatDesc: "The app knowledge base can help Ruyi generate task steps better. Please write the app knowledge base in a specific format, including task names and task steps. Separate each task with a blank line.",
    appKnowledgeExample: "Example Format:",
    appKnowledgeExampleContent:
`Task: Create a contact named "Ruyi"
Steps: 
  1. Open the "Contacts" app.
  2. Click on "Create Contact".
  3. Input "Ruyi" into "First Name" field.
  4. Click on "Save Icon" button in the top right corner.

Task: Search "Ruyi" in XiaoHongShu
Steps: 
  1. Click on "Search Icon" in the upper right corner.
  2. Input "Ruyi" into "Search" field.
  3. Click on "Search" button.`,
    
    websocketConnectionError: "Ruyi Assistant connection failed, please start Ruyi Assistant in remote control mode first",

    selectDevice: "Device List",
    selectDeviceError: "Failed to select device, please try again",

    // Device connection status related
    deviceDisconnected: "Device Disconnected",
    deviceDisconnectedDesc: "The phone has been disconnected. Please select another device or reconnect the phone.",
    deviceReconnected: "Device Reconnected",
    deviceReconnectedDesc: "The phone has been reconnected, and the screen mirroring will automatically resume.",
    deviceReconnecting: "Device Reconnecting",
    deviceReconnectingDesc: "Reestablishing screen mirroring connection, please wait...",
    selectOtherDevice: "Select Other Device",
    reconnectDevice: "Reconnect",

    ruyiClientInstallSuccess: 'Ruyi Assistant installed successfully!',
    ruyiClientInstallFailed: 'Ruyi Assistant installation failed, please try again',

    checkingRuyiClient: 'Checking Ruyi Assistant...',
    updatingRuyiClient: 'Updating Ruyi Assistant...',
    ruyiClientReady: 'Ruyi Assistant ready',
    ruyiClientChecking: 'Ruyi Assistant is checking, please wait...',
    ruyiClientUpdating: 'Ruyi Assistant is updating, please wait...',
    ruyiClientError: 'Ruyi Assistant check failed',
    ruyiClientCheckFailed: 'Ruyi Assistant check failed, please try again',
    
    grantingPermissions: 'Granting permissions...',
    permissionsGranted: 'Permissions granted',
    permissionsGrantFailed: 'Failed to grant permissions',
    permissionsPartiallyGranted: 'Permissions partially granted',

    saveHistoryModalTitle: "Save History",
    saveHistoryModalPlaceholder: "Please enter save message...",
    saveHistorySuccess: "History saved successfully",
    saveHistoryFailed: "Failed to save history",

    publishFormTitle: "Publish Task",
    publishFormName: "Task Name",
    publishFormDescription: "Task Description",
    publishFormVersion: "Version",
    publishFormReleaseNotes: "Release Notes",

    publishNeedSelectAgent: "Please create or switch to a project first",
    publishNeedSaveFirst: "Please save the current project before publishing",

    publishManagement: "Publish Management",
    publishNewVersion: "Publish",
    viewPublishHistory: "Publish History",
    publishHistory: "Publish History",
    releaseNotes: "Release Notes", 
    publishTime: "Publish Time",

    publishFormNameRequired: "Please fill in the publish name",
    publishFormDescriptionRequired: "Please fill in the publish description",
    publishFormVersionRequired: "Please fill in the version number",

    viewHistory: "Modify History",
    viewProjectHistory: "View Project History",
    projectHistory: "Project History",
    totalHistory: "Total History Records",
    viewHistoryNeedSelectAgent: "Please create or switch to a task first",
    viewHistoryFailed: "Failed to get history records",
    projectId: "Project ID",
    messages: "Messages",
    messagesCount: "items",

    viewDetail: "View Detail",
    historyDetail: "History Detail",
    basicInfo: "Basic Information",
    historyId: "History ID",
    comment: "Comment",
    createTime: "Create Time",
    scriptInfo: "Script Information",
    codeScript: "Code Script",
    NLScript: "Natural Language Script",
    messageHistory: "Message History",
    complete: "Complete",
    demoing: "Demoing",
    error: "Error",
    loading: "Loading",
    question: "Question",

    uploadNeedTask: "No task yet, please create a new task first!",

    batchExecute: "Batch Execute",
    batchExecuteTitle: "Batch Execution",
    batchExecuteDesc: "Batch execution supports parallel task execution on multiple mobile devices, which can be used in software testing and other broad fields.",
    batchExecuteContact: "To use this feature, please contact Dr. Li",
    batchExecuteEmail: "Email: <EMAIL>",
    batchExecutePhone: "Phone: 2345678",

    settings: "Ruyi Studio Settings",
    modelSettings: "Model Settings (Not Supported Yet)",
    portSettings: "Port Settings",
    browserSettings: "Browser Settings",
    browserHomepage: "Browser Default Homepage",
    browserHomepagePlaceholder: "Enter URL (e.g., https://www.baidu.com)",
    browserHomepageInvalid: "Please enter a valid URL",
    browserSettingsChanged: "Browser settings updated",
    browserSettingsChangeFailed: "Failed to update browser settings, please try again",
    defaultSearchEngine: "Default Search Engine",
    searchEngineGoogle: "Google",
    searchEngineBaidu: "Baidu",
    searchEngineBing: "Bing",
    searchEngineSougou: "Sougou",
    defaultSearchEngineChanged: "Default search engine updated",
    defaultSearchEngineChangeFailed: "Failed to update default search engine, please try again",
    modelType: "Model Type",
    apiKey: "API Key",
    screencastPort: "Screencast Port",
    backendPort: "Backend Port",
    saveSettings: "Save Settings",
    portChangeMessage: "The local port configuration has been modified successfully. The new configuration will take effect after restarting the Studio",
    settingsSaved: "Settings saved",
    defaultAvatar: "Default Avatar",
    userProfile: "User Profile",
    username: "Username",
    displayName: "Display Name",
    email: "Email",
    notSet: "Not Set",
    githubId: "GitHub ID",
    wechatId: "WeChat ID",

    loadPortConfigFailed: 'Failed to load port configuration',
    setPortConfigFailed: 'Failed to save port configuration',

    taskPort: "Task Execution Port",
    electronHttpPort: "Browser Service Port",
    portRangeWarning: "Port numbers must be between 1024 and 65535",
    invalidPortRange: "Please ensure all port numbers are within the valid range (1024-65535)",
    duplicatePorts: "Port numbers cannot be duplicated, please check and try again",

    stopExecute: "Stop Execute",
    stoppingExecute: "Stopping execution...",
    stopExecuteComplete: "Execution stopped",
    stopExecuteError: "Failed to stop execution, please try again",

    autoDebug: "Auto Debug",

    deviceList: "Device List",
    deviceId: "Device ID",
    productName: "Product Name", 
    status: "Status",
    noDevicesFound: "No Devices Found",
    unknownBrand: "Unknown Brand",
    unknownModel: "Unknown Model", 
    unknownProduct: "Unknown Product",
    authorized: 'Authorized',
    unauthorized: "Unauthorized",
    connected: "Connected",
    disconnected: 'Disconnected',
    getDeviceListFailed: "Failed to get device list",
    cloudPhone: "Cloud Phone",
    batchTestDevice: "Batch Test Device",

    virtualDeviceUnsupported: "This device is not supported yet. Please contact Ruyi administrator if needed",

    ruyiAssistantStatus: 'Ruyi Assistant Status',
    installed: 'Installed',
    notInstalled: 'Not Installed',
    noDeviceSelected: 'No Device Selected',
    switchDevice: 'Switch Device',
    howToConnect: 'How to Connect to Ruyi Assistant?',

    executeSelectedCode: "Execute",
    demoSelectedCode: "Demonstrate",

    languageSettings: "Language Settings",
    ideLanguage: "Studio Language",
    ideLanguageChanged: "Studio language settings updated",
    ideLanguageChangeFailed: "Failed to update Studio language settings, please try again",
    appLanguage: "Task Language",
    appLanguageChanged: "Task language settings updated",
    appLanguageChangeFailed: "Failed to update task language settings, please try again",

    loginTitle: "Login to Ruyi Studio",
    registerTitle: "Register for Ruyi Studio Account",
    usernameRequired: "Please enter your username or email!",
    usernameCannotStartWithNumber: "Username cannot start with a number",
    passwordRequired: "Please enter your password!",
    confirmPasswordRequired: "Please confirm your password!",
    passwordMinLength: "Password must be at least 8 characters!",
    passwordComplexity: "Password must contain both letters and numbers",
    passwordMismatch: "Passwords do not match!",
    usernamePlaceholder: "Username or Email",
    usernameOnlyPlaceholder: "Username",
    passwordPlaceholder: "Password",
    confirmPasswordPlaceholder: "Confirm Password",
    emailPlaceholder: "Email",
    loginButton: "Login",
    registerButton: "Register",
    rememberMe: "Remember me",
    noAccount: "Don't have an account?",
    haveAccount: "Already have an account?",
    registerNow: "Register Now",
    loginNow: "Login Now",
    backendStartFailed: "Backend server failed to start: ",
    checkBackendFailed: "Failed to check backend server status: ",
    loginSuccess: "Login successful!",
    loginFailed: "Login failed, please try again!",
    registerSuccess: "Registration successful!",
    registerFailed: "Registration failed, please try again!",

    logout: "Logout",
    logoutConfirmTitle: "Confirm Logout",
    logoutConfirmContent: "Logging out will redirect to the login page and all unsaved content will be lost. Are you sure?",
    logoutSuccess: "Successfully logged out",
    cancel: "Cancel",
    confirm: "Confirm",

    executionLog: 'Execution Log',
    createTaskModalTitle: "Create New Task",
    taskName: "Task Name",
    taskNameRequired: "Please enter task name",
    taskNamePlaceholder: "Task Short Name (Optional)",
    taskDescriptionPlaceholder: "Task Description (Required)",
    taskDescriptionRequired: "Please enter task description",
    appName: "App Name",
    appNamePlaceholder: "App Name (Optional)",

    createTask: "Create Task",
    createTaskSuccess: "Task created successfully",
    createTaskFailed: "Task creation failed",
    loadExampleTaskSuccess: "Task loaded successfully",

    downloadVH: "Download View Hierarchy",
    downloadScreenshot: "Download Screenshot",
    downloadSuccess: "Download successful",
    downloadFailed: "Download failed",
    downloadScreenshotSuccess: "Screenshot downloaded successfully",
    downloadVHSuccess: "View hierarchy downloaded successfully",
    downloadScreenshotFailed: "Screenshot download failed",
    downloadVHFailed: "View hierarchy download failed",

    delete: "Delete",
    deleteSuccess: "Delete successful",
    deleteFailed: "Delete failed",
    deleteLocalFailed: "Delete local ruyi project failed",
    deleteCloudFailed: "Delete cloud ruyi project failed",
    deleteCloudAndLocalFailed: "Delete ruyi project failed",
    
    // Main process delete related messages
    taskNameEmpty: "Task name cannot be empty",
    taskDirectoryNotExists: "Task directory does not exist",
    taskDeleteSuccess: "Task deleted successfully",
    taskDeleteFailed: "Failed to delete task",
    taskSaveSuccess: "Task saved successfully",
    taskSaveFailed: "Failed to save task",
    taskNameAndFileNameEmpty: "Task name and file name cannot be empty",
    fileExists: "File already exists",
    fileCreateSuccess: "File created successfully",
    fileCreateFailed: "Failed to create file",
    fileNotExists: "File does not exist",
    fileDeleteSuccess: "File deleted successfully",
    fileDeleteFailed: "Failed to delete file",
    fileUpdateSuccess: "File updated successfully",
    fileUpdateFailed: "Failed to update file",
    taskNameAndDirectoryNameEmpty: "Task name and directory name cannot be empty",
    directoryExists: "Directory already exists",
    directoryCreateSuccess: "Directory created successfully",
    directoryCreateFailed: "Failed to create directory",
    taskNameAndDirectoryPathEmpty: "Task name and directory path cannot be empty",
    pathNotDirectory: "Specified path is not a directory",
    directoryNotExists: "Directory does not exist",
    directoryDeleteSuccess: "Directory deleted successfully",
    directoryDeleteFailed: "Failed to delete directory",
    parametersEmpty: "Parameters cannot be empty",
    originalFileNotExists: "Original file or directory does not exist",
    newNameExists: "New name already exists",
    renameSuccess: "Renamed successfully",
    renameFailed: "Failed to rename",

    annotateScreenshot: "Annotate Data",
    annotationSuccess: "Annotation saved successfully",
    annotationFailed: "Failed to get screenshot, annotation failed",
    saveAnnotationToCloudFailed: 'Annotation save to cloud failed',

    annotationModalTitle: 'Manual Patch Annotation Panel',
    annotationLabelPlaceholder: 'Please enter element description',
    annotationsList: 'Annotations List',
    save: 'Save',
    add: 'Add',
    addEnsureAction: 'Add Navigation Action',

    saveToLocal: "Save to Local",
    saveSuccess: "Save Success",
    saveFailed: "Save Failed",
    savedToPath: "Saved to Path",

    annotationMode: "Annotation Mode",
    annotationItemLabelPlaceholder: "Enter item description",
    annotationGroupTitlePlaceholder: "Enter overall description",
    checkDescriptionPlaceholder: "Enter check description",
    ensureDescriptionPlaceholder: "Enter ensure description",

    loadingScreenshot: "Loading screenshot...",

    annotationLabelTitle: "Element Description",
    annotationLabelListTitle: "Element Description List",
    annotationLabelAdd: "Add Description",
    annotationLabelDelete: "Delete Description",
    annotationGroupTitle: "Overall Description",
    annotationItemLabelTitle: "Item Description",
    annotationItemLabelListTitle: "Item Description List",
    annotationItemLabelAdd: "Add Description",
    annotationItemLabelDelete: "Delete Description",
    checkDescriptionTitle: "Check Description",
    checkValueTitle: "Check Value",
    ensureDescriptionTitle: "Ensure Description",
    position: "Position",

    description: "Description",
    cloudPhoneDescription: "Test your script in the cloud phone provided by Ruyi. If needed, <NAME_EMAIL>",
    batchTestDeviceDescription: "Test your script in 100 different brand devices. If needed, <NAME_EMAIL>",

    tokenBalance: "Token Management",
    tokenUsed: "Token Used",
    tokenRemaining: "Token Remaining",
    tokenExchange: "Exchange Token",
    tokenExchangePlaceholder: "Enter exchange code",
    tokenExchangeButton: "Exchange",
    tokenExchangeSuccess: "Exchange successful",
    tokenExchangeFailed: "Exchange failed",

    emailRequired: "Please enter your email!",
    verificationCodeRequired: "Please enter the verification code!",
    verificationCodePlaceholder: "Verification Code",
    sendVerificationCode: "Send Verification Code",
    verificationCodeSent: "Verification code sent",
    verificationCodeFailed: "Failed to send verification code",
    emailFormatInvalid: "Invalid email format",

    viewAnnotations: "Annotation Management",
    noAnnotations: "No annotations available",
    loadAnnotationsFailed: "Failed to load annotations",
    deleteAll: "Delete All",
    deleteAllConfirmTitle: "Confirm Deleting All Annotations",
    deleteAllConfirmContent: "This action will permanently delete all local annotation data and cannot be undone. Are you sure?",
    deleteAllSuccess: "All annotations have been deleted successfully.",
    deleteAllFailed: "Failed to delete all annotations.",

    screenshotPreview: "Screenshot Preview",
    annotationCount: "Annotation Count",
    checkValue: "Check Value",
    groupTitle: "Group Title",
    ensureDescription: "Ensure Description",

    testModel: "Test Model",
    testMode: "Test Mode",
    testModelDescription: "Please enter description",
    testModelButton: "Test",
    testModelModalTitle: "Model Test",
    testModelResult: "Model Test Output",
    originalImage: "Original Screenshot",
    predictedImage: "Test Result Display",
    predictedCoordinate: "Predicted Coordinate",
    testModelFailed: "Model test failed",
    getScreenshotFailed: "Failed to get screenshot",
    noTestResults: "No test results",
    clickImageToDescribe: "Click anywhere on the screenshot to get a description",
    describeResult: "Description Result",
    clickCoordinate: "Click Coordinate",
    manualPatch: "Manual Patch",

    resetPasswordTitle: "Reset Password",
    resetPasswordButton: "Send Reset Link",
    resetPasswordSuccess: "Reset link has been sent to your email",
    resetPasswordFailed: "Failed to send reset link",
    forgotPassword: "Forgot Password?",
    backToLogin: "Back to Login",
    retryInSeconds: "Retry ({seconds}s)",

    tokenBalanceZeroTitle: "Token Balance Insufficient",
    tokenBalanceZeroContent: "Your current Token balance is 0. Generation and execution features will fail. Please recharge first.",
    insufficientTokenGenerateFirstScript: "Insufficient Token, generation failed, please recharge and try again",
    insufficientTokenReply: "Insufficient Token, optimization failed, please recharge and try again",
    insufficientTokenUpdateScript: "Insufficient Token, update script failed, please recharge and try again",
    insufficientTokenUpload: "Insufficient Token, update script failed, cannot upload task, please recharge and try again",
    insufficientTokenPublish: "Insufficient Token, update script failed, cannot publish task, please recharge and try again",
    insufficientTokenTransferSelectedScript: "Insufficient Token, transfer selected script failed, please recharge and try again",
    insufficientTokenTestModel: "Insufficient Token, test model failed, please recharge and try again",
    insufficientTokenExecute: "Insufficient Token balance, execution failed, please recharge and try again",

    newVersionTitle: "New Version Ready",
    newVersionFoundTitle: "New Version Found",
    newVersionFoundContent: "A new version is available. Download now?",
    downloadNow: "Download Now",
    downloadLater: "Download Later",
    restartNow: "Restart Now",
    restartLater: "Restart Later",
    saveWorkReminder: "Note: Please save your work before clicking the button below to restart and update.",
    updateDownloadedMessage: "Update has been downloaded. Would you like to restart now to install the update?",
    checkingUpdate: "Checking for updates...",
    updateAvailable: "New version found, downloading in background...",
    updateNotAvailable: "Already up to date",
    updateError: "Update error",
    checkForUpdates: "Check for Updates",
    checkUpdateFailed: "Failed to check for updates",

    // Update notification related translations
    remindMeLater: "Remind Me Later",
    remindMeLaterDescription: "Choose reminder time:",
    dismissThisVersion: "Dismiss This Version",
    remindLaterSet: "Reminder Set",
    remindLater1Hour: "Will remind you in 1 hour",
    remindLater1Day: "Will remind you in 1 day",
    remindLater1Week: "Will remind you in 1 week",
    in1Hour: "In 1 Hour",
    in1Day: "In 1 Day",
    in1Week: "In 1 Week",
    updateProgress: "Update Progress",
    downloadProgress: "Download Progress",

    serverNotice: "System Notice",
    serverMessage: "System Message",

    reportIssue: "Report Issue",
    reportIssueTitle: "Report Issue",
    reportIssueContent: "Issue Description",
    reportIssueContentPlaceholder: "Please describe the issue in detail...",
    reportIssueImage: "Add Screenshot",
    reportIssueImageTip: "Click or drag image here",
    reportIssueCategory: "Issue Type",
    reportIssueCategoryIdea: "Feature Request",
    reportIssueCategorySmallBug: "Minor Bug",
    reportIssueCategoryUrgentBug: "Urgent Bug",

    reportIssueCategoryIdeaTip: "New ideas or suggestions for Ruyi Studio",
    reportIssueCategorySmallBugTip: "Doesn't affect main functionality",
    reportIssueCategoryUrgentBugTip: "Ruyi Studio can not work properly",

    reportIssueSuccess: "Issue report submitted successfully",
    reportIssueFailed: "Failed to submit issue report",
    reportIssueSubmit: "Submit",
    reportIssueCancel: "Cancel",
    reportIssueContentRequired: "Please enter issue description",

    comingSoon: "Coming Soon!",
    ruyiCloudPhone: "Ruyi Cloud Phone",
    ruyiBatchTestDevice: "Ruyi Batch Test Device",
    contactPrompt: "Please fill in your contact information on our website, we will notify you as soon as the development is complete!",
    redirectingToWebsite: "seconds until redirecting to our website...",
    redirecting: "Redirecting...",
    cancelRedirect: "Cancel",
    sdkTooLow: "Unavailable (This device's Android SDK is {sdk}, lower than the minimum supported SDK 24)",
    sdkTooHigh: "Unavailable (This device's Android SDK is {sdk}, higher than the maximum supported SDK 35)",

    editorUsageTipNL: `\
Auto-completion:\n\
- Type \`/\` to get predefined prompt completions. Use \`tab\` key to switch between variables after auto-completion.\n\
Execution Methods:\n\
- **Single Line Execution**: Each line has a button on the left to select and execute that line.\n\
- **Partial Execution**: Select multiple lines in the editor to execute partially.\n\
- **Full Execution**: Click the "Execute" button above the editor to execute all content.
`,
editorUsageTipCode: `\
Language:\n\
Supports two ways to write - Task Steps (Natural Language) and Task Script (Python), default is Task Steps (Natural Language).\n\
Use \`Command+Shift+L\` to switch to natural language.\n\
Auto-completion:\n\
- Type \`/\` to get predefined prompt completions. Use \`tab\` key to switch between variables after auto-completion.\n\
- For Task Script (Python), type \`.\` after \`ui\`, \`device\`、\`fm\`、\`tools\`、\`data\` to see their API auto-completion.\n\
Execution Methods:\n\
- **Single Line Execution**: Each line has a button on the left to select and execute that line.\n\
- **Partial Execution**: Select multiple lines in the editor to execute partially.\n\
- **Full Execution**: Click the "Execute" button above the editor to execute all content.
Interface explanation (Task Script mode):\n\
- **ui**: Used to operate and recognize UI elements, such as clicking, inputting, scrolling, finding controls, waiting for views, etc.\n\
- **device**: Used to call system-level functions of the device, such as starting/killing apps, getting clipboard, notifying user, taking screenshots, etc.\n\
- **fm**: Used to call foundation models (e.g., language/vision models) for intelligent Q&A or reasoning.\n\
- **tools**: Provides common utility functions, such as getting current time, generating temp file paths, image processing, etc.\n\
- **data**: Used to store and manage common data structures, such as tables, images, etc.\n\
`,

    manualPatchTip: `\
When to use?:\n\
- When cloud model output is inaccurate, you can manually patch the model.\n\
Patch logic?:\n\
- During task execution, text descriptions in scripts will be matched with descriptions in patches.\n\
- If matched successfully, values from local patches will directly replace cloud model calls.\n\
How to patch?:\n\
- **Grounding**: Draw a box on the screenshot corresponding position -> input element description -> click add -> annotate more elements or click save.\n\
- **Enumerate**: Input overall description -> draw a box on the screenshot corresponding position -> input element description -> annotate more elements or click save (this mode needs to draw multiple boxes, and each box gives an element description).\n\
- **Check**: Input check description -> select check result -> click add -> add more or click save.\n\
- **Ensure**: Draw a box on the screenshot corresponding position -> input target description -> click add -> annotate more elements or click save.\n\
- **Note**: Please only patch one of the above four annotation modes for a single screenshot.`,
    manualPatchTitle: "Annotation Panel Usage Guide",

    ruyiClientInstallationDrawerTitle: "Ruyi Assistant Installation",
    ruyiClientConfigurationGuideDrawerTitle: "Ruyi Assistant Connection Guide",
    ruyiClientInstallationTitle: "Installation Notice",
    ruyiClientInstallationDesc: "Ruyi Assistant is not installed on your phone",
    ruyiClientInstallationRequired: "This application is required for task execution",
    ruyiClientInstalling: "Installing Ruyi Assistant...",
    ruyiClientInstallConfirm: "Please confirm installation on your phone",
    ruyiClientInstallWait: "This may take a while, please be patient",
    ruyiClientConfigTitle: "Ruyi Assistant Connection Guide",
    ruyiClientConfigDesc: [
      "After installing Ruyi Assistant for the first time, you need to grant relevant permissions.",
      "Once these permissions are granted, Ruyi Studio will automatically connect to Ruyi Assistant.",
      "These permission requests will pop up automatically, just follow the guide and click allow."
    ],
    ruyiClientConfigNote: "Note: After each restart of Ruyi Assistant, you will be prompted to enable accessibility service permission, please follow the guide to agree.",
    ruyiClientNormalPermissions: "Normal Permissions",
    ruyiClientSpecialPermissions: "Special Permissions",
    ruyiClientPermissionMic: "【Microphone Access】",
    ruyiClientPermissionMedia: "【Media Access】",
    ruyiClientPermissionNotification: "【Send Notifications】",
    ruyiClientPermissionScreen: "【Screen Recording/Casting】",
    ruyiClientPermissionAllow: "Allow button",
    ruyiClientOverlayTitle: "Display Over Other Apps",
    ruyiClientOverlaySteps: [
      "Search or scroll down to find 【Ruyi Assistant】",
      "Click on 【Ruyi Assistant】 icon",
      "Click on 'Display over other apps' button"
    ],
    ruyiClientAccessibilityTitle: "Accessibility Service",
    ruyiClientAccessibilitySteps: [
      "Scroll down to find 【Installed Services】",
      "Scroll down to find 【Ruyi Assistant】",
      "Click on 【Ruyi Assistant】 icon",
      "Click on the enable function button"
    ],
    deviceConnectionGuideTitle: "Device Connection Guide",
    deviceConnectionGuideDesc: "To execute tasks on your device, you need to complete these two steps:",
    deviceConnectionGuideSteps: [
      "Connect the Android device to your computer;",
      "Connect Ruyi Assistant to Ruyi Studio."
    ],
    deviceConnectionGuideNote: "Note: Currently only Android devices with SDK version between 24 and 35 are supported. Devices with unsupported Android SDK versions will show a connection error.",
    connectToComputerTitle: "How to connect Android device to computer?",
    connectToComputerDesc: "You need to complete these four steps:",
    connectToComputerSteps: [
      "Enable Developer Mode on your phone;",
      "Enable USB Debugging on your phone;",
      "Connect your phone to computer via USB cable;",
      "Allow debugging from your computer on your phone."
    ],
    connectToComputerNote: "After completing these steps, the device card will appear in Studio, indicating successful connection.",
    enableDevModeTitle: "How to enable Developer Mode?",
    enableDevModeSteps: [
      "Open Settings app on your phone.",
      "Go to About Phone:",
      "Find Build Number:",
      "Activate Developer Mode:"
    ],
    enableDevModeSubSteps: {
      aboutPhone: "In Settings menu, find and tap options like \"About phone\", \"About device\", \"System\" or \"My device\" (names may vary by brand).",
      versionNumber: "Find options like \"Build number\" or \"Software version\" or \"Version number\" (names may vary by brand).",
      activate: "Tap \"Build number\" repeatedly (usually 7 times) until you see a message like \"You are now a developer\" or \"Developer mode has been enabled\"."
    },
    enableUsbDebugTitle: "How to enable USB Debugging?",
    enableUsbDebugSteps: [
      "Go to \"Developer options\" settings page.",
      "Find and enable \"USB debugging\""
    ],
    enableUsbDebugSubSteps: {
      findSwitch: "In Developer options, find \"USB debugging\" switch (usually at top or search for \"USB\").",
      confirm: "Tap the switch to enable USB debugging, a warning will appear:",
      allow: "Tap \"OK\" or \"Allow\" to continue."
    },
    connectAndAllowTitle: "Connect and Allow Debugging",
    connectAndAllowSteps: [
      "Connect your Android device to computer using a USB cable.",
      "If this is the first time connecting, your phone will show a prompt asking to allow debugging from this computer, tap allow."
    ],
    connectToRuyiTitle: "How to connect Ruyi Assistant to Ruyi Studio?",
    connectToRuyiDesc: [
      "After selecting a device, Ruyi Studio will automatically install Ruyi Assistant.", 
      "Then you need to manually grant required permissions to Ruyi Assistant. After authorization, Ruyi Studio will automatically connect to Ruyi Assistant.",
      "For detailed authorization steps, please select a device and click \"How to Connect?\"."
    ],
    markCurrentScreenAsTarget: "Mark current screen as target screen",
    annotationLabelAddOtherPlaceholder: "Add another description for this element",
    annotationLabelAddOther: "Add another description",
    annotationCheckAddOtherPlaceholder: "Add another description for this check",
    annotationEnsureAddOtherPlaceholder: "Add another description for this target",
    ready: "Ready",
    browserDeviceDescription: "Open web browser in the app",
    enterUrl: "Please enter URL",
    assistantQuestion: "Assistant Question",
    answerPlaceholder: "Please enter your answer here...",
    agentSentMessage: 'Ruyi Assistant sent a message',
    tablePanelTitle: 'Table',
    download: 'Download',
    agentSentTable: 'Ruyi Assistant sent a table',
    agentSentImage: 'Ruyi Assistant sent an image',
    receivedImage: 'Received Image',
    receivedImageNotificationMessage: "Please check the image panel on the left",
    usePatchInExecution: "Use patch during task execution",
    patchEnabledSuccess: "Patch feature has been enabled",
    patchDisabledSuccess: "Patch feature has been disabled",
    loadPatchConfigFailed: "Failed to load patch configuration",
    updatePatchConfigFailed: "Failed to update patch configuration",

    // Git 操作相关翻译
    gitInitSuccess: "Git repository initialized and pushed successfully",
    gitInitFailed: "Git repository initialization failed",
    gitPushSuccess: "Successfully pushed to cloud repository",
    gitPushFailed: "Failed to push to cloud repository",
    createTaskAndPushSuccess: "Task created and successfully pushed to cloud repository (using built-in Git)",
    createTaskPushFailed: "Task created successfully, but failed to push to cloud repository",
    uploadAndPushSuccess: "Upload successful and successfully pushed to cloud repository (using built-in Git)",
    uploadPushFailed: "Upload successful, but failed to push to cloud repository",

    // File manager related translations
    fileManager: "File Manager",
    newTask: "Create New Task",
    createNewTask: "Create New Task",
    fileTaskName: "Task Name",
    fileTaskDescription: "Task Description",
    fileTaskNamePlaceholder: "Enter task name (optional)",
    fileTaskDescriptionPlaceholder: "Please describe the task you want to complete in detail...",
    fileTaskCreateSuccess: "Task created successfully",
    fileTaskCreateFailed: "Failed to create task",
    loadFileSuccess: "File loaded successfully",
    loadFileFailed: "Failed to load file",
    deleteFileSuccess: "File deleted successfully",
    deleteFileFailed: "Failed to delete file",
    confirmDeleteFile: "Are you sure you want to delete this file?",
    noTaskSelected: "Please select a task first",
    noFilesInProject: "No files in current project",
    getTaskFilesFailed: "Failed to get task files list",
    createAction: "Create",

    // New task related translations
    taskShortName: "Task Short Name",
    taskDetailedDescription: "Task Detailed Description",
    taskShortNamePlaceholder: "Please enter task short name (optional)",
    taskDetailedDescriptionPlaceholder: "Please describe the task you want to complete in detail...",
    taskDetailedDescriptionRequired: "Task detailed description cannot be empty",
    aiHelpWrite: "AI Help Write",
    newTaskButton: "Create New Task",
    
    // Knowledge.json file display name
    knowledgeFileDisplayName: "Task Prompt",
    projectKnowledgeLoadedToEditor: "Task Prompt loaded to editor",
    createSpaceFirst: "Please create a ruyi project first",
    taskLoadedToEditor: "Task loaded to editor",
    taskFilePreview: "Task File Preview",
    loadThisTask: "Load This Task",
    refreshFileList: "Refresh File List",
    naturalLanguageScript: "Natural Language Script",

    // Git 同步相关翻译
    gitSyncProgress: "Project synchronization in progress",
    gitSyncSuccess: "Project synchronization successful",
    gitSyncFailed: "Project synchronization failed",
    gitCloneSuccess: "Project cloning successful",
    gitCloneFailed: "Project cloning failed",
    gitPullSuccess: "Project update successful",
    gitPullFailed: "Project update failed",
    gitConflictTitle: "Project synchronization conflict",
    gitConflictMessage: "Local version conflicts with cloud version. Please select the version to use:",
    useRemoteVersion: "Use cloud version",
    useLocalVersion: "Use local version",
    applyingRemoteVersion: "Applying cloud version...",
    applyingLocalVersion: "Pushing local version...",
    applyRemoteVersionSuccess: "Cloud version applied",
    applyRemoteVersionFailed: "Failed to apply cloud version",
    applyLocalVersionSuccess: "Local version applied",
    applyLocalVersionFailed: "Failed to push local version",
    gitTokenMissing: "Unable to retrieve project access token, project will be loaded but Git synchronization will be skipped",
    gitSyncException: "Git synchronization operation exception, project will be loaded",
    
    // Auto-save related translations
    autoSaveStart: "Start auto-save",
    autoSaveInProgress: "Auto-saving in progress",
    autoSaveCompleted: "Auto-save completed",
    autoSaveFailed: "Auto-save failed",
    autoSyncStart: "Start auto-sync",
    autoSyncInProgress: "Auto-syncing to cloud",
    autoSyncCompleted: "Auto-sync completed",
    autoSyncFailed: "Auto-sync failed",
    
    // File pinning related translations
    pinFile: "Pin File",
    unpinFile: "Unpin File",
    filePinned: "File pinned",
    fileUnpinned: "File unpinned",
    togglePinnedFailed: "Failed to toggle pin status",
    
    // File import functionality
    importLocalFile: "Import Local Data File",
    importFileSuccess: "File imported successfully",
    importFileFailed: "Failed to import file",
    selectFileToImport: "Select file to import",
    importingFile: "Importing file...",
    dataFileTag: "Data",
    setDataDirPathFailed: 'Failed to set RuyiAgent data directory path.',
    loadingAndParsingFile: "Loading and parsing file...",
    addDevice: "Add Device",
    addDeviceModalTitle: "Select Device Type",
    browserDevice: "Browser Device",
    ruyiCloudDevice: "Ruyi Cloud Phone",
    deviceAdded: "Device added",
    deviceAddFailed: "Failed to add device",
    scanPhysicalDevices: "Scan Physical Devices",
    scanDevicesFailed: "Scan devices failed",
    scanningDevices: "Scanning devices...",
    foundDevices: "Found devices",
    selectAll: "Select All",
    rescan: "Rescan",
    addSelectedDevices: "Add Selected Devices",
    scanDevicesDescription: "Scan current connected physical devices, select devices to add to the device list.",
    retry: "Retry", 
    pleaseSelectDevices: "Please select devices to add",
    
    // Directory management related translations
    createNewFolder: "Create New Folder",
    folderName: "Folder Name",
    folderNamePlaceholder: "Please enter folder name",
    folderNameRequired: "Folder name cannot be empty",
    createFolderSuccess: "Folder created successfully",
    createFolderFailed: "Failed to create folder",
    deleteFolderSuccess: "Folder deleted successfully",
    deleteFolderFailed: "Failed to delete folder",
    confirmDeleteFolder: "Are you sure you want to delete this folder? This will delete all files in the folder.",
    renameItem: "Rename",
    renameTask: "Rename Task",
    renameItemSuccess: "Rename successful",
    renameItemFailed: "Rename failed",
    newItemName: "New Name",
    newTaskName: "New Task Name",
    newItemNamePlaceholder: "Please enter new name",
    newTaskNamePlaceholder: "Please enter new task name",
    newItemNameRequired: "Name cannot be empty",
    createFileInFolder: "Create file in this folder",
    createFolderInFolder: "Create subfolder in this folder",
    expandFolder: "Expand folder",
    collapseFolder: "Collapse folder",
    folder: "Folder",
    knowledgeFile: "Prompt File",
    taskFile: "Task File",
    dataFile: "Data File",
    jsonFile: "JSON File",
    
    // Visibility related translations
    private: "Private",
    public: "Public", 
    setToPrivateSuccess: "Set to private successfully",
    setToPublicSuccess: "Set to public successfully",
    changeVisibilityFailed: "Failed to change visibility",
    changeVisibility: "Change visibility",
    makePublic: "Make public",
    makePrivate: "Make private",

    // Pagination related translations
    currentPage: "Current Page",
    totalItems: "Total {total} items",
    pageSize: "Page Size",
    itemsPerPage: "items per page",
    firstPage: "First",
    lastPage: "Last",
    previousPage: "Previous",
    nextPage: "Next",
    jumpToPage: "Go to",
    pageOf: "Page {current} of {total}",

    viewTable: "View Table",
    viewImage: "View Image",
    replyToQuestionPlaceholder: "Reply to the question above...",
    onlyMoveFileToFolder: "You can only move files into folders.",
    moveFileSuccess: "File moved successfully.",
    moveFileFailed: "Failed to move file.",

    // Add the new translation keys for LogPanel
    clearLogs: "Clear Logs",
    downloadLogs: "Download Logs",
    shrinkPanel: "Shrink Panel",
    expandPanel: "Expand Panel",
    viewTaskDetails: "View Task Details",
    taskDetails: "Task Details",
    close: "Close",
    taskDescription: "Task Description",
    noDescription: "No Description",
    editTaskDetails: "Edit Task Details",
    saving: "Saving",
    taskDetailsInfo: "Edit task name and description. Changes will be automatically synced to the cloud",

    // File search functionality translations
    searchMatchedIn: "Matched in",
    searchMatchFilename: "Filename",
    searchMatchDisplayName: "Display Name",
    searchMatchKnowledge: "Knowledge Content",
    searchMatchNLScript: "Task Steps",
    searchMatchCodeScript: "Code Script",
    searchMatchFileContent: "File Content",
    searchMatchTaskDescription: "Task Description",
    searchNoResults: "No results found",
    searchPlaceholder: "Type to search files...",
    searchTypeToSearchFiles: "Type to search filenames, content, and descriptions",
    
    // Task creation mode related translations
    creatingNewTask: "Creating New Task",
    creatingTaskFor: "Creating New Task...",
    cancelTaskCreation: "Cancel Creation",
    taskCreationCancelled: "Task creation cancelled",
    taskCreationInProgress: "Task creation in progress, please continue the conversation to refine task requirements",
    taskCreationMultiRound: "System is refining task requirements through multi-round conversation",
    taskCreationGeneratingWorkflow: "Generating complete task workflow based on your description",
  }
};
