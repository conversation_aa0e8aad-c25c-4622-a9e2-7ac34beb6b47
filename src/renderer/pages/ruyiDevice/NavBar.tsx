import React, { useState, useEffect, useRef } from 'react';
import { createRoot } from 'react-dom/client';
import { Button, Modal, List, message, Tag, Input, Dropdown, Menu, Avatar, Select as AntSelect, Pagination, Tooltip, Upload } from 'antd';
import { Download, CloudUpload, AppWindow, Eye, Smartphone, ChevronDown, AppWindow as Appstore, History, Settings, User, Plus, FolderOpen, Database, MessageCircle, Inbox, Save, Loader2, CheckCircle, XCircle, Search, RefreshCw } from 'lucide-react';
import { ChatInterface } from './ChatInterface';
import { CodeEditorUtils } from './CodeEditor';
import { I18nUtils } from './languageSupport/i18nUtils';
import { UpdateBadge } from '../../components/UpdateBadge';
import { UpdatePreferencesManager } from '../../utils/updatePreferences';
import SettingsPanel from './SettingsPanel';
import { useLanguage } from './languageSupport/LanguageContext';
import { useNavigate } from 'react-router-dom';
import { repoApi, userApi, systemApi, promptApi } from '../../../services/api';
import { AnnotationManagement } from './AnnotationManagement';
import { useBrowserViewControl, useSearchInputBrowserViewControl } from '../../../renderer/hooks/useBrowserViewControl';

import { flaskApi } from '../../../services/FlaskApiService';
import { useDevices } from './DeviceContext';
import { StateManager } from './StateManager';
import { FileSearchOverlay } from './FileSearchOverlay';

import '../styles/NavBar.css';

interface NavBarProps {
  title?: string;
  onSelectDevice?: () => void;
}

interface AgentInfo {
  id: any;
  name: string;
  description: string;
  private: boolean;
  created_at?: string;
  updated_at?: string;
}

// 添加端口配置接口
interface PortConfig {
  flask_port: string;
  scrcpy_port: string;
  device_port: string;
  electron_http_port: string;
}

// 工具函数：从完整名称中提取真实名称（去除 uuid 前缀）
const extractRealName = (fullName: string): string => {
  if (fullName && fullName.includes('/')) {
    return fullName.split('/').pop() || fullName;
  }
  return fullName;
};

// 处理代理列表，提取真实名称
const processAgentList = (agents: AgentInfo[]): AgentInfo[] => {
  return agents.map(agent => ({
    ...agent,
    name: extractRealName(agent.name)
  }));
};

export const NavBarComponent: React.FC<NavBarProps> = ({
  title = 'Ruyi Studio',
  onSelectDevice 
}) => {
  const { language, setLanguage } = useLanguage();
  const [appLanguage, setAppLanguage] = useState('zh');
  
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [agentList, setAgentList] = useState<AgentInfo[]>([]);
  const [version, setVersion] = useState('v1.0.0');
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);
  // 新增本地状态，用于保存当前最新的 task
  const [currentTask, setCurrentTask] = useState(ChatInterface.task);

  const [isEditing, setIsEditing] = useState(false);
  const [editingTask, setEditingTask] = useState('');

  const [currentProjectId, setCurrentProjectId] = useState<string>('');

  const [isHistoryModalVisible, setIsHistoryModalVisible] = useState(false);
  const [historyMessage, setHistoryMessage] = useState('');
  const [tempProjectId, setTempProjectId] = useState('');

  // 添加搜索框状态
  const [searchValue, setSearchValue] = useState('');
  const [searchVisible, setSearchVisible] = useState(false);
  const [searchOverlayVisible, setSearchOverlayVisible] = useState(false);
  const [searchPosition, setSearchPosition] = useState({ top: 0, left: 0, width: 0 });
  const searchInputRef = useRef<any>(null);

  // const [isProjectHistoryModalVisible, setIsProjectHistoryModalVisible] = useState(false);
  // const [projectHistory, setProjectHistory] = useState<any[]>([]);
  // const [totalHistory, setTotalHistory] = useState(0);

  // 添加新的状态来控制详情模态框
  // const [isHistoryDetailModalVisible, setIsHistoryDetailModalVisible] = useState(false);
  // const [currentHistoryDetail, setCurrentHistoryDetail] = useState<any>(null);

  // const [isModelSettingsVisible, setIsModelSettingsVisible] = useState(false);
  // const [isPortSettingsVisible, setIsPortSettingsVisible] = useState(false);
  const [modelSettings, setModelSettings] = useState({
    modelType: 'gpt-4',
    apiKey: ''
  });
  const [portSettings, setPortSettings] = useState<PortConfig>({
    flask_port: '11825',
    scrcpy_port: '31825',
    device_port: '51825',
    electron_http_port: '18542'
  });
  const [browserSettings, setBrowserSettings] = useState({
    homepage: 'https://www.baidu.com',
    searchEngine: 'baidu'
  });

  // 修改设置相关的状态和函数
  const [isSettingsPanelVisible, setIsSettingsPanelVisible] = useState(false);

  const [isCreateProjectModalVisible, setIsCreateProjectModalVisible] = useState(false);
  const [projectName, setProjectName] = useState('');
  const [projectDescription, setProjectDescription] = useState('');
  // const [appName, setAppName] = useState('');

  const navigate = useNavigate();

  // 新增状态
  const [isUserPanelVisible, setIsUserPanelVisible] = useState(false);
  const [userInfo, setUserInfo] = useState({
    username: '',
    displayName: '',
    email: '',
    githubId: '',
    wechatId: ''
  });

  const [isAnnotationManagementVisible, setIsAnnotationManagementVisible] = useState(false);

  // 添加报告问题相关的状态
  const [isReportIssueModalVisible, setIsReportIssueModalVisible] = useState(false);
  const [issueForm, setIssueForm] = useState({
    category: 'idea',
    content: '',
    image: null as string | null
  });

  // 添加切换可见性的状态
  const [changingVisibility, setChangingVisibility] = useState<string>('');



  // 添加检查更新相关的状态
  const [checkingUpdate, setCheckingUpdate] = useState(false);

  // Update availability state
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [availableVersion, setAvailableVersion] = useState<string | null>(null);

  // Add the hook to control browser view visibility for all modals (except search overlay)
  useBrowserViewControl(isModalVisible || isHistoryModalVisible || 
    isCreateProjectModalVisible || isSettingsPanelVisible || 
    isReportIssueModalVisible || isUserPanelVisible || isAnnotationManagementVisible);
  
  // Use focus-aware browser view control specifically for search functionality
  useSearchInputBrowserViewControl(searchInputRef, searchOverlayVisible, 200);

  // 添加加载配置的 useEffect
  useEffect(() => {
    const loadPortConfig = async () => {
      try {
        const config = await window.electronAPI.getConfig();
        setPortSettings({
          flask_port: config.flask_port.toString(),
          scrcpy_port: config.scrcpy_port.toString(),
          device_port: config.device_port.toString(),
          electron_http_port: ((config as any).electron_http_port || 18542).toString()
        });
      } catch (error) {
        console.error('加载端口配置失败:', error);
        message.error(I18nUtils.getText('loadPortConfigFailed'));
      }
    };

    const loadBrowserConfig = async () => {
      try {
        const [homepage, searchEngine] = await Promise.all([
          window.electronAPI.getBrowserHomepageConfig(),
          window.electronAPI.getDefaultSearchEngineConfig()
        ]);
        setBrowserSettings({
          homepage: homepage || 'https://www.baidu.com',
          searchEngine: searchEngine || 'baidu'
        });
      } catch (error) {
        console.error('加载浏览器配置失败:', error);
        // Use default value on error
        setBrowserSettings({
          homepage: 'https://www.baidu.com',
          searchEngine: 'baidu'
        });
      }
    };

    loadPortConfig();
    loadBrowserConfig();
  }, []);

  // 当组件初次加载时，监听 ChatInterface.task 的变化
  useEffect(() => {
    const handleTaskChange = (newTask: string) => {
      // 更新本地状态，以便重新渲染标题
      setCurrentTask(newTask);
    };

    const handleProjectNameChange = (newProjectName: string) => {
      // 项目名称变化时强制重新渲染
      setCurrentTask(prev => prev); // 触发重新渲染
    };

    // 注册回调
    ChatInterface.setOnTaskChangeCallback(handleTaskChange);
    ChatInterface.setOnProjectNameChangeCallback(handleProjectNameChange);

    // 卸载时取消
    return () => {
      ChatInterface.setOnTaskChangeCallback(undefined);
      ChatInterface.setOnProjectNameChangeCallback(undefined);
    };
  }, []);

  useEffect(() => {
    fetchAgentList(1, 10); // 初始加载使用第一页
  }, []);

  // 分页变化处理函数
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
    }
    fetchAgentList(page, size || pageSize);
  };

  // 每页显示数量变化处理函数
  const handlePageSizeChange = (current: number, size: number) => {
    setCurrentPage(1); // 重置到第一页
    setPageSize(size);
    fetchAgentList(1, size);
  };

  // 在 useEffect 中监听 language 变化
  useEffect(() => {
    // 更新导航栏等需要翻译的内容
    console.log('Language changed, updating NavBar UI');
  }, [language]);

  // 搜索框处理函数
  const handleSearch = (value: string) => {
    setSearchValue(value);
    // 如果有搜索内容且搜索框获得焦点，显示搜索结果
    if (value.trim() && searchInputRef.current) {
      updateSearchPosition();
      setSearchOverlayVisible(true);
    } else if (!value.trim()) {
      setSearchOverlayVisible(false);
    }
  };

  const handleSearchFocus = () => {
    setSearchVisible(true);
    // 如果有搜索内容，显示搜索结果
    if (searchValue.trim()) {
      updateSearchPosition();
      setSearchOverlayVisible(true);
    }
  };

  const handleSearchBlur = () => {
    // 延迟隐藏搜索框，给用户时间点击搜索结果
    setTimeout(() => {
      setSearchVisible(false);
    }, 200);
  };

  // 更新搜索结果位置
  const updateSearchPosition = () => {
    if (searchInputRef.current) {
      const inputElement = searchInputRef.current.input || searchInputRef.current;
      const rect = inputElement.getBoundingClientRect();
      setSearchPosition({
        top: rect.bottom + 4,
        left: rect.left,
        width: rect.width
      });
    }
  };

  // 处理文件选择
  const handleFileSelect = async (filePath: string) => {
    try {
      // 使用现有的文件点击处理逻辑
      const result = await window.electronAPI.readTaskFile({
        taskName: ChatInterface.projectName,
        fileName: filePath
      });

      if (!result.success || result.content === undefined) {
        message.error('Failed to load file: ' + result.message);
        return;
      }

      // 处理 Knowledge.json - 使用与TaskFilesList相同的逻辑
      if (filePath === 'Knowledge.json') {
        try {
          const knowledgeData = JSON.parse(result.content);
          
          // 处理新格式的 Knowledge.json
          if (knowledgeData.device_information !== undefined && knowledgeData.operation_guide !== undefined) {
            // 新格式：渲染为固定模板
            const knowledgeContent = `# 设备信息
${knowledgeData.device_information || ''}

# 操作指南
${knowledgeData.operation_guide || ''}`;
            ChatInterface.setCurrentEditingFile(filePath);
            CodeEditorUtils.setAppKnowledge(knowledgeContent);
            CodeEditorUtils.showProjectKnowledge(knowledgeContent);
            
            // 通知TaskFilesList更新选中状态
            window.dispatchEvent(new CustomEvent('fileSelectedFromSearch', { 
              detail: { fileName: filePath } 
            }));
            
            message.success('Knowledge file loaded successfully');
            return;
          }
          // 向后兼容旧格式
          else if (knowledgeData.knowledge !== undefined) {
            const knowledgeContent = knowledgeData.knowledge;
            ChatInterface.setCurrentEditingFile(filePath);
            CodeEditorUtils.setAppKnowledge(knowledgeContent);
            CodeEditorUtils.showProjectKnowledge(knowledgeContent);
            
            // 通知TaskFilesList更新选中状态
            window.dispatchEvent(new CustomEvent('fileSelectedFromSearch', { 
              detail: { fileName: filePath } 
            }));
            
            message.success('Knowledge file loaded successfully');
            return;
          }
        } catch (e) {
          console.warn('Failed to parse Knowledge.json:', e);
        }
      }

      // 检查是否是任务文件
      if (filePath.endsWith('.json') && filePath !== 'Knowledge.json') {
        try {
          const taskData = JSON.parse(result.content);
          if ('task_name' in taskData && 'NL_script' in taskData && 'code_script' in taskData) {
            // 加载任务文件
            if (CodeEditorUtils.viewAppKnowledge || CodeEditorUtils.viewProjectKnowledge) {
              await CodeEditorUtils.hideKnowledgeViewOnly();
            }
            ChatInterface.setCurrentEditingFile(filePath);
            ChatInterface.setTask(taskData.task_name || '');
            ChatInterface.setTaskDescription(taskData.task_description || '');
            ChatInterface.setProjectName(taskData.project_name || taskData.task_name || '');
            CodeEditorUtils.setNLScript(taskData.NL_script || '');
            CodeEditorUtils.setCodeScript(taskData.code_script || '');
            
            // 更新编辑器内容
            await new Promise(resolve => setTimeout(resolve, 100));
            const context = (window as any).__codeEditorContext;
            if (context?.monacoEditor) {
              const currentScript = CodeEditorUtils.scriptMode === "NL" 
                ? CodeEditorUtils.getNLScript() 
                : CodeEditorUtils.getCodeScript();
              if (currentScript) {
                context.monacoEditor.setValue(currentScript);
              }
            }
            
            // 通知TaskFilesList更新选中状态
            window.dispatchEvent(new CustomEvent('fileSelectedFromSearch', { 
              detail: { fileName: filePath } 
            }));
            
            message.success('Task file loaded successfully');
            return;
          }
        } catch (e) {
          console.warn('Failed to parse task JSON file:', e);
        }
      }

      // 对于其他文件，显示预览
      if (['csv', 'xlsx', 'xls'].includes(filePath.split('.').pop()?.toLowerCase() || '')) {
        // 这里可以添加表格文件预览逻辑
        message.info('Table file preview not implemented yet');
        return;
      }

      // 显示文件内容预览
      Modal.info({
        title: filePath,
        content: (
          <div style={{ maxHeight: '400px', overflow: 'auto' }}>
            <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
              {result.content}
            </pre>
          </div>
        ),
        width: 800,
        okText: 'OK'
      });
    } catch (error) {
      console.error('Failed to handle file selection:', error);
      message.error('Failed to load file');
    }
  };

  // 在组件挂载时初始化语言配置
  useEffect(() => {
    (async () => {
      await I18nUtils.initialize();
      // 同步配置到全局语言上下文中
      setLanguage(I18nUtils.getCurrentLanguage());

      // 获取并设置 App 语言配置
      const appLanguage = await window.electronAPI.getAppLanguageConfig();
      // 如果当前值为 default，则获取浏览器语言
      if (appLanguage === 'default') {
        const finalAppLanguage = navigator.language.toLowerCase().startsWith('zh') ? 'zh' : 'en';
        setAppLanguage(finalAppLanguage);
        window.electronAPI.setAppLanguageConfig(finalAppLanguage);
      }
      else {
        setAppLanguage(appLanguage);
      }
    })();
  }, [setLanguage]);

  // Monitor update availability and preferences changes
  useEffect(() => {
    // Function to check if update should be shown
    const checkUpdateAvailability = async () => {
      if (availableVersion) {
        const shouldShow = await UpdatePreferencesManager.shouldShowUpdate(availableVersion);
        setUpdateAvailable(shouldShow);
      }
    };

    // Initial check
    checkUpdateAvailability();

    // Listen for preference changes
    const handlePreferencesChanged = () => {
      checkUpdateAvailability();
    };

    window.addEventListener('updatePreferencesChanged', handlePreferencesChanged);

    return () => {
      window.removeEventListener('updatePreferencesChanged', handlePreferencesChanged);
    };
  }, [availableVersion]);

  // Listen for update messages from main process
  useEffect(() => {
    const handleUpdateMessage = async (updateMessage: any) => {
      if (updateMessage.type === 'available-confirm' && updateMessage.version) {
        setAvailableVersion(updateMessage.version);
        const shouldShow = await UpdatePreferencesManager.shouldShowUpdate(updateMessage.version);
        setUpdateAvailable(shouldShow);
      } else if (updateMessage.type === 'not-available') {
        setUpdateAvailable(false);
        setAvailableVersion(null);
      }
    };

    window.electronAPI.onUpdateMessage(handleUpdateMessage);

    return () => {
      window.electronAPI.offUpdateMessage && window.electronAPI.offUpdateMessage();
    };
  }, []);

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + P 打开文件搜索
      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        if (searchInputRef.current) {
          searchInputRef.current.focus();
          updateSearchPosition();
          if (searchValue.trim()) {
            setSearchOverlayVisible(true);
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [searchValue]);

  // 新增 useEffect 获取用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const response = await userApi.getUserInfo();
        const userInfo = response.data;
        console.log("------ getUserInfo info ------");
        console.log("getUserInfo info:", userInfo);
        console.log("------ getUserInfo info end ------");
        setUserInfo({
          username: userInfo.username,
          displayName: userInfo.display_name,
          email: userInfo.email,
          githubId: userInfo.github_id,
          wechatId: userInfo.wechat_id
        });
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    };
    fetchUserInfo();
  }, []);

  const fetchAgentList = async (page = currentPage, size = pageSize) => {
    try {
      setLoading(true);
      // 获取远程仓库的任务列表
      const response = await repoApi.listRepos(page, size);
      
      // 处理分页响应
      if (response && typeof response === 'object') {
        // 检查是否有分页信息
        if (response.data && Array.isArray(response.data)) {
          setAgentList(processAgentList(response.data));
          setTotalItems(response.total || response.data.length);
        } else if (Array.isArray(response)) {
          // 兼容没有分页信息的旧格式
          setAgentList(processAgentList(response));
          setTotalItems(response.length);
        } else {
          setAgentList([]);
          setTotalItems(0);
        }
      } else {
        setAgentList([]);
        setTotalItems(0);
      }
    } catch (error) {
      console.error(I18nUtils.getText('getAgentListFailed'), error);
      // 发生错误时确保 agentList 是空数组
      setAgentList([]);
      setTotalItems(0);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProject = async () => {
    try {
      console.log('[NavBar] ===== 开始保存项目流程 =====');
      // 获取当前项目名称
      const projectName = ChatInterface.projectName || currentProjectId;
      console.log('[NavBar] 当前项目名称:', projectName);
      if (!projectName) {
        console.warn('[NavBar] 项目名称为空，无法保存');
        message.warning(I18nUtils.getText('saveProjectNeedLoad'));
        return;
      }

      console.log('[NavBar] 开始更新编辑器内容...');
      // 先更新并保存当前编辑器内容到本地
      CodeEditorUtils.checkAndUpdateAppKnowledge();
      
      // 检查当前是否在查看知识库，如果是则跳过脚本转换
      if (!CodeEditorUtils.viewAppKnowledge && !CodeEditorUtils.viewProjectKnowledge) {
        console.log('[NavBar] 当前不在查看知识库，执行脚本更新...');
        await CodeEditorUtils.updateScriptBeforeExecute();
        // 等待下一个事件循环，从而获取更新后的 script
        await new Promise(resolve => setTimeout(resolve, 0));
      } else {
        console.log('[NavBar] 当前正在查看知识库，跳过脚本转换...');
      }
      console.log('[NavBar] 编辑器内容更新完成');

      console.log('[NavBar] 获取仓库访问令牌...');
      // 获取repo token
      const tokenResponse = await repoApi.getRepoToken(projectName);
      if (!tokenResponse || !tokenResponse.token) {
        console.error('[NavBar] ✗ 无法获取仓库访问令牌');
        message.error('无法获取仓库访问令牌，请先确保项目已上传');
        return;
      }
      console.log('[NavBar] ✓ 已获取仓库访问令牌');

      const loadingMsgId = ChatInterface.addLoadingMessage(I18nUtils.getText('savingProject'));
      console.log('[NavBar] 开始Git保存和推送...');

      // 调用新的保存并推送函数
      const result = await window.electronAPI.saveAndPushProjectToGit({
        taskName: projectName,
        repoToken: tokenResponse.token,
        commitMessage: `Save project: ${projectName} at ${new Date().toISOString()}`,
        userInfo
      });

      console.log('[NavBar] Git操作结果:', result);

      if (result.success) {
        console.log('[NavBar] ✓ 项目保存成功');
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('saveProjectSuccess'));
        message.success(I18nUtils.getText('saveProjectSuccess'));
      } else {
        console.error('[NavBar] ✗ 项目保存失败:', result.message);
        ChatInterface.updateToCompleteMessage(loadingMsgId, I18nUtils.getText('saveProjectFailed') + ': ' + result.message, true);
        message.error(I18nUtils.getText('saveProjectFailed') + ': ' + result.message);
      }

      console.log('[NavBar] ===== 保存项目流程结束 =====');

    } catch (error) {
      console.error('[NavBar] ===== 保存项目失败 =====');
      console.error('[NavBar] 错误详情:', error);
      if (error instanceof Error && error.message === 'Insufficient Token') {
        message.error(I18nUtils.getText('insufficientTokenUpload'));
      } else {
        message.error(I18nUtils.getText('saveProjectFailed') + ': ' + ((error as Error).message || '未知错误'));
      }
    }
  };

  const handleUploadAgent = async () => {
    try {
      // 检查是否有 task
      if (!currentTask) {
        message.warning(I18nUtils.getText('uploadNeedTask'));
        return;
      }

      // 先检查用户修改并更新
      CodeEditorUtils.checkAndUpdateAppKnowledge();
      await CodeEditorUtils.updateScriptBeforeExecute();
      
      // 等待下一个事件循环，从而获取更新后的 script
      await new Promise(resolve => setTimeout(resolve, 0));

      // 如果当前项目ID为空，则创建一个新项目
      if (!currentProjectId) {
        const response = await repoApi.createRepo(
          ChatInterface.task,
          ChatInterface.taskDescription,
        );
        console.log('【handleUploadAgent】远程仓库创建成功:', response, ChatInterface.task, ChatInterface.taskDescription);
        
        // The new `createRepo` might not return a usable id for history, so this part is simplified.
        // The id is now the name.
        setCurrentProjectId(response.name);
        
        // 获取 token 并进行 git 推送
        try {
          const tokenResponse = await repoApi.getRepoToken(response.name);
          
          // 保存任务到本地仓库
          const saveResult = await window.electronAPI.saveTaskToLocalRepo({
            taskName: response.name,
            taskDescription: ChatInterface.taskDescription,
            codeScript: CodeEditorUtils.getCodeScript() || '',
            NLScript: CodeEditorUtils.getNLScript() || ''
          });
          
          if (saveResult.success) {
            // 进行 git 推送
            const gitResult = await window.electronAPI.initializeAndPushGitRepo({
              taskName: response.name,
              repoToken: tokenResponse.token || response.token,
              projectDescription: ChatInterface.taskDescription || '',
              userInfo
            });
            
            if (gitResult.success) {
              message.success(I18nUtils.getText('uploadAndPushSuccess'));
            } else {
              message.warning(I18nUtils.getText('uploadPushFailed') + ': ' + gitResult.message);
            }
          }
        } catch (gitError) {
          console.error('Git 操作失败:', gitError);
          message.warning(I18nUtils.getText('uploadPushFailed'));
        }
        
        await fetchAgentList(currentPage, pageSize);
        // setIsHistoryModalVisible(true); // History modal is deprecated with git backend
        return;
      }

      // 更新项目内容
      // With git backend, renaming and updating description are separate.
      // For simplicity, we only update description here as renaming requires old_name and can be complex.
      // The content update (scripts) should be done via git commands, which is not implemented here.
      const agent = agentList.find(a => a.name === currentProjectId);
      if(agent && agent.name !== ChatInterface.task) {
        await repoApi.renameRepo(agent.name, ChatInterface.task);
        setCurrentProjectId(ChatInterface.task); // update current project id to new name
      }
      await repoApi.updateRepoDescription(ChatInterface.task, ChatInterface.taskDescription);

      // 更新项目后也进行 git 推送
      try {
        const tokenResponse = await repoApi.getRepoToken(ChatInterface.task);
        
        // 保存任务到本地仓库
        const saveResult = await window.electronAPI.saveTaskToLocalRepo({
          taskName: ChatInterface.task,
          taskDescription: ChatInterface.taskDescription,
          codeScript: CodeEditorUtils.getCodeScript() || '',
          NLScript: CodeEditorUtils.getNLScript() || ''
        });
        
        if (saveResult.success) {
          // 进行 git 推送
          const gitResult = await window.electronAPI.initializeAndPushGitRepo({
            taskName: ChatInterface.task,
            repoToken: tokenResponse.token,
            projectDescription: ChatInterface.taskDescription || '',
            userInfo
          });
          
          if (gitResult.success) {
            message.success(I18nUtils.getText('uploadAndPushSuccess'));
          } else {
            message.warning(I18nUtils.getText('uploadPushFailed') + ': ' + gitResult.message);
          }
        }
      } catch (gitError) {
        console.error('Git 操作失败:', gitError);
        message.warning(I18nUtils.getText('uploadPushFailed'));
      }

      // setTempProjectId(currentProjectId);
      // setIsHistoryModalVisible(true); // History modal is deprecated
    } catch (error) {
      console.log("[NavBar] handleUploadAgent error: ", error);
      if (error instanceof Error && error.message === 'Insufficient Token') {
        message.error(I18nUtils.getText('insufficientTokenUpload'));
      }
      else {
        console.error(I18nUtils.getText('uploadFailed'), error);
        message.error(I18nUtils.getText('uploadFailed'));
      }
    }
  };

  const handleHistoryModalOk = async () => {
    // This function is tied to the old history saving mechanism and is now deprecated.
    // Kept for now to avoid breaking changes, but should be removed.
    // try {
    //   await projectApi.saveProjectHistory(tempProjectId, historyMessage);
    //   setIsHistoryModalVisible(false);
    //   setHistoryMessage('');
    //   message.success(I18nUtils.getText('uploadSuccess'));
    //   await fetchAgentList();
    // } catch (error) {
    //   console.error(I18nUtils.getText('saveHistoryFailed'), error);
    //   message.error(I18nUtils.getText('saveHistoryFailed'));
    // }
    setIsHistoryModalVisible(false);
    message.info("This feature is deprecated.");
  };



  const handleLoadAgent = async (projectName: string) => {
    try {
      await CodeEditorUtils.forceSave();

      // 查找要加载的任务
      const agent = agentList.find(a => a.name === projectName);
      if (!agent) {
        message.error(I18nUtils.getText('loadFailed'));
        return;
      }
      
      // 设置当前项目ID
      setCurrentProjectId(projectName);
      
      console.log(`[Project Load] Loading project: ${projectName}`);

      // Git 同步操作：检查本地是否有该项目，如果没有就clone，如果有就pull
      console.log(`[Git Sync] 开始同步项目: ${projectName}`);
      message.loading(`${I18nUtils.getText('gitSyncProgress')} ${projectName}...`, 0);
      
      try {
        // 获取仓库访问令牌
        const tokenResponse = await repoApi.getRepoToken(projectName);
        if (!tokenResponse || !tokenResponse.token) {
          console.warn(`[Git Sync] 无法获取项目 ${projectName} 的访问令牌，跳过Git同步`);
          message.destroy();
          message.warning(I18nUtils.getText('gitTokenMissing'));
        } else {
          const repoToken = tokenResponse.token;
          
          // 首先尝试pull，如果项目不存在本地会失败，然后执行clone
          const pullResult = await window.electronAPI.pullProjectFromGit({
            projectName,
            repoToken,
            userInfo
          });

          console.log('[Git Sync] pullResult: ', pullResult);
          
          if (pullResult.success) {
            console.log(`[Git Sync] ✓ 项目 ${projectName} pull成功: ${pullResult.message}`);
            message.destroy();
            // message.success(`${I18nUtils.getText('gitPullSuccess')}: ${pullResult.message}`);
          } else if (pullResult.message.includes('项目目录不存在')) {
            // 本地不存在，执行clone
            console.log(`[Git Sync] 本地不存在项目 ${projectName}，开始clone`);
            const cloneResult = await window.electronAPI.cloneProjectFromGit({
              projectName,
              repoToken,
              userInfo
            });
            
            if (cloneResult.success) {
              console.log(`[Git Sync] ✓ 项目 ${projectName} clone成功: ${cloneResult.message}`);
              message.destroy();
              message.success(`${I18nUtils.getText('gitCloneSuccess')}: ${cloneResult.message}`);
            } else {
              console.error(`[Git Sync] ✗ 项目 ${projectName} clone失败: ${cloneResult.message}`);
              message.destroy();
              message.error(`${I18nUtils.getText('gitCloneFailed')}: ${cloneResult.message}`);
            }
          } else if (pullResult.hasConflict) {
            // 有冲突，询问用户选择
            message.destroy();
            console.log(`[Git Sync] ✗ 项目 ${projectName} 存在冲突: ${pullResult.message}`);
            
            // 使用Modal.confirm来询问用户选择
            const { Modal } = await import('antd');
            Modal.confirm({
              title: I18nUtils.getText('gitConflictTitle'),
              content: `${I18nUtils.getText('projectName')} "${projectName}" ${I18nUtils.getText('gitConflictMessage')}\n\n${pullResult.message}\n\n`,
              okText: I18nUtils.getText('useRemoteVersion'),
              cancelText: I18nUtils.getText('useLocalVersion'),
              onOk: async () => {
                try {
                  message.loading(I18nUtils.getText('applyingRemoteVersion'), 0);
                  const forceRemoteResult = await window.electronAPI.forceUseRemoteVersion({
                    projectName,
                    repoToken,
                    userInfo
                  });
                  
                  message.destroy();
                  if (forceRemoteResult.success) {
                    message.success(`${I18nUtils.getText('applyRemoteVersionSuccess')}: ${forceRemoteResult.message}`);
                  } else {
                    message.error(`${I18nUtils.getText('applyRemoteVersionFailed')}: ${forceRemoteResult.message}`);
                  }
                } catch (error) {
                  message.destroy();
                  console.error('应用云端版本失败:', error);
                  message.error(I18nUtils.getText('applyRemoteVersionFailed'));
                }
              },
              onCancel: async () => {
                try {
                  message.loading(I18nUtils.getText('applyingLocalVersion'), 0);
                  const forceLocalResult = await window.electronAPI.forceUseLocalVersion({
                    projectName,
                    repoToken,
                    userInfo
                  });
                  
                  message.destroy();
                  if (forceLocalResult.success) {
                    message.success(`${I18nUtils.getText('applyLocalVersionSuccess')}: ${forceLocalResult.message}`);
                  } else {
                    message.error(`${I18nUtils.getText('applyLocalVersionFailed')}: ${forceLocalResult.message}`);
                  }
                } catch (error) {
                  message.destroy();
                  console.error('推送本地版本失败:', error);
                  message.error(I18nUtils.getText('applyLocalVersionFailed'));
                }
              }
            });
          } else {
            // 其他pull失败
            console.error(`[Git Sync] ✗ 项目 ${projectName} pull失败: ${pullResult.message}`);
            message.destroy();
            message.error(`${I18nUtils.getText('gitPullFailed')}: ${pullResult.message}`);
          }
        }
      } catch (error) {
        console.error(`[Git Sync] Git同步操作异常:`, error);
        message.destroy();
        message.error(I18nUtils.getText('gitSyncException'));
      }

      // 设置 RuyiAgent 的数据目录路径
      try {
        await window.electronAPI.setDataDirPathConfig(projectName);
        console.log(`[RuyiAgent Config] Set data_dir_path to project: ${projectName}`);
      } catch (error) {
        console.error(`[RuyiAgent Config] Failed to set data_dir_path for ${projectName}:`, error);
        message.error('设置 RuyiAgent 数据目录路径失败');
      }

      // 更新各个组件的数据
      ChatInterface.setProjectName(agent.name);  // 设置项目名称
      ChatInterface.setTask('');  // 清空任务名称，因为加载项目时还没有具体任务
      ChatInterface.setTaskDescription(agent.description);
      ChatInterface.setCurrentEditingFile(''); // 清空当前正在编辑的文件
      // Clear messages and scripts as they are not fetched.
      ChatInterface.setAllMessages([]);

      CodeEditorUtils.resetKnowledgeView();

      CodeEditorUtils.setCodeScript("");
      CodeEditorUtils.setNLScript("");
      CodeEditorUtils.lastScript = "";

      setIsModalVisible(false);
      message.success(I18nUtils.getText('loadSuccess'));
    } catch (error) {
      console.error(I18nUtils.getText('loadFailed'), error);
      message.error(I18nUtils.getText('loadFailed'));
    }
  };



  const handleTaskEdit = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      ChatInterface.setTask(editingTask);
      setIsEditing(false);
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditingTask(currentTask);
    }
  };

  const handleTaskBlur = () => {
    setIsEditing(false);
    setEditingTask(currentTask);
  };



  /**
   * 处理用户点击查看项目历史按钮 --> 在 git server 中被弃用
   */
  // const handleViewProjectHistory = async () => {
  //   try {
  //     // 如果没有 currentProjectId
  //     if (!currentProjectId) {
  //       // 如果有 task 内容
  //       if (currentTask) {
  //         // 先保存当前内容
  //         const response = await projectApi.createProject({
  //           name: currentTask,
  //           description: ChatInterface.taskDescription,
  //           code_script: CodeEditorUtils.getCodeScript(),
  //           nl_script: CodeEditorUtils.getNLScript(),
  //           messages: ChatInterface.getAllMessages(),
  //         });

  //         // 设置当前项目ID
  //         setCurrentProjectId(response.id);
          
  //         // 获取历史记录
  //         const historyResponse = await projectApi.getProjectHistory(response.id);
  //         setProjectHistory(historyResponse.data);
  //         setTotalHistory(historyResponse.total);
  //         setIsProjectHistoryModalVisible(true);
  //       } else {
  //         // 如果既没有 projectId 也没有 task，提示用户先选择或创建 agent
  //         message.error(I18nUtils.getText('viewHistoryNeedSelectAgent'));
  //         return;
  //       }
  //     } else {
  //       // 如果有 currentProjectId，直接获取历史记录
  //       const response = await projectApi.getProjectHistory(currentProjectId);
  //       setProjectHistory(response.data);
  //       setTotalHistory(response.total);
  //       setIsProjectHistoryModalVisible(true);
  //     }
  //   } catch (error) {
  //     console.error(I18nUtils.getText('viewHistoryFailed'), error);
  //     message.error(I18nUtils.getText('viewHistoryFailed'));
  //   }
  // };

  // 添加查看详情的处理函数  --> 在 git server 中被弃用
  // const handleViewHistoryDetail = (record: any) => {
  //   setCurrentHistoryDetail(record);
  //   setIsHistoryDetailModalVisible(true);
  // };

  const handleCreateNewAgent = () => {
    setIsCreateProjectModalVisible(true);
  };

  const handleCreateProject = async () => {
    if (!projectName.trim()) {
      message.error(I18nUtils.getText('projectNameRequired'));
      return;
    }

    try {
      await CodeEditorUtils.forceSave();
      
      // 清空当前项目ID
      setCurrentProjectId('');
      
      // 清空任务名称和项目名称
      ChatInterface.setTask('');
      ChatInterface.setTaskDescription('');
      ChatInterface.setProjectName('');
      ChatInterface.setCurrentEditingFile(''); // 清空当前正在编辑的文件
      
      // 重置知识库视图
      CodeEditorUtils.resetKnowledgeView();
      
      // 重置编辑器内容
      CodeEditorUtils.setNLScript("");
      CodeEditorUtils.setCodeScript("");
      
      // 清空聊天记录
      ChatInterface.setAllMessages([]);

      // 1. 创建远程仓库
      const repoResponse = await repoApi.createRepo(projectName, projectDescription.trim());
      console.log('远程仓库创建成功:', repoResponse, projectName, projectDescription.trim());
      
      // 更新当前项目ID
      setCurrentProjectId(repoResponse.name);
      
      // 设置项目名称为用户填写的项目名称
      ChatInterface.setProjectName(projectName);
      
      // 1.1. 将新创建的仓库设置为私有 (private: true)
      await repoApi.updateRepoVisibility(projectName, true);
      console.log('仓库已设置为私有');
      
      // 2. 获取仓库 token
      const tokenResponse = await repoApi.getRepoToken(projectName);
      console.log('获取仓库 token 成功:', tokenResponse);
      
      // 3. 创建本地 git 仓库并推送到远程
      const gitResult = await window.electronAPI.initializeAndPushGitRepo({
        taskName: projectName,
        repoToken: tokenResponse.token || repoResponse.token,
        projectDescription: projectDescription || '',
        userInfo
      });
      
      if (gitResult.success) {
        console.log('Git 仓库创建和推送成功:', gitResult.message);
        message.success(I18nUtils.getText('createProjectSuccess'));
        
        // 设置 RuyiAgent 的数据目录路径
        await window.electronAPI.setDataDirPathConfig(projectName);

        // 通知项目创建完成
        ChatInterface.notifyProjectCreated(projectName);
      } else {
        console.error('Git 操作失败:', gitResult.message);
        message.warning(I18nUtils.getText('createProjectFailed') + ': ' + gitResult.message);
      }
      
      // 刷新仓库列表，使用当前分页参数
      await fetchAgentList(currentPage, pageSize);
          
    } catch (error) {
      console.error('创建项目过程中发生错误:', error);
      message.error(I18nUtils.getText('createProjectFailed') + '：' + ((error as Error).message || '未知错误'));
    }

    // 关闭模态框并重置表单
    setIsCreateProjectModalVisible(false);
    setProjectName('');
    setProjectDescription('');
  };

  const handleModelSettingsSave = () => {
    // 这里可以添加保存到本地存储或发送到服务器的逻辑
    message.success(I18nUtils.getText('settingsSaved'));
    // setIsModelSettingsVisible(false);
  };

  const handlePortChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'flask_port' | 'scrcpy_port' | 'device_port' | 'electron_http_port') => {
    const value = e.target.value;
    // 只允许输入数字
    if (/^\d*$/.test(value)) {
      // 如果输入为空，允许继续输入
      if (value === '') {
        setPortSettings(prev => ({ ...prev, [field]: value }));
        return;
      }
      
      // 转换为数字进行范围检查
      const numValue = parseInt(value);
      if (numValue >= 1024 && numValue <= 65535) {
        setPortSettings(prev => ({ ...prev, [field]: value }));
      } else {
        message.warning(I18nUtils.getText('portRangeWarning')); // 需要在 i18n 中添加对应的文本
      }
    }
  };

  const handlePortSettingsSave = async () => {
    try {
      // 添加保存前的验证
      const ports = [
        parseInt(portSettings.flask_port),
        parseInt(portSettings.scrcpy_port),
        parseInt(portSettings.device_port),
        parseInt(portSettings.electron_http_port)
      ];

      // 检查是否有空值或无效值
      if (ports.some(port => isNaN(port) || port < 1024 || port > 65535)) {
        message.error(I18nUtils.getText('invalidPortRange'));
        return;
      }

      // 检查是否有重复的端口号
      if (new Set(ports).size !== ports.length) {
        message.error(I18nUtils.getText('duplicatePorts'));
        return;
      }

      await (window.electronAPI as any).setPortConfig({
        flask_port: ports[0],
        scrcpy_port: ports[1],
        device_port: ports[2],
        electron_http_port: ports[3]
      });
      
      message.info(I18nUtils.getText('portChangeMessage'));
      // setIsPortSettingsVisible(false);
    } catch (error) {
      console.error('保存端口配置失败:', error);
      message.error(I18nUtils.getText('setPortConfigFailed'));
    }
  };

  // 修改后的 handleLanguageChange 函数
  const handleLanguageChange = async (newLanguage: string) => {
    try {
      // 调用 electronAPI 更新语言配置
      await window.electronAPI.setLanguageConfig(newLanguage);
      // 更新 I18nUtils 中的当前语言
      I18nUtils.currentLanguage = newLanguage;
      // 更新全局语言上下文
      setLanguage(newLanguage);
      message.success(I18nUtils.getText('ideLanguageChanged'));
    } catch (error) {
      console.error('更新语言配置失败:', error);
      message.error(I18nUtils.getText('ideLanguageChangeFailed'));
    }
  };

  const handleAppLanguageChange = async (newAppLanguage: string) => {
    try {
      await window.electronAPI.setAppLanguageConfig(newAppLanguage);
      setAppLanguage(newAppLanguage);
      message.success(I18nUtils.getText('appLanguageChanged'));
    } catch (error) {
      console.error('更新 App 语言配置失败:', error);
      message.error(I18nUtils.getText('appLanguageChangeFailed'));
    }
  };

  const handleBrowserHomepageChange = (homepage: string) => {
    setBrowserSettings(prev => ({ ...prev, homepage }));
  };

  const handleBrowserSearchEngineChange = (searchEngine: string) => {
    setBrowserSettings(prev => ({ ...prev, searchEngine }));
  };

  const handleBrowserSettingsSave = async () => {
    try {
      // Validate URL
      if (browserSettings.homepage && !isValidUrl(browserSettings.homepage)) {
        message.error(I18nUtils.getText('browserHomepageInvalid'));
        return;
      }
      
      await Promise.all([
        window.electronAPI.setBrowserHomepageConfig(browserSettings.homepage),
        window.electronAPI.setDefaultSearchEngineConfig(browserSettings.searchEngine)
      ]);
      
      message.success(I18nUtils.getText('browserSettingsChanged'));
    } catch (error) {
      console.error('Failed to save browser settings:', error);
      message.error(I18nUtils.getText('browserSettingsChangeFailed'));
    }
  };

  // Helper function to validate URL
  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleLogout = () => {
    Modal.confirm({
      title: I18nUtils.getText('logoutConfirmTitle'),
      content: I18nUtils.getText('logoutConfirmContent'),
      okText: I18nUtils.getText('confirm'),
      cancelText: I18nUtils.getText('cancel'),
      onOk: async () => {
        try {
          // 1. 先重置前端状态
          console.log('[NavBar] 登出时重置前端状态');
          StateManager.resetToWelcomeState();
          
          // 2. 调用API登出
          await userApi.logout();
          
          // 3. 清理当前用户的本地仓库数据
          if (userInfo.username) {
            try {
              await window.electronAPI.clearUserRepoData(userInfo.username);
              console.log(`已清理用户 ${userInfo.username} 的本地仓库数据`);
            } catch (error) {
              console.warn('清理用户本地数据失败:', error);
              // 不阻断登出流程，只记录警告
            }
          }
          
          // 4. 重置当前用户状态
          try {
            await window.electronAPI.setCurrentUser({ username: null });
          } catch (error) {
            console.warn('重置用户状态失败:', error);
          }
          
          message.success(I18nUtils.getText('logoutSuccess'));
          navigate('/login');
        } catch (error) {
          console.error('Logout failed:', error);
          message.error(error instanceof Error ? error.message : 'Logout failed');
        }
      }
    });
  };

  // 修改后的用户面板渲染
  const renderUserPanel = () => {
    return (
      <Modal
        title={I18nUtils.getText('userProfile')}
        open={isUserPanelVisible}
        onCancel={() => setIsUserPanelVisible(false)}
        footer={null}
        width={400}
      >
        <div className="user-panel">
          <div className="user-info">
            <div className="user-info-item">
              <span className="label">{I18nUtils.getText('username')}:</span>
              <span className="value">{userInfo.username}</span>
            </div>
            <div className="user-info-item">
              <span className="label">{I18nUtils.getText('displayName')}:</span>
              <span className="value">{userInfo.displayName}</span>
            </div>
            <div className="user-info-item">
              <span className="label">{I18nUtils.getText('email')}:</span>
              <span className="value">{userInfo.email || I18nUtils.getText('notSet')}</span>
            </div>
            <div className="user-info-item">
              <span className="label">{I18nUtils.getText('githubId')}:</span>
              <span className="value">{userInfo.githubId || I18nUtils.getText('notSet')}</span>
            </div>
            <div className="user-info-item">
              <span className="label">{I18nUtils.getText('wechatId')}:</span> 
              <span className="value">{userInfo.wechatId || I18nUtils.getText('notSet')}</span>
            </div>
          </div>
          <Button
            type="primary"
            danger
            onClick={handleLogout}
            className="logout-button"
          >
            {I18nUtils.getText('logout')}
          </Button>
        </div>
      </Modal>
    );
  };

  // 修改后的头像点击事件
  const handleAvatarClick = () => {
    setIsUserPanelVisible(true);
  };

  /**
   * 渲染标题
   * 显示当前项目名称，如果没有项目则显示默认标题
   */
  const renderTitle = () => {
    // 优先显示项目名称，其次显示任务名称，最后显示默认标题
    const displayName = ChatInterface.projectName || currentTask || title;
    return (
      <div className="nav-bar-title">{displayName}</div>
    );
  };



  const handleDeleteAgent = async (projectName: string) => {
    // 查找要删除的任务
    const agent = agentList.find(a => a.name === projectName);
    if (!agent) {
      message.error(I18nUtils.getText('deleteFailed'));
      return;
    }

    let localDeleteSuccess = false;
    let cloudDeleteSuccess = false;
    let localError = '';
    let cloudError = '';

    // 尝试本地删除
    try {
      const result = await window.electronAPI.deleteTaskFromLocalRepo(projectName);
      if (result.success) {
        localDeleteSuccess = true;
      } else {
        // 处理主进程返回的消息键
        const messageKey = result.message;
        // 检查是否是已知的消息键
        if (messageKey === 'taskNameEmpty') {
          localError = I18nUtils.getText('taskNameEmpty');
        } else if (messageKey === 'taskDirectoryNotExists') {
          localError = I18nUtils.getText('taskDirectoryNotExists');
        } else if (messageKey === 'taskDeleteSuccess') {
          localError = I18nUtils.getText('taskDeleteSuccess');
        } else if (messageKey && messageKey.startsWith('taskDeleteFailed')) {
          localError = I18nUtils.getText('taskDeleteFailed') + ': ' + messageKey.split(': ').slice(1).join(': ');
        } else {
          localError = result.message || I18nUtils.getText('deleteLocalFailed');
        }
      }
    } catch (error) {
      localError = (error as Error).message || I18nUtils.getText('deleteLocalFailed');
    }

    // 尝试云端删除
    try {
      await repoApi.deleteRepo(projectName);
      cloudDeleteSuccess = true;
    } catch (error) {
      cloudError = (error as Error).message || I18nUtils.getText('deleteCloudFailed');
    }

    // 根据删除结果显示相应的消息
    if (localDeleteSuccess && cloudDeleteSuccess) {
      // 两个都成功
      message.success(I18nUtils.getText('deleteSuccess'));
      // 重新获取任务列表，保持当前分页参数
      fetchAgentList(currentPage, pageSize);
      // 如果当前项目是被删除的项目，重置当前项目ID和任务状态
      if (currentProjectId === projectName) {
        setCurrentProjectId('');
        ChatInterface.setTask('');
        ChatInterface.setTaskDescription('');
        ChatInterface.setProjectName('');
      }
    } else if (!localDeleteSuccess && !cloudDeleteSuccess) {
      // 两个都失败
      message.error(`${I18nUtils.getText('deleteCloudAndLocalFailed')}: ${cloudError}; ${localError}`);
    } else if (!localDeleteSuccess) {
      // 本地失败，云端成功
      message.error(`${I18nUtils.getText('deleteLocalFailed')}: ${localError}`);
      // 即使本地删除失败，云端成功了也要刷新列表
      fetchAgentList(currentPage, pageSize);
    } else if (!cloudDeleteSuccess) {
      // 本地成功，云端失败
      message.error(`${I18nUtils.getText('deleteCloudFailed')}: ${cloudError}`);
      // 即使云端删除失败，本地成功了也要刷新列表和重置状态
      fetchAgentList(currentPage, pageSize);
      if (currentProjectId === projectName) {
        setCurrentProjectId('');
        ChatInterface.setTask('');
        ChatInterface.setTaskDescription('');
        ChatInterface.setProjectName('');
      }
    }
  };

  // 修改后的 handleViewAnnotations 函数
  const handleViewAnnotations = () => {
    setIsAnnotationManagementVisible(true);
  };

  // 添加处理报告问题的函数
  const handleReportIssue = async () => {
    try {
      if (!issueForm.content.trim()) {
        message.error(I18nUtils.getText('reportIssueContentRequired'));
        return;
      }

      const feedbackResponse = await systemApi.feedback(
        issueForm.category,
        issueForm.content,
        issueForm.image || ''
      );
      if (feedbackResponse.success) {
        message.success(I18nUtils.getText('reportIssueSuccess'));
      } else {
        message.error(feedbackResponse.message);
      }
      setIsReportIssueModalVisible(false);
      setIssueForm({
        category: 'idea',
        content: '',
        image: null
      });
    } catch (error) {
      console.error('Report issue failed:', error);
      message.error(I18nUtils.getText('reportIssueFailed'));
    }
  };

  // 处理图片上传
  const handleImageUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      setIssueForm(prev => ({
        ...prev,
        image: e.target?.result as string
      }));
    };
    reader.readAsDataURL(file);
    return false; // 阻止自动上传
  };

  // 切换项目可见性
  const handleToggleVisibility = async (projectName: string, currentPrivate: boolean) => {
    try {
      setChangingVisibility(projectName);
      
      // 调用API切换可见性
      await repoApi.updateRepoVisibility(projectName, !currentPrivate);
      
      // 更新本地状态
      setAgentList(prevList => 
        prevList.map(agent => 
          agent.name === projectName 
            ? { ...agent, private: !currentPrivate }
            : agent
        )
      );
      
      message.success(
        !currentPrivate 
          ? I18nUtils.getText('setToPrivateSuccess') 
          : I18nUtils.getText('setToPublicSuccess')
      );
    } catch (error) {
      console.error('切换可见性失败:', error);
      message.error(I18nUtils.getText('changeVisibilityFailed'));
    } finally {
      setChangingVisibility('');
    }
  };

  // 手动检查更新
  const handleManualCheckUpdate = async () => {
    setCheckingUpdate(true);
    try {
      await window.electronAPI.checkForUpdates();
    } catch (error) {
      console.error('检查更新失败:', error);
      message.error(I18nUtils.getText('checkUpdateFailed'));
    } finally {
      setCheckingUpdate(false);
    }
  };

  return (
    <div className="nav-bar">
      <div className="nav-bar-content">
        {/* 左侧按钮组 */}
        <div className="nav-bar-actions-left">
          <div className="ruyi-space-section">
            <div className="ruyi-space-buttons">
              <Button
                icon={<Plus size={16} />}
                onClick={handleCreateNewAgent}
                className="nav-bar-button"
                size="small"
              >
                {I18nUtils.getText('createNew')}
              </Button>
              <Button
                icon={<Download size={16} />}
                onClick={() => setIsModalVisible(true)}
                className="nav-bar-button"
                size="small"
              >
                {I18nUtils.getText('load')}
              </Button>
            </div>
          </div>
        </div>

        {/* 中央搜索框 */}
        <div className="nav-bar-search-container">
          <Input
            ref={searchInputRef}
            placeholder="搜索项目、文件或命令..."
            prefix={<Search style={{ color: '#bfbfbf' }} size={16} />}
            value={searchValue}
            onChange={(e) => handleSearch(e.target.value)}
            onFocus={handleSearchFocus}
            onBlur={handleSearchBlur}
            className="nav-bar-search-input"
            size="small"
          />
        </div>

        {/* 自动保存状态指示器 */}
        {/* <div className="nav-bar-auto-save-status">
          {autoSaveStatus === 'saving' && (
            <Tooltip title={I18nUtils.getText('autoSaveInProgress')}>
              <div className="auto-save-indicator saving">
                <LoadingOutlined />
                <span>{I18nUtils.getText('autoSaveInProgress')}</span>
              </div>
            </Tooltip>
          )}
          {autoSaveStatus === 'syncing' && (
            <Tooltip title={I18nUtils.getText('autoSyncInProgress')}>
              <div className="auto-save-indicator syncing">
                <CloudUploadOutlined />
                <span>{I18nUtils.getText('autoSyncInProgress')}</span>
              </div>
            </Tooltip>
          )}
          {autoSaveStatus === 'completed' && (
            <Tooltip title={I18nUtils.getText('autoSyncCompleted')}>
              <div className="auto-save-indicator completed">
                <CheckCircleOutlined />
                <span>{I18nUtils.getText('autoSyncCompleted')}</span>
              </div>
            </Tooltip>
          )}
          {autoSaveStatus === 'error' && (
            <Tooltip title={I18nUtils.getText('autoSyncFailed')}>
              <div className="auto-save-indicator error">
                <CloseCircleOutlined />
                <span>{I18nUtils.getText('autoSyncFailed')}</span>
              </div>
            </Tooltip>
          )}
        </div> */}

        {/* 右侧按钮组 */}
        <div className="nav-bar-actions-right">
          <Tooltip title={I18nUtils.getText('viewAnnotations')}>
            <Button
              icon={<Database size={16} />}
              onClick={handleViewAnnotations}
              className="nav-bar-button"
              size="small"
            >
              {I18nUtils.getText('viewAnnotations')}
            </Button>
          </Tooltip>
          <Tooltip title={I18nUtils.getText('reportIssue')}>
            <Button
              icon={<MessageCircle size={16} />}
              onClick={() => setIsReportIssueModalVisible(true)}
              className="nav-bar-button"
              size="small"
            >
              {I18nUtils.getText('reportIssue')}
            </Button>
          </Tooltip>
          <Tooltip title={I18nUtils.getText('checkForUpdates')}>
            <UpdateBadge show={updateAvailable} className="update-badge-button">
              <Button
                icon={<RefreshCw size={16} />}
                className="nav-bar-icon-button"
                onClick={handleManualCheckUpdate}
                loading={checkingUpdate}
                size="small"
              />
            </UpdateBadge>
          </Tooltip>
          <Tooltip title={I18nUtils.getText('settings')}>
            <Button
              icon={<Settings size={16} />}
              className="nav-bar-icon-button"
              onClick={() => setIsSettingsPanelVisible(true)}
              size="small"
            />
          </Tooltip>
          <Avatar
            icon={<User size={16} />}
            className="nav-bar-avatar"
            onClick={handleAvatarClick}
            size="small"
          />
          {renderUserPanel()}
        </div>
      </div>

      <Modal
        title={I18nUtils.getText('selectAgentToLoad')}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          // 重置分页状态
          setCurrentPage(1);
          setPageSize(10);
        }}
        footer={null}
        width={800}
        maskClosable={false}
        className="ruyi-space-modal"
      >
        <List
          className="ruyi-space-list"
          loading={loading}
          dataSource={Array.isArray(agentList) ? agentList : []}
          renderItem={item => (
            <List.Item
              key={item.name}
              actions={[
                <Button
                  type={item.private ? "default" : "primary"}
                  loading={changingVisibility === item.name}
                  onClick={() => handleToggleVisibility(item.name, item.private)}
                  style={{ 
                    backgroundColor: item.private ? '#f6ffed' : '#e6f7ff',
                    borderColor: item.private ? '#b7eb8f' : '#91d5ff',
                    color: item.private ? '#52c41a' : '#1890ff'
                  }}
                >
                  {item.private ? I18nUtils.getText('makePublic') : I18nUtils.getText('makePrivate')}
                </Button>,
                <Button
                  type="primary"
                  onClick={() => handleLoadAgent(item.name)}
                >
                  {I18nUtils.getText('load')}
                </Button>,
                <Button
                  type="primary"
                  danger
                  onClick={() => handleDeleteAgent(item.name)}
                >
                  {I18nUtils.getText('delete')}
                </Button>
              ]}
            >
              <List.Item.Meta
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span>{item.name}</span>
                    <Tag color={item.private ? 'blue' : 'green'}>
                      {item.private ? I18nUtils.getText('private') : I18nUtils.getText('public')}
                    </Tag>
                  </div>
                }
                description={item.description}
              />
            </List.Item>
          )}
        />
        
        {/* 分页组件 */}
        <div className="agent-list-pagination">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={totalItems}
            onChange={handlePageChange}
            onShowSizeChange={handlePageSizeChange}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => 
              I18nUtils.getText('totalItems').replace('{total}', total.toString()) + 
              ` (${range[0]}-${range[1]})`
            }
            pageSizeOptions={['5', '10', '20', '50']}
            size="small"
          />
        </div>

      </Modal>

      <Modal
        title={I18nUtils.getText('saveHistoryModalTitle')}
        open={isHistoryModalVisible}
        onOk={handleHistoryModalOk}
        onCancel={() => setIsHistoryModalVisible(false)}
      >
        <Input.TextArea
          value={historyMessage}
          onChange={(e) => setHistoryMessage(e.target.value)}
          placeholder={I18nUtils.getText('saveHistoryModalPlaceholder')}
          rows={4}
        />
      </Modal>


      
      {/* 查看项目历史的 modal --> 在 git server 中被弃用 */}
      {/* <Modal
        title={I18nUtils.getText('projectHistory')}
        open={isProjectHistoryModalVisible}
        onCancel={() => setIsProjectHistoryModalVisible(false)}
        footer={null}
        width={800}
      >
        <div>
            {I18nUtils.getText('totalHistory')}: {totalHistory}
        </div>
        <List
            dataSource={projectHistory}
            renderItem={item => (
                <List.Item
                    key={item.id}
                    actions={[
                        <Button
                            type="link"
                            icon={<EyeOutlined />}
                            onClick={() => handleViewHistoryDetail(item)}
                        >
                            {I18nUtils.getText('viewDetail')}
                        </Button>
                    ]}
                >
                    <List.Item.Meta
                        title={
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <span>{item.comment}</span>
                                <Tag color="blue">{new Date(item.created_at).toLocaleString()}</Tag>
                            </div>
                        }
                        description={
                            <div>
                                <p>{I18nUtils.getText('projectId')}: {item.project_id}</p>
                                <p>{I18nUtils.getText('messages')}: {item.messages.length} {I18nUtils.getText('messagesCount')}</p>
                            </div>
                        }
                    />
                </List.Item>
            )}
        />
      </Modal> */}

      {/* 添加详情模态框 --> 在 git server 中被弃用 */}
      {/* <Modal
        title={I18nUtils.getText('historyDetail')}
        open={isHistoryDetailModalVisible}
        onCancel={() => setIsHistoryDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {currentHistoryDetail && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div>
              <h4>{I18nUtils.getText('basicInfo')}</h4>
              <p>{I18nUtils.getText('historyId')}: {currentHistoryDetail.id}</p>
              <p>{I18nUtils.getText('projectId')}: {currentHistoryDetail.project_id}</p>
              <p>{I18nUtils.getText('comment')}: {currentHistoryDetail.comment}</p>
              <p>{I18nUtils.getText('createTime')}: {new Date(currentHistoryDetail.created_at).toLocaleString()}</p>
            </div>
            
            <div>
              <h4>{I18nUtils.getText('scriptInfo')}</h4>
              <div className="code-block">
                <h5>{I18nUtils.getText('codeScript')}</h5>
                <pre className="code-content python">
                  {currentHistoryDetail.code_script}
                </pre>
              </div>
              <div className="code-block">
                <h5>{I18nUtils.getText('NLScript')}</h5>
                <pre className="code-content markdown">
                  {currentHistoryDetail.nl_script}
                </pre>
              </div>
            </div>

            <div>
              <h4>{I18nUtils.getText('messageHistory')}</h4>
              <List
                dataSource={currentHistoryDetail.messages}
                renderItem={(message: any) => (
                  <List.Item>
                    <List.Item.Meta
                      title={
                        I18nUtils.getText(message.sender || 'system')
                      }
                      description={message.content}
                    />
                    {message.isComplete && <Tag color="green">{I18nUtils.getText('complete')}</Tag>}
                    {message.isDemoing && <Tag color="processing">{I18nUtils.getText('demoing')}</Tag>}
                    {message.isError && <Tag color="error">{I18nUtils.getText('error')}</Tag>}
                    {message.isLoading && <Tag color="warning">{I18nUtils.getText('loading')}</Tag>}
                    {message.isQuestion && <Tag color="blue">{I18nUtils.getText('question')}</Tag>}
                  </List.Item>
                )}
              />
            </div>
          </div>
        )}
      </Modal> */}

      {/* 添加创建项目模态框 */}
      <Modal
        title={I18nUtils.getText('createNewProjectModalTitle')}
        open={isCreateProjectModalVisible}
        onOk={handleCreateProject}
        onCancel={() => {
          setIsCreateProjectModalVisible(false);
          setProjectName('');
          setProjectDescription('');
        }}
        className="ruyi-space-modal"
      >
        <div className="create-project-form">
          <Input
            placeholder={I18nUtils.getText('projectNamePlaceholder')}
            value={projectName}
            onChange={e => setProjectName(e.target.value)}
            required
          />
          <Input.TextArea
            placeholder={I18nUtils.getText('projectDescriptionPlaceholder')}
            value={projectDescription}
            onChange={e => setProjectDescription(e.target.value)}
            rows={3}
            showCount
            maxLength={500}
          />
        </div>
      </Modal>

      {/* 模型设置模态框（已移入设置面板） */}
      {/* <Modal
        title={I18nUtils.getText('modelSettings')}
        open={isModelSettingsVisible}
        onOk={handleModelSettingsSave}
        onCancel={() => setIsModelSettingsVisible(false)}
      >
        <div className="settings-form">
          <div className="settings-form-item">
            <label>{I18nUtils.getText('modelType')}</label>
            <AntSelect
              value={modelSettings.modelType}
              onChange={(value) => setModelSettings(prev => ({ ...prev, modelType: value }))}
              style={{ width: '100%' }}
            >
              <AntSelect.Option value="gpt-4">GPT-4</AntSelect.Option>
              <AntSelect.Option value="gpt-4-turbo">GPT-4 Turbo</AntSelect.Option>
              <AntSelect.Option value="claude-3-sonnet">Claude 3.5 Sonnet</AntSelect.Option>
            </AntSelect>
          </div>
          <div className="settings-form-item">
            <label>{I18nUtils.getText('apiKey')}</label>
            <Input.Password
              value={modelSettings.apiKey}
              onChange={(e) => setModelSettings(prev => ({ ...prev, apiKey: e.target.value }))}
            />
          </div>
        </div>
      </Modal> */}

      {/* 端口设置模态框（已移入设置面板） */}
      {/* <Modal
        title={I18nUtils.getText('portSettings')}
        open={isPortSettingsVisible}
        onOk={handlePortSettingsSave}
        onCancel={() => setIsPortSettingsVisible(false)}
      >
        <div className="settings-form">
          <div className="settings-form-item">
            <label>{I18nUtils.getText('backendPort')}</label>
            <Input
              value={portSettings.flask_port}
              onChange={(e) => handlePortChange(e, 'flask_port')}
              type="text"
              maxLength={5}
            />
          </div>
          <div className="settings-form-item">
            <label>{I18nUtils.getText('screencastPort')}</label>
            <Input
              value={portSettings.scrcpy_port}
              onChange={(e) => handlePortChange(e, 'scrcpy_port')}
              type="text"
              maxLength={5}
            />
          </div>
          <div className="settings-form-item">
            <label>{I18nUtils.getText('taskPort')}</label>
            <Input
              value={portSettings.device_port}
              onChange={(e) => handlePortChange(e, 'device_port')}
              type="text"
              maxLength={5}
            />
          </div>
        </div>
      </Modal> */}

      {/* 添加设置面板模态框 */}
      <Modal
        title={I18nUtils.getText('settings')}
        open={isSettingsPanelVisible}
        onCancel={() => setIsSettingsPanelVisible(false)}
        footer={null}
        width={800}
      >
        <SettingsPanel
          modelSettings={modelSettings}
          portSettings={portSettings}
          browserSettings={browserSettings}
          language={language}
          appLanguage={appLanguage}
          onModelSettingsChange={setModelSettings}
          onPortSettingsChange={setPortSettings}
          onBrowserSettingsChange={setBrowserSettings}
          onLanguageChange={handleLanguageChange}
          onAppLanguageChange={handleAppLanguageChange}
          onPortChange={handlePortChange}
                      onBrowserHomepageChange={handleBrowserHomepageChange}
            onBrowserSearchEngineChange={handleBrowserSearchEngineChange}
            onPortSettingsSave={handlePortSettingsSave}
            onModelSettingsSave={handleModelSettingsSave}
            onBrowserSettingsSave={handleBrowserSettingsSave}
        />
      </Modal>

      {/* 添加 AnnotationManagement 组件 */}
      <AnnotationManagement
        visible={isAnnotationManagementVisible}
        onClose={() => setIsAnnotationManagementVisible(false)}
      />

      {/* 文件搜索结果覆盖层 */}
      <FileSearchOverlay
        visible={searchOverlayVisible}
        searchQuery={searchValue}
        onClose={() => setSearchOverlayVisible(false)}
        onFileSelect={handleFileSelect}
        position={searchPosition}
      />

      {/* 添加报告问题模态框 */}
      <Modal
        title={I18nUtils.getText('reportIssueTitle')}
        open={isReportIssueModalVisible}
        onOk={handleReportIssue}
        onCancel={() => {
          setIsReportIssueModalVisible(false);
          setIssueForm({
            category: 'idea',
            content: '',
            image: null
          });
        }}
        okText={I18nUtils.getText('reportIssueSubmit')}
        cancelText={I18nUtils.getText('reportIssueCancel')}
        width={820}
        className="report-issue-modal"
      >
        <div className="report-issue-content">
          {/* 问题类型选择 */}
          <div className="report-issue-section">
            <h4 className="report-issue-section-title">{I18nUtils.getText('reportIssueCategory')}</h4>
            <div className="report-issue-category-grid">
              <div 
                className={`report-issue-category-card ${issueForm.category === 'idea' ? 'active' : ''}`}
                onClick={() => setIssueForm(prev => ({ ...prev, category: 'idea' }))}
              >
                <div className="category-icon idea-icon">💡</div>
                <div className="category-content">
                  <div className="category-title">{I18nUtils.getText('reportIssueCategoryIdea')}</div>
                  <div className="category-description">{I18nUtils.getText('reportIssueCategoryIdeaTip')}</div>
                </div>
              </div>
              
              <div 
                className={`report-issue-category-card ${issueForm.category === 'small_bug' ? 'active' : ''}`}
                onClick={() => setIssueForm(prev => ({ ...prev, category: 'small_bug' }))}
              >
                <div className="category-icon bug-icon">🐛</div>
                <div className="category-content">
                  <div className="category-title">{I18nUtils.getText('reportIssueCategorySmallBug')}</div>
                  <div className="category-description">{I18nUtils.getText('reportIssueCategorySmallBugTip')}</div>
                </div>
              </div>
              
              <div 
                className={`report-issue-category-card ${issueForm.category === 'urgent_bug' ? 'active' : ''}`}
                onClick={() => setIssueForm(prev => ({ ...prev, category: 'urgent_bug' }))}
              >
                <div className="category-icon urgent-icon">🚨</div>
                <div className="category-content">
                  <div className="category-title">{I18nUtils.getText('reportIssueCategoryUrgentBug')}</div>
                  <div className="category-description">{I18nUtils.getText('reportIssueCategoryUrgentBugTip')}</div>
                </div>
              </div>
            </div>
          </div>
          
          {/* 问题描述 */}
          <div className="report-issue-section">
            <h4 className="report-issue-section-title">{I18nUtils.getText('reportIssueContent')}</h4>
            <div className="report-issue-textarea-wrapper">
              <Input.TextArea
                value={issueForm.content}
                onChange={e => setIssueForm(prev => ({ ...prev, content: e.target.value }))}
                placeholder={I18nUtils.getText('reportIssueContentPlaceholder')}
                rows={6}
                className="report-issue-textarea"
              />
            </div>
          </div>

          {/* 截图上传 */}
          <div className="report-issue-section">
            <h4 className="report-issue-section-title">{I18nUtils.getText('reportIssueImage')}</h4>
            <div className="report-issue-upload-wrapper">
              <Upload.Dragger
                accept="image/*"
                beforeUpload={handleImageUpload}
                showUploadList={false}
                className="report-issue-upload"
              >
                {issueForm.image ? (
                  <div className="uploaded-image-container">
                    <img src={issueForm.image} alt="Issue" className="uploaded-image" />
                    <div className="image-overlay">
                      <span className="overlay-text">点击更换图片</span>
                    </div>
                  </div>
                ) : (
                  <div className="upload-placeholder">
                    <div className="upload-icon">
                      <Inbox size={48} />
                    </div>
                    <div className="upload-text">
                      <div className="upload-primary-text">{I18nUtils.getText('reportIssueImageTip')}</div>
                      <div className="upload-secondary-text">支持 PNG、JPG、GIF 格式，文件大小不超过 10MB</div>
                    </div>
                  </div>
                )}
              </Upload.Dragger>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

// 如果需要在其他地方使用这些方法，可以导出一个工具类
export class NavBar {
  private static container: HTMLElement | null = null;

  static mount() {
    this.container = document.createElement('div');
    document.body.insertBefore(this.container, document.body.firstChild);
    return this.container;
  }

  static updateTitle(newTitle: string) {
    if (this.container) {
      // 使用 React 重新渲染组件
      const root = createRoot(this.container);
      root.render(<NavBarComponent title={newTitle} />);
    }
  }

  static getContainer() {
    return this.container;
  }
}