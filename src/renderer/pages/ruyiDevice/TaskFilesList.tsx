import React, { useState, useEffect, useRef } from 'react';
import { List, Button, Modal, Input, message, Popconfirm, Checkbox, Dropdown, Tag, Tree, Spin } from 'antd';
import { PlusOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined, DownOutlined, EditOutlined } from '@ant-design/icons';
import { Folder, FolderOpen, File, FileCode, Database, Book, Pin, Loader2, CheckCircle, XCircle, UploadCloud, Trash2, Eye, Pencil, FilePlus, FolderPlus } from 'lucide-react';
import { I18nUtils } from './languageSupport/i18nUtils';
import { ChatInterface } from './ChatInterface';
import { CodeEditorUtils } from './CodeEditor';
import { repoApi, userApi } from '../../../services/api';
import '../styles/TaskFilesList.css';
import { TablePanel, TableData } from './TablePanel';
import { useBrowserViewControl } from '../../hooks/useBrowserViewControl';

interface TaskFile {
  name: string;
  path: string;
  size?: number;
  modified: string;
  created: string;
  isPinned: boolean;
  pinnedTime: string | null;
  fileType?: 'knowledge' | 'task' | 'data' | 'folder';
  isDirectory?: boolean;
  children?: TaskFile[];
  depth?: number;
  content?: string;
}

interface TaskFilesListProps {
  onFileSelect?: (fileName: string) => void;
}

// 工具函数：移除脚本中的标号
const removeLabelFromScript = (labeledScript: string): string => {
  if (!labeledScript) return '';
  
  return labeledScript
    .split('\n')
    .map(line => {
      // 移除行首的 [数字] 标号，但保留标号后的缩进空格 (NL script格式)
      const lineWithoutPrefixLabel = line.replace(/^\[\d+\] ?/, '');
      
      // 移除行尾的注释标号 # [数字] (Code script格式)
      const lineWithoutSuffixLabel = lineWithoutPrefixLabel.replace(/\s*#\s*\[\d+\]\s*$/, '');
      
      return lineWithoutSuffixLabel;
    })
    .join('\n');
};

// 文件类型图标映射
const getFileIcon = (fileType?: string, isDirectory?: boolean, isExpanded?: boolean) => {
  if (isDirectory) {
    return isExpanded ? 
      <FolderOpen color="#1890ff" size={18} /> : 
      <Folder color="#1890ff" size={18} />;
  }
  
  switch (fileType) {
    case 'knowledge':
      return <Book color="#1890ff" size={18} />;
    case 'task':
      return <FileCode color="#52c41a" size={18} />;
    case 'data':
      return <Database color="#fa8c16" size={18} />;
    default:
      return <File color="#8c8c8c" size={18} />;
  }
};

// 获取文件类型标签
const getFileTypeTag = (fileType?: string, isDirectory?: boolean) => {
  if (isDirectory) {
    return <Tag color="blue">{I18nUtils.getText('folder')}</Tag>;
  }
  
  switch (fileType) {
    case 'knowledge':
      return <Tag color="blue">{I18nUtils.getText('knowledgeFile')}</Tag>;
    case 'task':
      return <Tag color="green">{I18nUtils.getText('taskFile')}</Tag>;
    case 'data':
      return <Tag color="orange">{I18nUtils.getText('dataFile')}</Tag>;
    default:
      return null;
  }
};

// 构建Tree组件需要的数据结构
const buildTreeData = (files: TaskFile[], selectedPath: string, expandedKeys: string[], autoSaveStatus: string, currentEditingFile: string): any[] => {
  return files.map((file) => {
    const isSelected = file.path === selectedPath;
    const isExpanded = expandedKeys.includes(file.path);
    const isCurrentEditingFile = file.path === currentEditingFile;
    
    // 获取自动保存状态指示器 - 始终显示，但只有当前编辑文件有动画
    const getAutoSaveIndicator = () => {
      if (!isCurrentEditingFile) {
        return (
          <div className="auto-save-indicator completed inactive" title="已同步">
            <CheckCircle color="#52c41a" size={14} />
          </div>
        );
      }
      switch (autoSaveStatus) {
        case 'saving':
          return (
            <div className="auto-save-indicator saving active" title={I18nUtils.getText('autoSaveInProgress')}>
              <Loader2 color="#d46b08" size={14} className="spin" />
            </div>
          );
        case 'syncing':
          return (
            <div className="auto-save-indicator syncing active" title={I18nUtils.getText('autoSyncInProgress')}>
              <UploadCloud color="#1890ff" size={14} />
            </div>
          );
        case 'completed':
          return (
            <div className="auto-save-indicator completed active" title={I18nUtils.getText('autoSyncCompleted')}>
              <CheckCircle color="#52c41a" size={14} />
            </div>
          );
        case 'error':
          return (
            <div className="auto-save-indicator error active" title={I18nUtils.getText('autoSyncFailed')}>
              <XCircle color="#ff4d4f" size={14} />
            </div>
          );
        default:
          return (
            <div className="auto-save-indicator completed active" title="已同步">
              <CheckCircle color="#52c41a" size={14} />
            </div>
          );
      }
    };
    
    // 获取文件显示名称并处理截断
    const fullDisplayName = file.isDirectory ? file.name : getFileNameWithoutExtension(file.name, file.fileType, file.content);
    const { displayName, isTruncated } = truncateFileName(fullDisplayName);

    const title = (
      <div className={`task-file-tree-item ${isSelected ? 'selected' : ''} ${isCurrentEditingFile ? 'editing' : ''}`}>
        <div className="task-file-info">
          <span className="task-file-icon">
            {getFileIcon(file.fileType, file.isDirectory, isExpanded)}
          </span>
          <span 
            className="task-file-name" 
            title={isTruncated ? fullDisplayName : undefined}
          >
            {displayName}
          </span>
          {getFileTypeTag(file.fileType, file.isDirectory)}
          {file.isPinned && (
            <Pin className="task-file-pinned-icon" color="#fa8c16" size={14} />
          )}
          {getAutoSaveIndicator()}
        </div>
      </div>
    );

    const treeNode: any = {
      title,
      key: file.path,
      isLeaf: !file.isDirectory,
      isDirectory: file.isDirectory,
      fileData: file,
    };

    if (file.children && file.children.length > 0) {
      treeNode.children = buildTreeData(file.children, selectedPath, expandedKeys, autoSaveStatus, currentEditingFile);
    }

    return treeNode;
  });
};

// 移除文件后缀，只显示文件名，特殊处理 Knowledge.json
// 对于任务文件，显示任务名称而不是文件名
const getFileNameWithoutExtension = (fileName: string, fileType?: string, fileContent?: string) => {
  if (fileName === 'Knowledge.json') {
    return I18nUtils.getText('knowledgeFileDisplayName');
  }
  
  // 如果是任务文件且有内容，尝试解析并显示任务名称
  if (fileType === 'task' && fileContent) {
    try {
      const taskData = JSON.parse(fileContent);
      if (taskData.task_name) {
        return taskData.task_name;
      }
    } catch (error) {
      console.warn('Failed to parse task file for display name:', error);
    }
  }
  
  // 默认行为：移除文件后缀
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === 0) {
    return fileName;
  }
  return fileName.substring(0, lastDotIndex);
};

// 智能截断文件名，类似VSCode
const truncateFileName = (fullName: string, maxLength: number = 30): { displayName: string; isTruncated: boolean } => {
  if (fullName.length <= maxLength) {
    return { displayName: fullName, isTruncated: false };
  }
  
  // 中间截断，保留开头和结尾
  const startLength = Math.ceil(maxLength * 0.6);
  const endLength = Math.floor(maxLength * 0.3);
  const start = fullName.substring(0, startLength);
  const end = fullName.substring(fullName.length - endLength);
  
  return { 
    displayName: `${start}...${end}`, 
    isTruncated: true 
  };
};

export const TaskFilesList: React.FC<TaskFilesListProps> = ({ onFileSelect }) => {
  const [taskFiles, setTaskFiles] = useState<TaskFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedFileName, setSelectedFileName] = useState<string>('');
  const [currentTaskName, setCurrentTaskName] = useState<string>('');
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [treeMode, setTreeMode] = useState<boolean>(true);
  const [spaceName, setSpaceName] = useState<string>('');
  
  // 新建任务相关状态
  const [isCreateTaskModalVisible, setIsCreateTaskModalVisible] = useState(false);
  const [taskShortName, setTaskShortName] = useState('');
  const [taskDetailedDescription, setTaskDetailedDescription] = useState('');
  const [useAiForTaskName, setUseAiForTaskName] = useState(false);
  
  // 文件导入相关状态
  const [importingFile, setImportingFile] = useState(false);
  
  // 目录管理相关状态
  const [isCreateFolderModalVisible, setIsCreateFolderModalVisible] = useState(false);
  const [folderName, setFolderName] = useState('');
  const [parentFolderPath, setParentFolderPath] = useState('');
  const [creatingFolder, setCreatingFolder] = useState(false);
  
  // 重命名相关状态
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [renameItemPath, setRenameItemPath] = useState('');
  const [newItemName, setNewItemName] = useState('');
  const [renamingItem, setRenamingItem] = useState(false);
  const [isRenamingTaskFile, setIsRenamingTaskFile] = useState(false);
  
  // 新建文件相关状态
  const [isCreateFileModalVisible, setIsCreateFileModalVisible] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [newFileContent, setNewFileContent] = useState('');
  const [newFileParentPath, setNewFileParentPath] = useState('');
  const [creatingFile, setCreatingFile] = useState(false);

  // 表格预览相关状态
  const [tablePanelState, setTablePanelState] = useState<{
    visible: boolean;
    data: TableData | null;
  }>({
    visible: false,
    data: null,
  });

  // 用户信息状态
  const [userInfo, setUserInfo] = useState({
    username: '',
    displayName: '',
    email: '',
    githubId: '',
    wechatId: ''
  });

  // 自动保存状态
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'syncing' | 'completed' | 'error'>('idle');
  const [currentEditingFile, setCurrentEditingFile] = useState<string>('');

  const suppressNextSelect = useRef(false); // 新增：用于抑制下次 select
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null); // 新增：用于存储定时器引用

  // 使用浏览器视图控制钩子来隐藏/显示 browser view 当创建任务弹窗出现时
  useBrowserViewControl(isCreateTaskModalVisible);

  // 获取用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const response = await userApi.getUserInfo();
        const userInfo = response.data;
        setUserInfo({
          username: userInfo.username,
          displayName: userInfo.display_name,
          email: userInfo.email,
          githubId: userInfo.github_id,
          wechatId: userInfo.wechat_id
        });
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    };
    fetchUserInfo();
  }, []);

  // 初始化和设置回调函数
  useEffect(() => {
    // 设置项目名称变化的回调
    ChatInterface.setOnProjectNameChangeCallback(handleProjectNameChange);
    
    // 设置项目创建的回调
    ChatInterface.setOnProjectCreatedCallback(handleProjectCreated);
    
    // 初始化当前项目名称
    setSpaceName(ChatInterface.projectName || '');
    
    // 初始化文件列表
    fetchTaskFiles();
    
    // 监听文件创建事件
    const handleProjectFileCreated = ({ taskName, fileName }: { taskName: string; fileName: string }) => {
      console.log('[TaskFilesList] 文件创建事件:', taskName, fileName);
      if (taskName === currentTaskName || taskName === spaceName || taskName === ChatInterface.projectName) {
        console.log('[TaskFilesList] 文件创建后刷新文件列表');
        fetchTaskFiles(true, taskName);
      }
    };
    
    window.electronAPI.onProjectFileCreated(handleProjectFileCreated);
    
    // 监听任务详情更新事件
    const handleTaskDetailsUpdated = (event: CustomEvent) => {
      const { projectName, fileName } = event.detail;
      console.log('[TaskFilesList] 任务详情更新事件:', projectName, fileName);
      if (projectName === currentTaskName || projectName === spaceName || projectName === ChatInterface.projectName) {
        console.log('[TaskFilesList] 任务详情更新后刷新文件列表');
        fetchTaskFiles(true, projectName);
      }
    };
    
    window.addEventListener('taskDetailsUpdated', handleTaskDetailsUpdated as EventListener);
    
    // 监听自动保存状态变化
    CodeEditorUtils.setAutoSaveStatusCallback((status) => {
      setAutoSaveStatus(status);
    });

    // 监听当前编辑文件变化
    const handleEditingFileChange = (fileName: string) => {
      setCurrentEditingFile(fileName);
      setSelectedFileName(fileName);
    };
    ChatInterface.setOnEditingFileChangeCallback(handleEditingFileChange);
    
    // 监听从搜索结果选择文件的事件
    const handleFileSelectedFromSearch = (event: CustomEvent) => {
      const { fileName } = event.detail;
      console.log('[TaskFilesList] 收到从搜索选择文件事件:', fileName);
      setSelectedFileName(fileName);
    };
    
    window.addEventListener('fileSelectedFromSearch', handleFileSelectedFromSearch as EventListener);
    
    // 监听用户切换事件
    const handleUserSwitched = (data: { previousUser: string; newUser: string }) => {
      console.log(`[TaskFilesList] 用户切换事件: ${data.previousUser} -> ${data.newUser}`);
      
      // 重置文件列表相关状态
      setCurrentTaskName('');
      setSpaceName('');
      setTaskFiles([]);
      setCurrentEditingFile('');
      setAutoSaveStatus('idle');
      
      console.log('[TaskFilesList] 文件列表状态重置完成');
    };
    
    // 注册用户切换事件监听器
    window.electronAPI.onUserSwitched(handleUserSwitched);
    
    // 设置自动刷新文件列表的定时器 (每0.5秒刷新一次)
    refreshIntervalRef.current = setInterval(() => {
      const targetProjectName = ChatInterface.projectName || currentTaskName;
      if (targetProjectName) {
        console.log('[TaskFilesList] 自动刷新文件列表');
        fetchTaskFiles(true, targetProjectName);
      }
    }, 500);
    
    // 组件卸载时清理
    return () => {
      ChatInterface.setOnProjectNameChangeCallback(undefined);
      ChatInterface.setOnProjectCreatedCallback(undefined);
      ChatInterface.setOnEditingFileChangeCallback(undefined);
      CodeEditorUtils.setAutoSaveStatusCallback(undefined);
      window.electronAPI.offProjectFileCreated();
      window.electronAPI.offUserSwitched();
      window.removeEventListener('taskDetailsUpdated', handleTaskDetailsUpdated as EventListener);
      window.removeEventListener('fileSelectedFromSearch', handleFileSelectedFromSearch as EventListener);
      
      // 清除自动刷新定时器
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [currentTaskName, spaceName]);

  // 监听视图模式变化
  useEffect(() => {
    if (spaceName) {
      console.log(`[TaskFilesList] 视图模式变更为: ${treeMode ? '树形' : '列表'}, 刷新文件列表`);
      fetchTaskFiles(true, spaceName);
    }
  }, [treeMode]);

  // 处理项目名称变化
  const handleProjectNameChange = (newProjectName: string) => {
    console.log(`[TaskFilesList] 项目名称变更为: ${newProjectName}`);
    setCurrentTaskName(newProjectName);
    setSpaceName(newProjectName);
    fetchTaskFiles(true, newProjectName);
  };

  // 处理项目创建
  const handleProjectCreated = (projectName: string) => {
    console.log(`[TaskFilesList] 新项目创建: ${projectName}`);
    setCurrentTaskName(projectName);
    setSpaceName(projectName);
    fetchTaskFiles(true, projectName);
  };

  // 获取当前任务的文件列表/树
  const fetchTaskFiles = async (forceRefresh = false, projectName?: string) => {
    const targetProjectName = projectName || ChatInterface.projectName || currentTaskName;
    
    if (!targetProjectName) {
      console.log('[TaskFilesList] targetProjectName 为空，清空文件列表');
      setTaskFiles([]);
      return;
    }

    // console.log(`[TaskFilesList] 开始获取文件${treeMode ? '树' : '列表'}，项目名称: ${targetProjectName}, 强制刷新: ${forceRefresh}`);
    setLoading(true);
    try {
      if (treeMode) {
        // 使用树形结构API
        const result = await window.electronAPI.getTaskFileTree(targetProjectName);
        if (result.success) {
          // 为任务文件加载内容以获取任务名称
          const filesWithContent = await loadTaskFileContents(result.tree || [], targetProjectName);
          setTaskFiles(filesWithContent);
          // console.log(`[TaskFilesList] 树形结构获取成功，根节点数量: ${result.tree?.length || 0}`);
        } else {
          console.error('[TaskFilesList] 获取文件树失败:', result.message);
          message.error(I18nUtils.getText('getTaskFilesFailed'));
          setTaskFiles([]);
        }
      } else {
        // 使用原有的平铺列表API
        let files;
        if (forceRefresh) {
          files = await window.electronAPI.refreshTaskFiles(targetProjectName);
        } else {
          files = await window.electronAPI.getTaskFiles(targetProjectName);
        }
        // 为任务文件加载内容以获取任务名称
        const filesWithContent = await loadTaskFileContents(files, targetProjectName);
        setTaskFiles(filesWithContent);
        // console.log(`[TaskFilesList] 平铺列表获取成功，文件数量: ${files.length}`);
      }
    } catch (error) {
      console.error('[TaskFilesList] 获取任务文件列表失败:', error);
      message.error(I18nUtils.getText('getTaskFilesFailed'));
      setTaskFiles([]);
    } finally {
      setLoading(false);
    }
  };

  // 为任务文件加载内容以获取任务名称
  const loadTaskFileContents = async (files: TaskFile[], projectName: string): Promise<TaskFile[]> => {
    const filesWithContent = [...files];
    
    for (let i = 0; i < filesWithContent.length; i++) {
      const file = filesWithContent[i];
      
      // 如果是任务文件，加载其内容以获取任务名称
      if (file.fileType === 'task' && !file.isDirectory) {
        try {
          const result = await window.electronAPI.readTaskFile({
            taskName: projectName,
            fileName: file.name
          });
          
          if (result.success && result.content) {
            filesWithContent[i] = {
              ...file,
              content: result.content
            };
          }
        } catch (error) {
          console.warn(`[TaskFilesList] Failed to load content for task file ${file.name}:`, error);
        }
      }
      
      // 递归处理子文件
      if (file.children && file.children.length > 0) {
        filesWithContent[i] = {
          ...file,
          children: await loadTaskFileContents(file.children, projectName)
        };
      }
    }
    
    return filesWithContent;
  };

  // 创建新任务
  const handleCreateTask = async () => {
    if (!taskDetailedDescription.trim()) {
      message.error(I18nUtils.getText('taskDetailedDescriptionRequired'));
      return;
    }

    const projectName = ChatInterface.projectName || currentTaskName;
    if (!projectName) {
      message.error(I18nUtils.getText('noProjectSelected'));
      return;
    }

    try {
      // 立即关闭模态框并清空表单
      setIsCreateTaskModalVisible(false);
      const description = taskDetailedDescription;
      const shortName = taskShortName.trim();
      const useAi = useAiForTaskName;
      
      setTaskShortName('');
      setTaskDetailedDescription('');
      setUseAiForTaskName(false);

      // 清空聊天历史消息
      ChatInterface.setAllMessages([]);

      // 1. 先创建一个空的任务文件
      console.log('[TaskFilesList] 开始创建空任务文件...');
      
      // 使用新的 createEmptyTaskFile API
      const createResult = await window.electronAPI.createEmptyTaskFile({
        taskName: projectName,
        taskShortName: shortName,
        description: description,
        projectName: projectName
      });

      if (!createResult.success) {
        throw new Error('创建空任务文件失败: ' + createResult.message);
      }

      const taskFileName = createResult.fileName;
      if (!taskFileName) {
        throw new Error('创建空任务文件成功但未返回文件名');
      }

      console.log('[TaskFilesList] 空任务文件创建成功:', taskFileName);

      // 2. 加载这个任务文件到编辑器
      console.log('[TaskFilesList] 加载任务文件到编辑器...');
      
      // 如果当前在查看知识库，先退出知识库视图
      if (CodeEditorUtils.viewAppKnowledge || CodeEditorUtils.viewProjectKnowledge) {
        await CodeEditorUtils.hideKnowledgeViewOnly();
      }

      // 设置当前编辑的文件和任务信息
      ChatInterface.setCurrentEditingFile(taskFileName);
      ChatInterface.setTask(shortName || '新任务');
      ChatInterface.setTaskDescription(description);
      
      // 设置空的脚本内容到编辑器
      CodeEditorUtils.setNLScript('');
      CodeEditorUtils.setCodeScript('');
      CodeEditorUtils.labeledNLScript = '';
      CodeEditorUtils.labeledCodeScript = '';
      CodeEditorUtils.resetVersionFlags();

      // 刷新文件列表以显示新创建的文件
      await fetchTaskFiles(true, projectName);

      console.log('[TaskFilesList] 任务文件加载完成');

      // 3. 继续原有的任务创建流程
      // 构建任务创建消息
      let taskMessage = description;
      if (!useAi && shortName) {
        taskMessage = `任务名称：${shortName}\n任务描述：${description}`;
      }

      // 将任务详细描述作为消息添加到聊天界面，触发 generateWorkflow
      console.log('[TaskFilesList] 添加任务创建消息:', taskMessage);
      ChatInterface.addChatMessage('user', taskMessage, false);
      
      // 设置任务创建状态
      ChatInterface.setIsCreatingTask(true);
      ChatInterface.setTaskCreationInfo({
        useAiForTaskName: useAi,
        taskShortName: shortName,
        projectName: projectName,
        taskFileName: taskFileName // 添加任务文件名到创建信息中
      });
      
      // 触发任务创建处理逻辑
      await new Promise(resolve => setTimeout(resolve, 100)); // 等待消息状态更新
      await ChatInterface.processTaskCreationMessage(taskMessage);
      
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error(I18nUtils.getText('fileTaskCreateFailed') + ': ' + ((error as Error).message || '未知错误'));
    }
  };

  // 创建新目录
  const handleCreateFolder = async () => {
    if (!folderName.trim()) {
      message.error(I18nUtils.getText('folderNameRequired'));
      return;
    }

    const projectName = ChatInterface.projectName || currentTaskName;
    if (!projectName) {
      message.error(I18nUtils.getText('noProjectSelected'));
      return;
    }

    try {
      setCreatingFolder(true);

      const createResult = await window.electronAPI.createTaskFolder({
        taskName: projectName,
        folderName: folderName.trim(),
        parentPath: parentFolderPath
      });

      if (createResult.success) {
        message.success(I18nUtils.getText('createFolderSuccess'));
        setIsCreateFolderModalVisible(false);
        setFolderName('');
        setParentFolderPath('');
        await fetchTaskFiles(true, projectName);
      } else {
        message.error(I18nUtils.getText('createFolderFailed') + ': ' + createResult.message);
      }
    } catch (error) {
      console.error('创建目录失败:', error);
      message.error(I18nUtils.getText('createFolderFailed'));
    } finally {
      setCreatingFolder(false);
    }
  };

  // 创建新文件
  const handleCreateFile = async () => {
    if (!newFileName.trim()) {
      message.error(I18nUtils.getText('fileNameRequired'));
      return;
    }

    const projectName = ChatInterface.projectName || currentTaskName;
    if (!projectName) {
      message.error(I18nUtils.getText('noProjectSelected'));
      return;
    }

    try {
      setCreatingFile(true);

      const createResult = await window.electronAPI.createTaskFile({
        taskName: projectName,
        fileName: newFileName.trim(),
        content: newFileContent,
        folderPath: newFileParentPath
      });

      if (createResult.success) {
        message.success(I18nUtils.getText('createFileSuccess'));
        setIsCreateFileModalVisible(false);
        setNewFileName('');
        setNewFileContent('');
        setNewFileParentPath('');
        await fetchTaskFiles(true, projectName);
      } else {
        message.error(I18nUtils.getText('createFileFailed') + ': ' + createResult.message);
      }
    } catch (error) {
      console.error('创建文件失败:', error);
      message.error(I18nUtils.getText('createFileFailed'));
    } finally {
      setCreatingFile(false);
    }
  };

  // 重命名文件/目录
  const handleRenameItem = async () => {
    if (!newItemName.trim()) {
      message.error(I18nUtils.getText('newItemNameRequired'));
      return;
    }

    const projectName = ChatInterface.projectName || currentTaskName;
    if (!projectName) {
      message.error(I18nUtils.getText('noProjectSelected'));
      return;
    }

    try {
      setRenamingItem(true);

      // 检查是否是任务文件
      const isTaskFile = renameItemPath.endsWith('.json') && renameItemPath !== 'Knowledge.json';
      
      if (isTaskFile) {
        // 对于任务文件，更新JSON文件中的task_name字段
        try {
          const readResult = await window.electronAPI.readTaskFile({
            taskName: projectName,
            fileName: renameItemPath
          });
          
          if (readResult.success && readResult.content) {
            const taskData = JSON.parse(readResult.content);
            
            // 更新task_name字段
            taskData.task_name = newItemName.trim();
            
            // 保存更新后的文件
            const updateResult = await window.electronAPI.updateTaskFile({
              taskName: projectName,
              fileName: renameItemPath,
              content: JSON.stringify(taskData, null, 2)
            });
            
            if (updateResult.success) {
              message.success(I18nUtils.getText('renameItemSuccess'));
              setIsRenameModalVisible(false);
              setRenameItemPath('');
              setNewItemName('');
              
              // 如果当前正在编辑这个文件，更新ChatInterface中的任务名称
              if (ChatInterface.currentEditingFile === renameItemPath) {
                ChatInterface.setTask(newItemName.trim());
              }
              
              await fetchTaskFiles(true, projectName);
            } else {
              message.error(I18nUtils.getText('renameItemFailed') + ': ' + updateResult.message);
            }
          } else {
            message.error(I18nUtils.getText('renameItemFailed') + ': 无法读取任务文件');
          }
        } catch (error) {
          console.error('更新任务名称失败:', error);
          message.error(I18nUtils.getText('renameItemFailed') + ': 无法解析任务文件');
        }
      } else {
        // 对于非任务文件，使用原有的重命名逻辑
        const renameResult = await window.electronAPI.renameTaskItem({
          taskName: projectName,
          oldPath: renameItemPath,
          newName: newItemName.trim()
        });

        if (renameResult.success) {
          message.success(I18nUtils.getText('renameItemSuccess'));
          setIsRenameModalVisible(false);
          setRenameItemPath('');
          setNewItemName('');
          await fetchTaskFiles(true, projectName);
        } else {
          message.error(I18nUtils.getText('renameItemFailed') + ': ' + renameResult.message);
        }
      }
    } catch (error) {
      console.error('重命名失败:', error);
      message.error(I18nUtils.getText('renameItemFailed'));
    } finally {
      setRenamingItem(false);
    }
  };

  const showDataFileInTable = async (fileName: string) => {
    const hideLoading = message.loading(I18nUtils.getText('loadingAndParsingFile'), 0);

    await new Promise(resolve => setTimeout(resolve, 200));
    
    try {
      const result = await window.electronAPI.readAndParseDataFile({
        taskName: ChatInterface.projectName || currentTaskName,
        fileName,
      });

      if (result.success && result.data) {
        setTablePanelState({ visible: true, data: result.data });
        // message.success('数据文件已在表格中打开预览');
      } else {
        message.error('预览数据文件失败: ' + (result.message || '未知错误'));
      }
    } catch (error) {
      message.error('预览数据文件失败: ' + ((error as Error).message || '未知错误'));
      console.error('预览数据文件失败:', error);
    } finally {
      hideLoading();
    }
  };

  // 判断是否为编辑器文件（白名单）
  const isEditorFile = (fileName: string, fileContent: string) => {
    if (fileName === 'Knowledge.json') return true;
    if (fileName.endsWith('.json')) {
      try {
        const data = JSON.parse(fileContent);
        return 'task_name' in data && 'NL_script' in data && 'code_script' in data;
      } catch { return false; }
    }
    return false;
  };

  // 处理文件点击
  const handleFileClick = async (fileName: string) => {
    try {
      const result = await window.electronAPI.readTaskFile({
        taskName: ChatInterface.projectName || currentTaskName,
        fileName: fileName
      });

      if (!result.success || result.content === undefined) {
        message.error(I18nUtils.getText('loadFileFailed') + ': ' + result.message);
        return;
      }

      if (isEditorFile(fileName, result.content)) {
        setSelectedFileName(fileName);
        // 编辑器文件逻辑
        if (fileName === 'Knowledge.json') {
          try {
            const knowledgeData = JSON.parse(result.content);
            
            // 处理新格式的 Knowledge.json
            if (knowledgeData.device_information !== undefined && knowledgeData.operation_guide !== undefined) {
              // 新格式：渲染为固定模板
              const knowledgeContent = `# 设备信息
${knowledgeData.device_information || ''}

# 操作指南
${knowledgeData.operation_guide || ''}`;
              ChatInterface.setCurrentEditingFile(fileName);
              CodeEditorUtils.setAppKnowledge(knowledgeContent);
              CodeEditorUtils.showProjectKnowledge(knowledgeContent);
              return;
            }
            // 向后兼容旧格式
            else if (knowledgeData.knowledge !== undefined) {
              const knowledgeContent = knowledgeData.knowledge;
              ChatInterface.setCurrentEditingFile(fileName);
              CodeEditorUtils.setAppKnowledge(knowledgeContent);
              CodeEditorUtils.showProjectKnowledge(knowledgeContent);
              return;
            }
          } catch (e) {
            console.warn('Knowledge.json 格式解析失败:', e);
          }
        }
        if (fileName.endsWith('.json') && fileName !== 'Knowledge.json') {
          try {
            const taskData = JSON.parse(result.content);
            if ('task_name' in taskData && 'NL_script' in taskData && 'code_script' in taskData) {
              if (CodeEditorUtils.viewAppKnowledge || CodeEditorUtils.viewProjectKnowledge) {
                await CodeEditorUtils.hideKnowledgeViewOnly();
              }
              setSelectedFileName(fileName);
              ChatInterface.setCurrentEditingFile(fileName);
              ChatInterface.setTask(taskData.task_name || '');
              ChatInterface.setTaskDescription(taskData.task_description || '');
              ChatInterface.setProjectName(taskData.project_name || taskData.task_name || '');
              
              // 确保加载该任务的聊天消息
              ChatInterface.loadChatMessagesForTask(taskData.project_name || taskData.task_name || '', fileName);
              // 设置脚本，优先使用带标号的版本
              const labeledNL = taskData.NL_script_labeled || '';
              const labeledCode = taskData.code_script_labeled || '';
              const NLScript = taskData.NL_script || '';
              const codeScript = taskData.code_script || '';
              
              // 读取版本标志位，默认为0（兼容旧文件）
              const NLVersion = taskData.NL_script_version || 0;
              const codeVersion = taskData.code_script_version || 0;
              
              if (labeledNL && labeledCode) {
                // 如果有带标号的版本，同时设置两个版本
                CodeEditorUtils.setLabeledNLScript(labeledNL, NLScript);
                CodeEditorUtils.setLabeledCodeScript(labeledCode, codeScript);
              } else {
                // 如果没有带标号的版本，只设置去标号的版本（兼容旧文件）
                CodeEditorUtils.setNLScript(NLScript);
                CodeEditorUtils.setCodeScript(codeScript);
              }
              
              // 设置版本标志位
              CodeEditorUtils.setVersionFlags(NLVersion, codeVersion);
              
              // 根据版本标志位决定显示哪个脚本模式
              // 如果 CodeScriptVersion 为 1，显示 Code Script；否则显示 NL Script
              const shouldShowCodeScript = codeVersion === 1;
              if (shouldShowCodeScript && CodeEditorUtils.scriptMode !== "Code") {
                CodeEditorUtils.scriptMode = "Code";
              } else if (!shouldShowCodeScript && CodeEditorUtils.scriptMode !== "NL") {
                CodeEditorUtils.scriptMode = "NL";
              }
              
              await new Promise(resolve => setTimeout(resolve, 100));
              setSelectedFileName(fileName);
              const context = (window as any).__codeEditorContext;
              if (context?.monacoEditor) {
                const currentScript = CodeEditorUtils.scriptMode === "NL" 
                  ? CodeEditorUtils.getNLScript() 
                  : CodeEditorUtils.getCodeScript();
                if (currentScript) {
                  context.monacoEditor.setValue(currentScript);
                }
              }
              return;
            }
          } catch (e) {
            console.warn('任务JSON文件格式解析失败:', e);
          }
        }
      }
      // 其它所有文件：只预览，不设置选中状态
      if (['csv', 'xlsx', 'xls'].includes(fileName.split('.').pop()?.toLowerCase() || '')) {
        await showDataFileInTable(fileName);
        return;
      }
      Modal.info({
        title: (
          <span>
            {getFileNameWithoutExtension(fileName)}
            <span style={{ color: '#999', fontSize: '12px', marginLeft: '8px' }}>
              ({fileName})
            </span>
          </span>
        ),
        content: (
          <div style={{ maxHeight: '400px', overflow: 'auto' }}>
            <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
              {result.content}
            </pre>
          </div>
        ),
        width: 800,
        okText: I18nUtils.getText('confirm'),
        onOk: () => {}
      });
      message.success(I18nUtils.getText('loadFileSuccess'));
    } catch (error) {
      console.error('文件点击处理失败:', error);
      message.error(I18nUtils.getText('loadFileFailed'));
    }
  };

  // 删除文件
  const handleDeleteFile = async (fileName: string) => {
    try {
      let repoToken = '';
      try {
        const tokenResponse = await repoApi.getRepoToken(ChatInterface.projectName || currentTaskName);
        if (tokenResponse && tokenResponse.token) {
          repoToken = tokenResponse.token;
        }
      } catch (error) {
        console.warn('无法获取repo token，文件删除后将不会自动同步:', error);
      }

      const result = await window.electronAPI.deleteTaskFile({
        taskName: ChatInterface.projectName || currentTaskName,
        fileName: fileName,
        repoToken: repoToken,
        userInfo
      });

      if (result.success) {
        message.success(I18nUtils.getText('deleteFileSuccess'));
        await fetchTaskFiles();
        
        if (selectedFileName === fileName) {
          setSelectedFileName('');
        }
      } else {
        message.error(I18nUtils.getText('deleteFileFailed') + ': ' + result.message);
      }
    } catch (error) {
      console.error('删除文件失败:', error);
      message.error(I18nUtils.getText('deleteFileFailed'));
    }
  };

  // 删除目录
  const handleDeleteFolder = async (folderPath: string) => {
    try {
      let repoToken = '';
      try {
        const tokenResponse = await repoApi.getRepoToken(ChatInterface.projectName || currentTaskName);
        if (tokenResponse && tokenResponse.token) {
          repoToken = tokenResponse.token;
        }
      } catch (error) {
        console.warn('无法获取repo token，目录删除后将不会自动同步:', error);
      }

      const result = await window.electronAPI.deleteTaskFolder({
        taskName: ChatInterface.projectName || currentTaskName,
        folderPath: folderPath,
        repoToken: repoToken,
        userInfo
      });

      if (result.success) {
        message.success(I18nUtils.getText('deleteFolderSuccess'));
        await fetchTaskFiles();
      } else {
        message.error(I18nUtils.getText('deleteFolderFailed') + ': ' + result.message);
      }
    } catch (error) {
      console.error('删除目录失败:', error);
      message.error(I18nUtils.getText('deleteFolderFailed'));
    }
  };

  // 切换文件置顶状态
  const handleTogglePinned = async (fileName: string, currentPinnedState: boolean) => {
    try {
      const result = await window.electronAPI.toggleFilePinned({
        taskName: ChatInterface.projectName || currentTaskName,
        fileName: fileName
      });

      if (result.success) {
        message.success(result.message);
        await fetchTaskFiles();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      console.error('切换置顶状态失败:', error);
      message.error('切换置顶状态失败');
    }
  };

  // 导入本地文件
  const handleImportLocalFile = async () => {
    const projectName = ChatInterface.projectName || currentTaskName;
    if (!projectName) {
      message.error(I18nUtils.getText('noProjectSelected'));
      return;
    }

    try {
      setImportingFile(true);
      message.loading({ content: I18nUtils.getText('importingFile'), key: 'importing' });

      let repoToken = '';
      try {
        const tokenResponse = await repoApi.getRepoToken(projectName);
        if (tokenResponse && tokenResponse.token) {
          repoToken = tokenResponse.token;
        }
      } catch (error) {
        console.warn('无法获取repo token，文件导入后将不会自动同步:', error);
      }

      const result = await window.electronAPI.selectAndImportFile({
        taskName: projectName,
        repoToken: repoToken,
        userInfo
      });

      message.destroy('importing');

      if (result.success) {
        message.success(I18nUtils.getText('importFileSuccess'));
        await fetchTaskFiles(true, projectName);
      } else {
        if (result.message === '用户取消了文件选择' || result.message === '用户取消了文件导入') {
          return;
        }
        message.error(I18nUtils.getText('importFileFailed') + ': ' + result.message);
      }
    } catch (error) {
      console.error('导入文件失败:', error);
      message.destroy('importing');
      message.error(I18nUtils.getText('importFileFailed'));
    } finally {
      setImportingFile(false);
    }
  };

  // 导入本地文件到指定目录
  const handleImportLocalFileToFolder = async (targetFolderPath: string) => {
    const projectName = ChatInterface.projectName || currentTaskName;
    if (!projectName) {
      message.error(I18nUtils.getText('noProjectSelected'));
      return;
    }

    try {
      setImportingFile(true);
      message.loading({ content: I18nUtils.getText('importingFile'), key: 'importing' });

      let repoToken = '';
      try {
        const tokenResponse = await repoApi.getRepoToken(projectName);
        if (tokenResponse && tokenResponse.token) {
          repoToken = tokenResponse.token;
        }
      } catch (error) {
        console.warn('无法获取repo token，文件导入后将不会自动同步:', error);
      }

      const result = await window.electronAPI.selectAndImportFile({
        taskName: projectName,
        targetFolderPath: targetFolderPath,
        repoToken: repoToken,
        userInfo
      });

      message.destroy('importing');

      if (result.success) {
        message.success(I18nUtils.getText('importFileSuccess'));
        await fetchTaskFiles(true, projectName);
      } else {
        if (result.message === '用户取消了文件选择' || result.message === '用户取消了文件导入') {
          return;
        }
        message.error(I18nUtils.getText('importFileFailed') + ': ' + result.message);
      }
    } catch (error) {
      console.error('导入文件失败:', error);
      message.destroy('importing');
      message.error(I18nUtils.getText('importFileFailed'));
    } finally {
      setImportingFile(false);
    }
  };

  // Tree组件的事件处理
  const handleTreeSelect = (selectedKeys: React.Key[], info: any) => {
    if (suppressNextSelect.current) {
      suppressNextSelect.current = false;
      return;
    }
    if (selectedKeys.length > 0) {
      const selectedNode = info.node;
      if (selectedNode.isDirectory) {
        // 点击目录时，切换展开/收起状态
        const key = selectedNode.key as string;
        const newExpandedKeys = expandedKeys.includes(key)
          ? expandedKeys.filter(k => k !== key)
          : [...expandedKeys, key];
        setExpandedKeys(newExpandedKeys);
      } else {
        handleFileClick(selectedNode.key as string);
      }
    }
  };

  const handleTreeExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys as string[]);
  };

  // 拖拽处理函数
  const handleTreeDrop = async (info: any) => {
    const dragNode = info.dragNode;
    const dropNode = info.node;

    // 只允许文件拖到文件夹
    if (!dragNode || !dropNode || dragNode.isDirectory || !dropNode.isDirectory) {
      message.warning(I18nUtils.getText('onlyMoveFileToFolder'));
      return;
    }

    // 计算新路径
    const oldPath = dragNode.key;
    const fileName = dragNode.fileData.name;
    const newPath = dropNode.key + '/' + fileName;

    // 如果目标路径和原路径相同，忽略
    if (oldPath === newPath) return;

    try {
      const result = await window.electronAPI.renameTaskItem({
        taskName: ChatInterface.projectName || currentTaskName,
        oldPath,
        newName: newPath
      });
      if (result.success) {
        message.success(I18nUtils.getText('moveFileSuccess'));
        fetchTaskFiles(true);
      } else {
        message.error(I18nUtils.getText('moveFileFailed') + ': ' + result.message);
      }
    } catch (error) {
      message.error(I18nUtils.getText('moveFileFailed'));
    }
  };

  // 右键菜单处理
  const getContextMenuItems = (nodeData: any) => {
    const items: any[] = [];
    
    if (nodeData.isDirectory) {
      // 目录右键菜单 - 在该目录下操作
      items.push({
        key: 'createTask',
        label: I18nUtils.getText('createNewTask'),
        icon: <FilePlus color="#6366f1" size={16} />,
        onClick: () => setIsCreateTaskModalVisible(true)
      });
      items.push({
        key: 'createFolder',
        label: I18nUtils.getText('createNewFolder'),
        icon: <FolderPlus color="#1890ff" size={16} />,
        onClick: () => {
          setParentFolderPath(nodeData.key);
          setIsCreateFolderModalVisible(true);
        }
      });
      items.push({ type: 'divider' });
      items.push({
        key: 'importFile',
        label: I18nUtils.getText('importLocalFile'),
        icon: <UploadCloud color="#fa8c16" size={16} />,
        onClick: () => handleImportLocalFileToFolder(nodeData.key)
      });
      items.push({ type: 'divider' });
      items.push({
        key: 'delete',
        label: I18nUtils.getText('delete'),
        icon: <Trash2 color="#ff4d4f" size={16} />,
        danger: true,
        onClick: () => {
          Modal.confirm({
            title: I18nUtils.getText('confirmDeleteFolder'),
            onOk: () => handleDeleteFolder(nodeData.key)
          });
        }
      });
    } else {
      // 文件右键菜单
      // 对于任务文件，不显示"查看详情"选项，因为任务文件已经在编辑器中打开
      if (nodeData.fileData.fileType !== 'task') {
        items.push({
          key: 'view',
          label: I18nUtils.getText('viewDetail'),
          icon: <Eye color="#6366f1" size={16} />,
          onClick: (event: React.MouseEvent) => {
            event?.stopPropagation();
            handleFileClick(nodeData.key);
          }
        });
      }
      items.push({
        key: 'pin',
        label: nodeData.fileData.isPinned ? I18nUtils.getText('unpinFile') : I18nUtils.getText('pinFile'),
        icon: <Pin color="#fa8c16" size={16} />,
        onClick: () => handleTogglePinned(nodeData.key, nodeData.fileData.isPinned)
      });
      items.push({ type: 'divider' });
      // 对于任务文件，不显示"重命名"选项，因为任务编辑功能已移至编辑器中
      if (nodeData.fileData.fileType !== 'task') {
        items.push({
          key: 'rename',
          label: I18nUtils.getText('renameItem'),
          icon: <Pencil color="#52c41a" size={16} />,
          onClick: () => {
            setRenameItemPath(nodeData.key);
            const isTaskFile = nodeData.fileData.fileType === 'task';
            setIsRenamingTaskFile(isTaskFile);
            
            // 对于任务文件，显示任务名称而不是文件名
            if (isTaskFile && nodeData.fileData.content) {
              try {
                const taskData = JSON.parse(nodeData.fileData.content);
                setNewItemName(taskData.task_name || nodeData.fileData.name);
              } catch (error) {
                setNewItemName(nodeData.fileData.name);
              }
            } else {
              setNewItemName(nodeData.fileData.name);
            }
            setIsRenameModalVisible(true);
          }
        });
      }
      // 知识库文件（Knowledge.json）不显示删除选项
      if (nodeData.fileData.name !== 'Knowledge.json') {
        items.push({
          key: 'delete',
          label: I18nUtils.getText('delete'),
          icon: <Trash2 color="#ff4d4f" size={16} />,
          danger: true,
          onClick: (event: React.MouseEvent) => {
            if (nodeData.fileData.fileType === 'data') {
              suppressNextSelect.current = true;
            }
            Modal.confirm({
              title: I18nUtils.getText('confirmDeleteFile'),
              onOk: () => handleDeleteFile(nodeData.key)
            });
          }
        });
      }
    }
    
    return items;
  };

  // 获取当前空间名称
  const getCurrentSpaceName = () => {
    return spaceName || I18nUtils.getText('fileManager');
  };

  // 构建树形数据
  const treeData = buildTreeData(taskFiles, selectedFileName, expandedKeys, autoSaveStatus, currentEditingFile);

  return (
    <div className="task-files-list">
      <div className="task-files-header">
        <div className="task-files-title">
          <File color="#6366f1" size={18} style={{ marginRight: 8 }} />
          <span>{getCurrentSpaceName()}</span>
        </div>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            size="small"
            icon={<ReloadOutlined />}
            onClick={() => fetchTaskFiles(true)}
            disabled={!(ChatInterface.projectName || currentTaskName)}
            title={I18nUtils.getText('refreshFileList')}
          />
          <Dropdown
            menu={{
              items: [
                {
                  key: 'createTask',
                  label: I18nUtils.getText('createNewTask'),
                  onClick: () => setIsCreateTaskModalVisible(true)
                },
                {
                  key: 'createFolder',
                  label: I18nUtils.getText('createNewFolder'),
                  onClick: () => {
                    setParentFolderPath('');
                    setIsCreateFolderModalVisible(true);
                  }
                },
                { type: 'divider' },
                {
                  key: 'importFile',
                  label: I18nUtils.getText('importLocalFile'),
                  onClick: handleImportLocalFile
                }
              ]
            }}
            disabled={!(ChatInterface.projectName || currentTaskName) || importingFile}
          >
            <Button
              type="primary"
              size="small"
              icon={<PlusOutlined />}
              loading={importingFile}
            >
              <DownOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>

      <div className="task-files-content">
        {!(ChatInterface.projectName || currentTaskName) ? (
          <div className="no-task-selected">
            <p>{I18nUtils.getText('createSpaceFirst')}</p>
          </div>
        ) : (
          <Spin spinning={loading}>
            <Tree
              className="task-file-tree"
              treeData={treeData}
              selectedKeys={[selectedFileName]}
              expandedKeys={expandedKeys}
              onSelect={handleTreeSelect}
              onExpand={handleTreeExpand}
              showIcon={false}
              draggable
              motion={{
                motionName: '',          // 禁用 motion class
                motionAppear: false,
                motionEnter: false,
                motionLeave: false,
              }}
              onDrop={handleTreeDrop}
              titleRender={(nodeData) => (
                <Dropdown
                  menu={{ items: getContextMenuItems(nodeData) }}
                  trigger={['contextMenu']}
                >
                  <div>{nodeData.title}</div>
                </Dropdown>
              )}
            />
          </Spin>
        )}
      </div>

      {/* 创建新任务的模态框 */}
      <Modal
        title={I18nUtils.getText('createNewTask')}
        open={isCreateTaskModalVisible}
        onOk={handleCreateTask}
        onCancel={() => {
          setIsCreateTaskModalVisible(false);
          setTaskShortName('');
          setTaskDetailedDescription('');
          setUseAiForTaskName(false);
        }}
        confirmLoading={false}
        okText={I18nUtils.getText('createAction')}
        cancelText={I18nUtils.getText('cancel')}
        width={600}
      >
        <div className="create-task-form">
          <div className="form-item">
            <label>
              {I18nUtils.getText('taskShortName')}
              <Checkbox
                style={{ marginLeft: '12px' }}
                checked={useAiForTaskName}
                onChange={(e) => setUseAiForTaskName(e.target.checked)}
              >
                {I18nUtils.getText('aiHelpWrite')}
              </Checkbox>
            </label>
            <Input
              value={taskShortName}
              onChange={(e) => setTaskShortName(e.target.value)}
              placeholder={I18nUtils.getText('taskShortNamePlaceholder')}
              disabled={useAiForTaskName}
            />
          </div>
          <div className="form-item">
            <label>{I18nUtils.getText('taskDetailedDescription')} *</label>
            <Input.TextArea
              value={taskDetailedDescription}
              onChange={(e) => setTaskDetailedDescription(e.target.value)}
              placeholder={I18nUtils.getText('taskDetailedDescriptionPlaceholder')}
              rows={8}
            />
          </div>
        </div>
      </Modal>

      {/* 创建新目录的模态框 */}
      <Modal
        title={I18nUtils.getText('createNewFolder')}
        open={isCreateFolderModalVisible}
        onOk={handleCreateFolder}
        onCancel={() => {
          setIsCreateFolderModalVisible(false);
          setFolderName('');
          setParentFolderPath('');
        }}
        confirmLoading={creatingFolder}
        okText={I18nUtils.getText('createAction')}
        cancelText={I18nUtils.getText('cancel')}
      >
        <div className="form-item">
          <label>{I18nUtils.getText('folderName')} *</label>
          <Input
            value={folderName}
            onChange={(e) => setFolderName(e.target.value)}
            placeholder={I18nUtils.getText('folderNamePlaceholder')}
          />
        </div>
        {parentFolderPath && (
          <div className="form-item">
            <label>父目录: {parentFolderPath}</label>
          </div>
        )}
      </Modal>

      {/* 创建新文件的模态框 */}
      <Modal
        title={I18nUtils.getText('createNewFile')}
        open={isCreateFileModalVisible}
        onOk={handleCreateFile}
        onCancel={() => {
          setIsCreateFileModalVisible(false);
          setNewFileName('');
          setNewFileContent('');
          setNewFileParentPath('');
        }}
        confirmLoading={creatingFile}
        okText={I18nUtils.getText('createAction')}
        cancelText={I18nUtils.getText('cancel')}
        width={600}
      >
        <div className="create-file-form">
          <div className="form-item">
            <label>{I18nUtils.getText('fileName')} *</label>
            <Input
              value={newFileName}
              onChange={(e) => setNewFileName(e.target.value)}
              placeholder={I18nUtils.getText('fileNamePlaceholder')}
            />
          </div>
          {newFileParentPath && (
            <div className="form-item">
              <label>父目录: {newFileParentPath}</label>
            </div>
          )}
          <div className="form-item">
            <label>{I18nUtils.getText('fileContent')}</label>
            <Input.TextArea
              value={newFileContent}
              onChange={(e) => setNewFileContent(e.target.value)}
              placeholder={I18nUtils.getText('fileContentPlaceholder')}
              rows={6}
            />
          </div>
        </div>
      </Modal>

      {/* 重命名的模态框 */}
      <Modal
        title={isRenamingTaskFile ? I18nUtils.getText('renameTask') : I18nUtils.getText('renameItem')}
        open={isRenameModalVisible}
        onOk={handleRenameItem}
        onCancel={() => {
          setIsRenameModalVisible(false);
          setRenameItemPath('');
          setNewItemName('');
          setIsRenamingTaskFile(false);
        }}
        confirmLoading={renamingItem}
        okText={I18nUtils.getText('confirm')}
        cancelText={I18nUtils.getText('cancel')}
      >
        <div className="form-item">
          <label>{isRenamingTaskFile ? I18nUtils.getText('newTaskName') : I18nUtils.getText('newItemName')} *</label>
          <Input
            value={newItemName}
            onChange={(e) => setNewItemName(e.target.value)}
            placeholder={isRenamingTaskFile ? I18nUtils.getText('newTaskNamePlaceholder') : I18nUtils.getText('newItemNamePlaceholder')}
          />
        </div>
      </Modal>

      <TablePanel
        visible={tablePanelState.visible}
        onClose={() => setTablePanelState({ visible: false, data: null })}
        tableData={tablePanelState.data}
      />
    </div>
  );
}; 