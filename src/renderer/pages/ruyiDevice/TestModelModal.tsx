import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, Radio, message, List, Tag } from 'antd';
import { I18nUtils } from './languageSupport/i18nUtils';
import { flaskApi } from '../../../services/FlaskApiService';
import { ruyiModelApi } from '../../../services/api';
import { useBrowserViewControl } from '../../../renderer/hooks/useBrowserViewControl';
import '../styles/TestModelModal.css';

interface TestModelModalProps {
  visible: boolean;
  onClose: () => void;
}

interface TestResult {
  id: number;
  mode: 'grounding' | 'enumerate' | 'check' | 'ensure' | 'describe';
  description: string;
  coordinate?: string;
  boundingBox?: {
    x1: number;
    y1: number;
    x2: number;
    y2: number;
  };
  items?: string[];
  timestamp: number;
  predictedImage?: string;
  clickCoordinates?: {
    x: number;
    y: number;
  };
}

export const TestModelModal: React.FC<TestModelModalProps> = ({
  visible,
  onClose,
}) => {
  const [description, setDescription] = useState('');
  const [originalImage, setOriginalImage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [testMode, setTestMode] = useState<'grounding' | 'enumerate' | 'check' | 'ensure' | 'describe'>('grounding');
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [currentResultId, setCurrentResultId] = useState<number | null>(null);
  const [lastViewedResults, setLastViewedResults] = useState<{
    grounding?: number;
    enumerate?: number;
    check?: number;
    ensure?: number;
    describe?: number;
  }>({});
  const [clickIndicator, setClickIndicator] = useState<{ 
    x: number; 
    y: number; 
    imgWidth: number; 
    imgHeight: number;
    displayedImageWidth: number;
    displayedImageHeight: number;
    imageOffsetX: number;
    imageOffsetY: number;
  } | null>(null);

  // Add the hook to control browser view visibility
  useBrowserViewControl(visible);

  // Handle image click for describe mode
  const handleImageClick = async (event: React.MouseEvent<HTMLImageElement>) => {
    if (testMode !== 'describe') return;
    
    const clickedImg = event.currentTarget;
    const rect = clickedImg.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickY = event.clientY - rect.top;
    
    // Calculate the actual displayed image bounds within the container (accounting for object-fit: contain)
    const containerWidth = clickedImg.clientWidth;
    const containerHeight = clickedImg.clientHeight;
    const imageAspectRatio = clickedImg.naturalWidth / clickedImg.naturalHeight;
    const containerAspectRatio = containerWidth / containerHeight;
    
    let displayedImageWidth, displayedImageHeight, imageOffsetX, imageOffsetY;
    
    if (imageAspectRatio > containerAspectRatio) {
      // Image is wider than container - image fills width, has vertical margins
      displayedImageWidth = containerWidth;
      displayedImageHeight = containerWidth / imageAspectRatio;
      imageOffsetX = 0;
      imageOffsetY = (containerHeight - displayedImageHeight) / 2;
    } else {
      // Image is taller than container - image fills height, has horizontal margins
      displayedImageWidth = containerHeight * imageAspectRatio;
      displayedImageHeight = containerHeight;
      imageOffsetX = (containerWidth - displayedImageWidth) / 2;
      imageOffsetY = 0;
    }
    
    // Check if click is within the actual image bounds
    const relativeX = clickX - imageOffsetX;
    const relativeY = clickY - imageOffsetY;
    
    if (relativeX < 0 || relativeX > displayedImageWidth || relativeY < 0 || relativeY > displayedImageHeight) {
      // Click is outside the actual image area
      message.info(I18nUtils.getText('clickImageToDescribe'));
      return;
    }
    
    // Calculate coordinates relative to the actual image
    const imageX = (relativeX / displayedImageWidth) * clickedImg.naturalWidth;
    const imageY = (relativeY / displayedImageHeight) * clickedImg.naturalHeight;
    
    // Rescale to 1000x1000 coordinate system
    const normalizedX = Math.round((imageX / clickedImg.naturalWidth) * 1000);
    const normalizedY = Math.round((imageY / clickedImg.naturalHeight) * 1000);
    

    
    try {
      setIsLoading(true);
      // Show click indicator immediately
      setClickIndicator({ 
        x: imageX, 
        y: imageY, 
        imgWidth: clickedImg.naturalWidth, 
        imgHeight: clickedImg.naturalHeight,
        displayedImageWidth,
        displayedImageHeight,
        imageOffsetX,
        imageOffsetY
      });
      
      const result = await ruyiModelApi.describe(normalizedX, normalizedY, originalImage);
      
      if (result) {
        // Create a canvas to draw the click point using the same image that's displayed
        const canvasImg = new Image();
        canvasImg.onload = () => {
          const canvas = document.createElement('canvas');
          canvas.width = canvasImg.width;
          canvas.height = canvasImg.height;
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            console.error('无法获取 canvas context');
            return;
          }
          
          // Draw the original image
          ctx.drawImage(canvasImg, 0, 0);
          
          // Use the same coordinate calculation logic as the click
          // Convert normalized coordinates back to pixel coordinates on this specific image
          const actualClickX = (normalizedX / 1000) * canvasImg.width;
          const actualClickY = (normalizedY / 1000) * canvasImg.height;
          

          
          // Draw a red circle at the click point
          ctx.beginPath();
          ctx.fillStyle = 'red';
          ctx.arc(actualClickX, actualClickY, 8, 0, 2 * Math.PI);
          ctx.fill();
          
          // Draw a white border for better visibility
          ctx.beginPath();
          ctx.strokeStyle = 'white';
          ctx.lineWidth = 2;
          ctx.arc(actualClickX, actualClickY, 8, 0, 2 * Math.PI);
          ctx.stroke();
          
          const newResult: TestResult = {
            id: Date.now(),
            mode: 'describe',
            description: result,
            coordinate: `${normalizedX} ${normalizedY}`,
            clickCoordinates: { x: normalizedX, y: normalizedY },
            timestamp: Date.now(),
            predictedImage: canvas.toDataURL(),
          };
          
          setTestResults(prev => [newResult, ...prev]);
          setCurrentResultId(newResult.id);
          setLastViewedResults(prev => ({
            ...prev,
            describe: newResult.id
          }));
        };
        canvasImg.src = originalImage;
      }
    } catch (error) {
      if (error instanceof Error && error.message === 'Insufficient Token') {
        message.error(I18nUtils.getText('insufficientTokenTestModel'));
      } else {
        console.error('描述失败:', error);
        message.error(I18nUtils.getText('testModelFailed'));
      }
    } finally {
      setIsLoading(false);
      // Clear click indicator
      setClickIndicator(null);
    }
  };

  // 辅助函数：将图片 resize 到指定尺寸
  const resizeImage = (base64Image: string, targetWidth: number, targetHeight: number): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = targetWidth;
        canvas.height = targetHeight;
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('无法获取 canvas context'));
          return;
        }
        ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
        resolve(canvas.toDataURL('image/png'));
      };
      img.onerror = () => reject(new Error('图片加载失败'));
      img.src = base64Image;
    });
  };

  // 在模态框打开时获取截图
  useEffect(() => {
    if (visible) {
      loadScreenshot();
    }
  }, [visible]);

  const loadScreenshot = async () => {
    try {
      setIsLoading(true);
      const response = await flaskApi.getScreenshot();
      const screenshot = response.screenshot;
      
      // Only resize if the device type is browser
      if (response.device_type === 'browser') {
        const resizedScreenshot = await resizeImage(screenshot, 1080, 2400);
        setOriginalImage(resizedScreenshot);
      } else {
        setOriginalImage(screenshot);
      }
    } catch (error) {
      console.error('Failed to load screenshot:', error);
      message.error(I18nUtils.getText('getScreenshotFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestModel = async () => {
    if (testMode === 'describe') {
      message.info(I18nUtils.getText('clickImageToDescribe'));
      return;
    }
    
    try {
      setIsLoading(true);
      let result;
      switch (testMode) {
        case 'grounding':
          result = await ruyiModelApi.grounding(description, originalImage);
          break;
        case 'enumerate':
          result = await ruyiModelApi.enumerate(description, originalImage);
          break;
        case 'check':
          result = await ruyiModelApi.check(description, originalImage);
          break;
        case 'ensure':
          result = await ruyiModelApi.ensure(description, originalImage);
          break;
      }

      if (result) {
        const content = result;
        const newResult: TestResult = {
          id: Date.now(),
          mode: testMode,
          description,
          timestamp: Date.now(),
        };

        if (testMode === 'enumerate') {
          // 处理枚举结果
          // 移除方括号并分割字符串
          const items = content.replace(/[\[\]]/g, '').split(',').map((item: string) => item.trim());
          newResult.items = items;
          newResult.predictedImage = originalImage;
          setTestResults(prev => [newResult, ...prev]);
          setCurrentResultId(newResult.id);
          // 更新最后查看的结果
          setLastViewedResults(prev => ({
            ...prev,
            [testMode]: newResult.id
          }));
        } else if (testMode === 'check') {
          // 处理 check 结果
          const checkValue = content.trim();
          newResult.coordinate = checkValue; // 使用 coordinate 字段存储 check 的结果
          newResult.predictedImage = originalImage;
          setTestResults(prev => [newResult, ...prev]);
          setCurrentResultId(newResult.id);
          // 更新最后查看的结果
          setLastViewedResults(prev => ({
            ...prev,
            [testMode]: newResult.id
          }));
        } else {
          // 处理 grounding 和 ensure 结果
          const coordinates = content.split(' ').map(Number);
          if (coordinates.length === 4) {
            const [x1, y1, x2, y2] = coordinates;
            newResult.boundingBox = { x1, y1, x2, y2 };
            newResult.coordinate = `${x1} ${y1} ${x2} ${y2}`;
            
            // 创建一个新的 canvas 来绘制预测结果
            const img = new Image();
            img.onload = () => {
              const canvas = document.createElement('canvas');
              canvas.width = img.width;
              canvas.height = img.height;
              const ctx = canvas.getContext('2d');
              
              if (!ctx) {
                console.error('无法获取 canvas context');
                return;
              }
              
              // 绘制原始图片
              ctx.drawImage(img, 0, 0);
              
              // 计算实际像素坐标
              const actualX1 = (x1 / 1000) * img.width;
              const actualY1 = (y1 / 1000) * img.height;
              const actualX2 = (x2 / 1000) * img.width;
              const actualY2 = (y2 / 1000) * img.height;
              
              // 绘制边界框
              ctx.beginPath();
              ctx.strokeStyle = 'red';
              ctx.lineWidth = 2;
              ctx.rect(actualX1, actualY1, actualX2 - actualX1, actualY2 - actualY1);
              ctx.stroke();
              
              newResult.predictedImage = canvas.toDataURL();
              setTestResults(prev => [newResult, ...prev]);
              setCurrentResultId(newResult.id);
              // 更新最后查看的结果
              setLastViewedResults(prev => ({
                ...prev,
                [testMode]: newResult.id
              }));
            };
            img.src = originalImage;
          } else {
            // 当无法解析坐标时，直接使用原始截图
            newResult.predictedImage = originalImage;
            setTestResults(prev => [newResult, ...prev]);
            setCurrentResultId(newResult.id);
            // 更新最后查看的结果
            setLastViewedResults(prev => ({
              ...prev,
              [testMode]: newResult.id
            }));
          }
        }
      }
    } catch (error) {
      if (error instanceof Error && error.message === 'Insufficient Token') {
        message.error(I18nUtils.getText('insufficientTokenTestModel'));
      } else {
        console.error('测试模型失败:', error);
        message.error(I18nUtils.getText('testModelFailed'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setDescription('');
    setOriginalImage('');
    setTestResults([]);
    setCurrentResultId(null);
    setLastViewedResults({});
    onClose();
  };

  const getModeColor = (mode: string) => {
    switch (mode) {
      case 'grounding':
        return 'blue';
      case 'enumerate':
        return 'green';
      case 'check':
        return 'orange';
      case 'ensure':
        return 'purple';
      case 'describe':
        return 'magenta';
      default:
        return 'default';
    }
  };

  const getCurrentResult = () => {
    return testResults.find(result => result.id === currentResultId);
  };

  const renderResultContent = (result: TestResult) => {
    switch (result.mode) {
      case 'grounding':
      case 'ensure':
        return (
          <div className="result-content">
            <div className="result-header">
              <Tag color={getModeColor(result.mode)}>{result.mode}</Tag>
              <span className="result-time">
                {new Date(result.timestamp).toLocaleTimeString()}
              </span>
            </div>
            <div className="result-description">{result.description}</div>
            {result.coordinate && (
              <div className="result-coordinate">
                {I18nUtils.getText('predictedCoordinate')}: {result.coordinate}
              </div>
            )}
          </div>
        );
      case 'enumerate':
        return (
          <div className="result-content">
            <div className="result-header">
              <Tag color={getModeColor(result.mode)}>{result.mode}</Tag>
              <span className="result-time">
                {new Date(result.timestamp).toLocaleTimeString()}
              </span>
            </div>
            <div className="result-description">{result.description}</div>
            {result.items && (
              <div className="result-items">
                {result.items.map((item, index) => (
                  <Tag key={index} color="blue">{item}</Tag>
                ))}
              </div>
            )}
          </div>
        );
      case 'check':
        return (
          <div className="result-content">
            <div className="result-header">
              <Tag color={getModeColor(result.mode)}>{result.mode}</Tag>
              <span className="result-time">
                {new Date(result.timestamp).toLocaleTimeString()}
              </span>
            </div>
            <div className="result-description">{result.description}</div>
            {result.coordinate && (
              <div className="result-coordinate">
                {I18nUtils.getText('checkValue')}: {result.coordinate}
              </div>
            )}
          </div>
        );
      case 'describe':
        return (
          <div className="result-content">
            <div className="result-header">
              <Tag color={getModeColor(result.mode)}>{result.mode}</Tag>
              <span className="result-time">
                {new Date(result.timestamp).toLocaleTimeString()}
              </span>
            </div>
            {result.clickCoordinates && (
              <div className="result-coordinate">
                {I18nUtils.getText('clickCoordinate')}: ({result.clickCoordinates.x}, {result.clickCoordinates.y})
              </div>
            )}
            <div className="result-description">
              <strong>{I18nUtils.getText('describeResult')}:</strong> {result.description}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      title={I18nUtils.getText('testModelModalTitle')}
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={1200}
      className="test-model-modal"
    >
      <div className="test-model-content">
        <div className="test-model-controls">
          <div className="mode-selector">
            <div className="mode-label-row">
              <span className="mode-label">{I18nUtils.getText('testMode')}:</span>
              <Radio.Group 
                value={testMode} 
                onChange={(e) => {
                  const newMode = e.target.value as 'grounding' | 'enumerate' | 'check' | 'ensure' | 'describe';
                  setTestMode(newMode);
                  setDescription('');
                  
                  // 切换到该模式下最后查看的结果
                  const lastViewedId = lastViewedResults[newMode];
                  if (lastViewedId) {
                    setCurrentResultId(lastViewedId);
                  } else {
                    setCurrentResultId(null);
                  }
                }}
              >
                <Radio.Button value="grounding">Grounding</Radio.Button>
                <Radio.Button value="enumerate">Enumerate</Radio.Button>
                <Radio.Button value="check">Check</Radio.Button>
                <Radio.Button value="ensure">Ensure</Radio.Button>
                <Radio.Button value="describe">Describe</Radio.Button>
              </Radio.Group>
            </div>
          </div>

          {testMode !== 'describe' && (
            <div className="description-input-container">
              <Input.TextArea
                placeholder={I18nUtils.getText('testModelDescription')}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={1}
                className="description-input"
              />
              <Button 
                type="primary" 
                onClick={handleTestModel} 
                loading={isLoading}
                className="test-button"
              >
                {I18nUtils.getText('testModelButton')}
              </Button>
            </div>
          )}
        </div>
        
        <div className="test-model-main">
          <div className="image-section">
            <h4>{I18nUtils.getText('originalImage')}</h4>
            {originalImage && (
              <div className={`image-container ${testMode === 'describe' ? 'describe-mode' : ''}`}>
                <img 
                  src={originalImage} 
                  alt="Original" 
                  className={`preview-image ${testMode === 'describe' ? 'clickable-image' : ''}`}
                  onClick={handleImageClick}
                  style={testMode === 'describe' ? { cursor: 'crosshair' } : {}}
                />
                {testMode === 'describe' && clickIndicator && (
                  <div 
                    className="click-indicator"
                    style={{
                      position: 'absolute',
                      left: `${clickIndicator.imageOffsetX + (clickIndicator.x / clickIndicator.imgWidth) * clickIndicator.displayedImageWidth}px`,
                      top: `${clickIndicator.imageOffsetY + (clickIndicator.y / clickIndicator.imgHeight) * clickIndicator.displayedImageHeight}px`,
                      transform: 'translate(-50%, -50%)',
                      width: '16px',
                      height: '16px',
                      borderRadius: '50%',
                      backgroundColor: 'red',
                      border: '2px solid white',
                      pointerEvents: 'none',
                      zIndex: 10
                    }}
                  />
                )}
              </div>
            )}
          </div>

          <div className="image-section">
            <h4>{I18nUtils.getText('predictedImage')}</h4>
            {getCurrentResult()?.predictedImage ? (
              <img 
                src={getCurrentResult()?.predictedImage} 
                alt="Predicted" 
                className="preview-image"
              />
            ) : (
              <div className="placeholder">{I18nUtils.getText('noTestResults')}</div>
            )}
          </div>

          <div className="results-section">
            <h4>{I18nUtils.getText('testModelResult')}</h4>
            {testResults.filter(result => result.mode === testMode).length > 0 ? (
              <List
                dataSource={testResults.filter(result => result.mode === testMode)}
                renderItem={(result) => (
                  <List.Item
                    className={`result-item ${result.id === currentResultId ? 'active' : ''}`}
                    onClick={() => {
                      setCurrentResultId(result.id);
                      // 更新最后查看的结果
                      setLastViewedResults(prev => ({
                        ...prev,
                        [testMode]: result.id
                      }));
                    }}
                  >
                    {renderResultContent(result)}
                  </List.Item>
                )}
              />
            ) : (
              <div className="placeholder">{I18nUtils.getText('noTestResults')}</div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
}; 