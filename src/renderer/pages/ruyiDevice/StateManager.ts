/**
 * StateManager - 状态管理工具类
 * 负责管理用户切换时的前端状态重置
 */

import { ChatInterface } from './ChatInterface';
import { CodeEditorUtils } from './CodeEditor';

export class StateManager {
  /**
   * 重置所有用户相关的前端状态
   * 在用户切换或登出时调用此函数
   */
  static resetUserState(): void {
    console.log('[StateManager] 开始重置用户状态...');
    
    try {
      // 1. 重置 ChatInterface 状态
      console.log('[StateManager] 重置 ChatInterface 状态');
      ChatInterface.setTask('');
      ChatInterface.setTaskDescription('');
      ChatInterface.setProjectName('');
      ChatInterface.setCurrentEditingFile('');
      ChatInterface.setAllMessages([]);
      
      // 2. 重置 CodeEditor 状态
      console.log('[StateManager] 重置 CodeEditor 状态');
      CodeEditorUtils.resetKnowledgeView();
      CodeEditorUtils.setNLScript('');
      CodeEditorUtils.setCodeScript('');
      CodeEditorUtils.lastScript = '';
      
      // 3. 清除本地存储的用户相关数据（如果有的话）
      console.log('[StateManager] 清理本地存储');
      // 这里可以添加清理 localStorage 或 sessionStorage 的逻辑
      
      console.log('[StateManager] ✓ 用户状态重置完成');
    } catch (error) {
      console.error('[StateManager] ✗ 重置用户状态失败:', error);
    }
  }
  
  /**
   * 重置到初始欢迎状态
   * 确保界面回到新用户的初始状态
   */
  static resetToWelcomeState(): void {
    console.log('[StateManager] 重置到欢迎状态...');
    
    try {
      // 重置用户状态
      this.resetUserState();
      
      // 可以在这里添加更多特定的初始化逻辑
      // 例如：重置UI组件状态、清理缓存等
      
      console.log('[StateManager] ✓ 已重置到欢迎状态');
    } catch (error) {
      console.error('[StateManager] ✗ 重置到欢迎状态失败:', error);
    }
  }
  
  /**
   * 检查当前是否有未保存的用户数据
   * 用于在切换用户前提醒用户保存
   */
  static hasUnsavedData(): boolean {
    try {
      // 检查是否有未保存的脚本
      if (CodeEditorUtils.checkScriptUpdate && CodeEditorUtils.checkScriptUpdate()) {
        return true;
      }
      
      // 检查是否有当前正在编辑的项目
      if (ChatInterface.task || ChatInterface.projectName) {
        return true;
      }
      
      return false;
    } catch (error) {
      console.warn('[StateManager] 检查未保存数据时出错:', error);
      return false;
    }
  }
} 