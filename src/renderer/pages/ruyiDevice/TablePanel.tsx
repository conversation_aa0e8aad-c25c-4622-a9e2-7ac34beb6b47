import React, { useMemo } from 'react';
import { Drawer, Button, Table, Image } from 'antd';
import { DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import { unparse } from 'papaparse';
import { I18nUtils } from './languageSupport/i18nUtils';
import { useBrowserViewControl } from '../../hooks/useBrowserViewControl';
import '../styles/TablePanel.css';

export interface TableData {
  name: string;
  columns: string[];
  data: Record<string, any>[];
}

interface TablePanelProps {
  visible: boolean;
  onClose: () => void;
  tableData: TableData | null;
}

// 检测数据是否为图片格式
const isImageData = (value: any): boolean => {
  return value && 
         typeof value === 'object' && 
         value.image_data && 
         typeof value.image_data === 'string' && 
         value.image_data.startsWith('data:image/');
};

// 渲染图片单元格
const ImageCell: React.FC<{ imageData: any; columnTitle: string }> = ({ imageData, columnTitle }) => {
  if (!isImageData(imageData)) {
    return <span>{String(imageData)}</span>;
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <Image
        src={imageData.image_data}
        alt={imageData.metadata?.name || columnTitle}
        width={60}
        height={40}
        style={{ 
          objectFit: 'cover',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
        preview={{
          mask: <EyeOutlined style={{ fontSize: '16px' }} />,
          src: imageData.image_data
        }}
        // 当图片加载失败时，显示一个默认的SVG图片
        fallback="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFYzNkgyMFYyMFoiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTI4IDI4SDM2VjM2SDI4VjI4WiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4K"
      />
      <span style={{ fontSize: '12px', color: '#666' }}>
        {imageData.metadata?.name || 'Image'}
      </span>
    </div>
  );
};

export const TablePanel: React.FC<TablePanelProps> = ({ visible, onClose, tableData }) => {
  useBrowserViewControl(visible);

  const drawerWidth = useMemo(() => {
    if (!tableData) {
      return '50%';
    }
    const numColumns = tableData.columns.length;
    // 每列大约120px，基础宽度200px，最小500px，最大不超过屏幕宽度的90%
    // 如果有图片列，需要更多空间
    const hasImageColumn = tableData.data.some(row => 
      tableData.columns.some(col => isImageData(row[col]))
    );
    const baseWidth = hasImageColumn ? 150 : 120; // 图片列需要更多空间
    const calculatedWidth = 200 + numColumns * baseWidth;
    const maxWidth = typeof window !== 'undefined' ? window.innerWidth * 0.9 : 1200;
    return Math.max(500, Math.min(calculatedWidth, maxWidth));
  }, [tableData]);

  if (!tableData) {
    return null;
  }

  const handleDownload = () => {
    if (!tableData) return;

    // 对于CSV导出，将图片数据转换为描述文本
    const csvData = tableData.data.map(row => {
      const processedRow: Record<string, any> = {};
      tableData.columns.forEach(col => {
        if (isImageData(row[col])) {
          processedRow[col] = row[col].metadata?.name || '[Image]';
        } else {
          processedRow[col] = row[col];
        }
      });
      return processedRow;
    });

    const csv = unparse(csvData, {
      columns: tableData.columns,
    });

    const blob = new Blob([`\uFEFF${csv}`], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${tableData.name || 'table'}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const columns = tableData.columns.map(col => {
    // 检查这一列是否包含图片数据
    const hasImages = tableData.data.some(row => isImageData(row[col]));
    
    return {
      title: col,
      dataIndex: col,
      key: col,
      width: hasImages ? 150 : undefined, // 图片列设置固定宽度
      render: hasImages 
        ? (value: any) => <ImageCell imageData={value} columnTitle={col} />
        : undefined,
      // ellipsis: !hasImages, // 图片列不使用省略号
    };
  });

  return (
    <Drawer
      title={tableData.name || I18nUtils.getText('tablePanelTitle')}
      placement="left"
      onClose={onClose}
      open={visible}
      width={drawerWidth}
      maskClosable={false}
      extra={
        <Button
          icon={<DownloadOutlined />}
          onClick={handleDownload}
        >
          {I18nUtils.getText('download')}
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={tableData.data}
        rowKey={(record, index) => index!.toString()}
        pagination={false}
        scroll={{ y: 'calc(100vh - 150px)', x: 'max-content' }}
        bordered
      />
    </Drawer>
  );
}; 