import React, { useState, useEffect } from 'react';
import { Modal, List, Button, Tag, message, Popconfirm, Form, Switch } from 'antd';
import { I18nUtils } from './languageSupport/i18nUtils';
import '../styles/AnnotationManagement.css';
import { CloseOutlined } from '@ant-design/icons';
import { useBrowserViewControl } from '../../../renderer/hooks/useBrowserViewControl';

interface AnnotationItem {
  directory: string;
  screenshot: string;
  mode?: string;
  annotations: Array<{
    mode?: string;
    description?: string | string[];
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    value?: boolean;
    groupTitle?: string | string[];
    label?: string;
  }>;
}

interface AnnotationManagementProps {
  visible: boolean;
  onClose: () => void;
}

export const AnnotationManagement: React.FC<AnnotationManagementProps> = ({
  visible,
  onClose,
}) => {
  const [annotations, setAnnotations] = useState<AnnotationItem[]>([]);
  const [useAnnotation, setUseAnnotation] = useState(true);

  useBrowserViewControl(visible);

  useEffect(() => {
    if (visible) {
      loadAnnotations();
      loadUseAnnotationConfig();
    }
  }, [visible]);

  const loadUseAnnotationConfig = async () => {
    try {
      const useAnnotationConfig = await window.electronAPI.getUseAnnotationConfig();
      setUseAnnotation(useAnnotationConfig);
    } catch (error) {
      console.error('加载 use_annotation 配置失败:', error);
      message.error(I18nUtils.getText('loadPatchConfigFailed'));
    }
  };

  const handleUseAnnotationChange = async (checked: boolean) => {
    try {
      await window.electronAPI.setUseAnnotationConfig(checked);
      setUseAnnotation(checked);
      if (checked) {
        message.success(I18nUtils.getText('patchEnabledSuccess'));
      } else {
        message.success(I18nUtils.getText('patchDisabledSuccess'));
      }
    } catch (error) {
      console.error('更新 use_annotation 配置失败:', error);
      message.error(I18nUtils.getText('updatePatchConfigFailed'));
    }
  };

  const loadAnnotations = async () => {
    try {
      const data = await window.electronAPI.getAnnotations();
      if (data && data.length > 0) {
        // 将标注列表倒序排列，最新的放在最上面
        const reversedAnnotations = [...data].reverse();
        setAnnotations(reversedAnnotations);
      } else {
        setAnnotations([]);
      }
    } catch (error) {
      console.error('加载标注失败:', error);
      message.error(I18nUtils.getText('loadAnnotationsFailed'));
    }
  };

  const handleDeleteAnnotation = async (directory: string) => {
    try {
      await window.electronAPI.deleteAnnotation(directory);
      message.success(I18nUtils.getText('deleteSuccess'));
      loadAnnotations(); // 重新加载标注列表
    } catch (error) {
      message.error(I18nUtils.getText('deleteFailed'));
    }
  };

  const handleDeleteAllAnnotations = async () => {
    try {
      await window.electronAPI.deleteAllAnnotations();
      message.success(I18nUtils.getText('deleteAllSuccess'));
      loadAnnotations(); // 重新加载以更新列表
    } catch (error) {
      console.error('删除全部标注数据失败:', error);
      message.error(I18nUtils.getText('deleteAllFailed'));
    }
  };

  const handlePreviewImage = (screenshot: string) => {
    Modal.info({
      content: (
        <div className="preview-image-container">
          <img 
            src={screenshot} 
            className="annotation-preview-image"
            alt="screenshot"
          />
          <Button 
            type="text" 
            icon={<CloseOutlined />} 
            className="preview-close-button"
            onClick={() => Modal.destroyAll()}
          />
        </div>
      ),
      width: 'auto',
      icon: null,
      footer: null,
      maskClosable: true,
      className: 'annotation-preview-modal'
    });
  };

  const titleContent = (
    <div className="annotation-modal-title-container">
      <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
        <span>{I18nUtils.getText('viewAnnotations')}</span>
        <Form.Item label={I18nUtils.getText('usePatchInExecution')} style={{ marginBottom: 0, display: 'flex', alignItems: 'center' }}>
          <Switch checked={useAnnotation} onChange={handleUseAnnotationChange} />
        </Form.Item>
      </div>
      {annotations.length > 0 && (
        <Popconfirm
          title={I18nUtils.getText('deleteAllConfirmTitle')}
          description={I18nUtils.getText('deleteAllConfirmContent')}
          onConfirm={handleDeleteAllAnnotations}
          okText={I18nUtils.getText('confirm')}
          cancelText={I18nUtils.getText('cancel')}
        >
          <Button danger>
            {I18nUtils.getText('deleteAll')}
          </Button>
        </Popconfirm>
      )}
    </div>
  );

  return (
    <Modal
      title={titleContent}
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={null}
      maskClosable={false}
      keyboard={false}
    >
      <div className="annotation-management">
        {annotations.length === 0 ? (
          <div className="no-annotations">
            {I18nUtils.getText('noAnnotations')}
          </div>
        ) : (
          <List
            dataSource={annotations}
            renderItem={(item) => (
              <div className="annotation-item">
                <div className="annotation-content">
                  {/* 缩略图部分 */}
                  <div 
                    className="annotation-thumbnail"
                    onClick={() => handlePreviewImage(item.screenshot)}
                  >
                    <img 
                      src={item.screenshot} 
                      alt="thumbnail"
                    />
                  </div>
                  
                  {/* 标注信息部分 */}
                  <div className="annotation-info">
                    <div className="annotation-header">
                      <div>
                        <strong>{I18nUtils.getText('annotationMode')}: <Tag color="blue">{item.mode}</Tag> </strong>
                      </div>
                      <Button 
                        type="link" 
                        danger 
                        onClick={() => handleDeleteAnnotation(item.directory)}
                      >
                        {I18nUtils.getText('delete')}
                      </Button>
                    </div>
                    {/* 标注详情列表 */}
                    <div className="annotation-details">
                      {item.annotations.map((annotation, idx) => (
                        <div key={idx} className="annotation-detail-item">
                          {annotation.mode === 'grounding' && (
                            <>
                              <div style={{ marginBottom: '4px' }}>
                                <strong>{I18nUtils.getText('annotationLabelTitle')}:</strong> {
                                  annotation.description == null
                                    ? null
                                    : Array.isArray(annotation.description)
                                      ? (annotation.description as string[]).map((desc, i) => <span key={i}>{desc}{i < (annotation.description as string[]).length - 1 ? '，' : ''}</span>)
                                      : annotation.description
                                }
                              </div>
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                <strong>{I18nUtils.getText('position')}:</strong> X: {Math.round(annotation.x || 0)}, Y: {Math.round(annotation.y || 0)}, 
                                W: {Math.round(annotation.width || 0)}, H: {Math.round(annotation.height || 0)}
                              </div>
                            </>
                          )}
                          {annotation.mode === 'enumerate' && (
                            <>
                              <div style={{ marginBottom: '4px' }}>
                                <strong>{I18nUtils.getText('annotationGroupTitle')}:</strong> {
                                  annotation.groupTitle == null
                                    ? null
                                    : Array.isArray(annotation.groupTitle)
                                      ? (annotation.groupTitle as string[]).map((desc, i) => <span key={i}>{desc}{i < (annotation.groupTitle as string[]).length - 1 ? '，' : ''}</span>)
                                      : annotation.groupTitle
                                }
                              </div>
                              <div style={{ marginBottom: '4px' }}>
                                <strong>{I18nUtils.getText('annotationItemLabelTitle')}:</strong> {
                                  annotation.description == null
                                    ? null
                                    : Array.isArray(annotation.description)
                                      ? (annotation.description as string[]).map((desc, i) => <span key={i}>{desc}{i < (annotation.description as string[]).length - 1 ? '，' : ''}</span>)
                                      : annotation.description
                                }
                              </div>
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                <strong>{I18nUtils.getText('position')}:</strong> X: {Math.round(annotation.x || 0)}, Y: {Math.round(annotation.y || 0)}, 
                                W: {Math.round(annotation.width || 0)}, H: {Math.round(annotation.height || 0)}
                              </div>
                            </>
                          )}
                          {annotation.mode === 'check' && (
                            <div>
                              <strong>{I18nUtils.getText('checkDescriptionTitle')}:</strong> {
                                annotation.description == null
                                  ? null
                                  : Array.isArray(annotation.description)
                                    ? (annotation.description as string[]).map((desc, i) => <span key={i}>{desc}{i < (annotation.description as string[]).length - 1 ? '，' : ''}</span>)
                                    : annotation.description
                              }
                              <div style={{ marginTop: '4px' }}>
                                <strong>{I18nUtils.getText('checkValueTitle')}: </strong>
                                <Tag color={annotation.value ? 'green' : 'red'}>
                                  {annotation.value ? 'True' : 'False'}
                                </Tag>
                              </div>
                            </div>
                          )}
                          {annotation.mode === 'ensure' && (
                            <>
                              <div style={{ marginBottom: '4px' }}>
                                <strong>{I18nUtils.getText('ensureDescriptionTitle')}:</strong> {
                                  annotation.description == null
                                    ? null
                                    : Array.isArray(annotation.description)
                                      ? (annotation.description as string[]).map((desc, i) => <span key={i}>{desc}{i < (annotation.description as string[]).length - 1 ? '，' : ''}</span>)
                                      : annotation.description
                                }
                              </div>
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                <strong>{I18nUtils.getText('position')}:</strong> X: {Math.round(annotation.x || 0)}, Y: {Math.round(annotation.y || 0)}, 
                                W: {Math.round(annotation.width || 0)}, H: {Math.round(annotation.height || 0)}
                              </div>
                            </>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          />
        )}
      </div>
    </Modal>
  );
};
