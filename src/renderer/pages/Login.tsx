import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Card, message, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import { userApi, tokenApi } from '../../services/api';
import { useLanguage } from './ruyiDevice/languageSupport/LanguageContext';
import { I18nUtils } from './ruyiDevice/languageSupport/i18nUtils';
import './styles/auth.css';

interface LoginFormValues {
  username?: string;
  password?: string;
  remember?: boolean;
}

interface LoginProps {
  setIsLoggedIn: (isLoggedIn: boolean) => void;
}

const Login: React.FC<LoginProps> = ({ setIsLoggedIn }) => {
  const navigate = useNavigate();
  const [form] = Form.useForm<LoginFormValues>();
  const { language } = useLanguage();
  const [emailVerificationEnabled, setEmailVerificationEnabled] = useState(true); // 默认启用邮箱

  useEffect(() => {
    // 从 localStorage 获取记住的登录信息
    const remembered = localStorage.getItem('rememberedLogin');
    if (remembered) {
      const { username, password } = JSON.parse(remembered);
      form.setFieldsValue({ username, password, remember: true });
    }
    
    // 检查服务器状态以确定是否启用邮箱验证
    const checkServerStatus = async () => {
      try {
        const statusResponse = await userApi.status();
        if (statusResponse.success && statusResponse.data) {
          setEmailVerificationEnabled(statusResponse.data.email_verification || false);
        }
      } catch (error) {
        console.warn('Failed to check server status for email verification:', error);
        // 发生错误时保持默认值 true
      }
    };

    // 组件加载时检查 Flask 服务器
    const checkFlaskServer = async () => {
      try {
        const result = await window.electronAPI.checkAndStartFlaskServer();
        if (!result.success) {
          message.error(I18nUtils.getText('backendStartFailed') + result.message);
        }
      } catch (error: any) {
        message.error(I18nUtils.getText('checkBackendFailed') + error.message);
      }
    };

    checkServerStatus();
    checkFlaskServer();
  }, []);

  useEffect(() => {
    // 检查是否已经登录
    const checkLoginStatus = async () => {
      try {
        const userInfoResponse = await userApi.getUserInfo();
        if (userInfoResponse.success && userInfoResponse.data) {
          const userInfo = userInfoResponse.data;

          const tokenResponse = await tokenApi.getApiKey();
          await window.electronAPI.setApiKeyConfig(tokenResponse.data[0].key);
          
          await window.electronAPI.setCurrentUser({
            username: userInfo.username,
            displayName: userInfo.display_name,
            email: userInfo.email,
            uuid: userInfo.id
          });
          console.log('自动登录成功，当前用户已设置:', userInfo.username);

          setIsLoggedIn(true);
          navigate('/dashboard');
        }
      } catch (error) {
        // 未登录状态，继续显示登录页面
        console.log('自动登录检查失败:', error);
      }
    };
    checkLoginStatus();
  }, [navigate, setIsLoggedIn]); // 添加 setIsLoggedIn 到依赖数组

  const onFinish = async (values: LoginFormValues) => {
    try {
      const response = await userApi.login(values.username!, values.password!); // 使用非空断言
      console.log("login response:", response);
      if (response.success) {
        // 如果勾选了记住密码
        if (values.remember) {
          localStorage.setItem('rememberedLogin', JSON.stringify({
            username: values.username,
            password: values.password
          }));
        } else {
          localStorage.removeItem('rememberedLogin');
        }
        message.success(I18nUtils.getText('loginSuccess'));
        const tokenResponse = await tokenApi.getApiKey();
        await window.electronAPI.setApiKeyConfig(tokenResponse.data[0].key);
        
        // 获取用户信息并设置当前用户
        try {
          const userInfoResponse = await userApi.getUserInfo();
          const userInfo = userInfoResponse.data;
          await window.electronAPI.setCurrentUser({
            username: userInfo.username,
            displayName: userInfo.display_name,
            email: userInfo.email,
            uuid: userInfo.id
          });
          console.log('当前用户已设置:', userInfo.username);
        } catch (error) {
          console.warn('设置当前用户失败:', error);
          // 不阻断登录流程
        }
        
        setIsLoggedIn(true);
        navigate('/dashboard');
      } else {
        message.error(response.message || I18nUtils.getText('loginFailed'));
      }
    } catch (error: any) {
      message.error(error.message || I18nUtils.getText('loginFailed'));
    }
  };

  return (
    <div className="auth-container">
      <Card className="auth-card" title={I18nUtils.getText('loginTitle')}>
        <Form
          form={form}
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          layout="vertical"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: I18nUtils.getText('usernameRequired') }]}
          >
            <Input 
              className="auth-input" 
              prefix={<UserOutlined />} 
              placeholder={emailVerificationEnabled ? I18nUtils.getText('usernamePlaceholder') : I18nUtils.getText('usernameOnlyPlaceholder')} 
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: I18nUtils.getText('passwordRequired') }]}
          >
            <Input.Password className="auth-input" prefix={<LockOutlined />} placeholder={I18nUtils.getText('passwordPlaceholder')} />
          </Form.Item>

          <Form.Item name="remember" valuePropName="checked">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Checkbox>{I18nUtils.getText('rememberMe')}</Checkbox>
              {emailVerificationEnabled && (
                <Link className="auth-link" to="/reset-password">{I18nUtils.getText('forgotPassword')}</Link>
              )}
            </div>
          </Form.Item>

          <Form.Item>
            <Button className="auth-button" type="primary" htmlType="submit" block>
              {I18nUtils.getText('loginButton')}
            </Button>
          </Form.Item>

          <div style={{ textAlign: 'center' }}>
            {I18nUtils.getText('noAccount')} <Link className="auth-link" to="/register">{I18nUtils.getText('registerNow')}</Link>
            {emailVerificationEnabled && (
              <>
                <span style={{ margin: '0 8px' }}>|</span>
                <Link className="auth-link" to="/reset-password">{I18nUtils.getText('forgotPassword')}</Link>
              </>
            )}
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default Login;