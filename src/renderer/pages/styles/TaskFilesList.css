.task-files-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); /* 现代化的渐变背景 */
  border: none;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1); /* 内阴影效果 */
  position: relative;
}

/* 添加微妙的背景纹理 */
.task-files-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.task-files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px 0 20px; /* 增加内边距 */
  border-bottom: 1px solid rgba(148, 163, 184, 0.1); /* 更柔和的边框 */
  flex-shrink: 0;
  font-size: 14px;
  font-weight: 600;
  min-height: unset;
  height: 40px; /* 稍微增加高度 */
  text-transform: uppercase;
  letter-spacing: 0.6px;
  color: #64748b; /* 更现代的灰色 */
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); /* 现代化的渐变背景 */
  backdrop-filter: blur(10px); /* 毛玻璃效果 */
  position: relative;
  z-index: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* 微妙的阴影 */
}

.task-files-title {
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
}

.task-files-title .anticon {
  margin-right: 8px;
  font-size: 16px;
  color: #6366f1; /* 现代化的紫色 */
  filter: drop-shadow(0 1px 2px rgba(99, 102, 241, 0.1)); /* 图标阴影 */
  transition: all 0.2s ease;
}

.task-files-title:hover .anticon {
  transform: scale(1.05);
  filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.2));
}

.task-files-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden; /* 防止水平滚动 */
  padding: 8px 0; /* 添加垂直内边距 */
  background: transparent;
  position: relative;
  z-index: 1;
}

/* 按钮组样式改进 */
.task-files-header > div:last-child {
  display: flex !important;
  gap: 4px !important; /* 增加按钮间距 */
  align-items: center;
  padding: 0;
  min-height: 28px; /* 增加高度 */
  overflow: visible;
}

.task-files-header .ant-btn {
  height: 28px; /* 增加按钮高度 */
  border-radius: 6px; /* 更圆润的圆角 */
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); /* 更平滑的过渡 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 微妙的阴影 */
  border: 1px solid rgba(148, 163, 184, 0.1); /* 柔和的边框 */
  position: relative;
  margin: 0;
  z-index: 1;
  padding: 0 8px; /* 增加内边距 */
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); /* 渐变背景 */
  color: #64748b;
  backdrop-filter: blur(10px);
}

.task-files-header .ant-btn:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: rgba(99, 102, 241, 0.2);
  color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.task-files-header .ant-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-files-header .ant-btn.ant-btn-primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
  border: 1px solid rgba(99, 102, 241, 0.2) !important;
  color: #ffffff !important;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.task-files-header .ant-btn.ant-btn-primary:hover {
  background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%) !important;
  border: 1px solid rgba(99, 102, 241, 0.3) !important;
  color: #ffffff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
}

.task-files-header .ant-btn-icon-only {
  width: 28px;
  padding: 0;
  margin: 0;
  min-width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-file-item {
  cursor: pointer;
  margin-bottom: 0;
  padding: 6px 12px !important;
  transition: all 0.1s ease;
  border: none;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  min-height: 32px;
  border-bottom: 1px solid transparent;
  user-select: text; /* 允许文件列表项的文本选择 */
  -webkit-user-select: text; /* Safari 支持 */
  -moz-user-select: text; /* Firefox 支持 */
  -ms-user-select: text; /* IE/Edge 支持 */
}

.task-file-item:hover {
  background: #f0f0f0;
  border-bottom-color: transparent;
}

.task-file-item.selected {
  background: #e7f3ff;
  border-bottom-color: transparent;
}

.task-file-item.selected:hover {
  background: #e7f3ff;
}

.task-file-item.selected .task-file-name {
  color: #1890ff;
  font-weight: 500;
}

.task-file-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  flex: 1;
  min-width: 0;
}

.task-file-name {
  font-weight: 400;
  color: #374151;
  display: block;
  transition: all 0.1s ease;
  font-size: 13px;
  line-height: 1.2;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-file-pinned-icon {
  color: #1890ff;
  font-size: 11px;
  flex-shrink: 0;
  opacity: 0.8;
  transition: opacity 0.3s ease-in-out 0.15s;
}

.task-file-item:hover .task-file-pinned-icon {
  opacity: 0;
  transition: opacity 0.1s ease-in-out;
}

.task-file-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.task-file-description > div {
  margin-bottom: 2px;
}

/* 空状态样式 */
.no-task-selected {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 120px;
  color: #94a3b8;
  text-align: center;
  background: rgba(255, 255, 255, 0.6);
  margin: 16px;
  border: 2px dashed rgba(148, 163, 184, 0.3);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 13px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.no-task-selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
  pointer-events: none;
}

.no-task-selected:hover {
  border-color: rgba(99, 102, 241, 0.4);
  background: rgba(255, 255, 255, 0.8);
  color: #6366f1;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.1);
}

.no-task-selected p {
  margin: 0;
  font-size: 13px;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.task-file-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
  width: 0;
  opacity: 0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  justify-content: flex-end;
}

.task-file-item:hover .task-file-actions {
  width: 90px;
  opacity: 1;
}

.task-file-delete-btn,
.task-file-view-btn,
.task-file-pin-btn {
  opacity: 1;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 6px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.1);
  backdrop-filter: blur(10px);
}

.task-file-pin-btn.pinned {
  opacity: 1 !important;
  color: #6366f1;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-color: rgba(99, 102, 241, 0.2);
}

.task-file-delete-btn:hover {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

.task-file-view-btn:hover {
  color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.2);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.task-file-pin-btn:hover {
  color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.2);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.task-file-pin-btn.pinned:hover {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

/* 创建任务表单样式 */
.create-task-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.create-task-form .form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.create-task-form .form-item label {
  font-weight: 600;
  color: #475569;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 自定义滚动条 */
.task-files-content::-webkit-scrollbar {
  width: 8px;
}

.task-files-content::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
  margin: 4px 0;
}

.task-files-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(148, 163, 184, 0.3) 0%, rgba(148, 163, 184, 0.5) 100%);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.task-files-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.4) 0%, rgba(139, 92, 246, 0.4) 100%);
}

/* 空状态样式 */
.ant-list-empty-text {
  color: #94a3b8;
  font-size: 13px;
  padding: 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  margin: 16px;
  backdrop-filter: blur(10px);
}

/* 树形文件列表样式 */
.task-file-tree {
  background: transparent;
  width: 100%;
  font-size: 13px;
  padding: 0 8px; /* 添加水平内边距 */
}

/* 确保整个树结构占满宽度 */
.task-file-tree .ant-tree-list {
  width: 100%;
}

.task-file-tree .ant-tree-list-holder {
  width: 100%;
}

.task-file-tree .ant-tree-list-holder-inner {
  width: 100%;
}

.task-file-tree .ant-tree-node-content-wrapper {
  padding: 0;
  margin: 0;
  width: 100%;
  display: block;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 6px; /* 圆角 */
}

.task-file-tree .ant-tree-node-content-wrapper:hover {
  background-color: transparent;
}

.task-file-tree .ant-tree-node-selected .ant-tree-node-content-wrapper {
  background-color: transparent;
}

.task-file-tree .ant-tree-treenode {
  padding: 0;
  margin: 2px 0; /* 增加垂直间距 */
  width: 100%;
  height: 28px; /* 增加高度 */
}

/* 为子节点添加左边距以显示层级关系 */
.task-file-tree .ant-tree-treenode[data-level="0"] {
  margin-left: 0px;
  width: 100%;
}

.task-file-tree .ant-tree-treenode[data-level="1"] {
  margin-left: 20px; /* 调整缩进 */
  width: calc(100% - 20px);
}

.task-file-tree .ant-tree-treenode[data-level="2"] {
  margin-left: 40px; /* 调整缩进 */
  width: calc(100% - 40px);
}

.task-file-tree .ant-tree-treenode[data-level="3"] {
  margin-left: 60px; /* 调整缩进 */
  width: calc(100% - 60px);
}

.task-file-tree-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.task-file-tree-item:hover {
  background: rgba(99, 102, 241, 0.05);
  transform: translateX(2px);
}

.task-file-tree-item.selected {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-left: 3px solid #6366f1;
}

/* 当前编辑文件样式 */
.task-file-tree-item.editing {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(24, 144, 255, 0.1) 100%);
  border-left: 3px solid #1890ff;
  animation: editingPulse 2s infinite;
}

@keyframes editingPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.task-file-tree-item.selected .task-file-name {
  color: #6366f1;
  font-weight: 500;
}

.task-file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  height: 20px; /* 固定高度确保对齐 */
  position: relative; /* 为绝对定位的指示器提供参考 */
  padding-right: 20px; /* 为指示器留出空间 */
}

.task-file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.task-file-name {
  flex: 1; /* 允许增长以占用可用空间 */
  max-width: 200px; /* 设置最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
  color: #374151;
  transition: all 0.2s ease;
  line-height: 20px; /* 与容器高度一致 */
  min-width: 0; /* 允许文本收缩 */
}

.task-file-tree-item:hover .task-file-icon {
  transform: scale(1.1);
}

/* Tag样式优化 */
.task-file-info .ant-tag {
  margin: 0;
  padding: 0 6px;
  height: 18px;
  line-height: 16px;
  font-size: 11px;
  border-radius: 3px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

/* 置顶图标样式 */
.task-file-pinned-icon {
  color: #fa8c16;
  font-size: 12px;
  flex-shrink: 0;
  margin-left: 4px;
}

/* 自动保存状态指示器样式 */
.auto-save-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 12px;
  flex-shrink: 0;
  position: absolute; /* 绝对定位 */
  right: 0; /* 固定在右侧 */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%); /* 精确居中 */
  z-index: 1; /* 确保在其他元素之上 */
}

.auto-save-indicator.saving {
  color: #d46b08;
}

.auto-save-indicator.saving.active {
  animation: savingSpin 1s linear infinite;
}

.auto-save-indicator.syncing {
  color: #1890ff;
}

.auto-save-indicator.syncing.active {
  animation: syncingPulse 1.5s ease-in-out infinite;
}

.auto-save-indicator.completed {
  color: #52c41a;
}

.auto-save-indicator.completed.active {
  animation: completedBounce 0.6s ease;
}

.auto-save-indicator.error {
  color: #ff4d4f;
}

.auto-save-indicator.error.active {
  animation: errorShake 0.5s ease;
}

/* 非活跃状态的指示器样式 */
.auto-save-indicator.inactive {
  opacity: 0.3;
  filter: grayscale(1);
}

@keyframes savingSpin {
  from {
    transform: translateY(-50%) rotate(0deg);
  }
  to {
    transform: translateY(-50%) rotate(360deg);
  }
}

@keyframes syncingPulse {
  0%, 100% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-50%) scale(1.2);
    opacity: 0.8;
  }
}

@keyframes completedBounce {
  0% {
    transform: translateY(-50%) scale(0.8);
  }
  50% {
    transform: translateY(-50%) scale(1.2);
  }
  100% {
    transform: translateY(-50%) scale(1);
  }
}

@keyframes errorShake {
  0%, 100% {
    transform: translateY(-50%) translateX(0);
  }
  25% {
    transform: translateY(-50%) translateX(-2px);
  }
  75% {
    transform: translateY(-50%) translateX(2px);
  }
}

/* Tag样式调整 */
.task-file-tree-item .ant-tag {
  margin: 0;
  padding: 2px 8px;
  height: 20px;
  line-height: 16px;
  font-size: 11px;
  border-radius: 10px;
  flex-shrink: 0;
  margin-left: auto;
  border: 1px solid rgba(99, 102, 241, 0.2);
  font-weight: 500;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.task-file-tree-item .ant-tag:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.15) 0%, rgba(139, 92, 246, 0.15) 100%);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

/* 创建表单样式 */
.create-file-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.create-file-form .form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.create-file-form .form-item label {
  font-weight: 500;
  color: #475569;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-files-header {
    padding: 0 8px 0 12px;
  }
  
  .task-files-title span {
    font-size: 10px;
  }
  
  .task-file-tree-item {
    padding: 0 6px;
    min-height: 24px;
  }
  
  .task-file-name {
    font-size: 12px;
  }
  
  .task-file-icon {
    font-size: 14px;
  }
}

/* 加载状态样式 */
.task-files-content .ant-spin {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.task-files-content .ant-spin-dot {
  font-size: 24px;
}

.task-files-content .ant-spin-dot i {
  background-color: #6366f1;
}

/* Tag样式优化 */
.task-file-info .ant-tag {
  margin: 0;
  padding: 0 6px;
  height: 18px;
  line-height: 16px;
  font-size: 11px;
  border-radius: 3px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

/* 置顶图标样式 */
.task-file-pinned-icon {
  color: #fa8c16;
  font-size: 12px;
  flex-shrink: 0;
  margin-left: 4px;
}

/* 隐藏树形组件的展开/收起箭头 */
.task-file-tree .ant-tree-switcher {
  display: none !important;
}

/* 调整树节点的缩进 */
.task-file-tree .ant-tree-treenode {
  padding-left: 0 !important;
}

/* Optimize rendering during dragging by reducing expensive effects */
.task-files-list.dragging {
  backdrop-filter: none !important;
}

.task-files-list.dragging .task-files-header {
  backdrop-filter: none !important;
  box-shadow: none !important;
}

.task-files-list.dragging .task-file-tree-item {
  transition: none !important;
  transform: none !important;
}

.task-files-list.dragging .task-file-tree-item:hover {
  transform: none !important;
}