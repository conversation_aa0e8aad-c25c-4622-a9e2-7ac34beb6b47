.annotation-modal-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.annotation-modal-title-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 帮助图标样式 */
.help-icon-container {
  margin-left: 16px;
  display: flex;
  align-items: center;
  height: 100%;
}

.help-icon-hover {
  transition: all 0.3s ease;
  color: #8c8c8c;
}

.help-icon-hover:hover {
  color: #1890ff !important;
  transform: scale(1.1);
}

/* 提示弹窗样式 */
.annotation-tip-tooltip {
  max-width: 600px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  padding: 0;
  color: #262626;
  font-size: 14px;
  line-height: 1.8;
  border: 1px solid #f0f0f0;
  animation: tooltipFadeIn 0.3s ease;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.annotation-tip-tooltip .tip-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #262626;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  border-radius: 12px 12px 0 0;
}

.annotation-tip-tooltip .tip-content {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 #f5f5f5;
}

.annotation-tip-tooltip .tip-content::-webkit-scrollbar {
  width: 6px;
}

.annotation-tip-tooltip .tip-content::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.annotation-tip-tooltip .tip-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.annotation-tip-tooltip .tip-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.annotation-tip-tooltip .tip-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: #1890ff;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.annotation-tip-tooltip .tip-section:hover {
  background: #f0f7ff;
  border-color: #91caff;
  transform: translateX(4px);
}

.annotation-tip-tooltip .tip-section:hover::before {
  opacity: 1;
}

.annotation-tip-tooltip .tip-section:last-child {
  margin-bottom: 0;
}

.annotation-tip-tooltip .tip-section-title {
  font-weight: 600;
  margin-bottom: 6px;
  color: #1890ff;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 4px;
  border-bottom: 1px dashed #e6f4ff;
}

.annotation-tip-tooltip .tip-section-content {
  color: #434343;
  font-size: 14px;
  line-height: 1.6;
}

.annotation-tip-tooltip .tip-section-content li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 10px;
  list-style-type: none;
  transition: all 0.3s ease;
}

.annotation-tip-tooltip .tip-section-content li:last-child {
  margin-bottom: 0;
}

.annotation-tip-tooltip .tip-section-content li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #1890ff;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.annotation-tip-tooltip .tip-section-content li:hover::before {
  transform: scale(1.2);
  opacity: 1;
}

.annotation-tip-tooltip .tip-icon {
  color: #1890ff;
  font-size: 18px;
  vertical-align: middle;
}

.annotation-tip-tooltip code {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 3px 8px;
  margin: 0 2px;
  font-size: 13px;
  color: #c41d7f;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  display: inline-block;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.annotation-tip-tooltip code:hover {
  background: #fff;
  color: #eb2f96;
  border-color: #ffa39e;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.annotation-tip-tooltip strong {
  color: #262626;
  font-weight: 600;
  background: linear-gradient(120deg, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0.1) 100%);
  padding: 0 4px;
  border-radius: 3px;
}

/* 添加标题和提示的分隔样式 */
.annotation-modal-title-divider {
  width: 1px;
  height: 16px;
  background: #f0f0f0;
  margin: 0 12px;
}
/* 覆盖antd tooltip内部文本颜色与背景颜色 */
.annotation-tip-tooltip .ant-tooltip-inner {
  color: #262626;
  background-color: #ffffff;
}

/* 修正 tooltip 箭头颜色，与提示窗口背景一致 */
.annotation-tip-tooltip .ant-tooltip-arrow::before {
  background-color: #ffffff !important;
  border-color: #e8e8e8 !important;
}

/* 标注面板提示词列表样式 */
.annotation-tip-tooltip-list {
  padding-left: 0;
  margin: 0;
}

.annotation-tip-tooltip-list li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 10px;
  list-style-type: none;
  transition: all 0.3s;
  cursor: default;
}

.annotation-tip-tooltip-list li:last-child {
  margin-bottom: 0;
}

.annotation-tip-tooltip-list li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #1890ff;
  opacity: 0.6;
  transition: background 0.3s, opacity 0.3s, transform 0.3s;
}

.annotation-tip-tooltip-list li:hover::before {
  opacity: 1;
  transform: scale(1.2);
}

/* 分组容器样式 */
.annotation-tip-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.annotation-tip-section:last-child {
  margin-bottom: 0;
}

/* 分组标题样式 */
.annotation-tip-section-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: #1890ff;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #e6f4ff;
}

.annotation-tip-section strong {
  color: #262626;
  font-weight: 600;
  background: linear-gradient(120deg, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0.1) 100%);
  padding: 0 4px;
  border-radius: 3px;
}

.annotation-add-desc-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border: none;
  background: none;
  color: #1890ff;
  font-size: 13px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background 0.2s;
}
.annotation-add-desc-btn:hover {
  background: #e6f4ff;
}

