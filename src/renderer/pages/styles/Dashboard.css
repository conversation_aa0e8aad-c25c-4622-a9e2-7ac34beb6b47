:root {
    --main-bg-color: hsl(0, 0%, 100%);
    --stream-bg-color: hsl(0, 0%, 85%);
    --shell-bg-color: hsl(0, 0%, 0%);
    --text-shadow-color: hsl(218, 67%, 95%);
    --header-bg-color: hsl(0, 0%, 95%);
    --controls-bg-color: hsla(0, 0%, 95%, 0.8);
    --control-buttons-bg-color: hsl(0, 0%, 95%);
    --text-color: hsl(210, 16%, 22%);
    --text-color-light: hsl(200, 16%, 52%);
    --link-color: hsl(218, 85%, 43%);
    --link-color-light: hsl(218, 85%, 73%);
    --link-color_visited: hsl(271, 68%, 32%);
    --link-color_visited-light: hsl(271, 68%, 72%);
    --svg-checkbox-bg-color: hsl(172, 100%, 37%);
    --svg-button-fill: hsl(199, 17%, 46%);
    --kill-button-hover-color: hsl(342, 100%, 37%);
    --url-color: hsl(0, 0%, 60%);
    --button-text-color: hsl(214, 82%, 51%);
    --button-border-color: hsl(0, 0%, 70%);
    --progress-background-color: hsla(225, 100%, 50%, 0.2);
    --progress-background-error-color: hsla(0, 100%, 50%, 0.2);
    --font-size: 14px;
}

html {
    font-size: var(--font-size);
}

a {
    color: var(--link-color);
}

a:visited {
    color: var(--link-color_visited);
}

body {
    color: var(--text-color);
    background-color: var(--main-bg-color);
    position: absolute;
    margin: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
    user-select: none; /* 禁止全局文本选择 */
    -webkit-user-select: none; /* Safari 支持 */
    -moz-user-select: none; /* Firefox 支持 */
    -ms-user-select: none; /* IE/Edge 支持 */
}


body.shell {
    background-color: var(--shell-bg-color);
}

body.stream {
    background-color: var(--stream-bg-color);
}

.terminal-container {
    width: 100%;
    height: 100%;
    padding: 5px;
}

:focus {
    outline: none;
}

.flex-center {
    display: flex;
    align-items: center;
}

.wait {
    cursor: wait;
}

.video-layer {
    position: absolute;
    z-index: 0;
}

.touch-layer {
    position: absolute;
    z-index: 1;
}

.video {
    float: right;
    max-height: 100%;
    max-width: 100%;
    background-color: #000000;
}


.control-buttons-list {
    float: right;
    width: 3.715rem;
    background-color: var(--control-buttons-bg-color);
}

.control-button {
    margin: .357rem .786rem;
    padding: 0;
    width: 2.143rem;
    height: 2.143rem;
    border: none;
    opacity: 0.75;
    background-color: var(--control-buttons-bg-color);
}

.control-button:hover {
    opacity: 1;
}

.control-wrapper > input[type=checkbox] {
    display: none;
}

.control-wrapper > label {
    display: inline-block;
}

.control-button > svg {
    fill: var(--svg-button-fill);
}

.control-wrapper > input[type=checkbox].two-images:checked + label > svg.image-on {
    display: block;
}

.control-wrapper > input[type=checkbox].two-images:not(:checked) + label > svg.image-on {
    display: none;
}

.control-wrapper > input[type=checkbox].two-images:checked + label > svg.image-off {
    display: none;
}

.control-wrapper > input[type=checkbox].two-images:not(:checked) + label > svg.image-off {
    display: block;
}

.control-wrapper > input[type=checkbox]:checked + label > svg {
    fill: var(--svg-checkbox-bg-color);
}



.task-input {
    width: 25%;
}

/* 主容器样式 */
.main-container {
    position: absolute;
    left: 0;
    top: 40px; /* 更新为新的顶栏高度 */
    width: 100%;
    height: calc(100vh - 40px); /* 更新高度计算 */
    padding: 0;
    /* gap: 5px; */
    z-index: 2;
    /* 添加安全区域支持 */
    padding-top: env(safe-area-inset-top, 0);
}

/* 左侧容器样式 */
.left-container {
    display: block; /* Changed from flex to block for absolute positioning */
    height: 100%;
    padding-left: 0;
    padding-right: 0;
    /* 使用现代化的分割线替代实线边框 */
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.08);
    /* 使用现代化的渐变背景，与middle-container保持一致 */
    background: #f1f5f9;
    position: relative;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 添加微妙的背景纹理 */
.left-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

/* 任务文件列表容器 */
.task-files-container {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    /* Use absolute positioning to fix to top */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    /* 悬浮卡片效果 */
    background: #ffffff;
    border-radius: 12px; /* 更圆润的圆角 */
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(255, 255, 255, 0.05); /* 多层阴影创造悬浮效果 */
    margin: 8px; /* 添加外边距，创造悬浮效果 */
    z-index: 1;
    backdrop-filter: blur(10px); /* 毛玻璃效果 */
    border: 1px solid rgba(255, 255, 255, 0.2); /* 微妙的边框 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 平滑过渡 */
    overflow: hidden; /* 确保内容不溢出 */
}

/* 任务文件列表的悬浮效果增强 */
.task-files-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 12px;
    pointer-events: none;
    z-index: -1;
}

/* Optimize task files container during dragging */
.task-files-container.dragging {
    backdrop-filter: none !important;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    transition: none !important;
}

.task-files-container.dragging::before {
    display: none !important;
}

/* 聊天容器 */
.chat-container {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    /* Use absolute positioning to fix to bottom */
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    /* 悬浮卡片效果 */
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px; /* 更圆润的圆角 */
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(255, 255, 255, 0.05); /* 多层阴影创造悬浮效果 */
    margin: 8px; /* 添加外边距，创造悬浮效果 */
    z-index: 1;
    backdrop-filter: blur(10px); /* 毛玻璃效果 */
    border: 1px solid rgba(255, 255, 255, 0.2); /* 微妙的边框 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 平滑过渡 */
    overflow: hidden; /* 确保内容不溢出 */
}

/* 聊天容器的悬浮效果增强 */
.chat-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 12px;
    pointer-events: none;
    z-index: -1;
}

/* 右侧容器样式 */
.right-container {
    width: 100%;
    height: calc(100vh - 40px); /* 更新高度计算：只减去顶栏高度 */
    display: flex;
    flex-direction: column;
    /* 使用现代化的分割线替代实线边框 */
    position: relative;
    /* 使用现代化的渐变背景，与其他容器保持一致 */
    background: #f1f5f9;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    padding: 8px; /* 添加内边距，为卡片效果创造空间 */
    gap: 8px; /* 子元素之间的间距 */

}

/* 右侧容器的子元素卡片样式 */
.right-container > * {
    /* 悬浮卡片效果 */
    background: transparent;
    border-radius: 12px;
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 右侧容器子元素的悬浮效果增强 */
.right-container > *::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 12px;
    pointer-events: none;
    z-index: -1;
}

/* 右侧容器子元素的悬停效果 */


.phone-container-wrapper {
    width: 100%;
    height: calc(100% - 144px); /* Account for DeviceState component height */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 8px;
    background-color: transparent;
    border-radius: 12px;
    overflow: hidden;
}

.phone-container {
    width: 100%;
    height: 100%;
    display: block;
    border: none;
    background-color: transparent;
    border-radius: 12px;
    overflow: hidden;
}

/* 控制按钮容器样式 */
.control-buttons-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    /* gap: 10px; */
    gap: clamp(2px, 1vw, 10px);
    padding: 10px 0;
    /* background-color: var(--control-buttons-bg-color); */
    /* border-radius: 5px; */
    justify-content: space-between;
    /* 使用现代化的分割线替代实线边框 */
    position: relative;
    min-height: unset;
    height: 0; /* 隐藏工具栏容器 */
    flex-shrink: 0;
    align-items: center;
    overflow: hidden; /* 确保内容不可见 */
    display: none; /* 完全隐藏容器 */
}

/* 控制按钮容器的现代化分割线 */
.control-buttons-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
        to right,
        transparent 0%,
        rgba(179, 179, 179, 0.1) 10%,
        rgba(179, 179, 179, 0.2) 50%,
        rgba(179, 179, 179, 0.1) 90%,
        transparent 100%
    );
    pointer-events: none;
}

/* 代码编辑器容器样式 */
.code-editor-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 重要：允许容器缩小 */
    margin: 8px; /* 添加外边距，创造悬浮效果 */
    height: calc(100% - 16px); /* 调整高度以适应外边距 */
    user-select: text; /* 允许代码编辑器中的文本选择 */
    -webkit-user-select: text; /* Safari 支持 */
    -moz-user-select: text; /* Firefox 支持 */
    -ms-user-select: text; /* IE/Edge 支持 */
    /* 悬浮卡片效果 */
    background: #ffffff;
    border-radius: 12px; /* 更圆润的圆角 */
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(255, 255, 255, 0.05); /* 多层阴影创造悬浮效果 */
    overflow: hidden; /* 确保内容不溢出 */
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px); /* 毛玻璃效果 */
    border: 1px solid rgba(255, 255, 255, 0.2); /* 微妙的边框 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 平滑过渡 */
}

/* 代码编辑器容器的悬浮效果增强 */
.code-editor-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 12px;
    pointer-events: none;
    z-index: -1;
}

/* 调整任务输入框样式 */
.task-input {
    flex: 1;
    margin-bottom: 0;
    min-width: 0;
}

.task-label {
    flex: 0 0 auto;
    margin-bottom: 0;
    white-space: nowrap;
}

/* 调整按钮样式 */
.demo-button,
.executor-button,
.optimizer-button,
.change-language-button,
.app-knowledge-button {
    /* flex: 1; */
    /* flex: 0 1 auto; */
    /* flex: 1 0 auto; */
    flex: 1 1 0;
    padding: 8px 15px;
    margin: 0;
    background-color: var(--button-text-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    min-width: 0;
}

.demo-button:hover,
.executor-button:hover,
.optimizer-button:hover,
.change-language-button:hover,
.app-knowledge-button:hover {
    opacity: 0.9;
}

/* 通用输入元素文本选择样式 */
input, textarea, select {
    user-select: text !important; /* 允许输入框的文本选择 */
    -webkit-user-select: text !important; /* Safari 支持 */
    -moz-user-select: text !important; /* Firefox 支持 */
    -ms-user-select: text !important; /* IE/Edge 支持 */
}

/* Ant Design 组件文本选择样式 */
.ant-input, .ant-textarea, .ant-select-selector {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
}

/* 模态框和对话框文本选择样式 */
.ant-modal, .ant-drawer, .ant-popover {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
}

.ant-modal-content, .ant-drawer-content, .ant-popover-content {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
}

/* 对话框内容区域文本选择样式 */
.dialog-body, .dialog-content {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
}

/* 中间容器样式 */
.middle-container {
    /* width: 100%; */
    height: calc(100vh - 40px); /* 更新高度计算：只减去顶栏高度，因为移除了工具栏 */
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0; /* 减少间距，让标题栏和编辑器更紧密 */
    background: #f1f5f9;
    border: none;
    overflow: hidden; /* 确保内容不溢出 */
    position: relative;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 系统通知弹窗样式 */
.ant-modal.server-notice-modal .ant-modal-content {
    border-radius: 16px;
    box-shadow: 
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ant-modal.server-notice-modal .ant-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px 16px 0 0;
    padding: 20px 24px;
    border-bottom: none;
}

.ant-modal.server-notice-modal .ant-modal-title {
    color: white;
    font-weight: 600;
    font-size: 18px;
    margin: 0;
}

.ant-modal.server-notice-modal .ant-modal-close {
    color: white;
    top: 20px;
    right: 20px;
}

.ant-modal.server-notice-modal .ant-modal-close:hover {
    color: rgba(255, 255, 255, 0.8);
}

.ant-modal.server-notice-modal .ant-modal-body {
    padding: 24px;
    font-size: 15px;
    line-height: 1.6;
    color: #374151;
    background: #ffffff;
}

.ant-modal.server-notice-modal .ant-modal-body > div {
    margin-bottom: 12px;
}

.ant-modal.server-notice-modal .ant-modal-body > div:last-child {
    margin-bottom: 0;
}

.ant-modal.server-notice-modal .ant-modal-footer {
    padding: 16px 24px 24px;
    border-top: 1px solid #f3f4f6;
    background: #ffffff;
    border-radius: 0 0 16px 16px;
    text-align: center;
}

.ant-modal.server-notice-modal .ant-modal-footer .ant-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    height: 40px;
    padding: 0 32px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 15px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ant-modal.server-notice-modal .ant-modal-footer .ant-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.ant-modal.server-notice-modal .ant-modal-footer .ant-btn:active {
    transform: translateY(0);
}

/* 遮罩层样式 */
.ant-modal-mask.server-notice-mask {
    background: rgba(0, 0, 0, 0.65);
    backdrop-filter: blur(8px);
}

/* 设备断连遮罩样式 */
.device-disconnected-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
    border-radius: 8px;
}

.device-disconnected-content {
    text-align: center;
    color: white;
    padding: 2rem;
    max-width: 300px;
}

.device-disconnected-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.device-disconnected-content h3 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 0.8rem;
    font-weight: 600;
}

.device-disconnected-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1.5rem;
}

.device-disconnected-actions {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.reconnect-device-btn, .select-other-device-btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reconnect-device-btn {
    background: #1890ff;
    color: white;
}

.reconnect-device-btn:hover {
    background: #40a9ff;
    transform: translateY(-1px);
}

.select-other-device-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.select-other-device-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* 重连状态样式 */
.device-reconnecting-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
    animation: reconnectPulse 2s ease-in-out infinite;
}

@keyframes reconnectPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

.reconnecting-spinner {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
}

.spinner {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}