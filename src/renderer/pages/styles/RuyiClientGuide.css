.installation-container {
  text-align: center;
  padding: 20px;
}

.installation-title {
  color: #1890ff;
  margin-bottom: 16px;
}

.installation-text {
  color: #666;
  margin-bottom: 12px;
}

.installation-required {
  color: #ff4d4f;
  font-weight: 500;
}

.installation-status {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 12px;
  margin-top: 16px;
}

.installation-status-text {
  color: #52c41a;
  margin-bottom: 8px;
}

.guide-container {
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.guide-title {
  color: #1890ff;
  margin-bottom: 20px;
  font-size: 20px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.guide-desc {
  margin-bottom: 16px;
}

.guide-desc ul {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 16px;
  line-height: 1.6;
  font-size: 16px;
}

.guide-desc li {
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
  position: relative;
  padding-left: 28px;
  transition: all 0.3s ease;
}

.guide-desc li:hover {
  color: #1890ff;
  /* transform: translateX(4px); */
}

.guide-desc li::before {
  content: "";
  position: absolute;
  left: 8px;
  top: 10px;
  width: 8px;
  height: 8px;
  background-color: #1890ff;
  border-radius: 50%;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.guide-desc li:hover::before {
  transform: scale(1.2);
  opacity: 1;
  background-color: #1890ff;
}

.guide-note {
  margin: 16px 0;
  color: #ff4d4f;
  background-color: #fff1f0;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #ff4d4f;
  font-weight: 500;
}

.permissions-section {
  margin-bottom: 24px;
  background-color: #fafafa;
  padding: 16px;
  border-radius: 8px;
}

.permissions-title {
  color: #52c41a;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.permissions-title::before {
  content: "";
  display: none;
}

.permissions-list {
  list-style: none;
  padding: 0;
}

.permission-item {
  margin-bottom: 12px;
  background-color: #fff;
  padding: 10px 14px;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

.permission-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.permission-label {
  background-color: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  color: #333;
}

.permission-arrow {
  color: #666;
  margin: 0 10px;
  font-weight: bold;
}

.permission-action {
  color: #1890ff;
  background-color: #e6f7ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.special-permissions {
  margin-left: 0;
}

.special-permission-title {
  font-weight: bold;
  margin: 16px 0 12px;
  padding-left: 12px;
  border-left: 3px solid #1890ff;
  color: #333;
}

.special-permission-steps {
  color: #555;
  padding-left: 24px;
  background-color: #fff;
  padding: 12px 16px 12px 36px;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.special-permission-steps li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.special-permission-steps li:last-child {
  margin-bottom: 0;
}

/* 设备连接教程样式 */
.device-connection-guide-tooltip {
  max-width: 700px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  padding: 0;
  color: #262626;
  font-size: 14px;
  line-height: 1.8;
  border: 1px solid #f0f0f0;
  animation: tooltipFadeIn 0.3s ease;
}

.device-connection-guide-tooltip .guide-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #1890ff;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  border-radius: 12px 12px 0 0;
  transition: all 0.3s ease;
}

.device-connection-guide-tooltip .guide-title .guide-icon {
  font-size: 20px;
  color: #1890ff;
}

.device-connection-guide-tooltip .guide-content {
  padding: 24px;
  max-height: 80vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 #f5f5f5;
}

.device-connection-guide-tooltip .guide-content::-webkit-scrollbar {
  width: 6px;
}

.device-connection-guide-tooltip .guide-content::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.device-connection-guide-tooltip .guide-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.device-connection-guide-tooltip .guide-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.device-connection-guide-tooltip .guide-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: #1890ff;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.device-connection-guide-tooltip .guide-section:hover {
  background: #f0f7ff;
  border-color: #91caff;
  transform: translateX(4px);
}

.device-connection-guide-tooltip .guide-section:hover::before {
  opacity: 1;
}

.device-connection-guide-tooltip .guide-section:last-child {
  margin-bottom: 0;
}

.device-connection-guide-tooltip .guide-section-title {
  margin-bottom: 12px;
  padding-bottom: 8px;
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px dashed #e6f4ff;
}

.device-connection-guide-tooltip .guide-section-content {
  color: #434343;
  font-size: 14px;
  line-height: 1.6;
}

.device-connection-guide-tooltip .guide-steps {
  margin: 0;
  padding: 0;
  list-style: none;
}

.device-connection-guide-tooltip .guide-steps li {
  position: relative;
  padding-left: 24px;
  margin-bottom: 12px;
  list-style-type: none;
  transition: all 0.3s ease;
}

.device-connection-guide-tooltip .guide-steps li:last-child {
  margin-bottom: 0;
}

.device-connection-guide-tooltip .guide-steps li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #1890ff;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.device-connection-guide-tooltip .guide-steps li:hover::before {
  transform: scale(1.2);
  opacity: 1;
}

.device-connection-guide-tooltip .guide-steps.numbered-list {
  counter-reset: step-counter;
}

.device-connection-guide-tooltip .guide-steps.numbered-list li {
  counter-increment: step-counter;
}

.device-connection-guide-tooltip .guide-steps.numbered-list li::before {
  content: counter(step-counter) ".";
  width: auto;
  height: auto;
  background-color: transparent;
  border-radius: 0;
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
  top: 0;
  opacity: 1;
}

.device-connection-guide-tooltip .guide-icon {
  color: #1890ff;
  font-size: 20px;
  vertical-align: middle;
}

.device-connection-guide-tooltip .guide-sub-steps {
  margin: 8px 0 0 8px;
  padding: 12px 16px;
  background: #fff;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
}

.device-connection-guide-tooltip .guide-sub-section {
  margin: 16px 0 0 0;
  padding: 16px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.device-connection-guide-tooltip .guide-sub-section-title {
  font-weight: 600;
  color: #262626;
  font-size: 15px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px dashed #e6f4ff;
  padding-bottom: 8px;
}

.device-connection-guide-tooltip .guide-note {
  margin-top: 16px;
  padding: 12px 16px;
  background: #e6f4ff;
  border-radius: 6px;
  color: #1890ff;
  font-size: 14px;
  border: 1px solid #bae0ff;
}

.device-connection-guide-tooltip .guide-intro {
  margin-bottom: 16px;
  color: #262626;
  font-size: 14px;
}

.device-connection-guide-tooltip strong {
  color: #262626;
  font-weight: 600;
  background: linear-gradient(120deg, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0.1) 100%);
  padding: 0 4px;
  border-radius: 3px;
}

.device-connection-guide .help-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 12px;
  transition: all 0.3s ease;
}

.device-connection-guide .help-trigger:hover .help-icon-hover,
.device-connection-guide .help-trigger:hover .help-text {
  color: #1890ff;
  transform: translateY(-2px);
}

.device-connection-guide .help-text {
  color: #595959;
  font-size: 15px;
  transition: all 0.3s ease;
  line-height: 1;
}

.device-connection-guide .help-icon-hover {
  font-size: 16px;
  color: #595959;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}
