.modal-title-container {
  text-align: center;
  position: relative;
  padding-bottom: 16px;
}

.modal-title-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
      to right,
      transparent 0%,
      rgba(240, 240, 240, 0.3) 10%,
      rgba(240, 240, 240, 0.6) 50%,
      rgba(240, 240, 240, 0.3) 90%,
      transparent 100%
  );
  pointer-events: none;
}

.modal-title-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 8px;
  vertical-align: middle;
}

.modal-title {
  margin: 0;
  color: #1890ff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  text-align: center;
  padding: 20px 0;
  background: #fafafa;
  border-radius: 8px;
  margin: 0 0 20px 0;
}

.modal-description {
  font-size: 16px;
  margin-bottom: 24px;
  color: #262626;
  line-height: 1.8;
  padding: 0 24px;
}

.countdown-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #1890ff;
  font-size: 16px;
  font-weight: 500;
}

.countdown-icon {
  font-size: 20px;
}

.cancel-button {
  width: 150px;
  height: 40px;
  border-radius: 20px;
  background-color: #f5f5f5;
  border: none;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.device-list-container {
  padding: 0;
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: none;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  position: relative;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.device-list-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.03) 0%, transparent 50%);
  border-radius: 12px;
}

/* 设备列表容器的悬浮效果增强 */
.device-list-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 12px;
  pointer-events: none;
  z-index: -1;
}

.device-card {
  cursor: pointer;
  border-radius: 8px !important;
  border: 1px solid rgba(148, 163, 184, 0.1) !important;
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(10px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 90%;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.device-card:hover {
  background: rgba(255, 255, 255, 0.95) !important;
  border-color: rgba(99, 102, 241, 0.2) !important;
  transform: translateY(-1px);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.06);
}

.device-card .ant-card-body {
  padding: 16px;
  background: transparent;
  width: 100%;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  padding-right: 40px;
}

.device-card .ant-card-meta {
  display: flex;
  align-items: center;
}

.device-card .ant-card-meta-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 16px;
}

.device-card .ant-card-meta-title {
  margin-bottom: 4px !important;
  color: #374151;
  font-weight: 500;
}

.device-card .ant-card-meta-description p {
  margin-bottom: 2px;
  color: #64748b;
  font-size: 13px;
}

.device-card-connected {
  opacity: 1;
  border-color: rgba(34, 197, 94, 0.2) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(34, 197, 94, 0.1);
}

.device-card-connected:hover {
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(34, 197, 94, 0.15);
}

.device-card-disconnected {
  opacity: 0.8;
  border-color: rgba(239, 68, 68, 0.1) !important;
  background: rgba(255, 255, 255, 0.7) !important;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(239, 68, 68, 0.05);
}

.device-card-disconnected:hover {
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(239, 68, 68, 0.1);
}

.empty-container {
  margin-top: 40px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 设备连接教程样式 */
.device-connection-guide-drawer-btn {
  height: 28px;
  width: 28px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(148, 163, 184, 0.1);
  position: relative;
  margin: 0;
  z-index: 1;
  padding: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #64748b;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
}

.device-connection-guide-drawer-btn:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: rgba(99, 102, 241, 0.2);
  color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.device-connection-guide-drawer-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 设备号码样式 */
.device-number {
  margin-left: 8px;
  color: #6366f1;
  font-weight: 500;
  font-size: 11px;
  background: rgba(99, 102, 241, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(99, 102, 241, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.device-number:hover {
  background: rgba(99, 102, 241, 0.15);
  border-color: rgba(99, 102, 241, 0.3);
}

/* Drawer 内内容样式优化 */
.device-connection-guide-drawer .device-connection-guide-tooltip {
  max-width: 100%;
  border-radius: 0;
  box-shadow: none;
  border: none;
  padding: 0;
  background: #fff;
}

/* 设备连接指南Drawer样式优化 */
.device-connection-guide-drawer.ant-drawer-content-wrapper {
  z-index: 999 !important; /* 确保在navbar下方 */
  top: 40px !important; /* 设置顶部位置，避免与navbar重叠 */
  height: calc(100vh - 40px) !important; /* 调整高度，减去navbar高度 */
}

.device-connection-guide-drawer .ant-drawer-content {
  height: 100%; /* 确保内容区域占满整个drawer */
}

.device-connection-guide-drawer .ant-drawer-header {
  padding-top: 16px; /* 增加顶部内边距，确保关闭按钮不被遮挡 */
}

.device-connection-guide-drawer .ant-drawer-close {
  top: 16px !important; /* 调整关闭按钮位置，确保不被navbar遮挡 */
  right: 16px !important;
}

.device-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px 0 20px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  flex-shrink: 0;
  font-size: 11px;
  font-weight: 600;
  min-height: unset;
  height: 40px;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  color: #64748b;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 设备列表标题样式 */
.device-list-header .ant-typography {
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
  margin: 0;
  color: #64748b;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.6px;
}

/* 按钮组样式改进 */
.device-list-header > div:last-child {
  display: flex !important;
  gap: 4px !important;
  align-items: center;
  padding: 0;
  min-height: 28px;
  overflow: visible;
}

/* 添加设备按钮样式优化 */
.add-device-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 28px;
  width: 28px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(148, 163, 184, 0.1);
  position: relative;
  margin: 0;
  z-index: 1;
  padding: 0;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: #ffffff;
  backdrop-filter: blur(10px);
  min-width: 28px;
}

.add-device-button:hover {
  background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%);
  border-color: rgba(99, 102, 241, 0.3);
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
}

.add-device-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.device-type-card {
  text-align: center;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.device-type-card:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.device-type-icon {
  font-size: 32px;
  margin-bottom: 16px;
  color: #6366f1;
  transition: all 0.2s ease;
}

.device-type-card:hover .device-type-icon {
  transform: scale(1.1);
  color: #5855eb;
}

.device-type-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #374151;
}

.device-list-content::-webkit-scrollbar {
  width: 6px;
}

.device-list-content::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
}

.device-list-content::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.3);
  border-radius: 3px;
  transition: all 0.2s ease;
}

.device-list-content::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.5);
}

/* 空状态样式优化 */
.device-list-content .ant-empty {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  margin: 20px;
}

.device-list-content .ant-empty-description {
  color: #64748b;
  font-size: 14px;
}

/* 加载状态样式优化 */
.device-list-content .ant-spin {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.device-list-content .ant-spin-dot {
  font-size: 24px;
}

.device-list-content .ant-spin-dot i {
  background-color: #6366f1;
}

/* 确保Ant Design List组件不会产生水平滚动 */
.device-list-content .ant-list {
  width: 100%;
  overflow: hidden;
}

.device-list-content .ant-list-item {
  width: 100%;
  padding: 0 8px;
  box-sizing: border-box;
}

.device-list-content .ant-list-item .ant-card {
  width: 100%;
  max-width: none;
  margin: 0;
}

/* 移除原有的设备列表头部分割线，因为现在有边框了 */
.device-list-header::after {
  display: none;
}

.device-list-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px 0;
  background: transparent;
  position: relative;
  z-index: 1;
  width: 100%;
}

/* 标题图标样式 */
.device-list-header .ant-typography .anticon {
  filter: drop-shadow(0 1px 2px rgba(99, 102, 241, 0.1));
  transition: all 0.2s ease;
}

.device-list-header .ant-typography:hover .anticon {
  transform: scale(1.05);
  filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.2));
}

.device-list-header .ant-btn-primary:hover {
  background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%) !important;
  border: 1px solid rgba(99, 102, 241, 0.3) !important;
  color: #ffffff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
}

/* 设备删除按钮样式 */
.device-delete-button {
  opacity: 0;
  transition: all 0.2s ease;
  color: #ff4d4f;
  border: none;
  background: transparent;
  padding: 4px;
  border-radius: 4px;
  z-index: 10;
  position: absolute;
  top: 8px;
  right: 8px;
}

.device-delete-button:hover {
  opacity: 1 !important;
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  transform: scale(1.1);
}

/* 设备卡片悬停时显示删除按钮 */
.device-card:hover .device-delete-button {
  opacity: 0.7;
}

/* 确保删除按钮在卡片内部正确定位 */
.device-card {
  position: relative;
}

.device-card .ant-card-body {
  position: relative;
}