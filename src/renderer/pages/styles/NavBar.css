/* 导航栏基础样式 */
.nav-bar {
    width: 100%;
    height: 40px;
    background-color: #ffffff;
    border-bottom: 1px solid #e8eaed;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding-top: env(safe-area-inset-top, 0);
    position: relative;
    z-index: 1000;
    -webkit-app-region: drag;
    user-select: none;
}

.nav-bar-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 16px;
    position: relative;
    padding-left: 80px;
}

/* macOS特定样式 - 使用多种检测方法 */
@media screen and (-webkit-app-region: drag) {
    .nav-bar-content {
        padding-left: 80px; /* macOS窗口控制按钮区域 */
    }
}

/* 备用macOS检测方法 */
@media screen and (min-resolution: 2dppx) {
    .nav-bar-content {
        padding-left: 80px; /* macOS窗口控制按钮区域 */
    }
}

/* Windows/Linux样式 */
@media screen and (not (-webkit-app-region: drag)) and (max-resolution: 1.9dppx) {
    .nav-bar-content {
        padding-left: 16px; /* 其他平台不需要为窗口控制按钮留空间 */
    }
}

/* 移除logo相关样式 */
.nav-bar-logo {
    display: none;
}

/* 导航栏按钮容器 */
.nav-bar-actions-left {
    position: absolute;
    left: 16px;
    display: flex;
    gap: 8px;
    padding-left: 64px;
    -webkit-app-region: no-drag;
}

/* 搜索框容器 */
.nav-bar-search-container {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 400px; /* 固定搜索框宽度 */
    max-width: 40vw; /* 响应式最大宽度 */
    /* 确保搜索框在窗口控制按钮区域之外 */
    margin-left: 40px; /* 补偿左侧的padding */
    /* 禁用拖动，让搜索框可以交互 */
    -webkit-app-region: no-drag;
}

/* macOS特定样式 - 使用多种检测方法 */
@media screen and (-webkit-app-region: drag) {
    .nav-bar-search-container {
        margin-left: 40px; /* macOS窗口控制按钮区域补偿 */
    }
}

/* 备用macOS检测方法 */
@media screen and (min-resolution: 2dppx) {
    .nav-bar-search-container {
        margin-left: 40px; /* macOS窗口控制按钮区域补偿 */
    }
}

/* Windows/Linux样式 */
@media screen and (not (-webkit-app-region: drag)) and (max-resolution: 1.9dppx) {
    .nav-bar-search-container {
        margin-left: 0; /* 其他平台不需要补偿 */
    }
}

/* 导航栏按钮基础样式 */
.nav-bar-button {
    height: 32px;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #d9d9d9;
    background-color: #ffffff;
    color: #262626;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    /* 禁用拖动，让按钮可以点击 */
    -webkit-app-region: no-drag;
}

/* 按钮hover样式 */
.nav-bar-button:hover {
    color: #6366f1 !important;
    background: #ffffff !important;
    border-color: #6366f1 !important;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(99, 102, 241, 0.15);
}

/* 按钮active样式 */
.nav-bar-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
    background: #ffffff !important;
}

/* 按钮focus样式 */
.nav-bar-button:focus {
    outline: none;
    border-color: #6366f1 !important;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* 按钮图标样式 */
.nav-bar-button .anticon {
    font-size: 12px;
    color: inherit !important;
}

.nav-bar-button:hover .anticon {
    color: #6366f1 !important;
}

.nav-bar-button:active .anticon {
    color: #6366f1 !important;
}

.nav-bar-button:focus .anticon {
    color: #6366f1 !important;
}

/* 搜索框输入样式 */
.nav-bar-search-input {
    border-radius: 8px;
    border: 1px solid #e8eaed;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
    /* 禁用拖动，让输入框可以交互 */
    -webkit-app-region: no-drag;
}

.nav-bar-search-input:hover {
    border-color: #d9d9d9;
    background-color: #ffffff;
}

.nav-bar-search-input:focus,
.nav-bar-search-input.ant-input-focused {
    border-color: #6366f1;
    background-color: #ffffff;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

/* 导航栏标题容器样式 - 移除 */
.nav-bar-title-container {
    display: none;
}

/* 导航栏标题样式 - 移除 */
.nav-bar-title {
    display: none;
}

/* 导航栏任务标题样式 - 移除 */
.nav-bar-task {
    display: none;
}

/* 导航栏状态样式 */
.nav-bar-status {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 深色模式适配 */
/* @media (prefers-color-scheme: dark) {
    .nav-bar {
        background-color: var(--header-bg-color);
    }

    .nav-bar-title {
        color: var(--text-color);
    }
} */

.code-block {
    margin: 16px 0;
}

.code-block h5 {
    margin-bottom: 8px;
    color: #333;
}

.code-content {
    padding: 16px;
    border-radius: 6px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    overflow-x: auto;
    white-space: pre-wrap;
}

.code-content.python {
    background-color: #f6f8fa;
    border: 1px solid #e1e4e8;
    color: #24292e;
}

.code-content.markdown {
    background-color: #f8f9fa;
    border: 1px solid #eaecef;
    color: #2f363d;
}

/* 头像样式 */
.nav-bar-avatar {
    cursor: pointer;
    border: 2px solid #e8eaed;
    transition: all 0.2s ease;
    /* 禁用拖动，让头像可以点击 */
    -webkit-app-region: no-drag;
}

.nav-bar-avatar:hover {
    transform: scale(1.05);
}

.nav-bar-avatar-dropdown {
    min-width: 120px;
}

.user-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.user-info-item .label {
  font-weight: 500;
  color: #666;
}

.user-info-item .value {
  color: #333;
}

.logout-button {
  margin-top: 16px;
  width: 100%;
}

/* 右侧按钮区域样式优化 */
.nav-bar-actions-right {
    display: flex;
    align-items: center;
    gap: 8px;
    /* 调整位置，确保在右侧正确显示 */
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    /* 禁用拖动，让按钮可以点击 */
    -webkit-app-region: no-drag;
}

.nav-bar-actions-right .nav-bar-button {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
    color: #262626;
    transition: all 0.2s ease;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 4px 10px;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-right: 0;
}

/* 图标按钮样式 */
.nav-bar-icon-button {
    height: 32px;
    width: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #d9d9d9;
    background-color: #ffffff;
    color: #262626;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    /* 禁用拖动，让按钮可以点击 */
    -webkit-app-region: no-drag;
}

.nav-bar .nav-bar-icon-button:hover {
    background: #ffffff !important; /* 保持白色背景不变 */
    border-color: #6366f1 !important;
    color: #6366f1 !important;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(99, 102, 241, 0.15);
}

.nav-bar .nav-bar-icon-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
    background: #ffffff !important; /* 保持白色背景不变 */
}

.nav-bar .nav-bar-icon-button:focus {
    outline: none;
    border-color: #6366f1 !important;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.nav-bar .nav-bar-icon-button .anticon {
    font-size: 14px;
    color: inherit !important;
}

.nav-bar .nav-bar-icon-button:hover .anticon {
    color: #6366f1 !important;
}

.nav-bar .nav-bar-icon-button:active .anticon {
    color: #6366f1 !important;
}

.nav-bar .nav-bar-icon-button:focus .anticon {
    color: #6366f1 !important;
}

.ruyi-space-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ruyi-space-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    position: relative;
}

.ruyi-space-label::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 12px;
    background-color: #e8eaed;
}

.ruyi-space-buttons {
    display: flex;
    gap: 6px;
}

.ruyi-space-buttons .nav-bar-button {
    height: 28px;
    padding: 4px 12px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: 1px solid #d9d9d9;
    background: #ffffff;
    color: #262626;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@media (max-width: 1200px) {
    .ruyi-space-section {
        gap: 6px;
    }

    .ruyi-space-label {
        font-size: 11px;
    }

    .nav-bar .nav-bar-button {
        padding: 3px 8px;
        font-size: 11px;
        font-weight: 500;
    }
}

@media (max-width: 768px) {
    .ruyi-space-section {
        gap: 4px;
    }

    .ruyi-space-label::after {
        display: none;
    }

    .ruyi-space-buttons {
        gap: 4px;
    }

    .nav-bar .nav-bar-button {
        padding: 2px 6px;
        font-size: 10px;
        font-weight: 500;
    }

    .nav-bar-search-container {
        width: 300px;
        max-width: 50vw;
    }
}

/* 标注相关样式 */
.annotation-modal {
  width: 1000px !important;
  max-width: 90vw;
}

.annotation-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
}

.annotation-content {
  display: flex;
  gap: 16px;
}

.annotation-thumbnail {
  width: 200px;
  min-height: 150px;
  height: auto;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.annotation-thumbnail img {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.annotation-info {
  flex: 1;
  min-width: 0;
}

.annotation-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.annotation-details {
  max-height: 300px;
  overflow: auto;
  padding-right: 8px;
}

.annotation-detail-item {
  padding: 8px;
  margin-bottom: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.annotation-detail-item:last-child {
  margin-bottom: 0;
}

.annotation-preview-modal {
  width: auto !important;
  max-width: 90vw;
  top: 5vh !important;
}

.annotation-preview-modal .ant-modal-content {
  background: transparent;
  box-shadow: none;
}

.annotation-preview-modal .ant-modal-body {
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.annotation-preview-image {
  max-width: 90vw;
  max-height: 85vh;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

/* 模态窗口样式优化 - 文件资源管理器风格 */
.ruyi-space-modal .ant-modal-content {
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(148, 163, 184, 0.1);
    overflow: hidden;
}

.ruyi-space-modal .ant-modal-header {
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    padding: 16px 20px 12px;
    border-radius: 12px 12px 0 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.ruyi-space-modal .ant-modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #64748b;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.6px;
}

.ruyi-space-modal .ant-modal-body {
    padding: 16px 20px;
    max-height: 65vh;
    overflow-y: auto;
}

.ruyi-space-modal .ant-modal-footer {
    border-top: 1px solid rgba(148, 163, 184, 0.1);
    padding: 12px 20px;
    border-radius: 0 0 12px 12px;
    margin: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/* 项目列表样式 */
.ruyi-space-list {
    margin: 0 -20px;
}

.ruyi-space-list .ant-list-item {
    padding: 12px 20px;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.ruyi-space-list .ant-list-item:hover {
    background: rgba(99, 102, 241, 0.05);
}

.ruyi-space-list .ant-list-item-meta-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
}

.ruyi-space-list .ant-list-item-meta-description {
    font-size: 12px;
    color: #64748b;
    line-height: 1.5;
}

.ruyi-space-list .ant-list-item-action {
    margin-left: 16px;
}

.ruyi-space-list .ant-list-item-action > li {
    padding: 0 6px;
}

.ruyi-space-list .ant-tag {
    font-size: 11px;
    padding: 0 6px;
    height: 20px;
    line-height: 18px;
    border-radius: 4px;
    margin-right: 0;
    border: 1px solid rgba(148, 163, 184, 0.2);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

/* 按钮样式优化 */
.ruyi-space-list .ant-btn {
    height: 26px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    padding: 0 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.ruyi-space-list .ant-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.ruyi-space-list .ant-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 创建项目表单样式 */
.create-project-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 4px 0;
}

/* 确保TextArea的内部样式一致 */
.create-project-form .ant-input-textarea .ant-input {
    border: none;
    box-shadow: none;
    background: transparent;
}

.create-project-form .ant-input-textarea .ant-input:focus,
.create-project-form .ant-input-textarea .ant-input:hover {
    border: none;
    box-shadow: none;
    background: transparent;
}

.create-project-form .ant-input,
.create-project-form .ant-input-textarea,
.create-project-form .ant-input-affix-wrapper,
.create-project-form .ant-input-textarea-affix-wrapper {
    border-radius: 4px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    background: #ffffff;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.03);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.create-project-form .ant-input:hover,
.create-project-form .ant-input-textarea:hover,
.create-project-form .ant-input-affix-wrapper:hover,
.create-project-form .ant-input-textarea-affix-wrapper:hover {
    border-color: rgba(99, 102, 241, 0.3);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.create-project-form .ant-input:focus,
.create-project-form .ant-input-textarea:focus,
.create-project-form .ant-input-affix-wrapper:focus,
.create-project-form .ant-input-affix-wrapper-focused,
.create-project-form .ant-input-textarea-affix-wrapper:focus,
.create-project-form .ant-input-textarea-affix-wrapper-focused {
    border-color: rgba(99, 102, 241, 0.5);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
    outline: none;
}

/* 分页组件样式 */
.agent-list-pagination {
    margin-top: 16px;
    text-align: center;
    border-top: 1px solid rgba(148, 163, 184, 0.1);
    padding-top: 16px;
}

.agent-list-pagination .ant-pagination-item {
    border-radius: 4px;
    font-size: 12px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    background: #ffffff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.agent-list-pagination .ant-pagination-item:hover {
    border-color: rgba(99, 102, 241, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.agent-list-pagination .ant-pagination-item-active {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    border-color: rgba(99, 102, 241, 0.3);
    font-weight: 500;
}

.agent-list-pagination .ant-pagination-prev,
.agent-list-pagination .ant-pagination-next {
    border-radius: 4px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    background: #ffffff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.agent-list-pagination .ant-pagination-prev:hover,
.agent-list-pagination .ant-pagination-next:hover {
    border-color: rgba(99, 102, 241, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.agent-list-pagination .ant-pagination-options .ant-select-selector {
    border-radius: 4px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    background: #ffffff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.agent-list-pagination .ant-pagination-options .ant-select-selector:hover {
    border-color: rgba(99, 102, 241, 0.3);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.agent-list-pagination .ant-pagination-options .ant-select-dropdown {
    border-radius: 4px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.agent-list-pagination .ant-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.agent-list-pagination .ant-pagination-total-text {
    font-size: 13px;
    color: #666;
    margin-right: 16px;
}

.agent-list-pagination .ant-pagination-item {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: all 0.2s ease;
}

.agent-list-pagination .ant-pagination-item:hover {
    border-color: #6366f1;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.1);
}

.agent-list-pagination .ant-pagination-item-active {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    border-color: #6366f1;
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
    color: #ffffff !important;
}

.agent-list-pagination .ant-pagination-item-active a {
    color: #ffffff !important;
}

.agent-list-pagination .ant-pagination-prev,
.agent-list-pagination .ant-pagination-next {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: all 0.2s ease;
}

.agent-list-pagination .ant-pagination-prev:hover,
.agent-list-pagination .ant-pagination-next:hover {
    border-color: #6366f1;
    color: #6366f1;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.1);
}

.agent-list-pagination .ant-pagination-options {
    margin-left: 16px;
}

.agent-list-pagination .ant-pagination-options .ant-select {
    border-radius: 6px;
}

.agent-list-pagination .ant-pagination-jump-next,
.agent-list-pagination .ant-pagination-jump-prev {
    border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    /* 移动端分页样式调整 */
    .agent-list-pagination .ant-pagination-total-text {
        display: none;
    }
    
    .agent-list-pagination .ant-pagination-options {
        margin-left: 8px;
    }
    
    .agent-list-pagination {
        margin-top: 12px;
        padding-top: 12px;
    }
}

/* Tooltip 样式 */
.tooltip-container {
    padding: 12px;
    max-width: 400px;
}

.tooltip-title {
    margin: 0 0 12px 0;
    color: #333;  /* 深色字体，确保在白色背景上清晰可见 */
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
}

.tooltip-desc {
    margin-bottom: 12px;
    color: #666;  /* 中等深度的字体颜色 */
    line-height: 1.5;
}

.tooltip-example-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;  /* 深色字体 */
}

.tooltip-example-content {
    background: #f6f8fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e1e4e8;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
    color: #333;  /* 确保代码示例文字清晰可见 */
}

/* 深色模式适配 */
/* @media (prefers-color-scheme: dark) {
    .tooltip-title {
        color: #f0f0f0;
        border-bottom-color: #333;
    }

    .tooltip-desc {
        color: #ccc;
    }

    .tooltip-example-title {
        color: #f0f0f0;
    }

    .tooltip-example-content {
        background: #2d2d2d;
        border-color: #444;
        color: #f0f0f0;
    }
} */

/* 添加 Drawer 相关样式 */
.ant-drawer-content-wrapper {
    z-index: 999; /* 降低z-index，确保在navbar下方 */
    top: 40px !important; /* 设置顶部位置，避免与navbar重叠 */
    height: calc(100vh - 40px) !important; /* 调整高度，减去navbar高度 */
}

.ant-drawer-content {
    background-color: var(--control-buttons-bg-color);
    color: var(--text-color);
    height: 100%; /* 确保内容区域占满整个drawer */
}

.ant-drawer-header {
    background-color: var(--control-buttons-bg-color);
    border-bottom: 1px solid var(--button-border-color);
    padding-top: 16px; /* 增加顶部内边距，确保关闭按钮不被遮挡 */
}

.ant-drawer-title {
    color: var(--text-color);
}

.ant-drawer-close {
    color: var(--text-color);
    top: 16px !important; /* 调整关闭按钮位置，确保不被navbar遮挡 */
    right: 16px !important;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 8px 0;
}

.settings-form-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.settings-form-item label {
    font-weight: 500;
    color: var(--text-color);
}

/* 深色模式适配 */
/* @media (prefers-color-scheme: dark) {
    .nav-bar-icon-button {
        color: var(--text-color);
    }
    
    .nav-bar-icon-button:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .settings-form-item label {
        color: var(--text-color);
    }
} */

/* 执行按钮样式 - VSCode风格 */
.nav-bar-execute-button {
    background-color: #52c41a !important;
    color: #ffffff !important;
    border-color: #52c41a !important;
    border-radius: 50% !important;
    width: 28px !important;
    height: 28px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-right: 8px !important;
}

.nav-bar-execute-button:hover {
    background-color: #818cf8 !important;
    border-color: #818cf8 !important;
    color: #ffffff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}

.nav-bar-execute-button:active {
    background-color: #4f46e5 !important;
    border-color: #4f46e5 !important;
    color: #ffffff !important;
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(99, 102, 241, 0.2);
}

.nav-bar-execute-button:focus {
    border-color: #6366f1 !important;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.nav-bar-execute-button .anticon {
    font-size: 12px !important;
    color: #ffffff !important;
}

.nav-bar-execute-button:hover .anticon {
    color: #ffffff !important;
}

.nav-bar-execute-button:active .anticon {
    color: #ffffff !important;
}

.nav-bar-execute-button:focus .anticon {
    color: #ffffff !important;
}

/* 停止执行按钮样式 - VSCode风格 */
.nav-bar-stop-button {
    background-color: #ff4d4f !important;
    color: #ffffff !important;
    border-color: #ff4d4f !important;
    border-radius: 50% !important;
    width: 28px !important;
    height: 28px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-right: 8px !important;
}

.nav-bar-stop-button:hover {
    background-color: #ff7875 !important;
    border-color: #ff7875 !important;
    color: #ffffff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3);
}

.nav-bar-stop-button:active {
    background-color: #cf1322 !important;
    border-color: #cf1322 !important;
    color: #ffffff !important;
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(255, 77, 79, 0.2);
}

.nav-bar-stop-button:focus {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.nav-bar-stop-button .anticon {
    font-size: 12px !important;
    color: #ffffff !important;
}

.nav-bar-stop-button:hover .anticon {
    color: #ffffff !important;
}

.nav-bar-stop-button:active .anticon {
    color: #ffffff !important;
}

.nav-bar-stop-button:focus .anticon {
    color: #ffffff !important;
}

/* 通用Drawer样式 - 确保所有Drawer都不会与navbar重叠 */
.ant-drawer {
    z-index: 999 !important;
}

/* 确保Drawer的遮罩层也不会与navbar重叠 */
.ant-drawer-mask {
    z-index: 998 !important;
}

/* Report Issue Modal Styles */
.report-issue-modal .ant-modal-content {
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(148, 163, 184, 0.1);
}

.report-issue-modal .ant-modal-header {
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    padding: 16px 20px 12px;
    border-radius: 12px 12px 0 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    backdrop-filter: blur(10px);
}

.report-issue-modal .ant-modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #64748b;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.6px;
}

.report-issue-modal .ant-modal-body {
    padding: 0;
    max-height: 65vh;
    overflow-y: auto;
    background: transparent;
}

.report-issue-modal .ant-modal-footer {
    border-top: 1px solid rgba(148, 163, 184, 0.1);
    padding: 12px 20px;
    border-radius: 0 0 12px 12px;
    margin: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    backdrop-filter: blur(10px);
}

.report-issue-content {
    padding: 16px 20px;
    background: transparent;
}

.report-issue-section {
    margin-bottom: 20px;
}

.report-issue-section:last-child {
    margin-bottom: 0;
}

.report-issue-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    margin: 0 0 12px 0;
    line-height: 1.4;
    text-transform: uppercase;
    letter-spacing: 0.6px;
}

/* Category Selection Styles */
.report-issue-category-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.report-issue-category-card {
    border: 1px solid rgba(148, 163, 184, 0.1);
    border-radius: 8px;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    min-height: 90px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.report-issue-category-card:hover {
    border-color: rgba(99, 102, 241, 0.2);
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.report-issue-category-card.active {
    border-color: rgba(99, 102, 241, 0.2);
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
    transform: translateY(-1px);
}

.category-icon {
    font-size: 24px;
    margin-bottom: 8px;
    line-height: 1;
    filter: grayscale(50%);
    transition: filter 0.2s ease;
}

.report-issue-category-card.active .category-icon {
    filter: none;
}

.category-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.category-title {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    margin-bottom: 4px;
    line-height: 1.3;
}

.category-description {
    font-size: 11px;
    color: #94a3b8;
    line-height: 1.3;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.report-issue-category-card.active .category-title {
    color: #6366f1;
}

.report-issue-category-card.active .category-description {
    color: #6366f1;
    opacity: 0.8;
}

/* Textarea Styles */
.report-issue-textarea-wrapper {
    position: relative;
}

.report-issue-textarea {
    border-radius: 6px;
    border: 1px solid rgba(148, 163, 184, 0.1);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    font-size: 13px;
    line-height: 1.5;
    transition: all 0.2s ease;
    resize: vertical;
    min-height: 80px;
    color: #374151;
}

.report-issue-textarea:hover {
    border-color: rgba(99, 102, 241, 0.2);
    background: rgba(255, 255, 255, 0.9);
}

.report-issue-textarea:focus,
.report-issue-textarea.ant-input-focused {
    border-color: rgba(99, 102, 241, 0.3);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
    outline: none;
}

/* Upload Styles */
.report-issue-upload-wrapper {
    position: relative;
}

.report-issue-upload {
    border: 1px dashed rgba(148, 163, 184, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
    padding: 20px 16px;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.report-issue-upload:hover {
    border-color: rgba(99, 102, 241, 0.4);
    background: rgba(255, 255, 255, 0.8);
}

.report-issue-upload.ant-upload-drag-hover {
    border-color: rgba(99, 102, 241, 0.4);
    background: rgba(255, 255, 255, 0.8);
}

.upload-placeholder {
    text-align: center;
    width: 100%;
}

.upload-icon {
    color: #94a3b8;
    margin-bottom: 12px;
    transition: color 0.2s ease;
}

.report-issue-upload:hover .upload-icon {
    color: #6366f1;
}

.upload-text {
    color: #64748b;
}

.upload-primary-text {
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 6px;
    color: #64748b;
}

.upload-secondary-text {
    font-size: 11px;
    color: #94a3b8;
    line-height: 1.4;
}

/* Uploaded Image Styles */
.uploaded-image-container {
    position: relative;
    border-radius: 6px;
    overflow: hidden;
    max-width: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(148, 163, 184, 0.1);
}

.uploaded-image {
    max-width: 100%;
    max-height: 200px;
    display: block;
    margin: 0 auto;
    object-fit: contain;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(99, 102, 241, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.uploaded-image-container:hover .image-overlay {
    opacity: 1;
}

.overlay-text {
    color: #ffffff;
    font-size: 12px;
    font-weight: 500;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 模态框按钮样式 */
.report-issue-modal .ant-modal-footer .ant-btn {
    height: 28px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(148, 163, 184, 0.1);
    padding: 0 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #64748b;
    backdrop-filter: blur(10px);
}

.report-issue-modal .ant-modal-footer .ant-btn:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-color: rgba(99, 102, 241, 0.2);
    color: #6366f1;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
}

.report-issue-modal .ant-modal-footer .ant-btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
    border: 1px solid rgba(99, 102, 241, 0.2) !important;
    color: #ffffff !important;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.report-issue-modal .ant-modal-footer .ant-btn-primary:hover {
    background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%) !important;
    border: 1px solid rgba(99, 102, 241, 0.3) !important;
    color: #ffffff !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .report-issue-modal {
        width: 95vw !important;
        max-width: none !important;
    }
    
    .report-issue-category-grid {
        grid-template-columns: 1fr;
        gap: 6px;
    }
    
    .report-issue-category-card {
        min-height: 70px;
        padding: 10px 8px;
    }
    
    .category-icon {
        font-size: 20px;
        margin-bottom: 6px;
    }
    
    .category-title {
        font-size: 11px;
    }
    
    .category-description {
        font-size: 10px;
    }
    
    .report-issue-content {
        padding: 12px 16px;
    }
    
    .report-issue-section {
        margin-bottom: 16px;
    }
}

/* Update badge styles */
.update-badge-button .ant-badge-dot {
    background-color: #ff4d4f;
    box-shadow: 0 0 0 1px #fff;
    animation: update-pulse 2s infinite;
    z-index: 10;
}

@keyframes update-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Ensure badge is visible on different backgrounds */
.update-badge-button {
    position: relative;
}

.update-badge-button .ant-badge-dot {
    top: -2px;
    right: -2px;
}