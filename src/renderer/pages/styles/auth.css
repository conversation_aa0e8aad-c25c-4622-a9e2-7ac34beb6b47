.auth-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #e6f2ff, #f0f7ff);
}

.auth-card {
  width: 400px;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  padding: 24px;
}

.auth-input {
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.auth-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.auth-button {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border: none;
  transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.auth-button:hover {
  background: linear-gradient(135deg, #40a9ff, #69c0ff);
  transform: translateY(-1px);
}

.auth-button:disabled,
.auth-button:disabled:hover {
  background: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.auth-link {
  color: #1890ff;
  transition: all 0.3s ease;
}

.auth-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.verification-container {
  display: flex;
  gap: 8px;
}

.verification-input {
  flex: 1;
}

.verification-button {
  height:42px;
  /* 上下居中 */
  margin-top: 1px;
  white-space: nowrap;
  background: #1890ff !important;
  color: white !important;
  border: none !important;
  transition: all 0.3s ease !important;
  min-width: 110px;
}

.verification-button:hover {
  background: #40a9ff !important;
  transform: translateY(-1px);
}

.verification-button:disabled {
  background: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  transform: none;
} 