/* 
 * 字体加载CSS
 * 支持开发环境和生产环境
 * 在开发环境中使用相对路径
 * 在生产环境中使用通过IPC获取的路径
 */

/* 
 * 开发环境字体声明
 * 这些声明在开发环境中使用
 */
@font-face {
  font-family: 'HackNerdFont';
  src: url('../fonts/hacknerd.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MapleCNFont';
  src: url('../fonts/maplecn.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* 
 * 生产环境字体加载
 * 通过JavaScript动态添加，使用IPC获取的路径
 */ 