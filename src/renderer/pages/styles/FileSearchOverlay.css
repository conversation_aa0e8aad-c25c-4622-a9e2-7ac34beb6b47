.file-search-overlay {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
  max-height: 500px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.search-overlay-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.search-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: #6b7280;
  font-size: 13px;
}

.search-loading .ant-spin {
  margin-right: 0;
}

.search-results {
  max-height: 460px;
  overflow-y: auto;
  padding: 4px 0;
}

.search-result-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  font-size: 13px;
  min-height: 48px;
}

.search-result-item:hover {
  background-color: #f8f9fa;
}

.search-result-item.selected {
  background-color: #e7f3ff;
  border-left-color: #1890ff;
}

.search-result-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin-top: 2px;
}

.search-result-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.search-result-name {
  font-weight: 600;
  color: #374151;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.search-result-path {
  font-size: 11px;
  color: #9ca3af;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3;
}

.search-result-matches {
  font-size: 10px;
  color: #6366f1;
  font-weight: 500;
  line-height: 1.3;
  margin-top: 1px;
}

.search-result-snippet {
  font-size: 11px;
  color: #6b7280;
  line-height: 1.4;
  background-color: rgba(99, 102, 241, 0.05);
  padding: 4px 6px;
  border-radius: 4px;
  margin-top: 2px;
  border-left: 2px solid #e5e7eb;
  max-height: 40px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.search-highlight {
  background-color: #fef3c7;
  color: #d97706;
  font-weight: 700;
  padding: 0 2px;
  border-radius: 3px;
}

.search-no-results,
.search-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 24px 16px;
  color: #9ca3af;
  font-size: 13px;
  text-align: center;
}

.search-no-results svg,
.search-placeholder svg {
  opacity: 0.5;
}

/* Custom scrollbar for search results */
.search-results::-webkit-scrollbar {
  width: 6px;
}

.search-results::-webkit-scrollbar-track {
  background: transparent;
}

.search-results::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .file-search-overlay {
    background: #1f2937;
    border-color: #374151;
  }
  
  .search-result-item:hover {
    background-color: #374151;
  }
  
  .search-result-item.selected {
    background-color: #1e40af;
  }
  
  .search-result-name {
    color: #f9fafb;
  }
  
  .search-result-path {
    color: #9ca3af;
  }
  
  .search-result-matches {
    color: #a78bfa;
  }
  
  .search-result-snippet {
    color: #d1d5db;
    background-color: rgba(139, 92, 246, 0.1);
    border-left-color: #4b5563;
  }
  
  .search-loading,
  .search-no-results,
  .search-placeholder {
    color: #9ca3af;
  }
  
  .search-highlight {
    background-color: #451a03;
    color: #fbbf24;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .file-search-overlay {
    max-height: 400px;
  }
  
  .search-results {
    max-height: 360px;
  }
  
  .search-result-item {
    padding: 14px 16px;
  }
  
  .search-result-name {
    font-size: 14px;
  }
  
  .search-result-path {
    font-size: 12px;
  }
  
  .search-result-matches {
    font-size: 11px;
  }
  
  .search-result-snippet {
    font-size: 12px;
  }
} 