.annotation-management {
    max-height: 80vh;
    overflow: auto;
}
  
.annotation-item {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    padding: 16px;
}

.annotation-content {
    display: flex;
    gap: 16px;
}

.annotation-thumbnail {
    width: 200px;
    height: 200px;
    cursor: pointer;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
}

.annotation-thumbnail img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.annotation-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.annotation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.annotation-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.annotation-detail-item {
    padding: 8px;
    background-color: #fafafa;
    border-radius: 4px;
}

.no-annotations {
    text-align: center;
    padding: 32px;
    color: #999;
}

.annotation-preview-modal .ant-modal-content {
    padding: 0;
    background: transparent;
}

.annotation-preview-image {
    max-width: 100%;
    max-height: 20vh;
    object-fit: contain;
}

.preview-image-container {
    position: relative;
    display: inline-block;
}

.preview-close-button {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 1;
    color: rgba(0, 0, 0, 0.65);
    background: rgba(0, 0, 0, 0.25);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.preview-close-button:hover {
    background: rgba(0, 0, 0, 0.45) !important;
    color: #fff;
}

.annotation-modal-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 30px;
} 