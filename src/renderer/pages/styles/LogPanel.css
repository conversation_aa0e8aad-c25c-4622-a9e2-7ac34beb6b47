.log-panel {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 30px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(148, 163, 184, 0.1);
  margin: 0 8px 8px 8px; /* Align with code editor (8px on left and right, 8px on bottom) */
  flex-shrink: 0; /* Prevent panel from shrinking */
}

.log-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
      to right,
      transparent 0%,
      rgba(148, 163, 184, 0.1) 10%,
      rgba(148, 163, 184, 0.2) 50%,
      rgba(148, 163, 184, 0.1) 90%,
      transparent 100%
  );
  pointer-events: none;
}

.log-panel-header {
  height: 40px;
  min-height: 40px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
  padding: 0 12px 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: ns-resize;
  user-select: none;
  flex-shrink: 0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  color: #64748b;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.log-panel-header span {
  display: flex;
  align-items: center;
}

.log-panel-content {
  height: calc(100% - 40px);
  overflow-y: auto;
  padding: 12px;
  font-family: 'HackNerdFont', 'MapleCNFont', 'Consolas', 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  line-height: 1.5;
  background-color: #ffffff;
  color: #1e293b;
  border: none;
  display: block;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.log-line {
  white-space: pre-wrap;
  word-break: break-all;
  display: block;
  padding: 1px 0;
}

.log-panel.minimized .log-panel-content {
  display: none;
}

/* Custom scrollbar for terminal */
.log-panel-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.log-panel-content::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 4px;
}

.log-panel-content::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.log-panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Style for textarea */
.log-panel-content textarea {
  background-color: #ffffff;
  color: #1e293b;
  border: none;
  width: 100%;
  height: 100%;
  resize: none;
  font-family: 'HackNerdFont', 'MapleCNFont', 'Consolas', 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  line-height: 1.5;
  outline: none;
  padding: 0;
}

/* Add terminal-like cursor effect */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.log-panel-header .controls {
  display: flex;
  gap: 8px;
}

.log-panel-header .control-button {
  height: 26px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(148, 163, 184, 0.2);
  background: #ffffff;
  color: #64748b;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  cursor: pointer;
}

.log-panel-header .control-button:hover {
  background: #f1f5f9;
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 添加日志文本的样式 */
.log-panel-content .log-line {
  white-space: pre-wrap;
  word-break: break-all;
  display: block;
  padding: 1px 0;
}

/* 基础语法高亮 */
.log-panel-content .log-info {
  color: #3b82f6;
}

.log-panel-content .log-success {
  color: #10b981;
}

.log-panel-content .log-warning {
  color: #f59e0b;
}

.log-panel-content .log-error {
  color: #ef4444;
}

.log-panel-content .log-highlight {
  font-weight: bold;
  color: #6366f1;
}

/* 为不同类型的日志添加前缀图标 */
.log-panel-content .log-info::before {
  content: "ℹ ";
  color: #3b82f6;
}

.log-panel-content .log-success::before {
  content: "✓ ";
  color: #10b981;
}

.log-panel-content .log-warning::before {
  content: "⚠ ";
  color: #f59e0b;
}

.log-panel-content .log-error::before {
  content: "✗ ";
  color: #ef4444;
}