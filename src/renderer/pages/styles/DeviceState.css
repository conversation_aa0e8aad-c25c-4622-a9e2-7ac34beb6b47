.annotation-modal {
  width: 1000px !important;
  max-width: 90vw;
}

/* DeviceState卡片样式适配 */
.device-state-card {
  /* 移除原有的margin，使用父容器的padding */
  margin: 0 !important;
  /* 确保卡片背景透明，使用父容器的卡片样式 */
  background: transparent !important;
  border: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03) !important; /* 更新阴影效果 */
  border-radius: 12px !important;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
}

/* 添加微妙的背景纹理 */
.device-state-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.device-state-card .ant-card-head {
  /* 卡片头部样式 - 更实心的背景色 */
  background: #ffffff;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1); /* 更柔和的边框 */
  padding: 8px 12px; /* 收紧内边距 */
  border-radius: 12px 12px 0 0;
  position: relative;
  z-index: 1;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 40px; /* 固定高度 */
  display: flex;
  align-items: center;
}

.device-state-card .ant-card-body {
  /* 卡片内容区域样式 - 更实心的背景色 */
  background: #f8fafc;
  padding: 12px; /* 收紧内边距 */
  border-radius: 0 0 12px 12px;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  position: relative;
  z-index: 1;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.device-state-card .ant-card-head-title {
  /* 卡片标题样式 - 现代化文字风格 */
  font-weight: 600;
  color: #64748b;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
}

.device-state-card .ant-card-head-title svg {
  margin-right: 8px;
  color: #6366f1;
  filter: drop-shadow(0 1px 2px rgba(99, 102, 241, 0.1));
  transition: all 0.2s ease;
}

.device-state-card .ant-card-head-title:hover svg {
  transform: scale(1.05);
  filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.2));
}

.device-state-card .ant-card-extra {
  /* 卡片额外内容样式 */
  display: flex;
  gap: 8px;
  align-items: center;
}

.annotation-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
}

.annotation-content {
  display: flex;
  gap: 16px;
}

.annotation-thumbnail {
  width: 200px;
  height: 150px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.annotation-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.annotation-info {
  flex: 1;
  min-width: 0;
}

.annotation-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.annotation-details {
  max-height: 300px;
  overflow: auto;
  padding-right: 8px;
}

.annotation-detail-item {
  padding: 8px;
  margin-bottom: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.annotation-detail-item:last-child {
  margin-bottom: 0;
}

.annotation-preview-modal {
  width: auto !important;
  max-width: 90vw;
  top: 5vh !important;
}

.annotation-preview-modal .ant-modal-content {
  background: transparent;
  box-shadow: none;
}

.annotation-preview-modal .ant-modal-body {
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.annotation-preview-image {
  max-width: 90vw;
  max-height: 85vh;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

.device-status-tag {
  height: 22px; /* 减小高度 */
  line-height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 64px; /* 减小宽度 */
  font-weight: 500;
  font-size: 12px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  border-radius: 4px; /* 减小圆角 */
  padding: 0 8px; /* 减小内边距 */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 4px; /* 添加左边距 */
}

.device-status-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 使用实心背景色 */
.ant-tag.ant-tag-green.device-status-tag {
  background: rgba(82, 196, 26, 0.1);
  border-color: rgba(82, 196, 26, 0.3);
  color: #52c41a;
}

.ant-tag.ant-tag-red.device-status-tag {
  background: rgba(255, 77, 79, 0.1);
  border-color: rgba(255, 77, 79, 0.3);
  color: #ff4d4f;
}

.ant-tag.ant-tag-orange.device-status-tag {
  background: rgba(250, 140, 22, 0.1);
  border-color: rgba(250, 140, 22, 0.3);
  color: #fa8c16;
}

.ant-tag.ant-tag-blue.device-status-tag {
  background: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.3);
  color: #1890ff;
}

.device-state-card .ant-btn {
  height: 26px; /* 减小高度 */
  border-radius: 4px; /* 减小圆角 */
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(148, 163, 184, 0.2);
  position: relative;
  z-index: 1;
  padding: 0 6px; /* 减小内边距 */
  background: #ffffff; /* 实心背景 */
  color: #64748b;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 3px; /* 减小间距 */
}

/* 确保图标垂直居中 */
.device-state-card .ant-btn svg {
  display: block;
  margin-top: -1px; /* 微调图标位置 */
}

.device-state-card .ant-btn:hover {
  background: #f1f5f9; /* 实心背景 */
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1); /* 减小阴影 */
}

.device-state-card .ant-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.device-state-card .ant-btn svg {
  transition: all 0.2s ease;
}

.device-state-card .ant-btn:hover svg {
  transform: scale(1.1);
  color: #6366f1;
}

.device-state-card .ant-btn-icon-only {
  width: 28px;
  padding: 0;
  margin: 0;
  min-width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.manual-patch-label {
  font-size: 12px;
  color: #64748b;
  padding: 2px 8px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 6px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  user-select: none;
  white-space: nowrap;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.manual-patch-label:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: rgba(99, 102, 241, 0.2);
  color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.help-link {
  padding: 0 6px !important;
  height: 26px !important;
  line-height: 24px !important;
  font-size: 12px !important;
  color: #ef4444 !important;
  background: rgba(239, 68, 68, 0.05) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-weight: 500 !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 3px !important;
  border: 1px solid rgba(239, 68, 68, 0.2) !important;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05) !important;
  border-radius: 4px !important;
}

.help-link:hover {
  color: #dc2626 !important;
  background: rgba(239, 68, 68, 0.1) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 6px rgba(239, 68, 68, 0.15) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.help-link:active {
  transform: translateY(0) !important;
  box-shadow: 0 1px 2px rgba(239, 68, 68, 0.1) !important;
}

.guide-modal {
  top: 20px;
}

.guide-modal .ant-modal-body {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.guide-modal .ant-modal-content {
  border-radius: 8px;
}

/* 浏览器控制区域样式 */
.device-state-card .browser-controls {
  display: flex;
  gap: 6px; /* 减小间距 */
  align-items: center;
  width: 100%;
  background: #f1f5f9; /* 实心背景 */
  border-radius: 4px; /* 减小圆角 */
  padding: 4px; /* 减小内边距 */
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.03);
}

/* URL输入框样式 */
.device-state-card .ant-input {
  border-radius: 4px; /* 减小圆角 */
  height: 26px; /* 减小高度 */
  font-size: 12px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  background: #ffffff; /* 实心背景 */
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.03);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.device-state-card .ant-input:hover {
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.device-state-card .ant-input:focus {
  border-color: rgba(99, 102, 241, 0.5);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
  outline: none;
}

/* 自定义滚动条 */
.device-state-card ::-webkit-scrollbar {
  width: 8px;
}

.device-state-card ::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
  margin: 4px 0;
}

.device-state-card ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(148, 163, 184, 0.3) 0%, rgba(148, 163, 184, 0.5) 100%);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.device-state-card ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.4) 0%, rgba(139, 92, 246, 0.4) 100%);
}
