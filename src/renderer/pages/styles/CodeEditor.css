/* 编辑器标题容器样式 */
.code-editor-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px 12px 16px; /* 增加底部内边距 */
  /* 使用现代化的渐变背景，与其他部分保持一致 */
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  /* 使用现代化的分割线替代实线边框 */
  position: relative; /* 保持相对定位 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08); /* 保持阴影效果 */
  height: 58px; /* 增加高度以适应增加的底部内边距 */
  /* 添加毛玻璃效果 */
  backdrop-filter: blur(10px);
  /* 添加微妙的边框 */
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  z-index: 1;
}

/* 编辑器标题的现代化分割线 */
.code-editor-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
      to right,
      transparent 0%,
      rgba(179, 179, 179, 0.1) 10%,
      rgba(179, 179, 179, 0.2) 50%,
      rgba(179, 179, 179, 0.1) 90%,
      transparent 100%
  );
  pointer-events: none;
}

.code-editor-title-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.code-editor-title-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  max-width: 600px; /* 增加最大宽度，提供更多空间 */
  padding: 0 4px;
  animation: fadeIn 0.3s ease-out;
}

/* 标题行，使任务名称和脚本类型在同一行 */
.code-editor-title-row {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  flex-wrap: nowrap;
  margin-bottom: 2px;
  min-height: 32px; /* 增加最小高度 */
}

/* 主标题样式，支持多行省略 */
.code-editor-main-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  max-width: calc(100% - 150px); /* 预留脚本类型的空间 */
  /* min-width: 300px; */ /* 添加最小宽度限制 - Commented out to fix gap issue */
  cursor: pointer;
  position: relative;
  transition: color 0.3s ease;
  padding: 4px 0; /* 增加上下内边距 */
  min-height: 24px; /* 增加最小高度 */
}

.code-editor-main-title:hover {
  color: #262626;
}

/* 副标题样式，支持多行省略 */
.code-editor-sub-title {
  font-size: 14px;
  color: #595959;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  max-width: 100%;
  min-width: 400px; /* 添加最小宽度限制 */
  cursor: pointer;
  position: relative;
  width: 100%;
  padding: 0 8px;
  margin-right: -15px;
}

/* 省略号样式 */
.ellipsis {
  display: none; /* 默认隐藏省略号 */
  color: inherit;
  opacity: 0.8;
  margin-left: 2px;
  font-weight: bold;
  position: relative;
  z-index: 1;
}

/* 执行按钮样式 */
.execute-button {
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666"><path d="M8 5v14l11-7z"/></svg>') center center no-repeat;
  cursor: pointer;
  opacity: 0.5;
  margin-right: -15px;
}

.execute-button:hover {
  opacity: 1;
}

/* 脚本类型样式 */
.code-editor-script-type {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 0;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  flex-shrink: 0;
  padding-right: 0;
  color: #262626;
  text-shadow: none;
}

/* 默认标题样式 - 简单文本 */
.code-editor-default-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  text-align: center;
}

/* No Task Title Style */
.code-editor-no-task-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px; /* Normal font size */
  font-weight: 500; /* Default weight for " - Editor Title" part */
  color: #262626; /* Default color for " - Editor Title" part */
  padding: 4px 8px;
  border-radius: 6px;
  /* background: linear-gradient(to right, rgba(24, 144, 255, 0.05), rgba(24, 144, 255, 0.1)); */
  /* box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03); */
  animation: fadeIn 0.3s ease-out;
  transition: all 0.3s ease;
}

.code-editor-no-task-title:hover {
  /* background: linear-gradient(to right, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.15)); */
  transform: translateY(-1px);
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); */
}

.code-editor-no-task-title svg {
  /* marginRight is handled inline in TSX */
  color: #1890ff; /* Blue icon */
}

.code-editor-no-task-title .script-type-part,
.code-editor-no-task-title .separator-part,
.code-editor-no-task-title .editor-title-part {
  font-weight: 600; /* Bolder for script type part */
  color: #262626; /* Black text for script type part */
}

/* .code-editor-no-task-title .editor-title-part { */
  /* Inherits base style from .code-editor-no-task-title */
/* } */



/* 执行按钮组容器 */
.execution-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 70px; /* 确保有足够的空间 */
}

/* 执行按钮占位符 - 保持布局对称 */
.execution-buttons-placeholder {
  min-width: 70px; /* 与执行按钮组相同的宽度 */
  height: 26px;
}

/* 知识库标题居中样式 */
.code-editor-knowledge-title-center {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

/* 编辑器执行按钮样式 - VSCode风格 */
.editor-execute-button {
  background-color: #52c41a !important;
  color: #ffffff !important;
  border-color: #52c41a !important;
  border-radius: 50% !important;
  width: 26px !important;
  height: 26px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.editor-execute-button:hover {
  background-color: #73d13d !important;
  border-color: #73d13d !important;
  color: #ffffff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(82, 196, 26, 0.3);
}

.editor-execute-button:active {
  background-color: #389e0d !important;
  border-color: #389e0d !important;
  color: #ffffff !important;
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(82, 196, 26, 0.2);
}

.editor-execute-button:focus {
  border-color: #52c41a !important;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

.editor-execute-button:disabled {
  background-color: #d9d9d9 !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.editor-execute-button .anticon {
  font-size: 12px !important;
  color: #ffffff !important;
}

.editor-execute-button:hover .anticon {
  color: #ffffff !important;
}

.editor-execute-button:active .anticon {
  color: #ffffff !important;
}

.editor-execute-button:focus .anticon {
  color: #ffffff !important;
}

.editor-execute-button:disabled .anticon {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* 编辑器停止执行按钮样式 - VSCode风格 */
.editor-stop-button {
  background-color: #ff4d4f !important;
  color: #ffffff !important;
  border-color: #ff4d4f !important;
  border-radius: 4px !important;
  width: 26px !important;
  height: 26px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.editor-stop-button:hover {
  background-color: #ff7875 !important;
  border-color: #ff7875 !important;
  color: #ffffff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3);
}

.editor-stop-button:active {
  background-color: #d9363e !important;
  border-color: #d9363e !important;
  color: #ffffff !important;
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(255, 77, 79, 0.2);
}

.editor-stop-button:focus {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.editor-stop-button .anticon {
  font-size: 11px !important;
  color: #ffffff !important;
}

.editor-stop-button:hover .anticon {
  color: #ffffff !important;
}

.editor-stop-button:active .anticon {
  color: #ffffff !important;
}

.editor-stop-button:focus .anticon {
  color: #ffffff !important;
}

/* 帮助图标样式 */
.help-icon-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 70px; /* 与执行按钮组保持对称 */
}

.help-icon-hover {
  transition: all 0.3s ease;
  color: #8c8c8c;
}

.help-icon-hover:hover {
  color: #1890ff !important;
  transform: scale(1.1);
}

/* 编辑器使用提示弹窗样式 */
.editor-usage-tip-tooltip {
  max-width: 600px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  padding: 0;
  color: #262626;
  font-size: 14px;
  line-height: 1.8;
  border: 1px solid #f0f0f0;
  animation: tooltipFadeIn 0.3s ease;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.editor-usage-tip-tooltip .tip-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #262626;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  border-radius: 12px 12px 0 0;
}

.editor-usage-tip-tooltip .tip-content {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 #f5f5f5;
}

.editor-usage-tip-tooltip .tip-content::-webkit-scrollbar {
  width: 6px;
}

.editor-usage-tip-tooltip .tip-content::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.editor-usage-tip-tooltip .tip-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.editor-usage-tip-tooltip .tip-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.editor-usage-tip-tooltip .tip-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: #1890ff;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.editor-usage-tip-tooltip .tip-section:hover {
  background: #f0f7ff;
  border-color: #91caff;
  transform: translateX(4px);
}

.editor-usage-tip-tooltip .tip-section:hover::before {
  opacity: 1;
}

.editor-usage-tip-tooltip .tip-section:last-child {
  margin-bottom: 0;
}

.editor-usage-tip-tooltip .tip-section-title {
  margin-bottom: 6px;
  padding-bottom: 4px;
  font-weight: 600;
  color: #1890ff;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px dashed #e6f4ff;
}

.editor-usage-tip-tooltip .tip-section-content {
  color: #434343;
  font-size: 14px;
  line-height: 1.6;
}

/* 添加列表项样式 */
.editor-usage-tip-tooltip .tip-section-content li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 10px;
  list-style-type: none;
  transition: all 0.3s ease;
}

.editor-usage-tip-tooltip .tip-section-content li:last-child {
  margin-bottom: 0;
}

.editor-usage-tip-tooltip .tip-section-content li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #1890ff;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.editor-usage-tip-tooltip .tip-section-content li:hover::before {
  transform: scale(1.2);
  opacity: 1;
}

.editor-usage-tip-tooltip .tip-icon {
  color: #1890ff;
  font-size: 18px;
  vertical-align: middle;
}

.editor-usage-tip-tooltip code {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 3px 8px;
  margin: 0 2px;
  font-size: 13px;
  color: #c41d7f;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  display: inline-block;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.editor-usage-tip-tooltip code:hover {
  background: #fff;
  color: #eb2f96;
  border-color: #ffa39e;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-usage-tip-tooltip strong {
  color: #262626;
  font-weight: 600;
  background: linear-gradient(120deg, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0.1) 100%);
  padding: 0 4px;
  border-radius: 3px;
}

/* 主标题和副标题的通用样式 */
.code-editor-main-title,
.code-editor-sub-title {
  position: relative;
}

/* 被截断的文本样式 */
.truncated {
  position: relative;
}

.truncated .ellipsis {
  display: inline-block; /* 只有在被截断时才显示省略号 */
  position: absolute;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, transparent, white 30%);
  padding-left: 20px;
  margin-left: 0;
}

/* 添加编辑器标题的动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

.code-editor-title-content {
  animation: fadeIn 0.3s ease-out;
}

/* Monaco编辑器容器样式 */
.monaco-editor-wrapper {
  flex: 1;
  min-height: 0; /* 重要：允许编辑器缩小 */
  padding-top: 16px; /* 在标题栏下方添加适当的间距 */
  background-color: #ffffff; /* 保持白色背景 */
  overflow: visible; /* 允许内容溢出，让Monaco编辑器自己处理滚动 */
  position: relative; /* 添加相对定位 */
}

/* Monaco编辑器自定义滚动条样式 */
.monaco-editor .scrollbar .slider {
  background: rgba(0, 0, 0, 0.08) !important; /* 更淡的背景色 */
  border-radius: 4px !important;
  transition: background 0.2s ease !important;
  opacity: 0.6; /* 默认半透明 */
}

.monaco-editor .scrollbar .slider:hover {
  background: rgba(0, 0, 0, 0.15) !important;
  opacity: 1; /* 悬停时完全不透明 */
}

.monaco-editor .scrollbar .slider.active {
  background: rgba(0, 0, 0, 0.2) !important;
  opacity: 1;
}

/* 垂直滚动条样式 */
.monaco-editor .scrollbar.vertical .slider {
  width: 6px !important;
  min-width: 6px !important;
}

/* 水平滚动条样式 */
.monaco-editor .scrollbar.horizontal .slider {
  height: 6px !important;
  min-height: 6px !important;
}

/* 滚动条轨道样式 */
.monaco-editor .scrollbar .slider-track {
  background: transparent !important;
}

/* 滚动条箭头样式（隐藏） */
.monaco-editor .scrollbar .arrow {
  display: none !important;
}

/* 滚动条阴影效果 */
.monaco-editor .scrollbar.vertical {
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.03) !important; /* 更淡的阴影 */
}

.monaco-editor .scrollbar.horizontal {
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.03) !important; /* 更淡的阴影 */
}

/* 当编辑器内容不需要滚动时，隐藏滚动条 */
.monaco-editor .scrollbar.vertical:not(.visible) {
  opacity: 0 !important;
}

.monaco-editor .scrollbar.horizontal:not(.visible) {
  opacity: 0 !important;
}

/* 选中代码的操作按钮容器 */
.selected-code-actions {
  position: fixed;
  display: flex;
  gap: 8px;
  background-color: #ffffff;
  padding: 6px 8px;
  border-radius: 8px;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  border: 1px solid rgba(148, 163, 184, 0.1);
  backdrop-filter: blur(10px);
  animation: fadeInScale 0.2s ease-out;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.selected-code-button {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.selected-code-button:hover {
  background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
  border-color: rgba(99, 102, 241, 0.3);
}

.selected-code-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

/* 添加悬浮窗出现动画 */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 执行代码行高亮样式 */
.executing-line-highlight {
  background: rgba(255, 193, 7, 0.3) !important;
  /* border-left: 4px solid #ffc107 !important; */
  animation: executingLinePulse 1s ease-in-out infinite alternate;
}

.executing-line-decoration {
  background: transparent !important;
  color: #ffc107 !important;
  font-weight: bold !important;
  text-shadow: 0 0 3px rgba(255, 193, 7, 0.5) !important;
}

/* 执行代码错误行高亮样式 */
.executing-line-error-highlight {
  background: rgba(255, 77, 79, 0.3) !important;
  /* border-left: 4px solid #ff4d4f !important; */
  animation: executingLineErrorPulse 1s ease-in-out infinite alternate;
}

.executing-line-error-decoration {
  background: transparent !important;
  color: #ff4d4f !important;
  font-weight: bold !important;
  text-shadow: 0 0 3px rgba(255, 77, 79, 0.5) !important;
}

@keyframes executingLinePulse {
  0% {
    background: rgba(255, 193, 7, 0.2);
  }
  100% {
    background: rgba(255, 193, 7, 0.4);
  }
}

@keyframes executingLineErrorPulse {
  0% {
    background: rgba(255, 77, 79, 0.2);
  }
  100% {
    background: rgba(255, 77, 79, 0.4);
  }
}

/* 只读行样式 */
.readonly-line {
  background-color: rgba(240, 240, 240, 0.8) !important;
  border-left: 3px solid #d9d9d9 !important;
  position: relative;
}

.readonly-line-number {
  background-color: rgba(217, 217, 217, 0.5) !important;
  color: #8c8c8c !important;
  font-weight: bold;
}

.readonly-line-margin {
  background-color: rgba(217, 217, 217, 0.3) !important;
}

/* 只读行鼠标悬停效果 */
.readonly-line:hover {
  background-color: rgba(230, 230, 230, 0.9) !important;
}

/* 只读行选中效果 */
.readonly-line.selected {
  background-color: rgba(200, 200, 200, 0.8) !important;
}
  