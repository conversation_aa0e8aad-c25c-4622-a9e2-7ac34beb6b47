.settings-panel {
  display: flex;
  height: 400px;
  /* width: 600px; */
}

.settings-menu {
  width: 200px;
  position: relative;
}

/* 设置菜单的现代化分割线 */
.settings-menu::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(
      to bottom,
      transparent 0%,
      rgba(240, 240, 240, 0.3) 10%,
      rgba(240, 240, 240, 0.6) 50%,
      rgba(240, 240, 240, 0.3) 90%,
      transparent 100%
  );
  pointer-events: none;
}

.settings-content-container {
  flex: 1;
  padding: 20px;
}

.settings-content {
  position: relative;
  height: 100%;
  padding-bottom: 60px;
}

.settings-form-item {
  margin-bottom: 20px;
  display: flex;
}

.settings-form-item label {
  flex-shrink: 0;
  width: 150px;
  margin-bottom: 0;
}

.settings-form-item .ant-radio-group {
  display: flex;
  align-items: center;
}

.settings-save-button {
  position: absolute;
  /* bottom: 20px; */
  right: 0px;
}

.token-display {
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 16px;
  color: #333;
} 