.test-model-modal {
  .ant-modal-body {
    padding: 12px;
  }
}

.test-model-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.test-model-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mode-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mode-label-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.mode-label {
  font-weight: 500;
  white-space: nowrap;
}

.description-input-container {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.description-input {
  flex: 1;
}

.test-button {
  margin-top: 0;
}

.test-model-main {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  height: 540px;
}

.image-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: auto;
  padding: 0 8px;

  h4 {
    margin: 0;
    font-weight: 500;
  }
}

.results-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
  border-left: 1px solid #f0f0f0;
  padding-left: 24px;

  h4 {
    margin: 0;
    font-weight: 500;
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
  }

  .ant-list {
    padding: 0 8px;
    overflow-y: auto;
    flex: 1;
  }
}

.image-container {
  position: relative;
  width: 100%;
  height: 500px;
}

.preview-image {
  width: 100%;
  height: 500px;
  object-fit: contain;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
}

.click-indicator {
  position: absolute;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

.placeholder {
  width: 100%;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.result-item {
  padding: 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #f0f0f0;
  margin-bottom: 10px;
  background-color: #fff;

  &:hover {
    background-color: #f5f5f5;
  }

  &.active {
    background-color: #e6f7ff;
    border-color: #91d5ff;
  }
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-left: 10px;
  padding-right: 10px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.result-time {
  color: #999;
  font-size: 12px;
}

.result-description {
  color: #666;
  line-height: 1.5;
}

.result-coordinate {
  color: #1890ff;
  font-weight: 500;
}

.result-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding-top: 4px;
}

.describe-instruction {
  color: #666;
  font-style: italic;
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  text-align: center;
}

.clickable-image {
  transition: none;
}