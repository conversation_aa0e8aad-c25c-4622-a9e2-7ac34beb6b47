/* PhysicalDeviceScanner Modal Styles */
.device-scanner-modal .ant-modal-content {
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  overflow: hidden;
}

.device-scanner-modal .ant-modal-header {
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding: 16px 20px 12px;
  border-radius: 12px 12px 0 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
}

.device-scanner-modal .ant-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #64748b;
  margin: 0;
  letter-spacing: 0.6px;
}

.scanner-modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.scanner-modal-title .anticon {
  font-size: 18px;
  color: #6366f1;
}

.device-scanner-modal .ant-modal-body {
  padding: 0;
  background: transparent;
}

.device-scanner-modal .ant-modal-footer {
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  padding: 12px 20px;
  border-radius: 0 0 12px 12px;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
}

/* Scanner Content */
.scanner-content {
  padding: 0;
  max-height: 65vh;
  overflow-y: auto;
}

/* Info Banner */
.scanner-info-banner {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  margin: 0 0 16px 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.scanner-info-banner .info-icon {
  font-size: 16px;
  color: #6366f1;
}

.scanner-info-banner .info-text {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

/* Scanner Header */
.scanner-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  margin: 0 20px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
}

.scanner-select-all {
  font-weight: 500;
}

.scanner-select-all .ant-checkbox-wrapper {
  font-size: 14px;
  color: #475569;
}

.scanner-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
}

.stat-value {
  font-weight: 600;
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: rgba(99, 102, 241, 0.1);
}

.stat-value.found {
  color: #1890ff;
}

.stat-value.selected {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

/* Device List */
.scanner-device-list {
  padding: 0 20px 20px;
}

.scanner-list-item {
  padding: 8px 0;
}

.scanner-device-card {
  width: 100%;
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
  padding: 16px;
}

.scanner-device-card:hover {
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

.scanner-device-card.selected {
  border: 2px solid #6366f1;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.scanner-device-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.scanner-checkbox {
  transform: scale(1.2);
}

.scanner-device-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  border-width: 2px;
  border-style: solid;
  transition: all 0.3s ease;
}

.scanner-device-icon .anticon {
  font-size: 24px;
  transition: all 0.3s ease;
}

.scanner-device-card:hover .scanner-device-icon .anticon {
  transform: scale(1.1);
}

.scanner-device-details {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 24px;
  align-items: center;
}

.detail-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #64748b;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
  color: #334155;
}

.detail-value.error {
  color: #ff4d4f;
}

.detail-value.status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.device-name .device-title {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
  color: #334155;
}

.device-name .device-id {
  font-size: 13px;
  color: #64748b;
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  background: rgba(148, 163, 184, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

/* Warning Banner */
.scanner-warning-banner {
  margin-top: 12px;
  padding: 10px 12px;
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.05) 0%, rgba(245, 158, 11, 0.1) 100%);
  border: 1px solid rgba(250, 173, 20, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.warning-indicator {
  width: 4px;
  height: 16px;
  background-color: #fa8c16;
  border-radius: 2px;
}

.warning-text {
  color: #d46b08;
  font-size: 13px;
  font-weight: 500;
  line-height: 1.4;
}

/* Error Container */
.scanner-error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.05) 0%, rgba(245, 34, 45, 0.1) 100%);
  border-radius: 12px;
  margin: 20px;
}

.error-icon {
  font-size: 32px;
  color: #ff4d4f;
  margin-bottom: 16px;
}

.error-text {
  font-size: 16px;
  margin-bottom: 20px;
}

.scanner-retry-button {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%) !important;
  border-color: #ff4d4f !important;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.2);
  border-radius: 6px;
  height: 36px;
  padding: 0 16px;
  font-weight: 500;
}

.scanner-retry-button:hover {
  background: linear-gradient(135deg, #ff7875 0%, #ff9c9c 100%) !important;
  border-color: #ff7875 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

/* Empty Container */
.scanner-empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-hint {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
  padding: 8px 16px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
}

.search-icon {
  color: #6366f1;
}

/* Spinner */
.scanner-spinner .ant-spin-text {
  margin-top: 8px;
  color: #6366f1;
}

.scanner-spinner .ant-spin-dot-item {
  background-color: #6366f1;
}

/* Button Styles */
.scanner-button {
  height: 32px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.scanner-button-default {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(148, 163, 184, 0.2);
  color: #64748b;
}

.scanner-button-default:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: rgba(99, 102, 241, 0.2);
  color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
}

.scanner-button-primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
  border: 1px solid rgba(99, 102, 241, 0.2) !important;
  color: #ffffff !important;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.scanner-button-primary:hover {
  background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%) !important;
  border: 1px solid rgba(99, 102, 241, 0.3) !important;
  color: #ffffff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
}

.scanner-button-primary:disabled {
  background: linear-gradient(135deg, #a5a6f6 0%, #c4b5fa 100%) !important;
  border: 1px solid rgba(99, 102, 241, 0.1) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  box-shadow: none;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .scanner-device-details {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .scanner-device-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .scanner-device-icon {
    margin-left: 32px;
  }
  
  .scanner-checkbox {
    position: absolute;
    top: 16px;
    left: 16px;
  }
  
  .scanner-stats {
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
  }
  
  .scanner-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
} 