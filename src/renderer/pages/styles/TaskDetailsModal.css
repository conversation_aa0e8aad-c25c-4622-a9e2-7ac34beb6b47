/* Task Details Modal Styles */
.task-details-modal .ant-modal-content {
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  overflow: hidden;
}

.task-details-modal .ant-modal-header {
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding: 16px 20px 12px;
  border-radius: 12px 12px 0 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
}

.task-details-modal .ant-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #64748b;
  margin: 0;
  letter-spacing: 0.6px;
}

.task-modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-modal-title .anticon {
  font-size: 18px;
  color: #6366f1;
}

.task-details-modal .ant-modal-body {
  padding: 20px;
  background: transparent;
}

.task-details-modal .ant-modal-footer {
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  padding: 12px 20px;
  border-radius: 0 0 12px 12px;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
}

/* Form Content */
.task-form-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.task-form-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-form-label {
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
  letter-spacing: 0.3px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.task-form-label.required::after {
  content: ' *';
  color: #ff4d4f;
  font-weight: 600;
}

.task-form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #334155;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.task-form-input:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background: #ffffff;
}

.task-form-input::placeholder {
  color: #94a3b8;
  font-style: italic;
}

.task-form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #334155;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  min-height: 100px;
  resize: vertical;
  line-height: 1.5;
}

.task-form-textarea:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background: #ffffff;
}

.task-form-textarea::placeholder {
  color: #94a3b8;
  font-style: italic;
}

/* Button Styles */
.task-modal-button {
  height: 36px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  padding: 0 20px;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.task-modal-button-default {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(148, 163, 184, 0.2);
  color: #64748b;
}

.task-modal-button-default:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: rgba(99, 102, 241, 0.2);
  color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
}

.task-modal-button-primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: 1px solid rgba(99, 102, 241, 0.2);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.task-modal-button-primary:hover {
  background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%);
  border: 1px solid rgba(99, 102, 241, 0.3);
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
}

.task-modal-button-primary:disabled {
  background: linear-gradient(135deg, #a5a6f6 0%, #c4b5fa 100%);
  border: 1px solid rgba(99, 102, 241, 0.1);
  color: rgba(255, 255, 255, 0.7);
  box-shadow: none;
  cursor: not-allowed;
  transform: none;
}

.task-modal-button-primary:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Info Banner */
.task-info-banner {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin-bottom: 16px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 8px;
}

.task-info-icon {
  font-size: 16px;
  color: #6366f1;
}

.task-info-text {
  font-size: 13px;
  color: #64748b;
  line-height: 1.4;
}

/* Loading State */
.task-form-loading {
  opacity: 0.7;
  pointer-events: none;
}

.task-form-loading .task-form-input,
.task-form-loading .task-form-textarea {
  background: rgba(248, 250, 252, 0.8);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .task-details-modal .ant-modal-content {
    margin: 16px;
    width: calc(100% - 32px);
  }
  
  .task-details-modal .ant-modal-body {
    padding: 16px;
  }
  
  .task-form-content {
    gap: 16px;
  }
  
  .task-modal-button {
    height: 40px;
    font-size: 15px;
  }
} 