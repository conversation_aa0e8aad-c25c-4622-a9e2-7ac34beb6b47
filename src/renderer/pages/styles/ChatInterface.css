.chat-sender input::placeholder {
  color: #bfbfbf;
  font-size: 15px;
  font-style: italic;
  opacity: 1;
}

/* 聊天界面样式 */
#chat-container {
  width: 100%;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  padding: 5px;
  /* 使用现代化的分割线替代实线边框 */
  position: relative;
}

/* 聊天容器的现代化分割线 */
#chat-container::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(
      to bottom,
      transparent 0%,
      rgba(179, 179, 179, 0.1) 10%,
      rgba(179, 179, 179, 0.2) 50%,
      rgba(179, 179, 179, 0.1) 90%,
      transparent 100%
  );
  pointer-events: none;
}

.chat-messages {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  padding: 5px;
  /* background-color: var(--main-bg-color); */
  /* border-radius: 5px; */
  border: none;
  scrollbar-width: none;
  -ms-overflow-style: none;
  user-select: text; /* 允许聊天消息中的文本选择 */
  -webkit-user-select: text; /* Safari 支持 */
  -moz-user-select: text; /* Firefox 支持 */
  -ms-user-select: text; /* IE/Edge 支持 */
}

.chat-messages::-webkit-scrollbar {
  display: none;
}

.chat-messages {
  padding: 8px;
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.chat-input-area {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  user-select: text; /* 允许输入区域的文本选择 */
  -webkit-user-select: text; /* Safari 支持 */
  -moz-user-select: text; /* Firefox 支持 */
  -ms-user-select: text; /* IE/Edge 支持 */
  background-color: transparent;
  padding: 10px;
}

.chat-input-area .input-row {
  display: flex;
  gap: 3px;
}

.ant-bubble-content {
  background-color: #e5eefc !important;
}

/* 演示按钮容器样式 */
.chat-demo-buttons-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

/* 问题消息中的演示按钮基础样式 */
.chat-demo-buttons-container .chat-demo-button,
.chat-demo-buttons-container .chat-end-demo-button,
.chat-demo-buttons-container .chat-text-reply-button {
  flex: 1;
  margin-top: -5px;
  margin-right: 2px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  white-space: nowrap;
}

/* 开始演示按钮样式 */
.chat-demo-buttons-container .chat-demo-button {
  background-color: #e5eefc;
}

/* 结束演示按钮样式 */
.chat-demo-buttons-container .chat-end-demo-button {
  background: #ec5b56;
}

/* 文本回复按钮样式 */
.chat-demo-buttons-container .chat-text-reply-button {
  background-color: #e5eefc;
}

/* 按钮悬停效果 */
.chat-demo-buttons-container .chat-demo-button:hover,
.chat-demo-buttons-container .chat-end-demo-button:hover,
.chat-demo-buttons-container .chat-text-reply-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  opacity: 0.9;
}

/* 引用提示样式 */
.chat-quote-hint {
  padding: 8px;
  margin-bottom: 8px;
  margin-right: 5px;
  border-left: 3px solid var(--button-text-color);
  border-radius: 4px;
  font-size: 0.9em;
  color: var(--text-color-light);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  min-height: 32px;
}

/* 引用内容样式 */
.chat-quote-content {
  flex: 1;
  overflow-wrap: break-word;
  white-space: normal;
  line-height: 1.4;
}

/* 关闭按钮样式 */
.chat-quote-close {
  cursor: pointer;
  padding: 0 5px;
  font-size: 1.2em;
  color: var(--text-color-light);
  opacity: 0.7;
  transition: opacity 0.2s;
  flex-shrink: 0;
  line-height: 1;
}

.chat-quote-close:hover {
  opacity: 1;
}

.chat-sender {
  background-color: white;
  margin-bottom: 10px;
  user-select: text; /* 允许输入框的文本选择 */
  -webkit-user-select: text; /* Safari 支持 */
  -moz-user-select: text; /* Firefox 支持 */
  -ms-user-select: text; /* IE/Edge 支持 */
}

/* 任务创建模式下的输入框样式 */
.chat-interface-container .task-creation-mode .chat-sender {
  border: 2px solid rgba(99, 102, 241, 0.3);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

/* Card styles for table and image messages */
.chat-card {
  margin-top: 8px;
  border: 1px solid #d9d9d9;
}

.chat-card .antCardBody {
  padding: 10px !important;
}

.chat-card-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Table notification specific styles */
.chat-table-container {
  width: 100%;
}

.chat-table-header {
  margin-bottom: 4px;
  font-size: 14px;
  color: #666;
}

/* Image notification specific styles */
.chat-image-container {
  width: 100%;
}

.chat-image-header {
  margin-bottom: 4px;
  font-size: 14px;
  color: #666;
}

.chat-image-card {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chat-image-card.ant-card-hoverable:hover,
.chat-image-card:hover {
  box-shadow: none !important;
}

.chat-image-card .antCardBody {
  padding: 0 !important;
}

/* Prompts 组件样式 */
.ant-prompts {
  margin: 16px;
  padding: 16px;
  background-color: var(--main-bg-color);
  border-radius: 8px;
}

.ant-prompts-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 16px;
}

.ant-prompts-item {
  padding: 12px;
  margin: 8px 0;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.ant-prompts-item:hover {
  background-color: var(--controls-bg-color);
  transform: translateX(5px);
}

.ant-prompts-item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ant-prompts-item-icon {
  margin-right: 12px;
}

.ant-prompts-item-description {
  font-size: 0.95rem;
  color: var(--text-color);
}

.ant-prompts-item-description {
  color: #999;
}

/* 聊天界面样式 */
.chat-interface-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  background-color: transparent;
  width: 100%;
}

.chat-interface-header {
  padding: 10px 16px;
  position: relative;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  min-height: 44px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 0 12px 0 20px; /* 增加内边距 */
  border-bottom: 1px solid rgba(148, 163, 184, 0.1); /* 更柔和的边框 */
  color: #64748b; /* 更现代的灰色 */
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); /* 现代化的渐变背景 */
  backdrop-filter: blur(10px);
  z-index: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  user-select: none; /* Prevent text selection during drag */
  transition: background-color 0.2s ease; /* Smooth transition for hover effects */
}

.chat-interface-header-left {
  display: flex;
  align-items: center;
}

.chat-interface-header-right {
  display: flex;
  align-items: center;
}

/* 任务创建指示器样式 */
.task-creation-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(99, 102, 241, 0.2);
  /* 添加轻微的动画效果，提示用户当前在创建任务 */
  animation: task-creation-pulse 2s infinite;
}

@keyframes task-creation-pulse {
  0% { 
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
  }
  70% { 
    box-shadow: 0 0 0 6px rgba(99, 102, 241, 0);
  }
  100% { 
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

.task-creation-text {
  font-size: 12px;
  color: #6366f1;
  font-weight: 500;
  white-space: nowrap;
}

.task-creation-cancel-btn {
  padding: 2px 6px !important;
  height: auto !important;
  border-radius: 12px !important;
  font-size: 12px !important;
  color: #6366f1 !important;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.task-creation-cancel-btn:hover {
  background-color: rgba(99, 102, 241, 0.1) !important;
  color: #4f46e5 !important;
}

/* 聊天界面头部的现代化分割线 */
.chat-interface-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
      to right,
      transparent 0%,
      rgba(179, 179, 179, 0.1) 10%,
      rgba(179, 179, 179, 0.2) 50%,
      rgba(179, 179, 179, 0.1) 90%,
      transparent 100%
  );
  pointer-events: none;
}

.chat-interface-title {
  margin-left: 8px;
}

.chat-interface-header .anticon {
  color: rgb(99, 102, 241);
}

/* Optimize rendering during dragging */
.chat-interface-container.dragging {
  backdrop-filter: none !important;
}

.chat-interface-container.dragging .chat-interface-header {
  backdrop-filter: none !important;
  box-shadow: none !important;
}

.chat-interface-container.dragging .chat-message {
  transition: none !important;
}

.chat-interface-container.dragging .chat-message * {
  transition: none !important;
  animation: none !important;
}