import sys
import os
import subprocess
import tempfile
from datetime import datetime
import select
import time
import re

# 根据操作系统选择不同的模块
if os.name == 'posix':
    import fcntl
else:
    import msvcrt

class ScriptsExecutor:
    """
    This module is used to execute scripts using RuyiAgent.
    """
    def __init__(self):
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        # 跨平台日志路径设置
        if os.name == 'nt':  # Windows 系统
            self.log_file = os.path.join(os.path.expanduser('~'), 'Documents', 'ruyi_agent.log')
        else:  # macOS/Linux 系统
            self.log_file = os.path.join(os.path.expanduser('~'), 'Desktop', 'ruyi_agent.log')
        
        self.execute_process = None
        self.task_end = False
        self.task_success = False
        self.log_queue = None  # 新增 log_queue 属性

        # 定义模板字符串
        self.template_content = '''# -*- coding: utf-8 -*-
import sys
import os

# 添加 RuyiAgent 的 Python 路径
sys.path.append("{ruyi_agent_path}")
sys.path.append("{pyarmor_runtime_path}")

from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask
from ruyi.config import RuyiConfig, RuyiArgParser

class DynamicTask(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = "{task_description}"
        self.code_script_labeled = """{code_script_labeled}"""
        self.NL_script_labeled = """{NL_script_labeled}
"""

        
    def main(self, agent):
        device, ui, data, fm, tools = agent.device, agent.ui, agent.data, agent.fm, agent.tools
{script_content}

if __name__ == '__main__':
    yaml_file = os.path.join("{ruyi_agent_path}", 'config.yaml')
    parser = RuyiArgParser((RuyiConfig,))
    config = parser.parse_yaml_file(yaml_file=yaml_file)[0]
    agent = RuyiAgent(config)
    task = DynamicTask()
    agent.task.execute_task(task)
'''
        self.log_buffer = ""  # 新增日志缓冲区

    def set_log_queue(self, log_queue):
        """设置 log_queue"""
        self.log_queue = log_queue

    def _log(self, message, is_error=False):
        """跨平台日志写入"""
        # 去除ANSI转义字符
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        message = ansi_escape.sub('', message)
        
        # timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # log_type = "ERROR" if is_error else "INFO"
        # log_message = f"[{timestamp}] [{log_type}] {message}\n"
        log_message = message
        
        # try:
        #     # 确保日志目录存在
        #     os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        #     # 使用追加模式，并立即刷新缓冲区
        #     with open(self.log_file, 'a', encoding='utf-8', buffering=1) as f:
        #         f.write(log_message)
        #         f.flush()
        #         os.fsync(f.fileno())  # 确保写入磁盘
        # except Exception as e:
        #     # 如果写入日志失败，至少尝试打印出来
        #     print(f"写入日志失败: {str(e)}")
        #     print(log_message)
        
        # 将日志同时写入缓冲区
        self.log_buffer += log_message

        # 如果 log_queue 存在，将日志写入队列
        if self.log_queue:
            self.log_queue.put(log_message)

    def get_log(self):
        """获取当前日志内容并清空缓冲区"""
        log_content = self.log_buffer
        self.log_buffer = ""  # 清空缓冲区
        return log_content

    def check_task_end(self, log_content):
        """检查任务是否结束"""
        if "error" in log_content:
            self.task_success = False
            return True
        elif "finish task" in log_content:
            self.task_success = True
            return True
        else:
            return False

    def execute_scripts(self, scripts, code_script_labeled, NL_script_labeled, task="task"):
        self.log_buffer = ""  # 每次执行前清空缓冲区
        self.task_end = False
        self.task_success = False

        # 如果上一次执行进程存在，则杀死进程
        if self.execute_process:
            self.execute_process.kill()
        self.execute_process = None

        self._log(f"{'='*60}")  # 添加分隔符
        self._log(f"开始新的任务执行")
        # 对 task 中的双引号进行转义
        escaped_task = task.replace('"', '\\"')
        self._log(f"任务名称: {escaped_task}")
        # self._log(f"脚本内容:\n{scripts}")
        
        try:
            # 获取 RuyiAgent 路径（修复路径分隔符问题）
            ruyi_agent_path = os.path.join(os.path.dirname(self.current_dir), 'RuyiAgent')
            # pyarmor_runtime_path = os.path.join(os.path.dirname(self.current_dir), 'pyarmor_runtime_000000')
            pyarmor_runtime_path = os.path.dirname(os.path.dirname(self.current_dir))

            # 将路径转换为原始字符串格式
            ruyi_agent_path = os.path.normpath(ruyi_agent_path).replace('\\', '/')  # 统一使用正斜杠
            pyarmor_runtime_path = os.path.normpath(pyarmor_runtime_path).replace('\\', '/')  # 统一使用正斜杠
            # self._log(f"RuyiAgent 路径: {ruyi_agent_path}")
            # self._log(f"pyarmor_runtime 路径: {pyarmor_runtime_path}")
            # 准备脚本内容（添加适当的缩进）
            formatted_scripts = '\n'.join(f'        {line}' for line in scripts.splitlines())
            
            # 替换模板中的占位符（使用转义后的路径）
            task_content = self.template_content.format(
                ruyi_agent_path=ruyi_agent_path.replace('\\', r'\\'),  # 双重转义反斜杠
                pyarmor_runtime_path=pyarmor_runtime_path.replace('\\', r'\\'),  # 双重转义反斜杠
                task_description=escaped_task,
                code_script_labeled=code_script_labeled,
                NL_script_labeled=NL_script_labeled,
                script_content=formatted_scripts
            )
            
            # 创建临时文件
            try:
                with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_file:
                    temp_file.write(task_content)
                    temp_path = temp_file.name
                    # self._log(f"创建临时文件: {temp_path}")
                    # self._log(f"创建临时文件内容: {task_content}")
            except Exception as e:
                self._log(f"创建临时文件失败: {str(e)}", is_error=True)
                raise
            
            try:
                # self._log(f"执行临时文件: {temp_path}")
                # 执行临时文件并实时获取输出
                self.execute_process = subprocess.Popen(
                    [sys.executable, "-u", temp_path],  # unbuffered 模式
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding="utf-8",  # 指定编码为 utf-8
                    bufsize=1,
                    universal_newlines=True,
                    env={**os.environ, 'PYTHONUNBUFFERED': '1', 'PYTHONIOENCODING': 'utf-8'},  # 添加环境变量
                    start_new_session=True
                )
                # self._log(f"启动执行临时文件进程: {self.execute_process.pid}")

                if os.name == 'posix':
                    # Unix/Linux 系统：设置非阻塞读取
                    for fd in [self.execute_process.stdout, self.execute_process.stderr]:
                        fl = fcntl.fcntl(fd, fcntl.F_GETFL)
                        fcntl.fcntl(fd, fcntl.F_SETFL, fl | os.O_NONBLOCK)
                else:
                    # Windows 系统：使用线程读取输出
                    import threading, queue
                    stdout_queue = queue.Queue()
                    stderr_queue = queue.Queue()
                    
                    def enqueue_output(pipe, output_queue):
                        for line in iter(pipe.readline, ''):
                            output_queue.put(line)
                        pipe.close()
                    
                    threading.Thread(target=enqueue_output, args=(self.execute_process.stdout, stdout_queue), daemon=True).start()
                    threading.Thread(target=enqueue_output, args=(self.execute_process.stderr, stderr_queue), daemon=True).start()
                
                stop_reading = False
                while not stop_reading:
                    if os.name == 'nt':
                        # Windows 平台：从线程队列中获取输出
                        while not stdout_queue.empty():
                            line = stdout_queue.get()
                            print(line, end='', flush=True)
                            self._log(line.strip())
                            if self.check_task_end(line):
                                self.task_end = True
                                stop_reading = True
                                break
                        while not stderr_queue.empty():
                            line = stderr_queue.get()
                            print(line, end='', file=sys.stderr, flush=True)
                            self._log(line.strip(), is_error=True)
                            if self.check_task_end(line):
                                self.task_end = True
                                stop_reading = True
                                break
                    else:
                        # macOS/Linux 平台：继续使用非阻塞读取
                        output = ''
                        try:
                            chunk = self.execute_process.stdout.read()
                            if chunk:
                                output = chunk.decode('utf-8', errors='replace') if isinstance(chunk, bytes) else str(chunk)
                                print(output, end='')
                                self._log(output.strip())
                                if self.check_task_end(output):
                                    self.task_end = True
                                    stop_reading = True
                        except Exception as e:
                            pass

                        # 读取错误输出
                        error = ''
                        try:
                            err_chunk = self.execute_process.stderr.read()
                            if err_chunk:
                                error = err_chunk.decode('utf-8', errors='replace') if isinstance(err_chunk, bytes) else str(err_chunk)
                                print(error, end='', file=sys.stderr)
                                self._log(error.strip(), is_error=True)
                                if self.check_task_end(error):
                                    self.task_end = True
                                    stop_reading = True
                        except Exception as e:
                            pass
                    
                    # 检查进程是否结束
                    if self.execute_process.poll() is not None:
                        break

                    # 适当降低CPU占用
                    time.sleep(0.1)

                success = self.task_success
                self._log(f"任务执行结束{'成功' if success else '失败'}")
                return success
                
            except Exception as e:
                self._log(f"执行脚本时发生错误: {str(e)}", is_error=True)
                raise
            
        except Exception as e:
            self._log(f"发生未预期的错误: {str(e)}", is_error=True)
            raise
            
        finally:
            # 清理临时文件
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    self._log("临时文件已清理")
            except Exception as e:
                self._log(f"清理临时文件失败: {str(e)}", is_error=True)
            self._log("任务结束")
            self._log(f"{'='*60}")  # 添加结束分隔符


if __name__ == '__main__':
    scripts = "device.start_app('Contacts')\nui.root.locate_view('Create Contact').click()"
    scripts = "device.start_app('Contacts')\nui.root.locate_view('Create Contact').click()\nui.root.locate_view('First name').input('Ruyi')\nui.root.locate_view('Number').input('1234567890')\nui.root.locate_view('Save').click()"
    scripts = """device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm

# Open the Camera app
print("device.start_app('Camera')")
device.start_app('Camera')
print("device.start_app('Camera') END")

# # Click on the video mode button to switch to video mode
ui.root.locate_view('录像').click()
print("ui.root.locate_view('录像').click() END")

# # Locate the take video button and click it to start recording
ui.root.locate_view('Take video button').click()

# # Wait for 5 seconds to record the video
# agent.sleep(1)

# # Click the stop button on the bottom of the screen again to stop recording
# ui.root.locate_view('Stop button on the bottom of the screen').click()

# # Navigate back to exit the Camera app
# device.back()
"""
    executor = ScriptsExecutor()
    success = executor.execute_scripts(scripts, task="Create Contact")
    print(f"ScriptsExecutor 任务执行{'成功' if success else '失败'}")


