from flask import Flask
from flask_cors import CORS
import os
import sys
import platform
from flask_socketio import SocketIO

socketio = SocketIO()

def create_app():
    app = Flask(__name__)
    
    # 初始化扩展
    CORS(app, origins='*')
    socketio.init_app(app, cors_allowed_origins="*", async_mode='threading')
    
    # 根据平台选择对应的文件夹名
    if platform.system() == 'Windows':
        platform_folder = 'win32'
    elif platform.system() == 'Darwin':  # macOS
        platform_folder = 'darwin'
    else:  # Linux
        platform_folder = 'linux'
    
    # 获取正确的 adb 路径
    if getattr(sys, 'frozen', False):
        # 运行在打包环境
        application_path = os.path.dirname(sys.executable)
        platform_tools_path = os.path.join(application_path, '..', '..', 'resources', 'platform-tools', platform_folder)
    else:
        # 运行在开发环境
        application_path = os.path.dirname(os.path.abspath(__file__))
        platform_tools_path = os.path.join(application_path, '..', '..', 'resources', 'platform-tools', platform_folder)
    
    # 将 adb 路径添加到环境变量
    os.environ['PATH'] = f"{platform_tools_path}{os.pathsep}{os.environ['PATH']}"


    # 注册蓝图
    from .routes import main as main_blueprint
    # from .routesSimplified import main as main_blueprint
    app.register_blueprint(main_blueprint)
    
    return app, socketio
