"""
Browser HTTP Client for Electron Communication
"""
import requests
import time
from typing import Optional, Dict, Any
import logging
from config import config

logger = logging.getLogger(__name__)

class BrowserClient:
    """HTTP client for browser communication with Electron"""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize the browser client
        
        Args:
            base_url: Base URL for the Electron HTTP server. If None, will use port 3000
        """
        self.base_url = base_url or f"http://localhost:{config.electron_http_port}"
        self.session = requests.Session()
        self.session.timeout = 5  # 5 seconds timeout
        
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Make an HTTP request to the Electron server
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request data
            
        Returns:
            Response data as dictionary
            
        Raises:
            requests.RequestException: If the request fails
        """
        url = f"{self.base_url}{endpoint}"
        max_retries = 3
        retry_delay = 1  # seconds
        
        for attempt in range(max_retries):
            try:
                response = self.session.request(method, url, json=data)
                response.raise_for_status()
                return response.json() if response.content else {}
            except requests.RequestException as e:
                if attempt == max_retries - 1:
                    logger.error(f"Request failed after {max_retries} attempts: {str(e)}")
                    raise
                logger.warning(f"Request failed (attempt {attempt + 1}/{max_retries}): {str(e)}")
                time.sleep(retry_delay)
    
    def create_browser_view(self, url: Optional[str] = None, device_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new browser view
        
        Args:
            url: URL to load in the browser view
            device_id: Device ID for session persistence
        
        Returns:
            Response data
        """
        data = {}
        if url:
            data['url'] = url
        if device_id:
            data['deviceId'] = device_id
            
        return self._make_request('POST', '/browser/create', data if data else None)
    
    def get_browser_bounds(self) -> Dict[str, int]:
        """
        Get the browser view bounds
        
        Returns:
            Dictionary containing width and height
        """
        return self._make_request('POST', '/browser/command', {
            'command': 'getBounds'
        })
    
    def capture_page(self) -> bytes:
        """
        Capture the current page as a screenshot
        
        Returns:
            Screenshot data as bytes
        """
        response = self._make_request('POST', '/browser/command', {
            'command': 'capturePage'
        })
        return response.get('data', b'')
    
    def key_press(self, key: str) -> None:
        """
        Simulate a key press
        
        Args:
            key: Key to press
        """
        self._make_request('POST', '/browser/command', {
            'command': 'keyPress',
            'params': {'key': key}
        })
    
    def destroy_browser_view(self) -> None:
        """
        Destroy the browser view
        """
        self._make_request('POST', '/browser/destroy')

# Create a singleton instance
browser_client = BrowserClient() 