import subprocess
import platform
import tempfile
import os
from PIL import Image

class ADBHelper:
    """
    跨平台的 ADB 命令执行助手
    统一处理 Windows/Mac/Linux 平台上的 ADB 命令执行
    """
    def __init__(self):
        self.is_windows = platform.system().lower() == 'windows'
        # Windows 平台特定的 startupinfo 配置
        if self.is_windows:
            self.startupinfo = subprocess.STARTUPINFO()
            self.startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            self.startupinfo.wShowWindow = subprocess.SW_HIDE
        else:
            self.startupinfo = None

    def run_cmd(self, cmd, **kwargs):
        """
        执行 ADB 命令
        Args:
            cmd: 要执行的命令，可以是字符串或列表
            **kwargs: 传递给 subprocess.run 的其他参数
        Returns:
            subprocess.CompletedProcess 对象
        """
        default_kwargs = {
            'shell': isinstance(cmd, str),
            'capture_output': True,
            'text': True,
            'encoding': 'utf-8'
        }

        # 在 Windows 上添加 startupinfo
        if self.is_windows:
            default_kwargs['startupinfo'] = self.startupinfo

        # 合并默认参数和传入的参数
        kwargs = {**default_kwargs, **kwargs}
        
        try:
            return subprocess.run(cmd, **kwargs)
        except subprocess.CalledProcessError as e:
            print(f"ADB command failed: {e}")
            raise 

    def take_screenshot(self, device_id):
        """
        使用 ADB 命令进行截图（优化版本，使用 exec-out 直接输出）
        Args:
            device_id: 设备ID
        Returns:
            PIL.Image 对象
        """
        try:
            # 创建临时文件来存储截图
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_path = temp_file.name
            
            # 使用 adb exec-out 直接将截图数据输出到本地文件
            screencap_cmd = f"adb -s {device_id} exec-out screencap -p > {temp_path}"
            
            # 使用shell=True来支持重定向操作
            result = subprocess.run(
                screencap_cmd,
                shell=True,
                check=True,
                capture_output=False,  # 不捕获输出，因为要重定向到文件
                startupinfo=self.startupinfo if self.is_windows else None
            )
            
            # 加载图片
            image = Image.open(temp_path)
            
            # 删除本地临时文件
            os.remove(temp_path)
            
            return image
            
        except subprocess.CalledProcessError as e:
            # 清理临时文件
            if 'temp_path' in locals() and os.path.exists(temp_path):
                os.remove(temp_path)
            raise Exception(f"ADB screenshot failed: {str(e)}")
        except Exception as e:
            # 清理临时文件
            if 'temp_path' in locals() and os.path.exists(temp_path):
                os.remove(temp_path)
            raise Exception(f"ADB screenshot failed: {str(e)}") 
