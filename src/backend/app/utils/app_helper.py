import os
import subprocess
import threading
import time
import re
from .adb_helper import ADBHelper
from config import config

class AppHelper:
    """
    辅助进行与 app 相关的操作：
    1. 根据包名获取 app 名称
    2. 判断 Ruyi Client 是否安装
    3. 安装 Ruyi Client
    4. 启动 Ruyi Client
    5. 判断 Ruyi Client 的权限是否授予
    6. 检查 Ruyi Client 版本
    """
    def __init__(self, device=""):
        self.device = device
        self.aapt_path = './assets/aapt-binaries/aapt-arm-pie'
        self.remote_aapt_path = '/data/local/tmp/aapt-arm-pie'
        self.apk_path = ''
        self.package_name = ''
        self.app_name = ''
        self.adb_helper = ADBHelper()
        # self.push_aapt()
        # self.chmod_aapt()

    def push_aapt(self):
        """
        推送 aapt 到手机上
        """
        if self.device:
            push_cmd = f'adb -s {self.device} push {self.aapt_path} {self.remote_aapt_path}'
        else:
            push_cmd = f'adb push {self.aapt_path} {self.remote_aapt_path}'
        self.adb_helper.run_cmd(push_cmd)

    def chmod_aapt(self):
        """
        给 aapt 文件在手机上授权
        """
        if self.device:
            chmod_cmd = f'adb -s {self.device} shell chmod 0755 {self.remote_aapt_path}'
        else:
            chmod_cmd = f'adb shell chmod 0755 {self.remote_aapt_path}'
        self.adb_helper.run_cmd(chmod_cmd)
    
    def get_apk_path(self):
        """
        获取 apk 路径
        """
        if self.device:
            get_cmd = f"adb -s {self.device} shell pm list packages -f"
        else:
            get_cmd = f"adb shell pm list packages -f"
        result = self.adb_helper.run_cmd(get_cmd)
        apk_path_list = result.stdout.split('\n')
        self.apk_path = ""
        for apk_path in apk_path_list:
            if self.package_name in apk_path:
                self.apk_path = apk_path.split("package:")[1]
                self.apk_path = self.apk_path[:self.apk_path.rfind("=")]
                return self.apk_path

    def parse_name_from_apk(self):
        """
        使用 aapt 解析 apk
        """
        if self.device:
            dump_cmd = f'adb -s {self.device} shell {self.remote_aapt_path} d badging {self.apk_path}'
        else:
            dump_cmd = f'adb shell {self.remote_aapt_path} d badging {self.apk_path}'
        result = self.adb_helper.run_cmd(dump_cmd)
        apk_info_list = result.stdout.split('\n')
        for apk_info in apk_info_list:
            if "application-label" in apk_info:
                self.app_name = apk_info
                left_index = self.app_name.find("'")
                right_index = self.app_name.rfind("'")
                self.app_name = self.app_name[left_index + 1:right_index]
                return self.app_name

    def get_name_from_package(self, package_name):
        """
        根据 app 包名, 获取其名称
        """
        self.package_name = package_name
        self.app_name = ""
        self.get_apk_path()
        self.parse_name_from_apk()
        return self.app_name

    def is_ruyi_client_installed(self, device_id):
        """
        判断 Ruyi Client 是否安装
        """
        PACKAGE_NAME = "com.example.ruyiclient"
        
        check_command = ['adb', '-s', device_id, 'shell', 'pm', 'list', 'packages', PACKAGE_NAME]
        result = self.adb_helper.run_cmd(check_command)
        
        # 如果未安装，进行安装
        if PACKAGE_NAME not in result.stdout:
            return False
        return True

    def get_ruyi_client_version(self, device_id):
        """
        获取已安装的 Ruyi Client 版本号
        """
        PACKAGE_NAME = "com.example.ruyiclient"
        
        try:
            # 获取包信息
            check_command = ['adb', '-s', device_id, 'shell', 'dumpsys', 'package', PACKAGE_NAME]
            result = self.adb_helper.run_cmd(check_command)
            
            # 从输出中提取版本号
            lines = result.stdout.split('\n')
            for line in lines:
                if 'versionName=' in line:
                    version_match = re.search(r'versionName=([^\s]+)', line)
                    if version_match:
                        return version_match.group(1)
            
            return None
        except Exception as e:
            print(f"获取 Ruyi Client 版本失败: {e}")
            return None

    def compare_versions(self, version1, version2):
        """
        比较两个版本号
        返回值：
        - 1: version1 > version2
        - 0: version1 == version2
        - -1: version1 < version2
        """
        if not version1 or not version2:
            return None
            
        def version_tuple(v):
            return tuple(map(int, (v.split("."))))
        
        try:
            v1_tuple = version_tuple(version1)
            v2_tuple = version_tuple(version2)
            
            if v1_tuple > v2_tuple:
                return 1
            elif v1_tuple == v2_tuple:
                return 0
            else:
                return -1
        except Exception as e:
            print(f"版本比较失败: {e}")
            return None

    def should_update_ruyi_client(self, device_id):
        """
        检查是否需要更新 Ruyi Client
        返回：
        - True: 需要更新（未安装或版本过低）
        - False: 不需要更新
        """
        # 检查是否安装
        if not self.is_ruyi_client_installed(device_id):
            print(f"设备 {device_id} 未安装 Ruyi Client，需要安装")
            return True
        
        # 获取已安装版本
        installed_version = self.get_ruyi_client_version(device_id)
        if not installed_version:
            print(f"设备 {device_id} 无法获取 Ruyi Client 版本，需要重新安装")
            return True
        
        # 获取配置中的目标版本
        target_version = config.get('ruyi_client_version', '0.2.0.3')
        
        # 比较版本
        comparison = self.compare_versions(installed_version, target_version)
        if comparison is None:
            print(f"设备 {device_id} 版本比较失败，需要重新安装")
            return True
        elif comparison < 0:
            print(f"设备 {device_id} Ruyi Client 版本过低 ({installed_version} < {target_version})，需要更新")
            return True
        else:
            print(f"设备 {device_id} Ruyi Client 版本符合要求 ({installed_version} >= {target_version})")
            return False

    def grant_accessibility_permission(self, device_id):
        """
        自动授予 Ruyi Client 无障碍服务权限
        """
        try:
            print(f"正在为设备 {device_id} 授予无障碍服务权限...")
            
            # 启用无障碍服务
            accessibility_service = "com.example.ruyiclient/com.example.ruyiclient.executor.MyAccessibilityService"
            
            # 先获取当前启用的无障碍服务
            get_cmd = ['adb', '-s', device_id, 'shell', 'settings', 'get', 'secure', 'enabled_accessibility_services']
            # adb shell settings put secure enabled_accessibility_services com.example.ruyiclient/com.example.ruyiclient.executor.MyAccessibilityService
            current_services_result = self.adb_helper.run_cmd(get_cmd)
            current_services = current_services_result.stdout.strip()
            
            # 如果当前服务列表为空或null，直接设置新服务
            if not current_services or current_services == 'null':
                new_services = accessibility_service
            else:
                # 如果服务已经存在，则不需要重复添加
                if accessibility_service in current_services:
                    print(f"设备 {device_id} 无障碍服务权限已存在")
                    return True
                # 添加新服务到现有服务列表
                new_services = f"{current_services}:{accessibility_service}"
            
            # 设置无障碍服务
            set_cmd = ['adb', '-s', device_id, 'shell', 'settings', 'put', 'secure', 'enabled_accessibility_services', new_services]
            self.adb_helper.run_cmd(set_cmd, check=True)
            
            # 启用无障碍功能
            enable_cmd = ['adb', '-s', device_id, 'shell', 'settings', 'put', 'secure', 'accessibility_enabled', '1']
            self.adb_helper.run_cmd(enable_cmd, check=True)
            
            print(f"设备 {device_id} 无障碍服务权限授予成功")
            return True
            
        except Exception as e:
            print(f"设备 {device_id} 无障碍服务权限授予失败: {e}")
            return False

    def grant_notification_permission(self, device_id):
        """
        自动授予 Ruyi Client 通知权限
        """
        try:
            print(f"正在为设备 {device_id} 授予通知权限...")
            
            # 授予通知权限
            grant_cmd = ['adb', '-s', device_id, 'shell', 'pm', 'grant', 'com.example.ruyiclient', 'android.permission.POST_NOTIFICATIONS']
            self.adb_helper.run_cmd(grant_cmd, check=True)
            
            print(f"设备 {device_id} 通知权限授予成功")
            return True
            
        except Exception as e:
            print(f"设备 {device_id} 通知权限授予失败: {e}")
            return False

    def auto_grant_permissions(self, device_id):
        """
        自动授予 Ruyi Client 所需的权限
        包括：无障碍服务权限、通知权限
        """
        print(f"开始为设备 {device_id} 自动授予权限...")
        
        success_count = 0
        total_permissions = 2
        
        # 授予无障碍服务权限
        if self.grant_accessibility_permission(device_id):
            success_count += 1
        
        # 授予通知权限
        if self.grant_notification_permission(device_id):
            success_count += 1
        
        success_rate = success_count / total_permissions
        print(f"设备 {device_id} 权限授予完成，成功率: {success_count}/{total_permissions}")
        
        return success_rate == 1.0

    def check_and_update_ruyi_client(self, device_id):
        """
        检查并更新 Ruyi Client
        返回：
        - dict: 包含检查和更新结果的字典
        """
        result = {
            'installed': False,
            'version_checked': False,
            'needs_update': False,
            'update_success': False,
            'installed_version': None,
            'target_version': config.get('ruyi_client_version', '0.2.0.3'),
            'permissions_granted': False,
            'message': ''
        }
        
        try:
            # 检查是否需要更新
            needs_update = self.should_update_ruyi_client(device_id)
            result['needs_update'] = needs_update
            
            if not needs_update:
                # 不需要更新，但仍需检查并授予权限
                result['installed'] = True
                result['version_checked'] = True
                result['update_success'] = True
                result['installed_version'] = self.get_ruyi_client_version(device_id)
                result['message'] = f"Ruyi Client 版本符合要求 ({result['installed_version']})"
                
                # 自动授予权限
                print(f"开始为设备 {device_id} 自动授予权限...")
                permissions_granted = self.auto_grant_permissions(device_id)
                result['permissions_granted'] = permissions_granted
                
                if permissions_granted:
                    result['message'] += "，权限授予成功"
                else:
                    result['message'] += "，权限授予部分失败"
                
                return result
            
            # 需要更新，进行安装
            print(f"开始为设备 {device_id} 安装/更新 Ruyi Client")
            install_success = self.install_ruyi_client(device_id)
            
            if install_success:
                result['installed'] = True
                result['update_success'] = True
                result['installed_version'] = self.get_ruyi_client_version(device_id)
                result['message'] = f"Ruyi Client 安装/更新成功 (版本: {result['installed_version']})"
                
                # 启动 Ruyi Client
                try:
                    self.start_ruyi_client(device_id)
                    result['message'] += "，已启动应用"
                except Exception as e:
                    result['message'] += f"，但启动失败: {e}"
                
                # 自动授予权限
                print(f"开始为设备 {device_id} 自动授予权限...")
                permissions_granted = self.auto_grant_permissions(device_id)
                result['permissions_granted'] = permissions_granted
                
                if permissions_granted:
                    result['message'] += "，权限授予成功"
                else:
                    result['message'] += "，权限授予部分失败"
            else:
                result['message'] = "Ruyi Client 安装/更新失败"
                
        except Exception as e:
            result['message'] = f"检查更新过程中出错: {e}"
            print(f"检查更新 Ruyi Client 时出错: {e}")
        
        return result
    
    def _check_installation(self, device_id, check_interval=1):
        """
        在后台检查安装状态
        """
        while True:
            if self.is_ruyi_client_installed(device_id):
                return True
            time.sleep(check_interval)

    def install_ruyi_client(self, device_id, timeout=60):
        """
        安装 Ruyi Client，并在后台检查安装状态。
        由于 subprocess 在调用 adb 在安装完成之后，并不会立即返回安装完成，因此添加了一个检查线程，
        在安装完成之后，检查线程会立即返回安装完成，否则会超时返回安装失败。
        Args:
            device_id: 设备ID
            timeout: 超时时间（秒）
        Returns:
            bool: 安装是否成功
        """
        current_dir = os.path.dirname(os.path.abspath(__file__))
        client_path = os.path.join(current_dir, '..', '..', 'assets', 'RuyiClient-release.apk')
        
        # 启动安装进程
        install_thread = threading.Thread(target=lambda: self.adb_helper.run_cmd(
            ['adb', '-s', device_id, 'install', '-r', client_path], 
            check=True
        ))
        install_thread.start()

        # 启动检查线程
        check_thread = threading.Thread(target=self._check_installation, args=(device_id,))
        check_thread.start()
        
        # 等待检查线程完成或超时
        check_thread.join(timeout)
        if check_thread.is_alive():
            return False  # 超时安装失败
        
        return True  # 安装成功

    def start_ruyi_client(self, device_id):
        """
        启动 Ruyi Client
        """
        start_command = ['adb', '-s', device_id, 'shell', 'am', 'start', '-n', 'com.example.ruyiclient/com.example.ruyiclient.MainActivity']
        self.adb_helper.run_cmd(start_command, check=True)

    def check_ruyi_client_authorized(self, device_id):
        """
        检查 Ruyi Client 是否授权，权限包括：
        无障碍服务（Accessibility Service）
        发送通知：android.permission.POST_NOTIFICATIONS
        录制/投射屏幕：-> 无法判断
        访问麦克风：android.permission.RECORD_AUDIO
        访问媒体：android.permission.READ_EXTERNAL_STORAGE、android.permission.WRITE_EXTERNAL_STORAGE
        显示在其他应用上层：android.permission.SYSTEM_ALERT_WINDOW -> 无法判断（授予了还是显示 granted=false）
        """
        # print(f"----- 检查设备 {device_id} 的权限 -----")

        # 需要检查的权限列表
        required_permissions = [
            # "android.permission.RECORD_AUDIO",  # 访问麦克风
            "android.permission.POST_NOTIFICATIONS",  # 发送通知
            # "android.permission.READ_EXTERNAL_STORAGE",  # 访问媒体
            # "android.permission.WRITE_EXTERNAL_STORAGE",  # 访问媒体
        ]

        # 检查无障碍服务是否开启
        accessibility_cmd = f'adb -s {device_id} shell settings get secure enabled_accessibility_services'
        accessibility_result = self.adb_helper.run_cmd(accessibility_cmd)
        accessibility_enabled = "com.example.ruyiclient" in accessibility_result.stdout
        print(f"无障碍服务是否开启: {accessibility_enabled}")

        # 获取指定包名的权限状态
        if device_id:
            # dump_cmd = f'adb -s {self.device} shell dumpsys package com.example.ruyiclient'
            dump_cmd = f'adb -s {device_id} shell dumpsys package com.example.ruyiclient'
        else:
            dump_cmd = f'adb shell dumpsys package com.example.ruyiclient'
        dump_result = self.adb_helper.run_cmd(dump_cmd)

        # 检查所有权限是否已授予
        all_granted = True
        for permission in required_permissions:
            # 匹配格式：android.permission.XXX: granted=true
            pattern = re.compile(rf'{re.escape(permission)}: granted=true')
            print(f"权限 {permission} 未授予" if not pattern.search(dump_result.stdout) else f"权限 {permission} 已授予")
            if not pattern.search(dump_result.stdout):
                all_granted = False

        # print(f"----- 检查设备 {device_id} 的权限 结束 -----")
        return all_granted and accessibility_enabled

    def forward_port(self, device_id, port):
        """
        转发手机的 6666 端口到本地 port 端口
        """
        self.adb_helper.run_cmd(f'adb -s {device_id} forward tcp:{port} tcp:6666')

if __name__ == '__main__':
    app_helper = AppHelper()
    # print(app_helper.get_name_from_package('com.simplemobiletools.camera'))
    # print(app_helper.get_name_from_package('com.tencent.mobileqq'))

    # print(app_helper.get_ruyi_client_authorized('MXG0222125015306'))
    # print(app_helper.check_ruyi_client_authorized('2FD5T21225004881'))
    # print(app_helper.check_ruyi_client_authorized('emulator-5554'))
    
    # 测试权限授予功能
    # test_device_id = 'emulator-5554'  # 请替换为实际的设备ID
    # print(f"测试设备 {test_device_id} 的权限授予功能:")
    # result = app_helper.auto_grant_permissions(test_device_id)
    # print(f"权限授予结果: {result}")
    
    pass

