# Ruyi 编程语言

欢迎来到 Ruyi (如意) 的世界！Ruyi 是一门简单而强大的编程语言，旨在让每个人都能轻松地将想法转化为可执行的程序，无论是自动化日常任务、处理数据，还是与智能设备交互。

---

## 1. Ruyi 是什么？

Ruyi (如意) 的核心思想是 **“所思即所得，万事皆如意”**。它致力于打破传统编程语言复杂的语法壁垒，让用户可以通过自然、直观的方式描述自己的需求，由强大的人工智能模型将其解释并执行。

编程能力的重要性不言而喻。人类有别于其他动物的关键特征是可以创造和使用工具，而编程恰是当今时代创造和使用工具的最佳方式。会编程的人可以借助机器将自己的能力无限地复制和放大，从而更高效地完成工作、享受生活、探索自己的兴趣。自计算机发明以来，各种编程语言不断涌现，编程可以实现的功能越来越丰富，而最近出现的AI模型和工具例如GPT、Claude、Deepseek、Cursor等又进一步将编程的效率大大提升… 未来，编程者的能力边界将加速扩大，成为“超级个体”。

你可能会想，现在AI那么厉害，代码都可以由AI生成，人类还需要会编程吗？我们坚定的认为 —— 需要，甚至比以往更加需要。如果你有过稍微比较深入的AI辅助编程经历就会明白，虽然AI可以代劳大部分的繁琐工作，但核心的架构、处理问题的逻辑往往还需要人来理解（尤其是当遇到一些细节问题需要修复的时候）。这也是为什么当前AI工具的受益者绝大部分是会编程的程序员。

<!-- 或许，未来会出现可以全自主编写代码、调试代码、解决问题的通用AI，但这个未来还不知道有多遥远，因为这意味着在“创造和使用工具”这个人类有别于其他动物的核心能力上，人被AI超越。我们或许不应该期盼这一天到来… -->

总之，在这个有趣的历史节点，我们看到了AI带来的效率革命和巨大潜力，而这个红利应该（但尚未）被更广泛的人群享受。我们希望改变这个现状：**让编程不再是少数人的专利，而是一种所有人都可以掌握的、用来与机器精确沟通的思考和表达方式。**

Ruyi 语言就是出于这一目的而设计，它不是为了替代任何一个已有的传统编程语言，而是希望让想编程的人可以绕过繁琐的代码语法、数据结构、算法学习过程（这些已经基本可以由 AI 代劳了），直接训练编程的核心逻辑和架构思维，并在自己的工作和生活中应用。**Ruyi 的主要特点包括：**

1. **普适易学**：为所有人设计，无论你是否具备编程背景，都能快速上手。相比其他编程语言，Ruyi 的学习门槛极低，如同学习一种新的沟通方式。
2. **功能实用**：无论是重复性的办公任务、复杂的数据整理与分析，还是自动控制智能设备、个人品牌或组织平台的搭建，Ruyi 都能帮你胜任。
3. **自然语言风格**：Ruyi 鼓励使用日常语言来编写指令，对语法没有僵硬的要求，也可以容忍指令的不精确，更注重理解你的意图。
4. **注重架构思维**：虽然简单，Ruyi 依然支持核心的编程概念，如变量、循环、条件判断、数据集合（如列表、对象、表格）和函数，帮助用户构建结构化的任务流程。
5. **AI原生解释器**：Ruyi代码是由AI大模型（**Ruyi解释器**）直接解释执行的，随着模型能力提升，Ruyi代码的执行准确性和运行速度也会随之提升。

Ruyi 的愿景是：**让全世界有编程能力的人口增加一个数量级。**

## 2. 快速开始

上手使用 Ruyi 非常简单，你只需要在我们的[官方网站](https://www.wisewk.com)上下载 "Ruyi IDE" 应用，打开后按照引导完成基本设置，就可以在编辑窗口中开始创建你的第一个 Ruyi 程序了。

**你的第一个 Ruyi 程序：**

想象你想让如意助手向你问好。你可以这样写：

```python
# 我的第一个如意程序
告诉用户 "你好，如意！"
```

接下来，点击Ruyi IDE中的“执行”按钮，你就可以从对话窗口中看到助手发来的问候消息。

**再来一个与 AI 互动的例子：**

```python
# 询问AI并获取信息
询问AI，输入是 "中国的首都是哪里？"，输出结果记为{首都}
告诉用户 "中国的首都是：{首都}"
```

看到了吗？就像在和一位聪明的助手对话一样，你只需用清晰的语言描述你的步骤和目标即可。

**你可以尝试直接与应用中内置的“Ruyi智能助手”对话，告诉它你的需求，让它帮你自动生成一个程序草稿供你修改。**

接下来本文将介绍Ruyi的语法规范，我们会在介绍过程中插入一些可以在Ruyi IDE中直接运行的例子，便于你边学边用，快速掌握。

你也可以跳过语法介绍部分，直接去看 [实际案例](#6. 实战案例) 中感兴趣的例子，如果理解这些例子对你来说不算难事，那么恭喜你，你可以直接上手实操了！

## 3. 基本语法与概念

Ruyi 的语法非常灵活，但遵循一些简单的约定和概念将有助于系统更精确地理解你的意图。

Ruyi 程序由一系列语句组成，通常每行一个语句，描述一个操作或一个步骤，鼓励使用祈使句或清晰的陈述句。

### 注释

使用 `#` 符号开始一行，表示这一行是注释。注释用于解释代码，系统在执行时会忽略它们。

```python
# 这是一个注释，系统不会执行这句话
告诉用户 "你好，注释让代码更易懂" # 这也是一个行内注释
```

### 变量

变量就像是贴了标签的盒子，用来存储信息，方便后续使用。在 Ruyi 中，变量一般用大括号包裹的词语表示。

* **声明（记录）变量**：通常使用 `记为{变量名}` 或类似的自然语言方式。

  ```python
  询问AI，输入是"目前人工智能领域的头部公司有哪些？"，输出公司列表，记为{公司列表}
  设置变量 {用户姓名} 为 "小明"
  将 "北京" 存入 {当前城市}
  创建变量 {来访人数}，初始值为 0
  ```

* **使用变量**：在需要用到存储信息的地方，使用 `{变量名}`，即代表将变量的值填入对应的位置。

  ```python
  告诉用户 "你好，{用户姓名}"
  在浏览器中搜索 "{当前城市} 人口数量"，记为 {人口数量}
  告诉用户 "{当前城市} 的人口数量是 {人口数量}。"
  ```

* 变量名建议使用有意义的词语，方便理解。

### 数据类型

每个变量在系统中存储和传递时，是被理解为不同类型的数据进行实际操作的。不同类型的变量用于不同场景的数据处理，例如数字类型变量用于数据统计、文本类型的变量用于文档操作、对话等。

类型在传统的编程语言中是个重要的概念（也往往比较容易出错），但在 Ruyi 中，你不需要专门关心变量的类型，Ruyi 会根据你的代码自动理解每个变量的类型。当然你也可以在定义变量时补充说明类型，以对整个处理过程更有控制感。例如：

```python
询问AI "北京的人口数量是多少？"，输出数字，记为{人口数}
询问AI "北京去年的人均GDP是多少？"，输出文本，记为{人均GDP}
告诉用户 "北京的人口数量是 {人口数}，人均GDP为 {人均GDP}。"
```

这样你就得到了一个类型是数字的变量 {人口数}、和一个类型是文本的变量 {人均GDP}。

#### 基本数据类型

Ruyi 目前支持下列基本数据类型：

* **文本** (Text)：用引号括起来的字符序列，如 `"Hello world!"` 或 `'你好，世界！'`。

* **数字** (Number)：整数或小数，如 `10` 或 `3.14`。

* **布尔** (Boolean)：`真` 或 `假` (或者 `是` / `否`)，用于表示条件或状态。

  ```python
  询问用户 "你的年龄是多少？"，结果记为 {年龄}
  判断 {年龄} 是否大于 18，记为 {是否成年} # {是否成年} 会是 真 或 假
  如果 {是否成年} 为真：
      告诉用户 "你已成年。"
  ```

* **图像** (Image)：代表图片数据。可以通过拍照、读取文件或AI生成等方式获得。

  ```python
  拍照，记为 {产品图片}
  读取图片文件 "logo.png"，记为 {公司Logo}
  询问AI "生成一张卡通猫的图片"，输出图像，记为 {卡通猫图片}
  告诉用户 "这是拍摄的产品图片："
  把{产品图片}发送给用户
  ```

#### 对象 (Object)

对象是包含多个**属性**的集合，用于表示一个具体的事物或实体。每个属性都有一个名称和一个对应的值（也称为键值对）。属性的值可能是一个基本类型的变量，也可能是另外一个对象。

* **创建对象**：

  ```python
  创建一个对象，名为 {我的汽车}
  设置 {我的汽车} 的 {颜色} 为 "红色"
  设置 {我的汽车} 的 {品牌} 为 "特斯拉"
  设置 {我的汽车} 的 {型号} 为 "Model Y"
  
  # 或者更简洁的方式，AI会理解你的意图
  创建一个对象 {客户}，其属性包括： "姓名" 是 "李明"，"年龄" 是 24，"城市" 是 "上海"
  ```

* **访问对象属性**：
  可以直接在大括号中使用自然语言访问到属性值，或者使用更接近传统编程的点 `.` 操作符。

  ```python
  告诉用户 "我的汽车颜色是 {我的汽车的颜色}"
  如果 {客户的年龄} 大于 18： # 假设{客户}是一个前面定义的对象
      告诉用户 "{客户的姓名}是成年人。"
  
  # 或者使用{变量名.属性名}的方式
  告诉用户 "我的汽车颜色是：{我的汽车.颜色}，品牌是{我的汽车.品牌}，型号是{我的汽车.型号}"
  ```

#### 列表 (List)

列表是一系列有序的基本类型元素或对象。

```python
# 创建列表
将列表 {"苹果", "香蕉", "橙子"} 记为 {水果列表}
询问AI，输入是"目前人工智能领域的上市公司有哪些？"，输出公司名称列表，记为{公司列表} # {公司列表} 是一个文本列表

# 访问列表元素（AI 会智能理解）
获取 {水果列表} 的第一个，记为{水果1}
告诉用户 "我最喜欢的水果是：{水果1}"
获取 {公司列表} 中元素的数量，记为{公司数量}
告诉用户 "AI领域上市公司大约有 {公司数量} 家。"
```

#### 数据表 (Table)

数据表类似于电子表格，包含行和列。**数据表可以看作是由多个结构相同的对象组成的列表**。表中的每一行对应一个对象，每一列对应对象的一个属性。你可以创建数据表、添加、删除、修改数据表中的数据、将数据表从文件导入或导出到文件等。

```python
# 创建数据表
建立一个空的数据表，包含 "姓名"、"部门"、"工号" 列，记为 {员工信息表}

# 添加一行 (可以理解为添加一个符合结构的对象)
创建一个对象 {新员工A}，包含属性 "姓名"是"张三"，"部门"是"技术部"，"工号"是"T001"
将 {新员工A} 作为新行添加到 {员工信息表}

# 添加一行内容 (更简洁的方式)
将一行数据 {"姓名":"小明", "部门":"市场部", "工号":"M023"} 添加到 {员工信息表}

# 从文件加载数据到数据表
读取表格文件 "外部员工数据.xlsx" 的内容，存入新的数据表 {外部员工表}

# 保存数据表到文件
将{员工信息表}保存到文件，文件名为"员工信息.xlsx"
```

#### 数据处理

Ruyi 集成了AI大模型能力，在数据处理方面非常强大。你只需要描述你的处理需求，即可完成各种复杂的数据处理操作。

* **筛选**：

  ```python
  从{公司数据表}中筛选出所有“市值”（单位：亿）大于100的公司记录，结果记为{大型公司表}
  ```

* **提取**：

  ```python
  获取{公司数据表}中的“国家”列，去重后记为{国家名称列表}
  ```

* **汇总**：

  ```python
  计算{公司数据表}中所有“国家”属性为“中国”的公司的“市值”总和，记为{中国公司总市值}
  ```

* **排序**：

  ```python
  将{公司数据表}按“市值”列的值从大到小排序
  ```

* **合并**：

  ```python
  将{国内销售表}和{海外销售表}两个数据表按“产品ID”列合并，记为{全球销售总表}
  ```

* **计算**：`

  ```python
  将{公司数据表}中的“市值”除以“员工数量”，增加为“人均市值”列
  ```

对于**图像类数据**，也有很多内置的处理操作可以执行：

```python
# 例子1
拍照，记为 {原始照片}
调整 {原始照片} 的尺寸为宽度500像素，高度自动，记为 {调整后照片}
将 {调整后照片} 转换为灰度图像，记为 {灰度照片}
询问AI，输入 "{原始照片} 给这个照片配一个10个字以内的标题"，记为 {照片标题}
将 {灰度照片} 保存到文件，文件名是 "{照片标题}.jpg"
```

```python
# 例子2
读取图片文件 "风景.jpg"，记为 {风景照片}
询问AI，输入 "给这张照片 {风景照片} 应用复古滤镜效果"，输出处理后的图像，记为 {复古风景照片}
询问AI，输入 "{风景照片} 给这张照片配诗"，输出文字，记为 {照片配诗}
将 {复古风景照片} 发给用户
告诉用户 "这是你照片的配诗：{照片配诗}"
```

### 流程控制

代码默认是一行一行顺序执行的，但你可以通过流程控制语法，改变代码的执行流程。

#### 循环

当需要对一组项目中的每一个重复执行相同操作时，可以使用循环。

一个循环包括循环条件、循环内容两部分。循环条件描述循环的方式、次数等信息，循环内容则是每轮循环中需要重复执行的步骤。

循环内容的代码通常需要缩进（即在每行循环内容代码前面相对于循环条件留一些空格，通常使用四个空格），以表示这些操作是循环内的一部分。Ruyi 的 AI 解释器对于缩进有一定容错性，但良好缩进有助于可读性和准确性。

```python
# 可以指定循环次数进行循环
创建一个列表，名为{人员名单}
重复执行 5 次：
    告诉用户 "这是第 {循环当前次数} 次循环！" # {循环当前次数} 是一个特殊变量，从1开始
    询问用户 "请输入第{循环当前次数}个人名"，获取用户回答，插入{人员名单}列表
将 {人员名单} 保存到文件，文件名是 "名单"

# 也可以按照时间循环
每隔5分钟，执行：
    告诉用户 "当前时间是{当前时间}，这是第{循环当前次数}次问好！"

# 遍历{数据集合}中的每一个{单个项目变量名}
将列表 {"苹果派", "香蕉船", "橙汁"} 记为 {甜点菜单}
遍历{甜点菜单}中的每一个甜点，记为{当前甜点}
    告诉用户 "我们有售：{当前甜点}"
```

#### 条件分支

当程序需要根据不同的情况执行不同的操作时，使用条件分支。

在条件分支中，程序根据条件表达式的判定情况，决定执行哪一条分支。

条件表达式一般是一个判断，例如 `{市值}小于100亿`、 `{医院列表}是空的` 等，也可以是一个包含布尔值的变量。

* **基本格式**：

  ````python
  如果{条件1}: 
      ... （条件1成立时执行）
  否则如果{条件2}:    # 可选
      ... （条件1不成立，但条件2成立时执行）
  否则:              # 可选
      ... （条件1和条件2都不成立时执行）
  ````

- **例子：**

  ```python
  # 例子1
  询问用户"请输入你的年龄"，获取用户回答，记为{用户年龄}
  如果 {用户年龄} 小于 18：
      告诉用户 "你是未成年人。"
  否则如果 {用户年龄} 大于等于 18 并且 {用户年龄} 小于 60：
      告诉用户 "你是成年人。"
  否则：
      告诉用户 "你已步入老年。"
  
  # 例子2
  询问AI，输入是 "今天天气怎么样？"，输出天气描述，记为 {天气情况}
  如果 {天气情况} 包含 "雨"：
      告诉用户 "出门请记得带伞。"
  否则：
      告诉用户 "今天天气不错！"
  ```

### 任务分解（函数）

在 Ruyi 中，你可以将一组常用的操作定义为一个“任务”，这类似于传统编程语言中的“函数”或“子程序”。这样做可以使你的主流程更简洁，也方便代码复用。

* **定义任务**：
  使用 `定义一个任务，名为"{任务名称}"` 开始。任务可以有输入参数和输出（返回值）。

  ```python
  # 定义一个任务，用于向特定用户问好
  定义一个任务，名为 "定制问候语"
      任务输入： {用户名} (类型为文本)
      将 "你好，{用户名}！欢迎使用如意。" 记为 {问候语文本}
      告诉用户 {问候语文本}
      # 这个任务没有明确的返回值
  
  # 定义一个任务，用于计算两个数的和并返回结果
  定义一个任务，名为 "计算两数之和"
      任务输入： {数字一} (数字类型) 和 {数字二} (数字类型)
      将 {数字一} 加上 {数字二} 的结果记为 {计算结果}
      任务返回 {计算结果} # 任务结束，并输出{计算结果}
  ```

  任务定义的第一行或紧随其后的几行通常用来描述任务需要的输入参数及其类型（可选，但推荐）。

* **调用任务**：
  使用 `执行任务 "{任务名称}"`，并提供必要的输入参数，（可选）接收输出结果。

  ```python
  # 调用问候任务
  执行任务 "定制问候语"，{用户名} 为 "张三"
  
  # 调用计算任务并获取输出
  执行任务 "计算两数之和"，{数字一} 是 10，{数字二} 是 25，输出结果记为 {总和}
  告诉用户 "10 + 25 的结果是：{总和}"
  ```

  Ruyi解释器会智能匹配输入参数名。如果任务定义中的参数名和调用时提供的参数名不完全一致但意思相近，Ruyi解释器也会尝试理解。

利用函数（任务），你可以将一个复杂的大任务分解成一系列更小、更易于管理和描述的子任务，进而逐个完成。
这个分解任务的能力实际上就是架构和逻辑的能力，需要精心设计，使用变量在流程的不同阶段传递信息，同时保证程序总体结构的精简可读性。

## 4. 使用工具

很多复杂的功能需要依靠强大的工具实现，上面介绍的例子中实际上已经涉及到了一些，本章将详细介绍。

Ruyi支持的工具包括AI大模型、用户交互、浏览器、手机、文件、传感器等，通过搭配使用这些工具，可以实现很多日常工作和生活中的功能。

### 与大模型交互

Ruyi 语言的核心能力之一是与大模型 （如ChatGPT、DeepSeek、Claude、豆包等）进行交互，以获取信息、进行决策或生成内容。

AI大模型具备通用的语言和图像理解能力和生成能力，你可以把它当作一个有问必答的助手，但它的答案具有随机性，还可能不太可靠，需要注意提问方式以及问题边界。大模型的能力一直在变化，所以需要在代码开发过程中体会哪些问题适合用大模型来给出回答，哪些适合人工分解解决。

* **标准格式**：`询问AI，输入是{输入内容}，输出{期望的输出描述}，记为{输出变量}`

  * `输入是`：可以是直接的文本、变量，或者多个变量和文本的拼接。
  * `输出`：描述你期望 AI 返回什么类型或格式的信息。
  * `记为`：将 AI 的输出保存到指定的变量中。

  ```python
  询问AI，输入是 "法国的首都是哪里？"，输出城市名称，记为{法国首都}
  告诉用户 "法国首都是：{法国首都}"
  
  # 询问AI的输入可以是变量和文本的拼接，系统会根据输入内容和格式，自动选择合适的模型
  获取当前屏幕截图，记为{当前截图}
  询问AI，输入是 "{当前截图} 这家公司的主要业务是什么？总结不超过50字"，输出文本，记为{公司业务简介}
  告诉用户 {公司业务简介}
  
  # AI做决策
  询问AI，输入是 "我手头有{食材列表}，晚餐想吃中餐，请推荐几道菜并给出主要步骤"，输出菜谱列表（每个菜谱包含菜名和步骤列表），记为{菜谱列表}
  遍历 {菜谱列表} 中每个 {菜谱}:
      告诉用户 "菜名：{菜谱.菜名}，制作步骤：\n{菜谱.制作步骤}"
  ```

- **自动提问**：当你不想自己撰写给大模型的提问时，你可以给一个模糊的需求，Ruyi解释器会自动生成合适的发给大模型的问题。

  ```python
  询问AI，获取当前界面中的文字语言
  # 上面这行的作用大致等同于：
  #     询问AI，输入是"{当前界面} 中的文字的语言是什么？"，输出语言名称，记为"文字语言"
  ```

### 与用户交互

Ruyi 程序可以与用户收发消息，进行信息交换。

* **获取用户输入**：

  用户的输入可能是文本、图片、选择等等，Ruyi采用相同的语法提示用户获得输入：

  ```python
  # 获取文本输入
  询问用户 "请输入你的名字："，获取用户回答，记为{用户姓名}
  询问用户 "你今年多少岁了？"，获取用户回答，记为{用户年龄}
  
  # 获取文件输入
  询问用户 "请发给我一张你的自拍照"，获取用户文件，记为{用户图片}
  
  # 获取用户选择
  询问用户 "你同意用户协议吗？(是/否)"，获取用户选择，记为{同意协议}
  将列表 {"红色", "蓝色", "绿色"} 记为 {颜色选项}
  询问用户 "你喜欢哪种颜色？请从以下选项中选择：{颜色选项}"，获取用户选择，记为{用户选择的颜色}
  告诉用户 "你选择了 {同意协议} 和 {用户选择的颜色}。"
  ```

  与询问AI的方式类似，你也可以使用一些模糊的方式与用户交互，当没有给定用引号包裹的具体消息时，Ruyi解释器会智能生成给用户发送的消息内容。例如当你说 `询问用户获得症状信息` ，Ruyi解释器会主动设计问题来获取完整的症状信息，并将结果存入一个变量（如 `{症状信息}`）中。

  ```python
  # 模糊的用户交互指令
  询问用户获得症状信息  # 效果大致等同于：询问用户“请描述您的症状信息”，获取用户回答，记为{症状信息}
  告知用户当前的时间  # 效果大致等同于：告知用户“当前的时间是 {当前时间}”
  ```

* **向用户显示信息**：

  ```python
  告诉用户 "处理完成！结果保存在 'output.txt' 文件中。"
  将 {生成的报告} 发送给用户
  将 {分析结果图} 发送给用户 # 假设{分析结果图}是一个图像变量
  ```

* **用不同的方式与用户交互，如邮件、微信等（使用此功能需要配置相关的通信方式）**：

  ```python
  通过邮件告知用户 "您的会议纪要已生成完毕" ，主题是 "会议纪要"
  通过微信告知用户 {预约提醒消息内容}
  # AI会尝试根据描述选择合适的通信方式
  ```

### 操作文件

Ruyi 支持读取和保存文件，包括图片、文档、表格等。文件名建议用引号包裹起来，文件名是相对于当前IDE中工作区的文件路径。

```python
将文本内容 "这是写入的第一行。\n这是第二行。" 保存到文件 "我的笔记.txt"
读取文件 "我的笔记.txt" 的全部文本内容，记为{笔记内容}
告诉用户 "笔记内容是：\n{笔记内容}"

将{公司市值表}保存为表格文件 "公司市值报告.xlsx"
读取表格文件 "公司市值报告.xlsx" 中的数据，记为{读取的市值表}

检查文件 "论文初稿" 是否存在，记为{文件是否存在}
如果 {文件是否存在} 为 真：
    读取文件 "论文初稿" 的内容，记为{论文初稿}
否则：
    告诉用户 "文档不存在。"
```

AI 会根据文件名后缀和你的描述来智能处理文件的读写格式。

### 操作设备（手机与浏览器）

浏览器和手机提供了丰富的工具（网页、应用程序、传感器等），能够帮我们获取各种类型的信息，执行各种类型的任务。

在 Ruyi 中，你可以通过编写代码来操纵这些设备来实现任何你想要的功能。

首先，在Ruyi IDE的右侧，可以看到一个设备列表，你可以在列表中添加设备，被添加的设备会出现一个“设备名”（你也可以手动修改这个名称），然后你就可以在代码中引用这个设备名操作对应的设备了。

#### 手机（Android）

我们先以手机为例，假设你已经通过IDE添加了一个Android手机，设备名称为“手机1”。

在Android手机设备上，你可以选择使用手机中的应用程序（App）和传感器。

**使用App**

通过 Ruyi 代码使用App就好像指挥一个助手完成点击、输入、搜索等操作。举例如下：

```python
# 可以使用一步一步精确具体的操作指令
在设备"手机1"中：  # 指定要执行下列动作的设备，如果不指定，默认在当前预览的设备中执行
    打开 "联系人" 应用
    点击 "搜索联系人"
    在 "搜索框" 中输入 "张"
    点击 "搜索"
    获取当前屏幕的截图，记为 {全屏幕快照}
    将 {全屏幕快照} 保存到文件 "screenshot.png"
    对于当前界面中 "每个联系人" 元素，最多10个，执行：
        点击该元素
        获取 "姓名" 内容，记为 {姓名}
        获取 "电话" 内容，记为 {电话}
        将 {姓名}、{电话} 加入{联系方式列表}
告诉用户"名字包含‘张’的朋友联系方式：{联系方式列表}"

# 也可以采用精简的、更概括的描述（Ruyi的AI解释器会自动把它变成具体的动作）
# 可以看到，在这种描述方式中，我们省去了界面导航、输入等具体动作，直接用“寻找目标+执行动作”的方式描述每一个操作，其中：
# - ”寻找目标“可以是导航到某个界面或功能，或者是查询某种类型的数据
# - ”执行动作“则是目标界面下可以完成的一些具体的功能，例如获取某种数据、修改表单内容等
在设备"手机1"中的"联系人"应用中：
    查询名字包含"张"的联系人，获取最多10个，记为{联系人列表}
    对于{联系人列表}中的每个{联系人}：
        导航到"{联系人}的信息"，获取"姓名"、"电话"，加入{联系方式列表}
告诉用户"名字包含‘张’的朋友联系方式：{联系方式列表}"
```

当使用精简描述难以正确稳定地完成任务时，建议适当增加细节，或者改用精确的操作指令。

如果出现精确操作指令出错的情况（例如程序点击不到你想点击的位置），可以试试改换一些更具体的表述方式。

**使用传感器（相机、麦克风、GPS等）**

Android设备上通常内置很多有用的传感器，通过将这些设备放置在不同的位置，可以实现对不同环境信息的感知。Ruyi 可以操作这些传感器获取环境信息，AI 模型会负责处理具体的硬件访问权限和细节。

* **拍照**：

  ```python
  在设备"手机1"中：
      使用后置摄像头拍照，记为 {会议室照片}
      使用前置摄像头拍照，记为 {自拍照}
  将 {会议室照片} 保存为图片文件 "meeting_room_snapshot.jpg"
  ```

* **录音**：

  ```python
  在设备"手机1"中，录音10秒，记为 {录音}
  将 {录音} 保存为音频文件 "recording.wav"
  ```

* **获取地理位置**：

  ```python
  在设备"手机1"中获取地理位置，记为 {当前位置}
  # {当前位置} 是一个包含经度、纬度、海拔、精度等属性的对象
  告诉用户 "你当前的经度是 {当前位置.经度}，纬度是 {当前位置.纬度}"
  ```

* **其他传感器** (根据设备支持范围而定)：

  ```python
  在设备"手机1"中获取环境光线强度（勒克斯），记为 {光照度数值}
  在设备"手机1"中获取电池电量百分比，记为 {电池电量}
  ```

  使用这些功能时，请确保Ruyi执行环境（IDE或应用）已获得操作系统授予的相应权限。

#### 浏览器

在 Ruyi 中，使用浏览器的方式与使用手机App类似，只不过操作的对象从App变成了网页。

与添加“手机”设备类似，你也可以在Ruyi IDE中添加“浏览器”设备，添加之后获得设备名称（例如“浏览器1”），即可通过Ruyi程序操作它了。

```python
# 使用精确具体的操作指令
在设备"浏览器1"中：
    打开网址 "https://www.bing.com"
    在 "搜索框" 中输入 "Ruyi编程语言"
    点击 "搜索" 按钮
    等待 5 秒 # 等待页面加载
    点击 "搜索结果中的第一个"
    询问AI，获取当前界面的内容摘要
    将内容摘要发送给用户

# 或使用精简概括的描述
在设备"浏览器1"中，搜索"Ruyi编程语言"，获取搜索结果的内容摘要
将内容摘要发送给用户

# 另一个例子
在设备"浏览器1"中：
    导航到"www.unitconverters.net"的质量转换界面，计算1000磅对应的千克数，记为{千克重量}
告诉用户"1000磅等于{千克重量}千克"
```

为了AI解释器的稳定性，目前的Ruyi IDE中仅支持了以竖屏方式显示的网页浏览。

#### 多设备操作

有时你可能需要同时操作多个设备执行同一组动作，要实现这个目标，首先在设备列表中添加所有想要执行操作的设备。然后在代码中就可以用类似于单设备操作的方式操作多个设备，区别仅仅在于如何获取多设备执行的结果。

```python
在设备"手机1"、"手机2"、"手机3"上执行：  # 下列语句会在多个设备上运行
    打开"联系人"应用
    拍照，记为{照片}
    获取设备屏幕截图，记为{设备截图}
    询问AI，输入"{设备截图}中有什么?"，输出记为{截图描述}
汇总所有设备上的{照片}、{截图描述}，记为{批量执行结果}
# 上面这行是一个多设备操作特有的指令，功能是把多个设备上的运行数据汇总起来
# {批量执行结果}可以理解为一个列表，每个元素对应一个设备上的数据
把 {批量执行结果} 发送给用户
获取{批量执行结果}中所有设备的{截图描述}，记为{截图描述列表}
把 {截图描述列表} 发送给用户

# 多设备操作与单设备操作是兼容的，你仍然可以使用除了{批量执行结果}之外的变量
把{设备截图}发送给用户    # 该变量中保存的是第一个设备（例如"手机1"）上的数据
```



## 5. 进阶用法

随着你对 Ruyi 越来越熟悉，你可以开始构建更复杂的自动化流程。

### 后台任务执行

有时你可能希望Ruyi可以同时执行多个任务，而不是一个接一个地等待，这就需要用到后台任务的概念。

要想执行后台任务，首先定义任务，然后使用`后台执行任务`语句启动该任务。在后台任务启动之后，可以使用`等待后台任务`、`停止后台任务`指令进行对后台任务的等待和手动停止。

```python
# 首先定义希望后台执行的任务
定义一个任务，名为 "收集高校信息"
    任务输入：{高校名称列表}
    创建一个列表，记为{高校信息列表}
    遍历{高校名称列表}中的每一个{高校名称}：
        询问AI，输入"告诉我{高校名称}的国际排名、教师数量"，输出记为{国际排名}、{教师数量}
        将{'高校':{高校名称}, '排名':{国际排名}, '教师数量':{教师数量}}加入{高校信息列表}
    任务返回{高校信息列表}

# 启动后台执行
后台执行任务 "收集高校信息", {高校名称列表}是{"斯坦福", "麻省理工", "哈佛大学"}
告诉用户 "高校信息收集中"  # 由于上一句是后台执行的任务，这条消息会立即发给用户
# 你可以在后台任务执行过程中做任何其他任务，包括启动其他后台任务

# 等待后台任务结束后，可以获取到任务的执行结果
等待任务"收集高校信息"结束，结果记为{高校信息列表}
将{高校信息列表}发送给用户

# 在后台任务执行过程中，也可以随时打断
后台执行任务 "收集高校信息2", {高校名称列表}是{"清华大学", "北京大学"}
等待5秒
中止任务"收集高校信息2"
```

### 使用复杂提示词

在与AI交互时，尤其是对于生成复杂内容或执行精细指令的任务，可能需要使用精心设计的、较长的提示词（Prompt）。Ruyi允许你将这些提示词存储在变量中或从外部文件加载，以保持主代码的整洁。

```python
# 假设存在一个名为 "product_description_generator.txt" 的文件，内容如下：
# ===== 文件: product_description_generator.txt =====
# 作为一名顶级的电商文案专家，请为以下产品撰写一段吸引人的产品描述。
# 产品名称：{产品名}
# 核心卖点：{卖点列表}
# 目标人群：{目标客户描述}
# 字数要求：200-300字
# 风格要求：生动有趣，突出价值
# ===============================================================

加载文件 "product_description_generator.txt" 的内容，记为 {产品描述模板}
设置变量 {当前产品名} 为 "智能降噪耳机Pro"
设置变量 {核心卖点} 为 列表 {"极致静谧体验", "30小时超长续航", "佩戴舒适"}
设置变量 {目标客户} 为 "经常需要出差和在嘈杂环境中工作的商务人士"

将 {产品描述模板} 中的 "{产品名}" 替换为 {当前产品名}
将 {产品描述模板} 中的 "{卖点列表}" 替换为 {核心卖点} 
将 {产品描述模板} 中的 "{目标客户描述}" 替换为 {目标客户}
# 注意：以上替换操作AI会智能处理，或者可以设计专门的“格式化提示词”指令

询问AI，输入是 {产品描述模板}，输出产品描述文本，记为 {最终产品描述}
告诉用户 "生成的产品描述：\n{最终产品描述}"
```

这种方式使得提示词的管理、版本控制和复用变得更加容易。

### 服务来自不同用户的请求

（尚未支持）

当你想通过你编写的Ruyi程序向更多的人提供服务时，可以将你的代码打包成一个服务进行发布。

被发布的服务会作为一个聊天机器人，接收来自不同用户的提问，并自动回答。

要实现这样的功能，你需要编程实现这个服务的逻辑，方式如下：

```python
定义一个服务，名为"自动回复助手":
    输入：用户、消息、历史记录
    # {用户}是当前请求服务的用户，其类型是一个对象，包含名称、ID等属性
    # {消息}是当前用户发来的消息，其类型是一个对象，包括内容、类型（文本、图片、文件）、发送者（用户名或者服务名）、时间等
    # {历史记录}是一个列表，其中每个元素是一个消息对象，代表一条历史消息
    将 "你好 {用户.名称}， 你刚刚给我发了 {消息.内容}， 我们已经聊了 {历史记录的长度} 条" 设为{回复内容}
    将{回复内容}发送给{用户}
```

可以看到，服务的编写方式是与定义任务类似的，只不过服务有固定的输入数据格式。在服务的主体逻辑中，你可以调用其他任务完成复杂的请求处理。

### 使用MCP调用更多工具

（尚未支持）

MCP (Model Context Protocol) 是一种面向大模型的工具封装协议，目前已经有很多有用的工具被封装成了MCP并开放使用。你可以在你的代码中集成这些工具，实现更复杂多样的功能。

### 错误处理与调试技巧

由于 Ruyi 的执行高度依赖 AI 的理解和解释能力，以下技巧有助于获得更好、更可控的结果：

1. **清晰明确**：尽管 Ruyi 容忍模糊性，但更清晰、无歧义的指令通常能得到更准确的执行。尽量避免可能产生多种解释的说法。

2. **小步快跑，逐步验证**：对于复杂的流程，将其分解为更小的步骤。先编写并执行一小部分，验证结果符合预期后，再继续编写后续部分。这样便于调试和定位问题。

3. **使用有意义的变量名**：清晰、描述性的中文变量名不仅方便你自己阅读和维护代码，也帮助 AI 更好地理解数据流和你的意图。例如，使用 `{客户订单列表}` 而不是 `{列表1}`。

4. **如果AI未能正确理解，尝试换种说法**：就像与人沟通一样，如果一种表达方式效果不佳，尝试用其他同义词或句子结构来描述相同的意图。有时稍微调整措辞就能让AI正确理解。

5. **善用任务定义 (函数)**：对于重复的逻辑块或功能单元，将其定义为任务。这不仅使主流程更清晰，也帮助AI理解程序的模块化结构，提高其对复杂任务的推理能力。

6. **明确数据类型和格式期望**：在定义任务的输入输出，或在 `询问AI` 语句中描述期望输出时，如果可能，明确指出期望的数据类型（如“输出数字”，“输出一个包含姓名和年龄属性的对象列表”）或格式（如“输出为列表格式”，“结果用表格展示”）。这有助于AI返回更精确、更易于后续处理的结果。

7. **对AI和用户的输出进行检查和纠正**：AI和用户的回答往往可能有不确定性，可能不总是符合预期。在关键步骤后，可以加入检查逻辑，或者提示用户确认内容，必要时进行修正。

   ```python
   询问AI，输入是"总结这篇文章的主要观点"，输出摘要，记为{AI生成的摘要}
   告诉用户 "AI生成的摘要如下：\n{AI生成的摘要}"
   询问用户 "这个摘要准确吗？如果需要修改，请输入你的版本，否则请说'准确'。", 获取用户反馈，记为{用户反馈}
   如果 {用户反馈} 不是 "准确"：
       将 {用户反馈} 记为 {最终摘要}
   否则：
       将 {AI生成的摘要} 记为 {最终摘要}
   ```

## 6. 实战案例

在我们的[案例库](https://www.wisewk.com/examples)中，可以找到更多案例，使用搜索功能试试看有没有你需要的。

以下摘录几个有代表性的例子，展示Ruyi编程语言在不同场景下的能力。

**（TODO：以下案例大多由AI生成，需要修改确认）**

### 案例一：自动化数据爬取与汇总

```python
# 目标：从财经网站获取AI领域上市公司的名称、股票代码和市值，并保存到Excel文件。
# 假设已在IDE中配置浏览器设备，名为 "财经浏览器"

# 1. 初始化
告诉用户 "开始执行AI上市公司数据爬取与汇总任务..."
将列表 {"NVIDIA Corporation", "Microsoft Corp", "Alphabet Inc. Class A", "Meta Platforms Inc.", "Broadcom Inc."} 记为 {公司全称列表} # 示例列表，更优方案是从权威源获取

建立一个空的数据表，其列应包含 "公司名称", "股票代码", "当前市值(美元)", "数据获取时间"，此表记为 {全球AI公司市值表}

# 2. 遍历每个公司获取信息
在设备 "财经浏览器" 中： # 所有浏览器操作在此设备块内
    遍历 {公司全称列表} 中的每一个公司全称，记为 {当前公司全称}:
        告诉用户 "正在处理：{当前公司全称}"
        打开网址 "https://finance.yahoo.com/" # 雅虎财经作为示例
        在页面顶部的 "搜索框" 中输入文本 {当前公司全称}
        等待 1 秒 # 等待搜索建议出现
        # AI需要智能点击最匹配的搜索建议或提交搜索
        如果页面上有包含文本 {当前公司全称} 的 "搜索建议项"：
            点击该 "搜索建议项"
        否则：
            点击页面上的 "搜索提交按钮" # 假设有这样一个按钮
        等待 3 秒 # 等待公司详情页面加载

        # 获取股票代码 (Ticker Symbol)
        # AI尝试从页面典型位置（如公司名旁边）提取股票代码
        获取当前屏幕截图，记为 {页面截图A}
        询问AI，输入是 "{页面截图A} 请从这个页面中提取 {当前公司全称} 的股票代码(通常是一串大写字母)。"，输出文本，记为 {股票代码}
        
        # 获取市值 (Market Cap)
        # AI尝试从页面提取市值信息
        获取当前屏幕截图，记为 {页面截图B}
        询问AI，输入是 "{页面截图B} 请从这个页面中提取 {当前公司全称} 的市值(Market Cap)。"，输出文本，记为 {市值文本}
        
        # 将市值文本转换为数字
        询问AI，输入是 "请将市值文本 '{市值文本}' 转换为纯数字（单位：美元）。例如 '1.5T' 转为 1500000000000，'200B' 转为 200000000000。", 输出数字，记为 {当前市值美元}

        如果 {股票代码} 不为空 并且 {股票代码} 不包含 "未找到" 并且 {当前市值美元} 大于 0:
            获取当前日期和时间，格式化为 "YYYY-MM-DD HH:MM:SS"，记为 {数据获取时刻}
            创建一个新行对象，其属性为：
                "公司名称" 是 {当前公司全称},
                "股票代码" 是 {股票代码},
                "当前市值(美元)" 是 {当前市值美元},
                "数据获取时间" 是 {数据获取时刻}
            将此新行对象添加到数据表 {全球AI公司市值表}
            告诉用户 "已记录：{当前公司全称}, 代码: {股票代码}, 市值: {当前市值美元} 美元"
        否则:
            告诉用户 "未能完整获取 {当前公司全称} 的股票代码或市值信息，已跳过。"
            如果 {股票代码} 为空 或者 {股票代码} 包含 "未找到": 告诉用户 "DEBUG: 股票代码获取失败。原始提取：{股票代码}"
            如果 {当前市值美元} 等于或小于 0: 告诉用户 "DEBUG: 市值获取或转换失败。原始市值文本：{市值文本}"

# 3. 保存与通知
将数据表 {全球AI公司市值表} 保存为Excel文件 "全球AI公司市值报告.xlsx" # 文件保存在工作区
告诉用户 "数据已处理完毕，并保存到工作区的 '全球AI公司市值报告.xlsx' 文件中。"

# 清理 (可选)
在设备 "财经浏览器" 中：
    关闭所有标签页或者浏览器窗口 # AI会尝试执行关闭操作
```

### 案例二：数据清洗与分析

```python
# 目标：读取已爬取的公司数据，进行清洗，识别公司总部国家，并按国家统计总市值。

# 1. 读取数据
告诉用户 "开始执行AI公司数据清洗与分析任务..."
# 假设 "全球AI公司市值报告.xlsx" 文件在当前工作区
读取名为 "全球AI公司市值报告.xlsx" 的Excel文件，将其第一个工作表的数据加载到数据表，记为 {原始公司数据表}

如果 {原始公司数据表} 是空的或者其元素数量为0:
    告诉用户 "错误：未能从 '全球AI公司市值报告.xlsx' 读取到有效数据，或文件为空。"
    结束程序
告诉用户 "成功读取 {原始公司数据表} 中元素的数量 条原始数据。"

# 2. 数据预处理与国家识别
告诉用户 "开始为每家公司识别总部所在国家..."
# 创建一个新的数据表用于存储处理后的数据
建立一个空的数据表，其列与 {原始公司数据表} 的列相同，并增加一列 "总部国家"，此表记为 {处理后公司数据表}

遍历 {原始公司数据表} 中的每一行数据对象，记为 {当前公司行}:
    获取 {当前公司行} 的 "公司名称" 属性值，记为 {公司名}
    询问AI，输入是 "根据公司名称 '{公司名}'（其股票代码可能是 {当前公司行.股票代码}），其总部最有可能在哪个国家？请只返回国家名称，例如 '美国'。", 输出国家名称文本，记为 {总部国家}
    
    # 将原行数据复制到新行，并添加国家信息
    创建一个新对象，复制 {当前公司行} 的所有属性
    设置此新对象的 "总部国家" 属性为 {总部国家}
    将此新对象作为新行添加到 {处理后公司数据表}

告诉用户 "公司国家信息识别完毕。"

# 3. 数据分析与聚合：按国家统计总市值
告诉用户 "开始按国家统计总市值..."
获取 {处理后公司数据表} 中所有对象的 "总部国家" 属性值，去除重复项后形成列表，记为 {所有国家列表}

建立一个空的数据表，其列包含 "国家", "公司数量", "总市值(美元)"，此表记为 {国家市值统计表}

遍历 {所有国家列表} 中的每一个国家名称，记为 {当前国家}:
    如果 {当前国家} 是空的或者 {当前国家} 包含 "未知" 或者 {当前国家} 包含 "不确定":
        继续下一轮循环 # 跳过无效国家
    
    从 {处理后公司数据表} 中筛选出所有 "总部国家" 属性为 {当前国家} 的行，结果记为 {该国所有公司列表}
    获取 {该国所有公司列表} 中元素的数量，记为 {该国公司数量}
    
    如果 {该国公司数量} 大于 0:
        计算 {该国所有公司列表} 中所有对象的 "当前市值(美元)" 属性的总和，记为 {该国总市值美元}
        
        创建一个新行对象，其属性为：
            "国家" 是 {当前国家},
            "公司数量" 是 {该国公司数量},
            "总市值(美元)" 是 {该国总市值美元}
        将此新行对象添加到 {国家市值统计表}

# 4. 结果处理与保存
将数据表 {国家市值统计表} 按 "总市值(美元)" 列的值从大到小排序 (结果更新到 {国家市值统计表})
将排序后的 {国家市值统计表} 保存为表格文件 "国家AI公司总市值排名.csv" # 文件保存在工作区
告诉用户 "国家市值统计与排名已完成，并保存到工作区的 '国家AI公司总市值排名.csv'。"
将 {国家市值统计表} 发送给用户
```

### 案例三：操作手机完成挂号

```python
# 目标：根据用户症状和偏好，在手机上模拟搜索医院挂号信息并辅助完成挂号。
# 假设已在IDE中配置Android手机设备，名为 "我的安卓手机"

# 1. 获取用户需求
告诉用户 "您好，我是智能挂号助手。请问您需要什么帮助？"
询问用户 "请描述一下您的主要症状，例如：头痛、发烧3天。", 获取用户回答，记为{症状描述}

询问AI，输入是 "根据症状描述：'{症状描述}'，最有可能需要挂哪些科室？请返回一个包含1到3个科室名称的列表。", 输出科室名称列表，记为{推荐科室列表}

如果 {推荐科室列表} 是空的 或其元素数量为0:
    告诉用户 "抱歉，根据您的描述，我暂时无法判断合适的科室。建议您咨询医生或提供更详细的信息。"
    结束程序

如果 {推荐科室列表} 的元素数量 等于 1:
    将 {推荐科室列表} 的第一个元素记为 {选定科室}
否则: # 多个推荐科室，让用户选择
    询问用户 "根据您的症状，我推荐以下科室：{推荐科室列表}。您想选择哪个？请直接输入科室名称。", 获取用户选择的科室名称，记为{选定科室}

告诉用户 "好的，我们将为您查找 {选定科室} 的挂号信息。"
询问用户 "您希望在哪个城市或大致区域查找医院？（例如：北京市海淀区）", 获取用户回答，记为{目标区域}
询问用户 "您是否有指定的医院名称？如果有请告诉我，没有则输入'无'或按回车。", 获取用户回答，记为{指定医院名称}

# 2. 确定待查询医院列表
创建一个空列表，名为 {待查医院列表}
如果 {指定医院名称} 不等于 "无" 并且 {指定医院名称} 不为空:
    将 {指定医院名称} 添加到 {待查医院列表}
否则:
    询问AI，输入是 "请推荐在'{目标区域}'地区，设有'{选定科室}'科室的，口碑较好的三甲医院有哪些？请列出2到3家医院的全称。", 输出医院名称列表，记为{AI推荐医院}
    如果 {AI推荐医院} 不为空且其元素数量大于0:
        将 {AI推荐医院} 的所有元素添加到 {待查医院列表}

如果 {待查医院列表} 是空的 或其元素数量为0:
    告诉用户 "抱歉，未能确定可供查询的医院信息。请检查您的输入或稍后再试。"
    结束程序

# 3. 模拟在手机上搜索挂号信息 (此部分高度依赖AI对具体App的适配和UI理解能力)
告诉用户 "正在为您查找 {选定科室} 的可预约号源，请稍候..."
创建一个空列表，用于存储所有找到的号源信息，记为 {所有可预约号源列表}

在设备 "我的安卓手机" 中:
    遍历 {待查医院列表} 中的每一个医院名称，记为 {当前查询医院}:
        告诉用户 "正在查询 {当前查询医院} 的号源..."
        打开 "微信" 应用 # 假设通过微信小程序挂号
        # AI需要智能地在微信中搜索并打开对应的小程序
        在微信的 "小程序搜索入口" (AI需识别元素描述) 中输入文本 "{当前查询医院} 官方挂号"
        点击键盘上的 "搜索" 或 "确认" 键
        等待 2 秒
        点击搜索结果中看起来最像 "{当前查询医院} 官方挂号平台" 的小程序入口
        等待 3 秒 # 等待小程序加载
        
        # 在小程序内，智能导航到 "{选定科室}" 的预约界面
        # 这步非常复杂，AI需要理解小程序结构，可能包括点击多个按钮、选择日期等
        # 实际操作中，可能需要更细致的指令或AI对特定小程序的适配
        告诉用户 "模拟操作：在小程序内寻找 {选定科室} 并查看号源..."
        # 假设AI能够找到并提取未来7天内该科室的可预约时段信息
        # 例如：询问AI，输入是当前小程序屏幕截图和指令"查找{选定科室}的可用预约时段、医生和费用，整理成列表"，输出号源对象列表，记为{当前医院号源}
        
        # --- 模拟获取号源 (实际应由AI从屏幕解析或与小程序API交互) ---
        创建一个空列表，名为 {临时号源列表} # 用于当前医院的号源
        如果 {当前查询医院} 包含 "协和": # 模拟不同医院有不同号源
            创建一个对象 {号源1}，其属性为 {"医院名称":{当前查询医院}, "科室":{选定科室}, "医生姓名":"王爱国", "职称":"主任医师", "可约日期":"2024-08-15", "可约时间段":"上午9:00-9:30", "费用":"100元", "余号":5}
            将 {号源1} 添加到 {临时号源列表}
        否则如果 {当前查询医院} 包含 "人民医院":
            创建一个对象 {号源2}，其属性为 {"医院名称":{当前查询医院}, "科室":{选定科室}, "医生姓名":"李为民", "职称":"副主任医师", "可约日期":"2024-08-16", "可约时间段":"下午2:00-2:30", "费用":"80元", "余号":3}
            将 {号源2} 添加到 {临时号源列表}
        # --- 模拟结束 ---

        如果 {临时号源列表} 不为空且其元素数量大于0:
            将 {临时号源列表} 的所有元素添加到 {所有可预约号源列表}
            告诉用户 "在 {当前查询医院} 找到 {临时号源列表} 中元素的数量 个号源。"
        否则:
            告诉用户 "在 {当前查询医院} 未能找到 {选定科室} 的号源。"
        
        # 返回到微信主界面或关闭当前小程序，准备查询下一家医院
        # 例如：执行 "返回" 操作数次，直到回到微信主界面，或直接关闭微信再重开（策略由AI决定）
        告诉用户 "模拟操作：返回或关闭小程序..."
        打开 "微信" 应用 # 确保回到微信，或者AI有更智能的返回逻辑

# 4. 用户选择与模拟执行挂号
如果 {所有可预约号源列表} 是空的 或其元素数量为0:
    告诉用户 "抱歉，经过查询，未能找到 {选定科室} 的任何可预约号源。"
    结束程序

告诉用户 "为您找到了以下可预约号源："
# 向用户清晰展示号源信息并编号
重复 {所有可预约号源列表} 中元素的数量 次:
    获取 {所有可预约号源列表} 中第 {循环当前次数} 个元素，记为 {当前号源项}
    告诉用户 "序号 {循环当前次数}: {当前号源项.医院名称} - {当前号源项.科室} - {当前号源项.医生姓名} ({当前号源项.职称}) - 时间: {当前号源项.可约日期} {当前号源项.可约时间段} - 费用: {当前号源项.费用} (余号: {当前号源项.余号})"

询问用户 "请输入您想预约的号源序号（例如 1）。如果都不满意，请输入'取消'。", 获取用户回答，记为 {用户选择文本}

如果 {用户选择文本} 等于 "取消" 或者 {用户选择文本} 为空:
    告诉用户 "好的，如果您有其他需求，随时告诉我。"
    结束程序

将 {用户选择文本} 转换为数字，记为 {选择序号数值}
如果 {选择序号数值} 大于 0 并且 {选择序号数值} 小于等于 {所有可预约号源列表} 中元素的数量:
    将 {所有可预约号源列表} 中第 {选择序号数值} 个元素记为 {选定的号源}
    告诉用户 "您选择了预约：{选定的号源.医院名称} - {选定的号源.科室} - {选定的号源.医生姓名} ({选定的号源.可约日期} {选定的号源.可约时间段})。"
    询问用户 "确认预约吗？(请输入 是 或 否)", 获取用户选择，记为{确认预约选择}

    如果 {确认预约选择} 为 是:
        告诉用户 "正在为您提交预约，请按提示操作（如需验证）。"
        # 模拟AI再次进入对应的小程序，并根据{选定的号源}信息自动填写或引导用户完成预约步骤
        # ... 此处为复杂的UI交互过程，由AI驱动 ...
        # 模拟预约结果
        设置变量 {预约成功标志} 为 真 # 实际应根据挂号平台的反馈
        如果 {预约成功标志} 为 真:
            # 假设 {预约凭证信息} 是一个包含取号方式、就诊地点等信息的文本
            设置变量 {预约凭证信息} 为 "预约成功！请于 {选定的号源.可约日期} {选定的号源.可约时间段} 前30分钟到 {选定的号源.医院名称} {选定的号源.科室} 分诊台取号。"
            告诉用户 "恭喜您，预约成功！详情：{预约凭证信息}"
            # 通过短信发送给用户 (假设已配置短信服务且获取了用户手机号)
            # 例如：通过短信发送 "{预约凭证信息}" 给 "{用户手机号}"
        否则:
            告诉用户 "抱歉，预约失败。可能号源已被抢占或网络问题，请稍后重试。"
    否则:
        告诉用户 "已取消预约操作。"
否则:
    告诉用户 "输入无效的序号，操作已取消。"

# 5. 清理 (可选)
在设备 "我的安卓手机" 中：
    返回到手机主屏幕 # AI尝试执行此操作
告诉用户 "感谢使用智能挂号助手！"
```

### 案例四：智能邮件助手

```python
# 智能邮件助手
# 目标：定期检查新邮件 (通过浏览器访问Web邮箱)，对来自特定发件人的邮件进行摘要，并通知用户。
# 假设已在IDE中配置浏览器设备，名为 "邮箱浏览器"
# 假设用户邮箱账户信息已安全存储或由Ruyi IDE管理，此处不直接暴露密码

# 0. 定义一个任务用于邮件摘要
定义一个任务，名为 "总结邮件核心内容"
    任务输入： {邮件正文} (类型为文本)
    询问AI，输入是 "请将以下邮件正文总结为不超过50字的要点：\n{邮件正文}"，输出摘要文本，记为{邮件摘要}
    任务返回 {邮件摘要}

# 1. 设置参数
将 "<EMAIL>" 记为 {目标发件人邮箱} # 或关键词 "重要客户"
创建一个空列表，名为 {重要新邮件摘要列表}
将 "https://mail.example.com/login" 记为 {邮箱登录页URL} # 替换为实际邮箱登录页
将 "my_username" 记为 {邮箱用户名} # 实际应通过安全方式获取

# 2. 登录邮箱并筛选邮件 (所有浏览器操作在此设备块内)
在设备 "邮箱浏览器" 中:
    打开网址 {邮箱登录页URL}
    在页面上ID为 "username" 的输入框中输入文本 {邮箱用户名}
    在页面上ID为 "password" 的输入框中输入文本 "用户输入的密码" # Ruyi IDE应在执行时安全询问密码
    点击页面上类型为 "submit" 的 "登录按钮"
    等待 5 秒 # 等待邮箱主页加载

    # 导航到收件箱 (如果不是默认页)
    如果当前页面标题不包含 "收件箱":
        点击页面上文本为 "收件箱" 或 "Inbox" 的链接或按钮
        等待 3 秒

    告诉用户 "已登录邮箱，开始检查新邮件..."
    # AI需要智能识别邮件列表，并筛选
    # 以下为概念性指令，实际需要AI对邮件界面有良好理解
    # 获取当前页面中所有状态为 "未读" 并且发件人包含 {目标发件人邮箱} 的邮件元素列表，记为 {未读重要邮件元素列表}
    # 为简化，我们假设能直接获取结构化邮件数据
    # --- 模拟获取邮件列表 (实际应由AI从屏幕解析) ---
    创建一个空列表，名为 {模拟新邮件对象列表}
    创建一个对象 {邮件1}，其属性为 {"发件人":"<EMAIL>", "主题":"关于第三季度合作方案", "正文":"[邮件正文内容长篇大论...]", "是否未读":真}
    创建一个对象 {邮件2}，其属性为 {"发件人":"<EMAIL>", "主题":"无关邮件", "正文":"...", "是否未读":真}
    创建一个对象 {邮件3}，其属性为 {"发件人":"<EMAIL>", "主题":"紧急：会议时间调整", "正文":"[原定会议时间调整至明天下午...]", "是否未读":真}
    将 {邮件1} 添加到 {模拟新邮件对象列表}
    将 {邮件2} 添加到 {模拟新邮件对象列表}
    将 {邮件3} 添加到 {模拟新邮件对象列表}
    # 筛选
    创建一个空列表，名为 {新邮件对象列表}
    遍历 {模拟新邮件对象列表} 中的每一个 {邮件对象}:
        如果 {邮件对象.是否未读} 为 真 并且 ({邮件对象.发件人} 等于 {目标发件人邮箱} 或者 {邮件对象.发件人} 包含 {目标发件人邮箱}): # 根据实际情况调整匹配逻辑
            将 {邮件对象} 添加到 {新邮件对象列表}
    # --- 模拟结束 ---

    # 3. 处理邮件
    如果 {新邮件对象列表} 是空的 或者其元素数量为0:
        告诉用户 "没有来自 {目标发件人邮箱} 的新未读邮件。"
    否则:
        告诉用户 "发现 {新邮件对象列表} 中元素的数量 封来自 {目标发件人邮箱} 的新未读邮件。"
        遍历 {新邮件对象列表} 中的每一封邮件对象，记为 {当前邮件}:
            获取 {当前邮件.发件人}，记为 {当前发件人}
            获取 {当前邮件.主题}，记为 {当前主题}
            获取 {当前邮件.正文}，记为 {当前正文}
            
            执行任务 "总结邮件核心内容"，{邮件正文} 是 {当前正文}，输出结果记为 {邮件摘要内容}
            
            创建一个对象 {摘要条目}，其属性为：
                "发件人" 是 {当前发件人},
                "主题" 是 {当前主题},
                "摘要" 是 {邮件摘要内容}
            将 {摘要条目} 添加到 {重要新邮件摘要列表}
            
            # (可选) 在Web界面上将当前邮件标记为已读
            告诉用户 "模拟操作：将邮件 '{当前主题}' 标记为已读..."

    # 4. 通知用户
    如果 {重要新邮件摘要列表} 不为空且其元素数量大于0:
        创建一个空文本，名为 {汇总通知文本}
        将 "重要邮件摘要：\n" 追加到 {汇总通知文本}
        遍历 {重要新邮件摘要列表} 中的每一个 {摘要项}:
            将 "发件人：{摘要项.发件人}\n主题：{摘要项.主题}\n摘要：{摘要项.摘要}\n---\n" 追加到 {汇总通知文本}
        
        # 将汇总信息发送给用户 (例如通过Ruyi IDE的通知，或配置的其他通知方式)
        告诉用户 {汇总通知文本}
        将 {汇总通知文本} 保存到文件 "重要邮件摘要.txt"
        告诉用户 "重要邮件摘要已生成并保存到工作区的 '重要邮件摘要.txt'。"
    
    # 退出登录 (可选)
    告诉用户 "模拟操作：退出邮箱登录..."
    如果页面上有 "退出" 或 "Sign out" 按钮/链接，则点击它
    关闭浏览器或所有标签页

告诉用户 "智能邮件助手任务执行完毕。"
```

### 案例五：自动文件归档与整理

```python
# 自动文件归档与整理
# 目标：扫描指定文件夹（工作区内），根据文件类型和创建日期将文件移动到不同的归档子文件夹（也在工作区内）。

# 1. 设置参数
将 "工作区/待整理文件" 记为 {待扫描文件夹路径} # 假设在工作区下有个"待整理文件"的文件夹
將 "工作区/已归档/图片集" 记为 {图片归档文件夹路径}
將 "工作区/已归档/文档集" 记为 {文档归档文件夹路径}
將 "工作区/已归档/其他杂项" 记为 {其他文件归档文件夹路径}
将 "30天前" 记为 {文件归档时间标准} # AI需要能理解这个相对时间

# 2. 检查并创建归档路径 (如果它们不存在)
如果文件夹 {图片归档文件夹路径} 不存在:
    创建文件夹 {图片归档文件夹路径}
    告诉用户 "已创建文件夹：{图片归档文件夹路径}"
如果文件夹 {文档归档文件夹路径} 不存在:
    创建文件夹 {文档归档文件夹路径}
    告诉用户 "已创建文件夹：{文档归档文件夹路径}"
如果文件夹 {其他文件归档文件夹路径} 不存在:
    创建文件夹 {其他文件归档文件夹路径}
    告诉用户 "已创建文件夹：{其他文件归档文件夹路径}"

# 3. 扫描文件并处理
# 首先确保待扫描文件夹存在
如果文件夹 {待扫描文件夹路径} 不存在:
    告诉用户 "错误：待扫描文件夹 '{待扫描文件夹路径}' 不存在。请先创建并放入文件。"
    结束程序

获取 {待扫描文件夹路径} 中的所有文件和文件夹列表（每个是一个对象，包含名称、类型、路径、创建日期等属性），记为 {全部项目对象列表}
告诉用户 "开始扫描文件夹：{待扫描文件夹路径} ..."

创建一个空列表，名为 {待归档文件列表}
遍历 {全部项目对象列表} 中的每一个项目对象，记为 {当前项目}:
    如果 {当前项目.类型} 是 "文件": # 只处理文件，不处理子文件夹
        将 {当前项目} 添加到 {待归档文件列表}

如果 {待归档文件列表} 中元素的数量 等于 0:
    告诉用户 "在 '{待扫描文件夹路径}' 中没有找到任何文件需要归档。"
    结束程序

遍历 {待归档文件列表} 中的每一个文件对象，记为 {当前文件对象}:
    获取 {当前文件对象.名称}，记为 {当前文件名}
    获取 {当前文件对象.路径}，记为 {当前文件完整路径}
    # AI应能从文件名或内容初步判断文件类型
    询问AI，输入是 "根据文件名 '{当前文件名}'，这个文件最有可能是什么类型？（例如：图片, 文档, 视频, 音频, 压缩包, 其他）"，输出文件类型文本，记为 {推断文件类型}
    获取 {当前文件对象.创建日期}，记为 {当前文件创建日期} # 这是一个日期对象或标准格式的日期文本

    # 检查文件是否满足归档时间要求 (AI需要能比较日期)
    判断 {当前文件创建日期} 是否早于 {文件归档时间标准}，结果记为 {是否满足归档时间}

    如果 {是否满足归档时间} 为 真:
        如果 {推断文件类型} 等于 "图片":
            将文件从 {当前文件完整路径} 移动到文件夹 {图片归档文件夹路径}
            告诉用户 "已将图片文件 '{当前文件名}' 移动到图片归档。"
        否则如果 {推断文件类型} 等于 "文档":
            将文件从 {当前文件完整路径} 移动到文件夹 {文档归档文件夹路径}
            告诉用户 "已将文档文件 '{当前文件名}' 移动到文档归档。"
        否则: # 其他类型或无法明确判断的
            将文件从 {当前文件完整路径} 移动到文件夹 {其他文件归档文件夹路径}
            告诉用户 "已将其他类型文件 '{当前文件名}' 移动到其他杂项归档。"
    否则:
        告诉用户 "文件 '{当前文件名}' (创建于 {当前文件创建日期}) 未达到归档时间（{文件归档时间标准}），已跳过。"
        
告诉用户 "文件归档整理完成。"
```

### 案例六：每日新闻摘要生成器

```python
# 每日新闻摘要生成器
# 目标：从指定新闻源网站获取最新头条新闻，利用AI生成摘要，并发送给用户。
# 假设已在IDE中配置浏览器设备，名为 "新闻浏览器"

# 0. 定义获取和摘要单条新闻的任务
定义一个任务，名为 "获取并摘要单条新闻"
    任务输入： {新闻条目对象} (包含 "标题" 和 "链接" 属性)，{摘要字数限制} (数字，可选，默认为100)
    任务输出： {新闻摘要对象} (包含 "标题", "链接", "摘要文本" 属性)

    获取 {新闻条目对象.标题}，记为 {原始标题}
    获取 {新闻条目对象.链接}，记为 {原始链接}
    将 {摘要字数限制} 如果未提供则设为 100 # 设置默认值

    在设备 "新闻浏览器" 中:
        打开网址 {原始链接}
        等待 3 秒 # 等待页面加载
        获取当前页面的主要文本内容，记为 {新闻全文} # AI智能提取

    如果 {新闻全文} 为空 或者 {新闻全文} 长度小于50: # 简单判断是否成功获取内容
        创建一个对象 {失败摘要}，其属性为：
            "标题" 是 {原始标题},
            "链接" 是 {原始链接},
            "摘要文本" 是 "无法获取该新闻的详细内容或内容过短。"
        任务返回 {失败摘要}

    询问AI，输入是 "请将以下新闻内容总结为一段不超过 {摘要字数限制} 字的摘要：\n{新闻全文}"，输出摘要文本，记为{单条新闻摘要文本}
    
    创建一个对象 {成功摘要}，其属性为：
        "标题" 是 {原始标题},
        "链接" 是 {原始链接},
        "摘要文本" 是 {单条新闻摘要文本}
    任务返回 {成功摘要}

# 1. 设置参数
将列表 {"https://news.google.com/topstories?hl=zh-CN&gl=CN&ceid=CN:zh-Hans", "https://www.bbc.com/zhongwen/simp/topics/c207p54m4q5t"} 记为 {新闻来源网址列表} # Google新闻中国区, BBC中文科技版
将 "科技与财经" 记为 {用户关注类别描述} # 用于AI提取新闻时的参考
创建一个空列表，名为 {今日新闻摘要汇总列表}
获取当前日期，格式化为 "YYYY年MM月DD日"，记为 {今日日期}
告诉用户 "{今日日期} {用户关注类别描述} 新闻摘要生成中..."

# 2. 遍历新闻源获取头条新闻链接
创建一个空列表，名为 {所有头条新闻条目列表}
在设备 "新闻浏览器" 中:
    遍历 {新闻来源网址列表} 中的每一个 {当前新闻源网址}:
        打开网址 {当前新闻源网址}
        等待 3 秒
        # AI 智能定位并提取头条新闻列表 (仅标题和链接)
        获取当前屏幕截图，记为 {新闻列表截图}
        询问AI，输入是 "{新闻列表截图} 请从当前页面中提取主要的、看起来像头条新闻的标题及其对应的完整URL链接。关注与'{用户关注类别描述}'相关的内容。限制提取最新的3到5条。输出一个对象列表，每个对象包含'标题'和'链接'属性。", 输出新闻条目对象列表，记为{本站新闻条目列表}
        
        如果 {本站新闻条目列表} 不为空且其元素数量大于0:
            将 {本站新闻条目列表} 的所有元素添加到 {所有头条新闻条目列表}

# 3. 后台并行摘要新闻 (如果新闻条目较多)
如果 {所有头条新闻条目列表} 是空的或其元素数量为0:
    告诉用户 "未能从指定来源获取到任何新闻条目。"
    结束程序

告诉用户 "共获取到 {所有头条新闻条目列表} 中元素的数量 条新闻标题，开始生成摘要..."

# 使用后台任务并行处理摘要，提高效率
创建一个空列表，名为 {后台任务句柄列表}
遍历 {所有头条新闻条目列表} 中的每一个新闻条目对象，记为 {待摘要新闻}:
    将 "摘要任务_{循环当前次数}" 记为 {当前任务名}
    后台执行任务 "获取并摘要单条新闻"，{新闻条目对象} 是 {待摘要新闻}，{摘要字数限制} 是 150，将此后台任务命名为 {当前任务名}
    将 {当前任务名} 添加到 {后台任务句柄列表}

# 等待所有后台摘要任务完成
告诉用户 "所有摘要任务已启动，请耐心等待..."
遍历 {后台任务句柄列表} 中的每一个任务名称，记为 {待等待任务名}:
    等待后台任务 {待等待任务名} 结束执行，将任务的输出结果记为 {单个完成的摘要对象}
    如果 {单个完成的摘要对象} 不为空:
        将 {单个完成的摘要对象} 添加到 {今日新闻摘要汇总列表}

# 4. 发送摘要
如果 {今日新闻摘要汇总列表} 不为空且其元素数量大于0:
    创建一个空文本，名为 {最终摘要文本}
    将 "早上好！这是 {今日日期} 的 {用户关注类别描述} 新闻摘要：\n\n" 追加到 {最终摘要文本}
    遍历 {今日新闻摘要汇总列表} 中的每一个 {摘要新闻}:
        将 "标题：{摘要新闻.标题}\n摘要：{摘要新闻.摘要文本}\n链接：{摘要新闻.链接}\n\n---\n\n" 追加到 {最终摘要文本}
    
    # 将 {最终摘要文本} 通过邮件发送给 "<EMAIL>" (假设已配置邮件服务)
    # 或直接在Ruyi IDE中显示
    告诉用户 {最终摘要文本}
    将 {最终摘要文本} 保存到文件 "每日新闻摘要_{今日日期}.txt"
    告诉用户 "今日新闻摘要已生成并保存到工作区的 '每日新闻摘要_{今日日期}.txt'。"
else:
    告诉用户 "未能成功生成任何新闻摘要。"

# 清理
在设备 "新闻浏览器" 中:
    关闭浏览器或所有标签页
```

### 案例七：会议室状态自动监控

```python
# 会议室状态自动监控
# 目标：周期性拍摄会议室照片 (使用连接的手机摄像头)，分析照片中的人数，并报告状态。
# 假设已在IDE中配置Android手机设备，名为 "监控手机"，且此手机摄像头对准会议室。

# 1. 定义监控核心任务
定义一个任务，名为 "检查并报告会议室状态"
    任务输入： {被监控的会议室名称} (类型为文本)，{报告接收方式描述} (类型为文本，例如 "邮箱地址 <EMAIL>" 或 "微信联系人 管理员小张")
    任务输出： {状态报告文本} (类型为文本) 或 "失败" 信号

    告诉用户 "任务执行：正在检查会议室 '{被监控的会议室名称}' 的状态..."
    
    在设备 "监控手机" 中:
        使用后置摄像头拍照（确保摄像头对准会议室），记为 {当前会议室照片}
    
    如果 {当前会议室照片} 为空: # 检查拍照是否成功
        告诉用户 "错误：为会议室 '{被监控的会议室名称}' 拍照失败。"
        任务返回 "拍照失败"

    # 分析照片中的人数
    询问AI，输入是 "{当前会议室照片} 请分析这张照片，里面大概有多少个人？请只返回一个数字。", 输出数字，记为 {识别出的人数}
    
    # 准备报告内容
    获取当前时间，格式化为 "YYYY年MM月DD日 HH:mm:ss"，记为 {当前报告时间}
    如果 {识别出的人数} 大于 0:
        将 "{当前报告时间}：会议室 [{被监控的会议室名称}] 当前有 {识别出的人数} 人正在使用。" 记为 {本次状态报告文本}
    否则如果 {识别出的人数} 等于 0:
        将 "{当前报告时间}：会议室 [{被监控的会议室名称}] 当前无人使用。" 记为 {本次状态报告文本}
    否则: # AI可能返回非数字或无法识别
        将 "{当前报告时间}：会议室 [{被监控的会议室名称}] 状态未知（无法准确识别照片中的人数）。" 记为 {本次状态报告文本}
        
    # 发送报告 (Ruyi解释器需要根据 {报告接收方式描述} 选择合适的通知方式)
    # 例如：如果 {报告接收方式描述} 包含 "邮箱地址"
    #   将 {本次状态报告文本} 通过邮件发送给 (从{报告接收方式描述}中提取的邮箱)
    # 为了演示简单，这里直接告诉用户
    告诉用户 "报告已生成：{本次状态报告文本}"
    告诉用户 "模拟发送报告给：{报告接收方式描述}"
    
    任务返回 {本次状态报告文本}

# 2. 设置监控参数
将 "101大会议室" 记为 {要监控的会议室名}
将 "通过Ruyi IDE通知我" 记为 {报告接收配置} # 可以是更具体的，如邮箱
将 300 记为 {监控间隔秒数} # 5分钟 = 300秒

# 3. 启动周期性监控
告诉用户 "将每隔 {监控间隔秒数} 秒监控一次会议室 '{要监控的会议室名}'。"
告诉用户 "监控结果将通过 '{报告接收配置}' 进行通知。"

# 使用循环实现周期性监控
# 注意：无限循环需要Ruyi IDE提供手动停止机制
重复执行 10000 次: # 或使用 "无限循环:" (如果支持)
    告诉用户 "第 {循环当前次数} 轮监控开始..."
    执行任务 "检查并报告会议室状态"，{被监控的会议室名称} 是 {要监控的会议室名}，{报告接收方式描述} 是 {报告接收配置}，输出结果记为 {本次监控结果}
    
    如果 {本次监控结果} 等于 "拍照失败":
        告诉用户 "警告：本轮监控因拍照失败而未完成。将在 {监控间隔秒数} 秒后重试。"
    否则:
        告诉用户 "本轮监控完成。报告内容：{本次监控结果}"
    
    告诉用户 "等待 {监控间隔秒数} 秒后进行下一轮监控..."
    等待 {监控间隔秒数} 秒
```

### 案例八：智能客服订单处理

```python
# 智能客服订单处理
# 目标：模拟一个客服机器人，自动处理来自用户消息中的预订信息，提取订单要素，并将结构化订单通知店主。

# 0. 定义订单解析任务
定义一个任务，名为 "从用户消息中解析订单详情"
    任务输入： {原始客户消息文本} (类型为文本)
    任务输出： {订单对象} (一个包含订单要素的对象) 或 空值 (如果解析失败)

    # AI负责从自然语言消息中解析结构化信息
    询问AI，输入是 """
        请从以下客户消息中提取预订的关键信息。如果无法提取有效订单，则返回空。
        需要提取的要素包括（尽可能提取，不存在则留空）：
        - 商品列表 (一个包含商品名称的文本列表，例如 ["宫保鸡丁饭", "鱼香肉丝饭"])
        - 数量列表 (一个对应商品列表的数量数字列表，如果只有一个总数或无法区分，则为单个数字)
        - 客户姓名 (文本)
        - 联系电话 (文本，提取纯数字)
        - 送货地址 (文本)
        - 特别备注 (文本)
        请将结果组织成一个包含以上键的对象。
        客户消息内容如下：
        {原始客户消息文本}
        """, 输出一个订单对象或空值，记为 {解析结果对象}
    
    任务返回 {解析结果对象}

# 1. 设置店铺信息和通知方式
将 "店主老王的手机号13912345678" 记为 {店主通知方式描述} # AI需能理解并映射到如短信服务
将 "亲爱的顾客，您的订单已收到，我们正在快马加鞭为您准备并发货！" 记为 {自动回复客户模板文本}
# 假设订单数据存储在工作区的文件中
将 "店铺待处理订单记录.xlsx" 记为 {订单记录文件名}

# 检查订单记录文件是否存在，不存在则创建带表头的新表
如果文件 {订单记录文件名} 不存在:
    建立一个空的数据表，其列包含 "订单编号", "客户姓名", "联系电话", "送货地址", "商品详情", "特别备注", "处理状态", "接收时间"，此表记为 {店铺待处理订单表}
    将 {店铺待处理订单表} 保存为Excel文件 {订单记录文件名}
    告诉用户 "首次运行，已创建订单记录文件：{订单记录文件名}"

# 2. 模拟接收并处理新客户消息 (在实际应用中，这可能是由聊天机器人平台触发)
# 以下为单次处理示例，可放入循环或由外部事件调用
将 "你好呀，我想订两份宫保鸡丁饭，一份鱼香肉丝盖饭。送到中关村软件园3号楼A座前台，我是李小明，电话13800138000。麻烦米饭多一点，谢谢！" 记为 {当前客户新消息}
将 "客户微信ID_abcdef" 记为 {当前客户联系方式描述} # 假设能获取到客户的某种联系方式

# 3. 执行订单处理流程
告诉用户 "收到新消息，开始处理..."
执行任务 "从用户消息中解析订单详情"，{原始客户消息文本} 是 {当前客户新消息}，输出结果记为 {解析出的订单}

如果 {解析出的订单} 不为空 并且 {解析出的订单.商品列表} 不为空 且 {解析出的订单.商品列表} 中元素数量大于0:
    获取当前日期和时间，格式化为 "YYYYMMDDHHmmssSSS"，记为 {新订单编号} # 用时间戳作为简单ID
    
    # 准备通知店主的内容
    将 "新外卖订单提醒 (订单号: {新订单编号}):\n" 记为 {给店主的通知内容}
    如果 {解析出的订单} 包含 "客户姓名" 属性且不为空: 将 "客户：{解析出的订单.客户姓名} ({解析出的订单.联系电话})\n" 追加到 {给店主的通知内容}
    否则如果 {解析出的订单} 包含 "联系电话" 属性且不为空: 将 "客户电话：{解析出的订单.联系电话}\n" 追加到 {给店主的通知内容}
    
    将 "商品：\n" 追加到 {给店主的通知内容}
    # 合并商品和数量
    创建一个空文本，名为 {商品详情文本}
    重复 {解析出的订单.商品列表} 中元素的数量 次:
        获取 {解析出的订单.商品列表} 中第 {循环当前次数} 个元素，记为 {商品名}
        获取 {解析出的订单.数量列表} 中第 {循环当前次数} 个元素 (或统一数量)，记为 {商品数量}
        将 "- {商品名} x {商品数量}\n" 追加到 {商品详情文本}
    将 {商品详情文本} 追加到 {给店主的通知内容}
    
    如果 {解析出的订单} 包含 "送货地址" 属性且不为空: 将 "地址：{解析出的订单.送货地址}\n" 追加到 {给店主的通知内容}
    如果 {解析出的订单} 包含 "特别备注" 属性且不为空: 将 "备注：{解析出的订单.特别备注}\n" 追加到 {给店主的通知内容}
    
    # 通知店主 (Ruyi解释器根据描述选择通知方式，如短信)
    将 {给店主的通知内容} 发送给 {店主通知方式描述} # 例如，通过短信发送
    告诉用户 "新订单信息已通过适当方式通知店主。"

    # 自动回复客户 (Ruyi解释器根据描述选择回复方式)
    将 {自动回复客户模板文本} 发送给 {当前客户联系方式描述} # 例如，通过微信回复
    告诉用户 "已自动回复客户。"

    # 记录订单到Excel文件
    读取表格文件 {订单记录文件名} 的内容，存入数据表 {现有订单表}
    获取当前日期和时间，格式化为 "YYYY-MM-DD HH:mm:ss"，记为 {当前接收时间}
    创建一个新行对象 {新订单行}，其属性为：
        "订单编号" 是 {新订单编号},
        "客户姓名" 是 {解析出的订单.客户姓名},
        "联系电话" 是 {解析出的订单.联系电话},
        "送货地址" 是 {解析出的订单.送货地址},
        "商品详情" 是 {商品详情文本}, # 将合并后的商品详情存入
        "特别备注" 是 {解析出的订单.特别备注},
        "处理状态" 是 "待处理",
        "接收时间" 是 {当前接收时间}
    将 {新订单行} 添加到 {现有订单表}
    将 {现有订单表} 保存为Excel文件 {订单记录文件名} (覆盖旧文件)
    告诉用户 "订单 {新订单编号} 已记录到 {订单记录文件名}。"
    
否则: # 解析失败或无有效商品
    将 "收到一条消息，但未能解析出有效订单信息：\n'{当前客户新消息}'" 记录到文本文件 "订单解析错误日志.txt" (追加模式)
    告诉用户 "未能从客户消息中解析出有效订单信息，已记录到日志。"
    # 可以选择回复客户，告知解析失败
    将 "抱歉，我没有太理解您的订单信息，麻烦您重新说一下可以吗？例如：我要一份宫保鸡丁，送到XX地址。" 发送给 {当前客户联系方式描述}

# 提示：在实际应用中，上述接收和处理消息的逻辑会包裹在一个循环或事件监听器中，
# 以便持续处理来自不同客户的多个订单请求。
```

这些案例展示了Ruyi的潜在能力。实际执行效果会依赖于背后AI模型的理解能力、对外部应用和设备的控制能力，以及Ruyi IDE提供的具体执行环境。祝你Ruyi之旅愉快！

```

```