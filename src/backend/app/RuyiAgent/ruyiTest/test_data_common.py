from ruyi.data.common import LiveTable, Text
import unittest
import pandas as pd


class AgentForTest:
    def __init__(self, config=None):
        self.config = config


class TestLiveTable(unittest.TestCase):

    def setUp(self):
        agent = AgentForTest()
        self.live_table = LiveTable(name="test", agent=agent)   # type: ignore

    def test_add_row(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.assertEqual(self.live_table[0], {'a': 1, 'b': 2})
    
    def test_add_row_add_columns(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        self.assertEqual(self.live_table[1], {'a': 3, 'b': 4, 'c': 5})
        self.assertEqual(self.live_table[0], {'a': 1, 'b': 2, 'c': None})
    
    def test_getitem(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.assertEqual(self.live_table[0], {'a': 1, 'b': 2})
        self.assertEqual(self.live_table[0, 'a'], 1)
    
    def test_iterrows(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        for index, row in self.live_table.iterrows():
            self.assertEqual(row, self.live_table[index])
        
    def test_contains(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.assertTrue('a' in self.live_table)
        self.assertFalse('c' in self.live_table)
    
    def test_to_dataframe(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        df = self.live_table.to_dataframe()
        self.assertTrue(df.equals(pd.DataFrame([{'a': 1, 'b': 2}, {'a': 3, 'b': 4, 'c': 5}])))
    
    def test_len(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        self.assertEqual(len(self.live_table), 2)
    
    def test_name(self):
        self.assertEqual(self.live_table._name, "test")
    
    def test_add_column(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        self.live_table._add_column('d')
        self.assertEqual(self.live_table[0], {'a': 1, 'b': 2, 'c': None, 'd': None})
        self.assertEqual(self.live_table[1], {'a': 3, 'b': 4, 'c': 5, 'd': None})
    
    def test_update_row(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        self.live_table.update_row(1, {'a': 6, 'b': 7, 'c': 8})
        self.assertEqual(self.live_table[1], {'a': 6, 'b': 7, 'c': 8})
        self.assertEqual(self.live_table[0], {'a': 1, 'b': 2, 'c': None})
    
    def test_update_row_index_out_of_range(self):
        with self.assertRaises(IndexError):
            self.live_table.update_row(1, {'a': 6, 'b': 7, 'c': 8})
    
    def test_update_row_missing_columns(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        with self.assertRaises(ValueError):
            self.live_table.update_row(0, {'a': 6})
    
    def test_getitem_invalid_key(self):
        with self.assertRaises(TypeError):
            self.live_table['a']    # type: ignore
    
    def test_getitem_invalid_key_tuple(self):
        with self.assertRaises(IndexError):
            self.live_table[(0, 'a')]    # type: ignore
    
    def test_getitem_invalid_key_int(self):
        with self.assertRaises(IndexError):
            self.live_table[0]
    
    def test_getitem_invalid_key_int_tuple(self):
        with self.assertRaises(IndexError):
            self.live_table[(0, 'a')]
    
    def test_delete_row(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        self.live_table.delete_row(0)
        self.assertEqual(self.live_table[0], {'a': 3, 'b': 4, 'c': 5})
    
    def test_delete_row_index_out_of_range(self):
        with self.assertRaises(IndexError):
            self.live_table.delete_row(0)
    
    def test_rename_columns(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        self.live_table.rename_columns({'a': 'A', 'b': 'B'})
        self.assertEqual(self.live_table[0], {'A': 1, 'B': 2, 'c': None})
        self.assertEqual(self.live_table[1], {'A': 3, 'B': 4, 'c': 5})
    
    def test_clear(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        self.live_table.clear()
        self.assertEqual(len(self.live_table), 0)
    
    def test_add_livetable(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        other_agent = AgentForTest()
        other = LiveTable(name="other", agent=other_agent)  # type: ignore
        other.add_row({'a': 6, 'b': 7})
        other.add_row({'a': 8, 'b': 9, 'c': 10})
        self.live_table += other
        self.assertEqual(len(self.live_table), 4)
        self.assertEqual(self.live_table[2], {'a': 6, 'b': 7, 'c': None})
        self.assertEqual(self.live_table[3], {'a': 8, 'b': 9, 'c': 10})

    def test_to_dict(self):
        self.live_table.add_row({'a': 1, 'b': 2})
        self.live_table.add_row({'a': 3, 'b': 4, 'c': 5})
        self.assertEqual(self.live_table.to_dict(), {"name": "test", "data": [{'a': 1, 'b': 2, 'c': None}, {'a': 3, 'b': 4, 'c': 5}], "columns": ['a', 'b', 'c']})
    
    def test_from_dict(self):
        data = {"name": "test", "data": [{'a': 1, 'b': 2}, {'a': 3, 'b': 4, 'c': 5}], "columns": ['a', 'b', 'c']}
        agent = AgentForTest()
        live_table = LiveTable.from_dict(data, agent=agent) # type: ignore
        self.assertEqual(live_table.to_dict(), data)
    
    def test_sort_rows(self):
        self.live_table.add_row({'a': 3, 'b': 2})
        self.live_table.add_row({'a': 1, 'b': 4, 'c': 5})
        self.live_table.sort_rows('a')
        self.assertEqual(self.live_table[0], {'a': 1, 'b': 4, 'c': 5})
        self.assertEqual(self.live_table[1], {'a': 3, 'b': 2, 'c': None})

    def test_get_column(self):
        self.live_table.add_row({'a': 3, 'b': 2})
        self.live_table.add_row({'a': 1, 'b': 4, 'c': 5})
        self.assertEqual(self.live_table.get_column('a'), [3, 1])
        self.assertEqual(self.live_table.get_column('b'), [2, 4])
        self.assertEqual(self.live_table.get_column('c'), [None, 5])
    
    def test_drop_column(self):
        self.live_table.add_row({'a': 3, 'b': 2})
        self.live_table.add_row({'a': 1, 'b': 4, 'c': 5})
        self.live_table.drop_column('b')
        self.assertEqual(self.live_table[0], {'a': 3, 'c': None})
        self.assertEqual(self.live_table[1], {'a': 1, 'c': 5})
    
    def test_filter_rows(self):
        self.live_table.add_row({'a': 3, 'b': 2})
        self.live_table.add_row({'a': 1, 'b': 4, 'c': 5})
        new_table = self.live_table.filter_rows(lambda x: x['a'] > 1)
        self.assertEqual(len(new_table), 1)
        self.assertEqual(new_table._data, [{'a': 3, 'b': 2, 'c': None}])


class TestText(unittest.TestCase):
    def setUp(self) -> None:
        agent = AgentForTest()
        self.text = Text(text="Hello, world!", agent=agent)   # type: ignore

    def test_str(self):
        self.assertEqual(str(self.text), "Hello, world!")
    
    def test_upper(self):
        self.assertEqual(self.text.upper(), "HELLO, WORLD!")
    
    def test_lower(self):
        self.assertEqual(self.text.lower(), "hello, world!")
    
    def test_title(self):
        self.assertEqual(self.text.title(), "Hello, World!")
    
    def test_capitalize(self):
        self.assertEqual(self.text.capitalize(), "Hello, world!")
    
    def test_swapcase(self):
        self.assertEqual(self.text.swapcase(), "hELLO, WORLD!")
    
    def test_count(self):
        self.assertEqual(self.text.count('l'), 3)
    
    def test_find(self):
        self.assertEqual(self.text.find('l'), 2)
    
    def test_len(self):
        self.assertEqual(len(self.text), 13)
    
    def test_contains(self):
        self.assertTrue('world' in self.text)
        self.assertFalse('WORLD' in self.text)
    
    def test_iter(self):
        for i, char in enumerate(self.text):
            self.assertEqual(char, self.text[i])
    
    def test_getitem(self):
        self.assertEqual(self.text[0], 'H')
        self.assertEqual(self.text[1], 'e')
        self.assertEqual(self.text[2], 'l')
        self.assertEqual(self.text[3], 'l')
        self.assertEqual(self.text[4], 'o')
        self.assertEqual(self.text[5], ',')
        self.assertEqual(self.text[6], ' ')
        self.assertEqual(self.text[-1], '!')
    
    def test_add_string(self):
        test_agent = AgentForTest()
        self.assertEqual(self.text + "!", Text(text="Hello, world!!", agent=test_agent))   # type: ignore
    
    def test_add_text(self):
        test_agent = AgentForTest()
        other = Text(text="!", agent=test_agent)
        self.assertEqual(self.text + other, Text(text="Hello, world!!", agent=test_agent))    # type: ignore
    
    def test_sub_string(self):
        test_agent = AgentForTest()
        self.assertEqual(self.text - "!", Text(text="Hello, world", agent=test_agent))   # type: ignore
    
    def test_sub_text(self):
        test_agent = AgentForTest()
        other = Text(text="!", agent=test_agent)
        self.assertEqual(self.text - other, Text(text="Hello, world", agent=test_agent))    # type: ignore
    
    def test_mul(self):
        test_agent = AgentForTest()
        self.assertEqual(self.text * 2, Text(text="Hello, world!Hello, world!", agent=test_agent))    # type: ignore
    
    def test_mul_invalid(self):
        with self.assertRaises(TypeError):
            self.text * "2"   # type: ignore
    
    def test_split_normal(self, sep=' '):
        self.assertEqual(self.text.split(sep), ['Hello,', 'world!'])
    
    def test_split_regex(self):
        self.assertEqual(self.text.split(r'\W', regex=True), ['Hello', '', 'world', ''])
    
    def test_bool(self):
        self.assertTrue(self.text)
        self.assertFalse(Text(text="", agent=AgentForTest()))    # type: ignore
    
    def test_eq(self):
        test_agent = AgentForTest()
        self.assertEqual(self.text, Text(text="Hello, world!", agent=test_agent))    # type: ignore
    
    def test_strip(self):
        self.assertEqual(Text(text="  Hello, world!  ", agent=AgentForTest()).strip(), "Hello, world!")  # type: ignore

    def test_replace_normal(self):
        self.assertEqual(self.text.replace('l', 'L'), "HeLLo, worLd!")
    
    def test_replace_regex(self):
        self.assertEqual(self.text.replace(r'\W', ' ', regex=True), "Hello  world ")
    
    def test_join(self):
        test_agent = AgentForTest()
        self.assertEqual(Text(test_agent, " ").join(["Hello,", "world!"]), "Hello, world!")
    
    def test_startswith(self):
        self.assertTrue(self.text.startswith("Hello"))
        self.assertFalse(self.text.startswith("world"))
    
    def test_endswith(self):
        self.assertTrue(self.text.endswith("world!"))
        self.assertFalse(self.text.endswith("Hello"))
    
    def test_isalnum(self):
        self.assertFalse(self.text.isalnum())
        self.assertTrue(Text(text="HelloWorld", agent=AgentForTest()).isalnum())    # type: ignore


if __name__ == '__main__':
    unittest.main()
