### 运行说明
0. Connect to Molmo server
```bash
ssh -CNgv -L 8000:localhost:8000 [your_username]@********* -p 2233
```

1. Ask @RuiKong for demo test config

2. Run all demo tasks
```bash
python -m ruyiTest.test config_molmo.yaml
```

## Demo测试

### 1. 整体情况：
- iterate_views目前已实现：gpt4o描述界面+Molmo定位点击
- 测试的5个任务稳定性都很好，剩余6个还正在测试
- Molmo的中文定位能力有时候不太行，点击经常不准确，比如小红书搜出来的结果，经常点击不准确
- ui.root.locate_view('线路规划上面的旅游地图按钮')可以，而ui.root.locate_view('旅游地图')不行，也就是说描述更细致更好？

### 2. 问题
- 🔥🔥 Ensure（亟需实现）
    - 广告弹窗之类的
- 🔥🔥 Returns稳定性较差（亟需提高），建议先用llm生成json or list，然后正则表达式匹配，不然经常出错
    - video_desc = fm.llm(video_desc, returns=[('视频内容描述', 'str')]) 有时候返回并不是json格式
    - author_name = fm.llm(author_name, returns=[('作者昵称', 'str')]) 有时候返回并不是json格式
    - unsafe_flag = fm.llm(unsafe_flag, '是否有人摔倒', returns=[('是或否', 'bool')]) 有时候返回并不是json格式
    - returns=[('content_list', 'List[str]')] 不work
- Iterate_views
    - 需要开放接口，让开发者可以传入限制条件
- 除了UI的其他交互部分
    1.  Chat部分如何交互
    2. 完成任务后怎么发给用户
    3. 需要用户输入的Task怎么交互？
    4. Data：Live table 如何呈现，Key Value?
- 相册整理发票照片任务
    - 照片的iterate_views prompt怎么设计？
- ui.back_to
    - 完成这个接口    

### 测试结果v3 20241117
- 7/10表示总共测试了10次，成功了7次
- 采用Molmo72b float32模型,
- 大部分任务的完成率很高，失败多是因为有特殊情况，亟需ensure（比如广告 or 点进去是直播而不是内容）
- 测试时间现在有点长，每个task跑一次大概要10-20min
- 全程用英文界面测试（但内容有时候是中文的，比如小红书）

```txt
Create_contact: 9/10                        # 有一次没找到Email的位置（Molmo英文不该有问题的呀？）；有一次弹窗让打分（广告如何处理？）
Crawl_wechat_channels: 5/6                  # 头像点进去有时候是直播，导致失败（这种情况就应该ask用户）
Monitor_health: 7/10                        # 有时候没找到顶部的文本输入框（Molmo中文能力不足）
Weather_based_outing_recommendation: 7/7    # 天气预报的按钮会变，比如15-day forecast，在凌晨的显示就是14-day forecast（显示的图标是动态的该怎么办？）
Plan_Xian_Trip: 8/8
Organize_Invoice_Photos: /10
Track_Product_Price: /10
Find_collaborator: /10
Qzone_Tracker: /10
Search_cantonese_food: /10
Financial_Transactions_Analyzer: /10
```


### 测试结果v2 20241115
- 7/10表示总共测试了10次，成功了7次
- 采用Molmo72b bfloat16模型
- 绝大部分任务无法完成，因为bfloat16模型效果较差（Molmo官方没有做bfloat16优化）
- 该测试结果v2没有参考价值，请查看测试结果v3版本
```txt
Create_contact: 0/10            
Crawl_wechat_channels: 7/10
Monitor_health: 9/10
Weather_based_outing_recommendation: 0/10      
Plan_Xian_Trip: 0/10
Organize_Invoice_Photos: 0/10
Track_Product_Price: 10/10
Find_collaborator: 6/10
Qzone_Tracker: 6/10
Search_cantonese_food: 0/10
Financial_Transactions_Analyzer: 0/10
```

### 测试结果v1 20241110
- 7/10表示总共测试了10次，成功了7次
- 大部分任务的完成率很高，失败多是因为有广告弹出
- 以下实验均采用Claude作为view locator
- 以下任务去除了iterate_views()部分的代码
```txt
Create_contact: 10/10
Weather_based_outing_recommendation: 10/10
Plan_Xian_Trip: 10/10
Organize_Invoice_Photos: 8/10
Track_Product_Price: 7/10
Find_collaborator: 10/10
Qzone_Tracker: 10/10
Search_cantonese_food: 9/10
Financial_Transactions_Analyzer: 10/10
```


### Notes
- 切换默认仓库
gh repo set-default

- Returns语法
returns的写法:
returns=('the bound (x0, y0, x1, y1)', 'tuple[int, int, int, int]')