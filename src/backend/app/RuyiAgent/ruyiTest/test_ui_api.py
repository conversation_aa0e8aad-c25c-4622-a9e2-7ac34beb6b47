from ruyi.task import RuyiTask
import structlog

logger = structlog.get_logger("ruyi")


class Test_highlight(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """This task tests the highlight function of the UI API."""

    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        ui.root.locate_view("CLEAR Button").highlight(3)


class Test_ask_question(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """This task tests the ask_question function of the UI API."""

    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        ans = ui.ask_question("What is your name?")
        print(ans)