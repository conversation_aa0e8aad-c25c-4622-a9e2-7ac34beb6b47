# Test tasks written by @Yuanchun
# Date: 2024-10-15
# TODO better organization of the test cases

from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask
import structlog

logger = structlog.get_logger("ruyi")

class Crawl_wechat_channels(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            扫描微信视频号视频，记录创作者的资料，对其进行分类，并将信息存储到数据库中。 
            （资料应包括名字、作者描述、典型作品描述、作品最多点赞量）。"""

    def main(self, agent: RuyiAgent, sleep_interval: int = 3):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('微信')  # 打开微信

        ui.root.locate_view('发现').click()
        ui.root.locate_view('视频号').click()
        ui.ensure("当前界面是视频播放界面，左下角是用户昵称，右下角是有关视频的点赞收藏评论等数量。")
        ui.root.locate_view('推荐').click()
        profiles = data.live_table()  # 创建一个动态表格，名为profiles
        
        for _ in range(2):
            video_desc = fm.vlm(device.take_screenshot(), '描述一下这个视频', returns=[('视频内容描述', str)])
            num_likes = fm.vlm(device.take_screenshot(), '界面右下角点赞图标下面的数字', returns=[('点赞数', int)])
            ui.root.locate_view('界面左下角的头像右侧的用户昵称').click()

            author_name = fm.vlm(device.take_screenshot(), '头像右边的作者昵称', returns=[('作者昵称', str)])            
            author_desc = fm.vlm(device.take_screenshot(), '根据界面展示的作者信息，简要描述这个作者', returns=[('作者描述', str)])
            
            for v in ui.root.iterate_views('每个视频封面', limit=3):
                v_num_likes = fm.vlm(device.take_screenshot(), f"缩略图'{v.description}'右下角点赞数字", returns=[('点赞数', int)])

                if v_num_likes > num_likes:
                    num_likes = v_num_likes
            profiles.add_row(
                {'作者': author_name, '作者描述': author_desc, '代表作品描述': video_desc, '点赞数': num_likes})
            device.back()
            ui.root.scroll_down(1000)
            logger.info(f'{author_name} crawled!', action='crawl wechat channels', status='done')
            agent.sleep(sleep_interval)  # sleep 3 seconds
        
        ui.back_to('当前UI界面位于手机UI的桌面，桌面上有一些app图标，最下方也有几个app图标')


class Monitor_health(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            监控家中的摄像头，如果老人摔倒，立即发消息通知我，如果我5分钟没有回复，则立即发短信给紧急联系电话。"""

    def main(self, agent: RuyiAgent, sleep_interval: int = 2.5):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('相机')
        for _ in range(2):
            agent.sleep(sleep_interval)
            photo = device.take_screenshot()
            
            unsafe_flag = fm.vlm(photo, '图片中是否有人摔倒', returns=[('True or False', bool)])
            if unsafe_flag:
                response = self.contact_wait(agent, '兔宝', '家里有人摔倒了', photo)
                if response is None:
                    logger.info('Fall detected and no user response!', action='monitor health', status='done')
                    device.sms('17860628307', '科技园小区301有人摔倒')
            agent.sleep(sleep_interval)

    # 联系用户（Agent的主人）并获得回复可以封装成一个系统API，下面的实现just for fun
    def contact_wait(self, agent, name: str, msg: str, photo):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('微信')  # 打开微信
        
        # ui.root.locate_view('界面左下角微信按钮').click()
        # 
        ui.root.locate_view('界面右上角放大镜按钮').click()
        ui.root.locate_view('界面上方搜索文本框').input(name)
        ui.root.locate_view(f'联系人列表中的第一个<{name}>').click()
        ui.root.locate_view('底部的聊天输入文本框').input(msg)
        ui.root.locate_view('右下角的发送按钮').click()
        # TODO 如何支持photo发送？
        response_view = None    
        # response_view = ui.wait_view(f'我发送的消息<{msg}>下方的回复消息',
        #                                   timeout=5 * 60)  # wait_view()函数等待某个view出现，如果timeout时间之后还没出现，返回None
        ui.back_to('当前界面的左下角有电话app的图标')
        return response_view


# A very simple example for debugging
class Create_contact(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            创建一个名为Ruyi，email为*******************的联系人。"""

    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('Contacts')
        ui.wait_view('界面中没有出现奥特曼')
        ui.ensure("当前界面中没有弹窗")
        ui.root.locate_view('右下角的加号').click()
        ui.root.locate_view('名字').input('Ruyi')
        ui.root.locate_view('电子邮件').input('<EMAIL>')
        ui.root.locate_view('右上角的保存按钮').click()
        ui.back_to('当前界面的左下角有电话app的图标')
