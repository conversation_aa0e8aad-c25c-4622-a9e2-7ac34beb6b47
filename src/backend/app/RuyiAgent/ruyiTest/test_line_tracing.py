#!/usr/bin/env python3
"""
基于 task_lyc_241015.py 的行级跟踪演示
展示如何在实际的 RuyiTask 中使用行级跟踪功能
"""

import os
import time
import threading
from ruyi.agent import RuyiAgent
from ruyi.config import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RuyiArgParser
from ruyi.task import RuyiTask
import structlog


logger = structlog.get_logger(__name__)


class CreateContactWithTracing(RuyiTask):
    """ Create_contact 任务的跟踪演示"""
    
    def __init__(self):
        super().__init__()
        self.description = '创建联系人（带行级跟踪演示）'
        self.code_script_labeled = """
device.start_app('Contacts')  # [1]

ui.locate_view('右下角的加号').click()  # [2]

name = ui.locate_view('名字')  # [3]
print("Successfully locate view")  # [3]
name.input('Ruyi')  # [3]

name = ui.locate_view('电子邮件')  # [4]
print("Successfully locate view")  # [4]
name.input('<EMAIL>')  # [4]

ui.locate_view('右上角的保存按钮').click()  # [5]

ui.back_to('当前界面的左下角有电话app的图标')  # [6]

print("联系人创建完成！")  # [7]
        """
    
        self.NL_script_labeled = """
[1]打开 "Contacts" 应用。
[2]点击 "右下角的加号"。
[3]输入 "Ruyi" 到 "名字"。
[4]输入 "<EMAIL>" 到 "电子邮箱"。
[5]点击 "右上角的保存按钮"。
[6]返回，直到 "当前界面的左下角有电话app的图标" 出现。
[7]打印 "联系人创建完成！"
        """

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm

        device.start_app('Contacts')

        ui.locate_view('右下角的加号').click()

        name = ui.locate_view('名字')
        print("Successfully locate view")
        name.input('Ruyi')

        name = ui.locate_view('电子邮件')
        print("Successfully locate view")
        name.input('<EMAIL>')

        ui.locate_view('右上角的保存按钮').click()

        ui.back_to('当前界面的左下角有电话app的图标')

        print("联系人创建完成！")


class SimpleTask(RuyiTask):
    """简单的测试任务"""

    def __init__(self):
        super().__init__()
        self.description = '简单测试任务'
        # 添加标注脚本用于测试自然语言描述功能
        self.code_script_labeled = """
print("开始执行简单测试任务")  # [1]

device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm  # [2]

result = 1 + 1  # [3]
print(f"计算结果: {result}")  # [3]

for i in range(3):  # [4]
    print(f"循环第 {i+1} 次")  # [4]
    time.sleep(0.5)  # [4]
    raise Exception("test error")  # [4]

print("任务执行完成")  # [5]
        """

        self.NL_script_labeled = """
[1]开始执行测试任务，输出开始信息。
[2]获取 agent 的各个接口组件。
[3]执行简单的数学计算并输出结果。
[4]执行循环操作，每次输出当前循环次数。
[5]输出任务完成信息。
        """

    def main(self, agent: RuyiAgent):
        print("开始执行简单测试任务")

        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm

        result = 1 + 1
        print(f"计算结果: {result}")

        for i in range(3):
            print(f"循环第 {i + 1} 次")
            time.sleep(0.5)
            raise Exception("test error")

        print("任务执行完成")



def main():
    """主函数，演示行级跟踪功能"""
    
    print("===== RuyiTask 行级跟踪功能演示 =====")

    # 创建配置
    yaml_file = os.path.join(os.path.dirname(__file__), '..', 'config.yaml')
    if not os.path.exists(yaml_file):
        raise FileNotFoundError(f'Config file not found: {yaml_file}, you need copy config_example.yaml to config.yaml and modify it according to your environment.')
    parser = RuyiArgParser((RuyiConfig,))   # type: ignore
    config = parser.parse_yaml_file(yaml_file=yaml_file)
    config = config[0]
    
    # 配置行级跟踪
    config.enable_line_tracing = True
    # config.line_tracing_show_code = True  # 显示代码内容

    agent = RuyiAgent(config)
    
    try:
        demo_task = CreateContactWithTracing()
        # demo_task = SimpleTask()


        # 执行任务
        agent.task.execute_task(demo_task)

    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n执行出错: {e}")
    finally:
        agent.stop()


if __name__ == '__main__':
    main() 