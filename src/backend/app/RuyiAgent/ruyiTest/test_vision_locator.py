from ruyi.task import RuyiTask


class TestVisionLocator(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """测试视觉定位"""

    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('Contacts')
        ui.root.locate_view('First contact in the list').click()
        ui.root.locate_view("Email of this contact").content()


class Test_view_snapshot(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """测试截图"""

    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        for view in ui.iterate_views("每个权限"):
            img = view.snapshot()
            img.show()
            input()
