from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask
import structlog

logger = structlog.get_logger("ruyi")

class EN_Track_Product_Price(RuyiTask):
    def __init__(self, item_name: str = "iPhone 16", target_price: float = 10000, sleep_interval: int = 24 * 60 * 60):
        super().__init__()
        self.description = """
           Real-time tracking of product prices across all shopping apps (like eBay), notify me immediately to purchase if the price is below X."""
        self.item_name = item_name
        self.target_price = target_price
        self.sleep_interval = sleep_interval

    def searchOn(self, agent: RuyiAgent, user_query: str, app_name: str) -> int:
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app(app_name)
        ui.ensure(
            f"Currently on {app_name} APP homepage, there's a search box at the top, the main body contains shopping mall content with various shopping features and product information. There are no UI elements like pop-ups that affect normal operation.")
        
        ui.root.locate_view("Text input box at the top").click()
        ui.root.locate_view('Text input box at the top').input(user_query)
        ui.root.locate_view(f"The first search result of {self.item_name}").click()

        ui.ensure("The current interface shows some product images and product prices")

        price = fm.vlm(device.take_screenshot(), "What is the price of the first search result in CNY?", returns=[('price', float)])
        logger.info(
            f"price: {price}", 
            action='get price', 
            status='done',
            metadata={'price': price}
        )

        ui.back_to('Current UI interface is on the phone\'s desktop, with some app icons on the screen and several app icons at the bottom')

        return price

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        for i in range(2):
            discount_message = []
            tb_price = self.searchOn(agent, self.item_name, 'eBay')

            discount_message.append(("eBay", tb_price) if tb_price < self.target_price else None)

        ui.back_to('Current UI interface is on the phone\'s desktop, with some app icons on the screen and several app icons at the bottom')
