from ruyi.fm.models import ExpensiveModelAPI
from ruyi.agent import AgentConfig
import unittest
from openai import OpenAI
import os

class FMforTest:
    def __init__(self, agent):
        self.agent = agent
    
    def _record_token_cost(self, model, url, prompt_tokens, completion_tokens):
        pass

class AgentForTest:
    def __init__(self, config=None):
        self.config = config
        self.fm = FMforTest(self)

class TestModelAPI(unittest.TestCase):
    def setUp(self):
        self.agent = AgentForTest(config=AgentConfig())  # type: ignore
        self.model = ExpensiveModelAPI(self.agent)  # type: ignore
        self.model.client = OpenAI(api_key=os.environ['OPENAI_API_KEY'], base_url=os.environ['OPENAI_API_URL'])

    def test_call_without_client(self):
        with self.assertRaises(ValueError):
            self.model('hello', returns=None)

    def test_call_without_model_name(self):
        with self.assertRaises(ValueError):
            self.model('hello', returns=None)

    def test_organize_request_without_args(self):
        request = self.model._organize_request('hello', returns=['a', 'b'])
        expected_value = [
            {
                'role': 'system',
                'content': 'You are a helpful assistant.'
            },
            {
                'role': 'user',
                'content': 'hello\nYour answer should be a list that can be parsed by the json.loads API. ' + \
                    'Please follow the following descriptions and types for each element:\n[a, b]'
            },
        ]
        self.assertEqual(request, expected_value)

    def test_organize_request_with_args(self):
        request = self.model._organize_request('hello', 'world', returns=['a', 'b'])
        expected_value = [
            {
                'role': 'system',
                'content': 'You are a helpful assistant.'
            },
            {
                'role': 'user',
                'content': 'hello\nworld\nYour answer should be a list that can be parsed by the json.loads API. ' + \
                    'Please follow the following descriptions and types for each element:\n[a, b]'
            },
        ]
        self.assertEqual(request, expected_value)

    def test_organize_request_with_multiple_args(self):
        request = self.model._organize_request('hello', 'world', 'test', returns=['a', 'b'])
        expected_value = [
            {
                'role': 'system',
                'content': 'You are a helpful assistant.'
            },
            {
                'role': 'user',
                'content': 'hello\nworld\ntest\nYour answer should be a list that can be parsed by the json.loads API. ' + \
                    'Please follow the following descriptions and types for each element:\n[a, b]'
            },
        ]
        self.assertEqual(request, expected_value)

    def test_organize_request_with_returns_as_list_of_tuples(self):
        request = self.model._organize_request('hello', 'world', returns=[('a', 'int'), ('b', 'str')])
        expected_value = [
            {
                'role': 'system',
                'content': 'You are a helpful assistant.'
            },
            {
                'role': 'user',
                'content': 'hello\nworld\nYour answer should be a list that can be parsed by the json.loads API. ' + \
                    'Please follow the following descriptions and types for each element:\n[a(int), b(str)]'
            },
        ]
        self.assertEqual(request, expected_value)

    def test_organize_request_with_returns_as_list_of_mixed_types(self):
        request = self.model._organize_request('hello', 'world', returns=['a', ('b', 'int')])
        expected_value = [
            {
                'role': 'system',
                'content': 'You are a helpful assistant.'
            },
            {
                'role': 'user',
                'content': 'hello\nworld\nYour answer should be a list that can be parsed by the json.loads API. ' + \
                    'Please follow the following descriptions and types for each element:\n[a, b(int)]'
            },
        ]
        self.assertEqual(request, expected_value)

    def test_parse_response_with_returns(self):
        self.model.model_name = 'gpt-4o-mini'
        prompt = """
阅读如下文本，并给出中国的投资总额（单位：亿美元）和主要投资方向（用顿号分隔）。

美国：投资总额为5000亿美元，主要集中在人工智能（3000亿美元）、云计算（1500亿美元）和量子计算（500亿美元）
中国：投资总额为4000亿美元，重点领域包括人工智能（2000亿美元）、5G技术（1500亿美元）和区块链（500亿美元）
德国：投资总额为800亿美元，主要用于人工智能（400亿美元）、自动驾驶（300亿美元）和可再生能源（100亿美元）
日本：投资总额为600亿美元，重点领域有机器人技术（300亿美元）、人工智能（200亿美元）和半导体（100亿美元）
        """
        response = self.model(prompt, returns=[('country', 'str'), ('investment', 'int'), ('main_focus', 'str')])
        self.assertEqual(response, ['中国', 4000, '人工智能、5G技术、区块链'])
