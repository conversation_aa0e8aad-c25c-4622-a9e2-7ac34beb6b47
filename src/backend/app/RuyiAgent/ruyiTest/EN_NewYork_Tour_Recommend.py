# Test tasks written by @Rui
# Date: 2024-12-15

from ruyi.agent import Ruyi<PERSON><PERSON>
from ruyi.task import RuyiTask
import structlog

logger = structlog.get_logger("ruyi")

class EN_NewYork_Tour_Recommend(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """Please recommend places for an travel in New York based on the weather, for a group of about 6 people"""

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('Weather')

        ui.ensure("Currently on the Weather APP homepage, showing current location, weather information, and a button at the bottom to view more weather forecasts.")
        ui.root.locate_view('15-day forecast').click()
        
        weather_info = fm.vlm(device.take_screenshot(), "How's tomorrow's weather? Please answer sunny or cloudy", returns=[('sunny or cloudy', str)])
        ui.back_to('Current UI interface is on the phone desktop, with some app icons on the screen and several app icons at the bottom')

        logger.info(
            f"Weather information: {weather_info}", 
            action='get weather information', 
            status='done',
            metadata={'weather_info': weather_info}
        )

        device.start_app('Lemon8')
        ui.ensure("Currently on Lemon8 APP homepage, the main content consists of text and images. There are no pop-ups or UI elements affecting normal operation.")
        ui.root.locate_view('Search').click()
        ui.root.locate_view('Text entry boxes').input('3d New York Travel')
        ui.root.locate_view('Search').click()

        plans = []
        for v in ui.root.iterate_views('Titles of posts about New York Travel', limit=8):
            v.click()
            v.scroll_down(distance=300)
            v.scroll_down(distance=800)
            plans.append(fm.vlm(device.take_screenshot(), "Please extract the content in this screenshot for me", returns=str))
            ui.back_to('Current UI interface, containing some image and text posts, with a text input box at the top containing text')

        answer = fm.llm(plans, f"These plans are examples, please recommend a 3d New York Travel based on the current weather {weather_info}",
                        returns=[('outing plan', str)])
        logger.info(
            f"Answer: {answer}", 
            action='get answer', 
            status='done',
            metadata={'answer': answer}
        )
        
        ui.back_to('Current UI interface is on the phone desktop, with some app icons on the screen and several app icons at the bottom')