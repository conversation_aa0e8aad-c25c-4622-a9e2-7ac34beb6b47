from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask
from ruyi.utils.debug import _save_result
from deprecated import deprecated

class Qzone_Tracker(RuyiTask):
    def __init__(self, userQQ: str | int="1760768391", depth: int = 1, width: int = 10, fans_max: int = 10):
        super().__init__()
        self.description = """
            帮我记录qq用户A的最近10条qq空间中给他点赞评论的人，并且访问他们的空间，并记录他们最近10条的qq空间，以及谁给他们点赞评论了（至多10个）"""
        self.userQQ = userQQ
        self.depth = depth
        self.width = width
        self.fans_max = fans_max

    def _watcher(self, agent: RuyiAgent, depth: int = 1) -> (str, list):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        # user_name = ui.root.locate_view("用户名").content()
        user_name = fm.vlm(ui.root.snapshot(), "请告诉我这个qq空间的主人的名字", returns='用户名')
        prompt = """
        请你找寻图中的qq空间动态，返回一个list的"""
        qzone_data:list = []
        # for qzone in ui.root.iterate_views(prompt, limit=self.width):
        #     qzone.click()
        #     curr_qzone = {}
        #     curr_qzone['content'] = fm.llm(qzone.image(), "请你记录这个动态中的文字，并描述其中的文本",
        #                                    returns=['综合描述'])
        #     if depth > 0:
        #         curr_qzone['person'] = {}
        #         for person in ui.root.iterate_views("请你寻找这条动态中给ta点赞和评论的人", limit=self.fans_max):
        #             person.click()
        #             name, data = self._watcher(agent, depth=depth - 1)
        #             curr_qzone['person'][name] = data
        #             ui.back()
        #     ui.back()
        return user_name, qzone_data

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app("QQ")
        ui.root.locate_view('联系人').click()
        if isinstance(self.userQQ, int):
            self.userQQ = str(self.userQQ)
        ui.root.locate_view('顶部的搜索框').click()
        ui.root.locate_view('顶部的搜索框').input(self.userQQ)
        ui.root.locate_view("搜索结果中的第一个联系人").click()
        ui.root.locate_view("右上角的三个横线按钮").click()
        ui.root.locate_view("屏幕左上半部分的用户头像").click()
        ui.root.locate_view('ta的qq空间（此处的ta可能是她也可能是他）').click()
        user, data = self._watcher(agent, depth=self.depth)
        
        for _ in range(5): ui.back()


@deprecated
# 没有notification方面的内容
class Wechat_spam_filter(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
        当我收到微信时，分析内容的重要性，如果是重要信息，发出声音提醒"""

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        # TODO:获取通知栏
        while notification := device.get_notification():
            if notification.app == '微信' and fm.llm.analyze(notification.context, '请问这条信息的重要性高吗？',
                                                             returns=[("是否",bool)]):
                _save_result("重要信息！" + str(notification))
