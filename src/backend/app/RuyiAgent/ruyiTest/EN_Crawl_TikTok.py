# Test tasks written by @Rui
# Date: 2024-12-15

from ruyi.agent import <PERSON>uy<PERSON><PERSON><PERSON>
from ruyi.task import RuyiTask
import structlog

logger = structlog.get_logger("ruyi")

class EN_Crawl_TikTok(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            Scan TikTok videos, record creator profiles, categorize them, and store the information in the database.
            (Profile should include name, author description, typical work description, and maximum likes count)."""

    def main(self, agent: RuyiAgent, sleep_interval: int = 3):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('TikTok')  # Open TikTok

        ui.root.locate_view('Home').click()
        ui.ensure("Current interface is video playback interface, with likes, favorites, comments counts on the bottom right.")
        profiles = data.live_table()  # Create a dynamic table named profiles
        
        for _ in range(2):
            video_desc = fm.vlm(device.take_screenshot(), 'Describe this video', returns=[('video content description', str)])
            num_likes = fm.vlm(device.take_screenshot(), 'The number under the like icon on the right side of the interface', returns=[('likes count', int)])
            ui.root.locate_view('Author nickname in the bottom left corner of the interface').click()

            author_name = fm.vlm(device.take_screenshot(), 'Author nickname below the avatar', returns=[('author nickname', str)])            
            author_desc = fm.vlm(device.take_screenshot(), 'Briefly describe this author based on the profile information shown', returns=[('author description', str)])
            
            ui.root.locate_view('The center of the thumbnail image in the bottom half of the interface').scroll(direction='down')
            
            for v in ui.root.iterate_views('Brief description of each thumbnail at the bottom of the screen', limit=3):
                v_num_likes = fm.vlm(device.take_screenshot(), f"Like count in the bottom left corner of thumbnail '{v.description}'", returns=[('likes count', int)])

                if v_num_likes > num_likes:
                    num_likes = v_num_likes
            profiles.add_row(
                {'Author': author_name, 'Author Description': author_desc, 'Representative Work Description': video_desc, 'Likes Count': num_likes})
            device.back()
            ui.root.scroll_down(1000)
            logger.info(f'{author_name} crawled!', action='crawl wechat channels', status='done')
            agent.sleep(sleep_interval)  # sleep 3 seconds
        
        ui.back_to('Current UI interface is on the phone desktop, with some app icons on the screen and several app icons at the bottom')
