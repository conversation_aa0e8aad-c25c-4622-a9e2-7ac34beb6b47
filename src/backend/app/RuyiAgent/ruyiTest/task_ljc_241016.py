from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask
from ruyi.utils.debug import _save_result
from ruyi.utils import debug
import time
import structlog

logger = structlog.get_logger("ruyi")

class Track_Product_Price(RuyiTask):
    def __init__(self, item_name: str = "iPhone 16 pro max 256g", target_price: float = 0, sleep_interval: int = 24 * 60 * 60):
        super().__init__()
        self.description = """
           实时追踪某商品在所有购物应用（如淘宝、京东等）的价格，如果价格低于X元，立即提醒我购买。"""
        self.item_name = item_name
        self.target_price = target_price
        self.sleep_interval = sleep_interval

    def searchOn(self, agent: RuyiAgent, user_query: str, app_name: str) -> int:
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app(app_name)
        ui.ensure("当前界面中没有弹窗")
        logger.info(
            f"app_name: {app_name}", 
            action='start app', 
            status='done',
            metadata={'app_name': app_name}
        )
        
        ui.root.locate_view('顶部的文字输入框').click()
        ui.root.locate_view('顶部的文字输入框').input(user_query)
        ui.root.locate_view(f'搜索结果中的第一个{user_query}').click()

        price = fm.vlm(device.take_screenshot(), "找到搜索结果中的第一个的价格是多少元？", returns=[('价格', float)])
        logger.info(
            f"price: {price}", 
            action='get price', 
            status='done',
            metadata={'price': price}
        )

        ui.back_to('当前界面的左下角有电话app的图标')

        return price

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm

        discount_message = []
        for app_name in ['京东']:
            price = self.searchOn(agent, self.item_name, app_name)
            discount_message.append((app_name, price) if price < self.target_price else None)

        logger.info(
            f"discount_message: {discount_message}", 
            action='get discount message', 
            status='done',
            metadata={'discount_message': discount_message}
        )

        ui.back_to('当前界面的左下角有电话app的图标')

class Find_collaborator(RuyiTask):
    def __init__(self, product_des: str="洗碗机", max_num=2):
        super().__init__()
        self.description = """
           根据我司的xxx产品特点，自动在com.xingin.xhs/.index.v2.IndexActivityV2里面寻找适合推广这个产品的达人（粉丝数大于1000且作品内容与产品相关），找到之后聊一聊，如果对方有意向，通知我。"""
        self.product_des = product_des
        self.max_num = max_num

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        candidate_user_id: set[str] = set()
        used_keywords:set[str] = set()
        device.start_app("小红书")
        for i in range(2):
            keyword = fm.llm(
                f"我们公司的产品有以下特征：{self.product_des}，我想为该产品找一些代言人，请为我生成一个描述该产品特质的关键词，比如“干净卫生”。关键词要在这个范围之外：{used_keywords}",
                returns='关键词')
            used_keywords.add(keyword)
            ui.root.locate_view('搜索').click()

            ui.root.locate_view('顶部的文字输入框').input(f"{keyword}")

            ui.root.locate_view('搜索').click()


            ui.back()
            ui.back()

            debug.print_method_name_with_message(f"keyword: {keyword}")

            # for v in ui.root.iterate_views('搜索结果下的每个博客', limit=20):
            #     user_id = v.locate_view('该博客的发布者').content()
            #     candidate_user_id.add(user_id)

            # success_user_id = []
            # for user_id in candidate_user_id:
            #     success_user_id.append(user_id if self.chat_candidate(agent, user_id) else None)
            # _save_result(message=success_user_id)
        ui.back()
        ui.back()
        ui.back()
        ui.back()

    def chat_candidate(self, agent: RuyiAgent, user_id: str):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app("小红书")
        ui.root.locate_view("上面的搜索框").input(user_id)
        ui.root.locate_view("搜索").click()
        ui.root.locate_view("筛选用户").click()
        ui.root.locate_view("搜索结果中的第一个用户").click()
        ui.root.locate_view("下面的聊天框").click()
        ui.root.input(f"你好！" + user_id + "我们的产品是" + self.product_des + "请问您是否有兴趣接推广？")
        ui.root.locate_view("发送").click()
        answer = ui.wait_view("用户的新回复", timeout=30 * 60).content()
        return fm.llm(f"根据下面这段用户的回复，判断该用户是否对我们的产品有兴趣：{answer}", returns=[('是或否',bool)])
