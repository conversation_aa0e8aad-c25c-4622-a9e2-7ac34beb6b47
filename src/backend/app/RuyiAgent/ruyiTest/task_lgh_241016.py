from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask
from ruyi.utils import debug
import time
import structlog

logger = structlog.get_logger("ruyi")

class Plan_Xian_Trip(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            制定去西安的三天旅行计划，要求包括拍古装照、享受美食美景，并体验当地文化。要求不坐飞机。"""

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        # 新建计划
        plan_result = data.live_table()

        # 确定出行方式
        transport_plans = []
        device.start_app('携程旅行')
        ui.ensure("当前界面中没有弹窗")
        ui.root.locate_view('搜索按钮 界面右上角').click()
        ui.root.locate_view('搜索框').input('西安')
        ui.root.locate_view('搜索结果中的第一个西安 陕西').click()
        ui.root.locate_view('到这去').click()

        for v in ui.root.iterate_views('所有去西安的不同交通工具组合出行方式', limit=3):
            transport_methods = fm.llm(v.locate_description + ' 总结这个出行计划', returns='出行计划')
            if '飞机' in transport_methods:
                continue
            transport_plans.append(transport_methods)

        ui.back_to('当前界面的左下角有电话app的图标')

        # 确定游玩项目
        keywords = ['古装照', '美食美景']
        plan_list = []
        device.start_app('小红书')
        ui.ensure("当前界面中没有弹窗")
        for kw in keywords:
            ui.root.locate_view('右上角的搜索按钮').click()

            ui.root.locate_view('顶部的文字输入框').input(f'西安出行推荐:{kw}')
            
            ui.root.locate_view('右上角的搜索按钮').click()

            for v in ui.root.iterate_views(f'所有帖子的文字标题', limit=3):
                v.click()
                analysis = fm.vlm(device.take_screenshot(), f'请你提取出图片和文字中关于西安出行推荐:{kw}的关键信息', returns=[('出行推荐', str)])
                plan_list.extend(analysis)    
                ui.back_to('当前界面右上角有搜索按钮')
            
            ui.back_to('界面顶部有发现按钮')

        integrated_information = fm.llm(
            f'请你基于现有的出行方案以及景点的相关信息，结合用户需求，制定一份为期三天的旅行方案，并考虑到出行的舒适度等因素合理安排时间。出行相关信息：{str(transport_plans)}；景点相关信息：{str(plan_list)}',
            returns=[('出行方案', str)]) 
        
        logger.info(
            f"西安出行方案: {integrated_information}", 
            action='get answer', 
            status='done',
            metadata={'answer': integrated_information}
        )
        plan_result.add_row({'出行方案': integrated_information})
        plan_result.rich_console_display()
        plan_result.to_dataframe().to_csv('西安出行方案.csv', index=False)

        ui.back_to('当前界面的左下角有电话app的图标')

class Organize_Invoice_Photos(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            将手机中所有与发票相关的照片整理到一个文件夹里。"""

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('相册')
        ui.root.locate_view('相册').click()
        ui.root.locate_view('右下角的加号按钮').click()
        ui.root.locate_view('新建相册').click()
        ui.root.locate_view('输入相册名称').input("发票")
        ui.root.locate_view('右下角的确定按钮').click()

        for v in ui.root.iterate_views('照片的缩略图', limit=8):
            v.click()
            if fm.vlm(device.take_screenshot(), '这张照片是否是发票？', returns=[("True or False", 'bool')]):
                ui.root.locate_view('界面右上角的圆圈').click()
            ui.back()

        ui.root.locate_view('右上角的完成按钮').click()

        for _ in range(4): ui.back()
