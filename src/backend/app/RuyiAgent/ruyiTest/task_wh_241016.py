from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask
from ruyi.utils.debug import _save_result
from deprecated import deprecated
import time
import structlog

logger = structlog.get_logger()

class Search_cantonese_food(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            请搜索附近5km以内的所有粤菜厅，并查询最便宜的2～4人套餐"""

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app("美团")
        ui.ensure(
            f"当前在美团APP的首页，页面的上方有搜索框，页面的主体是生活服务的的内容，有各样的购物功能和商品信息。整个页面上没有影响正常操作的弹窗等 UI 元素。")
        ui.root.locate_view("左上屏幕的团购按钮").click()
        ui.root.locate_view("顶部文本输入框").click()
        ui.root.locate_view("顶部文本输入框").input("广东菜")
        ui.root.locate_view("右上角的搜索按钮").click()
        menu_info_list = []
        package_info_list = []
        current_state = '当前界面是广东菜商家列表'
        ui.ensure(current_state)
        for v in ui.root.iterate_views("广东菜商家列表", limit=10):
            v.click()
            v.locate_view("左下角的门店按钮").click()
            discription = fm.vlm(v.snapshot(), f"请你获取该界面商家的详细信息，比如名称，位置，评分，和距离。", returns="商家信息")
            name, location, score, distance = fm.llm(f"请你根据商家的详细信息({discription})，提取出出商家的名称，位置，评分，和距离。", returns=[('名称', 'str'), ('位置', 'str'), ('评分', 'float'), ("距离(千米)", 'float')])
            ui.back_to(current_state)
            logger.info(f"商家{name}的信息：{location}, {score}, {distance}")
        #     # 方案一，通过截屏来获取元素
        #     # name = ui.locate_view(f"商家{restaurant}右侧的名称").get_text()
        #     # location = ui.locate_view(f"商家{restaurant}右侧的具体位置").get_text()
        #     # score = ui.locate_view(f"商家{restaurant}评分").get_text()
        #     # distance = ui.locate_view(f"商家{restaurant}最右侧所示的距离").get_text()

        #     # # 方案二，通过VH来获取元素
        #     # name = resturant.locate_view("商家名称").get_text()
        #     # location = resturant.locate_view("商家位置").get_text()
        #     # score = resturant.locate_view("商家评分").get_text()
        #     # distance = resturant.locate_view("距离").get_text()

        #     # if distance.endswith("km"):
        #     #     distance = float(distance[:-2])
        #     # if distance.endswith("m"):
        #     #     distance = float(distance[:-1]) / 1000
        #     if distance > 5:
        #         continue

        #     # 如果已经遍历过这个饭店，就不继续查看它了
        #     restaurant_info = {"name": name, "location": location, "score": score, "distance": distance}
        #     restaurant_info_list.append(restaurant_info)

        #     ui.root.locate_view(f"商家{restaurant}卡片").click()

        #     if fm.vlm(ui.root.snapshot(), "是否存在团购列表", returns=[('是或否'), bool]):
        #         for v in ui.root.iterate_views('团购列表', limit=10):
        #             package_title, package_price, people_nums_min, people_nums_max = \
        #                 fm.vlm(v.image(), '分析以下团购套餐的内容',
        #                        returns=['团购名称', ('团购价格', float),
        #                                 ('适用套餐的最少人数（比如3-4人就是3）', int),
        #                                 ('适用套餐的最多人数（比如3-4人就是4）', int)])

        #             if people_nums_min >= 2 and people_nums_max <= 4:
        #                 package_info_list.append(
        #                     {"name": name, "package_name": package_title, "package_price": package_price,
        #                      "restaurant": restaurant_info})
        #     ui.back()

        min_price, min_price_package_info = float("inf"), None
        for package in package_info_list:
            if package["package_price"] < min_price:
                min_price = float(package["package_price"])
                min_price_package_info = package
        # _save_result(message=fm.llm(
        #     f'最便宜的套餐是{min_price_package_info["package_name"]}，价格为{min_price_package_info["package_price"]}，请你整理相关信息，汇总返回给用户'),
        #     returns=['相关信息'])
        for _ in range(5): ui.back()

@deprecated
class Nihong_plan(RuyiTask):
    """设置日期的方法尚未确定"""

    def __init__(self, src: str, dest: str, date: str):
        super().__init__()
        self.description = """
            检索飞机票"""
        self.src = src
        self.dest = dest
        self.date = date

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('微信')  # 打开微信
        ui.root.locate_view("我").click()
        ui.root.locate_view("服务").click()
        ui.root.locate_view("火车票机票").click()
        ui.root.locate_view("国际").click()
        ui.root.locate_view("出发地").click()
        ui.root.locate_view("搜索").click()
        ui.root.locate_view("搜索城市").input(self.src)
        ui.root.locate_view(self.src).click()
        ui.root.locate_view("目的地").click()
        ui.root.locate_view("搜索").click()
        ui.root.locate_view("搜索城市").input(self.dest)
        ui.root.locate_view(self.dest).click()
        ui.root.locate_view("出发日期").click()
        ui.root.locate_view(self.date).click()
        ui.root.locate_view("飞机票查询").click()
        to_flight_infos = []
        for flight in ui.root.iterate_views("去程所有航班列表", limit=100):
            screen_snapshot = flight.snapshot()
            price, time, code = fm.vlm(screen_snapshot, '请你分析这趟飞机的信息',
                                       returns=[('价格', float), '起飞时间', '航班号'])
            to_flight_info = {"价格": price, "起飞时间": time, "航班号": code}
            to_flight_infos.append(to_flight_info)
        _save_result(to_flight_infos)
        # while True:
        #     for flight in ui.iterate_views("返程所有航班列表", limit=100):
        #         price = ui.root.locate_view(f"机票{flight}的价格").get_text()
        #         time = ui.root.locate_view(f"机票{flight}的时间").get_text()
        #         plane = ui.root.locate_view(f"机票{flight}的航班").get_text()
        # 
        #         # 如果已经遍历过这个饭店，就不继续查看它了
        #         back_flight_info = {"price": price, "time": time, "plane": plane}
        #         if back_flight_info not in back_flight_infos:
        #             back_flight_infos.append(back_flight_info)
        #         else:
        #             continue
        # 
        #     scroll_success = ui.root.scroll("down", stride=ui.root.locate_view("返程机票列表").get_height())
        #     if not scroll_success:
        #         break
        # 
        # device.open_app('Booking')
        # ui.root.locate_view("目的地").click()
        # ui.root.locate_view("搜索框").set_text('东京')
        # ui.root.locate_view("东京").click()
        # ui.root.locate_view("10.11").click()
        # ui.root.locate_view("10.22").click()
        # ui.root.locate_view("选择日期").click()
        # ui.root.locate_view("搜索").click()
        # 
        # hotel_info_list = []
        # while True:
        #     for hotel in ui.iterate_views("所有酒店列表", limit=100):
        #         name = ui.root.locate_view(f"酒店{hotel}的名称").get_text()
        #         price = ui.root.locate_view(f"酒店{hotel}的价格").get_text()
        #         location = ui.root.locate_view(f"酒店{hotel}的位置").get_text()
        #         score = ui.root.locate_view(f"酒店{hotel}的评分").get_text()
        # 
        #         # 如果已经遍历过这个饭店，就不继续查看它了
        #         hotel_info = {"price": price, "name": name, "location": location, "score": score}
        #         if hotel_info not in hotel_info_list:
        #             hotel_info_list.append(hotel_info)
        #         else:
        #             continue
        # 
        #     scroll_success = ui.root.scroll("down", stride=ui.root.locate_view("所有酒店列表").get_height())
        #     if not scroll_success:
        #         break
        # 
        # all_possible_plans = []
        # for to_flight_info in to_flight_infos:
        #     for back_flight_info in back_flight_infos:
        #         for hotel_info in hotel_info_list:
        #             if to_flight_info["price"] + back_flight_info["price"] + hotel_info["price"] < 1000:
        #                 all_possible_plans.append(
        #                     {"to_flight": to_flight_info, "back_flight": back_flight_info, "hotel": hotel_info})
        # return fm.llm(
        #     f'共有{len(all_possible_plans)}个方案，它们是：{all_possible_plans}，你可以选择其中多个方案，整理相关信息，汇总返回给用户',
        #     returns=['方案'])
