from deprecated import deprecated
import time
from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask
import structlog

logger = structlog.get_logger("ruyi")

class Weather_based_outing_recommendation(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """请根据天气，给我推荐一下，最近适合去哪里郊游，6人左右"""

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('天气')
        ui.ensure("当前界面中没有弹窗")
        ui.root.locate_view('查看近15日天气').click()

        weather_info = fm.vlm(device.take_screenshot(), "明天天气情况怎么样？请回答晴天或阴天", returns=[('晴天或阴天', str)])
        
        ui.back_to('当前界面的左下角有电话app的图标')

        logger.info(
            f"Weather information: {weather_info}", 
            action='get weather information', 
            status='done',
            metadata={'weather_info': weather_info}
        )

        device.start_app('小红书')
        ui.ensure("当前界面中没有弹窗")
        ui.root.locate_view('搜索按钮 界面右上角').click()
        ui.root.locate_view('文字搜索框 屏幕顶部').input(f"北京郊游 {weather_info} 6人左右")
        ui.root.locate_view('搜索按钮 界面右上角').click()

        plans = []
        for v in ui.root.iterate_views('所有帖子的文字标题', limit=3):
            v.click()
            ui.root.scroll('down')
            plans.append(fm.vlm(device.take_screenshot(), "请你总结这个郊游计划", returns=[('郊游计划', str)]))
            ui.back_to('当前界面右上角有搜索按钮')

        answer = fm.llm(plans, f"这些计划是例子，请你根据当前天气{weather_info}，推荐一下，最近适合去哪里郊游，6人左右",
                        returns=[('郊游计划', str)])
        logger.info(
            f"Answer: {answer}", 
            action='get answer', 
            status='done',
            metadata={'answer': answer}
        )
        
        ui.back_to('当前界面顶部有关注按钮')


@deprecated
class Live_homework_assistance(RuyiTask):
    """Audio功能暂不支持，∴Deprecated"""

    def __init__(self, sleep_interval: int = 5):
        super().__init__()
        self.description = """
        请你帮我辅导孩子写作业，用摄像头看着孩子的作业，如果有不会的，及时辅导"""
        self.sleep_interval = sleep_interval
    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        while True:
            photo = device.take_picture()
            # TODO 没有录音功能
            audio = device.record_audio()

            result = fm.vlm(photo, '请根据天气，给我推荐一下，最近适合去哪里郊游，6人左右', returns='地点')
            # TODO 录音对应修改
            if fm.vlm(photo, "孩子写作业遇到了问题", returns=['是或否']) or fm.alm(audio, "孩子主动问问题",
                                                                                           returns=['是或否']):
                golden_answer = fm.llm("查看孩子作业里的问题并回答")

            agent.sleep(self.sleep_interval)
