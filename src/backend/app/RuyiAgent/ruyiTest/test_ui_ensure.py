# Test tasks written by @Yuanchun

import unittest
from ruyi.agent import RuyiAgent, RuyiConfig
from ruyi.task import RuyiTask


# A very simple example for testing `ensure` function
class Create_contact_1(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            创建一个名为Ruyi，email为*******************的联系人。"""

    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('Contacts')
        ui.root.locate_view('Create Contact').click()
        matched = ui.ensure(
            'A page for creating a contact, including text fields for First Name, Last Name, Email, and a Save button.')
        self.results['matched'] = matched
        ui.root.locate_view('First Name').input('Ruyi')
        ui.root.locate_view('Email').input('<EMAIL>')
        ui.root.locate_view('Save').click()


# A very simple example for testing `ensure` function
class Create_contact_2(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            创建一个名为Ruyi，email为*******************的联系人。"""

    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('Contacts')
        ui.root.locate_view('Create Contact').click()
        matched = ui.ensure('A page of email inbox.')
        self.results['matched'] = matched
        ui.root.locate_view('First Name').input('Ruyi')
        ui.root.locate_view('Email').input('<EMAIL>')
        ui.root.locate_view('Save').click()


class Taobao_ensure(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """帮助关闭开屏广告并进入 Taobao 首页，然后搜索关键字“手机”"""

    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.apps.start('Taobao')
        ui.ensure('当前在淘宝的首页，页面中没有广告、弹窗等其他元素')
        ui.root.locate_view('上面的搜索框').click()

class Ensure_patch(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """帮助关闭开屏广告并进入 Taobao 首页，通过patch关闭弹窗，然后搜索关键字“手机”"""

    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.apps.start('Taobao')
        ui.ensure('当前在淘宝的首页，页面中没有广告、弹窗等其他元素', ensure_id="taobao_home_page")
        ui.root.locate_view('上面的搜索框').click()

    def ensure_patches(self):
        @self.register_patch("taobao_home_page", description="当遇到广告弹窗时，关闭弹窗")
        def taobao_home_page(agent):
            device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
            ui.root.locate_view('关闭广告').click()



class TestModelAPI(unittest.TestCase):
    def setUp(self):
        config = RuyiConfig()
        self.agent = RuyiAgent(config)

    # def tearDown(self):
    #     self.agent.stop()

    def test_create_contact_with_correct_ensure(self):
        task = self.agent.task.execute_task(Create_contact_1)
        self.assertEqual(task.status, 'Finished')
        self.assertEqual(task.results['matched'], True)

    def test_create_contact_with_incorrect_ensure(self):
        task = self.agent.task.execute_task(Create_contact_2)
        self.assertEqual(task.status, 'Finished')
        self.assertEqual(task.results['matched'], False)
