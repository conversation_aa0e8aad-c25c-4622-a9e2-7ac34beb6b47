from deprecated import deprecated

from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask


@deprecated
class Monitor_health(RuyiTask):
    """
    日期相关内容暂不implement，且提取客户需求，日期，信息等部分太过模糊
    """

    def __init__(self):
        super().__init__()
        self.description = """
            在微信上跟踪客户沟通记录，识别每个客户需求和排期，并整理信息到日历的对应议程中。如果需要预定会议，则打开腾讯会议自动设置对应时间的会议。"""


    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('微信')  # 打开微信
        clients_data = data.live_table()
        clients = fm.vlm('列出聊天框内所有的用户名,返回一个list', returns=['所有用户名'])
        for client_name in clients:  # 循环执行以下操作
            device.start_app('微信')  # 打开微信
            ui.root.locate_view(f'{client_name} 按钮').click()
            # ui.assert(f'界面正在显示{client_name}聊天记录')  # 确保当前为聊天界面
            ui.root.scroll('down')
            for v in ui.root.iterate_views('识别聊天记录中客户的需求', limit=10):
                # chat_history = ui.root.long_snapshot().image()
                client_needs, client_schedule, client_meeting = fm.vlm(v.image(),
                                                                       '请你识别并描述聊天内容中客户的需求，并找找有没有对应事物的截止日期和会议日期',
                                                                       returns=[('用户需求', str),
                                                                                ('截止日期，如果没有返回"无"', str),
                                                                                ('会议日期，如果没有返回"无"', str)])
                # # 提取客户需求
                # client_needs = fm.vlm(chat_history, '识别并描述聊天内容中客户的需求', returns=['用户需求'])
                # 
                # # 检查是否有提到时间排期
                # client_schedule = fm.vlm(chat_history,
                #                          f'检测聊天记录中是否有提到需求 {client_needs} 的截止日期，并返回时间信息')
                # client_meeting = fm.vlm(ui.root.long_snapshot().image(),
                #                         f'检测聊天记录中是否有提到会议日期，如果有，则返回会议日期')
                # # 记录到动态表格中
                clients_data.add_row({'客户': client_name, '需求': client_needs, '排期': client_schedule})

                # 如果有排期，添加到日历
                if "无" != client_schedule:
                    device.start_app('日历')
                    ui.root.locate_view(f'添加 按钮').click()
                    ui.root.locate_view(f'选择 事件名 文本框').input(client_needs).click()
                    ui.root.locate_view(f'选择 日期 文本框').input(client_schedule).click()
                    ui.root.locate_view(f'完成 按钮').click()

                # 检查是否需要创建腾讯会议
                if "无" != client_meeting:
                    device.start_app('腾讯会议')  # 打开腾讯会议应用
                    ui.root.locate_view(f'预定会议 按钮').click()
                    ui.root.locate_view(f'下一步 按钮').click()
                    ui.root.locate_view(f'开始时间 按钮').click()
                    ui.root.locate_view(f'选择 日期 文本框').input(
                        client_meeting)  # 日期选择是滚动设置的，怎么用这一套语言表示？（感觉滚动交互对于大模型而言有本质的困难）.click()
                    ui.root.locate_view(f'完成 按钮').click()
                    ui.root.locate_view(f'分享 按钮').click()
                    ui.root.locate_view(f'微信 按钮')  # 一键分享到微信.click()
                    ui.root.locate_view(f'选择 搜索 文本框').input(client_name).click()
                    ui.root.locate_view(f'{client_name} 按钮').click()
                    ui.root.locate_view(f'分享 按钮').click()


class Scan_and_invite_candidate(RuyiTask):
    def __init__(self, job_requirements: str):
        super().__init__()
        self.description = """
            为 HR 部门自动扫描各大招聘平台（如LinkedIn、Indeed等）上投递的简历，将资料信息存储到数据库中，基于职位要求初步筛选一些符合条件的候选人，并自动发送面试邀请。"""
        self.job_requirements = job_requirements

    def main(self, agent: RuyiAgent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm

        candidates = data.live_table()  # 创建一个动态表格，名为candidates，存储候选人信息
        device.start_app('LinkedIn')

        candidate_list = fm.vlm(ui.root.snapshot(), f'列出当前页面所有候选人', returns=[('所有候选人', list)])
        for candidate_name in candidate_list:
            # device.start_app('LinkedIn')
            ui.root.locate_view(f"{candidate_name} 按钮").click()

            candidate_position, candidate_experience, candidate_skills, candidate_email, pass_or_fail = fm.vlm(
                ui.root.snapshot(),
                '请你帮我分析候选人的信息，并且分析ta是否符合对应职业需求\n职业需求：' + self.job_requirements,
                returns=['候选人当前职位', '总结候选人工作经历', '候选人技能', '候选人邮箱', ('候选人是否符合', bool)])
            # ui.assert('界面正在显示候选人简历，无遮挡')   # 确保当前是简历浏览页面
            # candidate_position = ui.root.locate_view('候选人当前职位').content()
            # candidate_experience = ui.root.locate_view('总结候选人工作经历').content()
            # candidate_skills = ui.root.locate_view('总结候选人技能').content()
            # candidate_email = ui.root.locate_view('候选人邮箱').content()

            # 基于职位要求筛选候选人
            if pass_or_fail:
                # 保存候选人信息
                candidates.add_row({
                    '姓名': candidate_name,
                    '职位': candidate_position,
                    '工作经历': candidate_experience,
                    '技能': candidate_skills,
                    '邮箱': candidate_email
                })

                # 自动发送面试邀请
                device.start_app('Email')  # 打开邮箱
                ui.root.locate_view(f'创建 按钮').click()
                ui.root.locate_view(f'选择 收件人 右边 文本框').input(candidate_email)
                ui.root.locate_view(f'选择 主题 右边 文本框').input("面试邀请")
                email_content = f'诚邀您参加敝公司的面试招聘，薪酬可谈，100w起'
                ui.root.locate_view(f'选择 输入内容 文本框').input(email_content).click()
                ui.root.locate_view(f'发送 按钮').click()
                # ui.assert(f'确认发送成功')
