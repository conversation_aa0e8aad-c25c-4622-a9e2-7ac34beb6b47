from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask
import time

class Financial_Transactions_Analyzer(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            对所有的收支进行分析，按照不同的应用（如支付宝、微信）进行分类，并进行汇总。"""

    def iterate_and_summary(self, agent: RuyiAgent, iterate_desc: str, item_desc: str, summary_desc: str,
                            limit: int = 10) -> str:
        device, ui, data, fm, tool = agent.device, agent.ui, agent.data, agent.fm, agent.tools
        answer = ""
        for v in ui.root.iterate_views(iterate_desc, limit=limit):
            answer += fm.vlm(v.image(), item_desc, returns=['文本返回'])
        return fm.llm(answer, summary_desc, returns=['文本返回'])

    def main(self, agent: RuyiAgent):
        device, ui, data, fm, tool = agent.device, agent.ui, agent.data, agent.fm, agent.tools

        # 创建一个动态表格，汇总收支的统计信息
        income_expenses = data.live_table()

        # 总结微信的收支
        device.start_app('微信')
        ui.root.locate_view('我').click()
        ui.root.locate_view('服务').click()
        ui.root.locate_view('钱包').click()
        ui.root.locate_view('账单 界面右上角').click()
        time.sleep(2.5) # 这个界面打开很慢，需要等等
        ui.root.locate_view('统计 界面右上角').click()

        ui.root.locate_view('收入 界面右上角').click()
        income_wx = fm.vlm(device.take_screenshot(), '请总结用户的收入', returns=[('收入', str)])

        ui.root.locate_view('支出 界面右上角').click()
        expenses_wx = fm.vlm(device.take_screenshot(), '请总结用户的支出', returns=[('支出', str)])

        income_expenses.add_row({'app': '微信', '收入统计': income_wx, '支出描述': expenses_wx})

        ui.back_to('当前界面的左下角有电话app的图标')

        device.start_app('支付宝')
        ui.root.locate_view('我的').click()
        ui.root.locate_view('账单').click()
        ui.root.locate_view('收支分析').click()
        time.sleep(2.5) # 这个界面打开很慢，需要等等
        expenses_zfb = fm.vlm(device.take_screenshot(), '请总结用户的支出', returns=[('支出', str)])

        ui.root.locate_view('收入').click()
        income_zfb = fm.vlm(device.take_screenshot(), '请总结用户的收入', returns=[('收入', str)])
        income_expenses.add_row({'app': '支付宝', '收入统计': income_zfb, '支出描述': expenses_zfb})

        ui.back_to('当前界面的左下角有电话app的图标')

class Currency_fund_management(RuyiTask):

    def __init__(self):
        super().__init__()
        self.description = """
            记录理财通货币基金近七天的收益率"""

    def main(self, agent: RuyiAgent):
        device, ui, data, fm, tool = agent.device, agent.ui, agent.data, agent.fm, agent.tools
        # 微信理财通
        device.start_app('微信')  # 打开微信
        ui.root.locate_view('界面右下角我的按钮').click()
        ui.root.locate_view('服务按钮').click()
        ui.root.locate_view('理财通按钮').click()

        # 先查看在售的货币基金
        currency_fund_on_sale = data.live_table()  # 统计在售的货币基金
        ui.root.locate_view('界面左下角今日按钮').click()
        ui.root.locate_view('活期+按钮').click()
        ui.root.locate_view('转入按钮').click()
        ui.root.locate_view('查看转入明细按钮').click()

        for v in ui.root.iterate_views('界面下方展示的货币基金', limit=50):  # 遍历每一个在售基金
            name = v.locate_view('左上角的货币基金名称').content()
            v.locate_view('详情按钮').click()
            v.locate_view('7日年化按钮').click()
            date = tool.time_now().strftime('%Y-%m-%d')

            rate_7days = []
            for i in range(7):  # 遍历获取近7天的收益率
                date = date - i
                rate = float(ui.root.locate_view('图表中日期<{rate}>对应收益率').content())
                rate_7days.append(rate)

            currency_fund_on_sale.add_row({'name': name, 'rate': rate_7days, 'rate_average': sum(rate_7days) / 7})
            ui.back()
            # 此处自动跳回"转入"界面了，需要再点击"查看转入明细"
            ui.root.locate_view('查看转入明细按钮').click()

        ui.back()
        ui.back()
        # 
        # 
        # currency_fund_on_sale.sort('rate_average', 'DESC')  # 按照降序排序现有的基金，便于后面比较时，从最高的开始比较
        # 
        # # 再检查用户买过的货币基金
        # ui.root.locate_view('界面右下角我的按钮').click()  # 查看用户当前的基金
        # ui.root.locate_view('活期资产按钮').click()
        # ui.root.locate_view('账户金额下面的金额展示按钮').click()
        # for v in ui.root.iterate_views('账户金额下面的每个货币基金'):  # 循环查看每一个基金
        #     name = v.locate_view('左上角的货币基金名称').content()
        #     v.locate_view('右侧的进入按钮').click()
        #     assets_held = float(ui.root.locate_view('持有资产(元)下面的资产数字').content())
        # 
        #     date = device.get_date("y-m-d")  # 获取当前日期的 API
        #     ui.root.locate_view('近7日年化按钮').click()
        #     rate_7days = []
        #     for i in range(7):  # 遍历获取近7天的收益率
        #         date = date - i
        #         rate = float(ui.root.locate_view('图表中日期<{rate}>对应收益率').content())
        #         rate_7days.append(rate)
        # 
        #     # 遍历在售的货币基金，检查是否比当前买的基金收益好
        #     for currency_fund in currency_fund_on_sale:
        #         # 先检查是否是相同的货币基金
        #         if currency_fund.name == name:
        #             continue
        # 
        #         # 再检查是否比当前买的货币基金收益好
        #         higher = True
        #         for i in range(7):
        #             if currency_fund[i] < rate_7days[i]:
        #                 higher = False
        #                 break
        # 
        #         # 如果收益更好，就转移
        #         if higher:
        #             if device.confirm('您购买的货币基金 {name} 近 7 日的收益率均小于 {currency_fund.name}，是否为您将现有基金转移至更高收益基金？'):
        #                 ui.root.scroll_up()
        #                 ui.locate_view('更换产品按钮').click()
        #                 ui.locate_view('更换金额文本框').input(assets_held)
        # 
        #                 # 找到这个在售的货币基金，并选择它
        #                 if ui.root.locate_view('{currency_fund.name} 货币基金'):  # 不用展开所有就能找到该基金
        #                     ui.root.locate_view('{currency_fund.name} 货币基金选择按钮').click()
        #                 else:  # 需要展开所有才能找到该基金
        #                     ui.root.locate_view('查看所有').click()
        #                     for v in ui.iterate_views('更换至下面的所有货币基金'):
        #                         v_name = v.locate_view('上侧的货币基金名称').content()
        #                         if v_name == currency_fund.name:
        #                             ui.root.locate_view('{currency_fund.name} 货币基金选择按钮').click()
        #                             break
        # 
        #                 ui.root.locate_view('最下面的下一步按钮').click()
        #                 ui.root.locate_view('已悉知风险提示并同意服务协议按钮').click()
        #                 ui.root.locate_view('最下面的确认按钮').click()
        # 
        #                 # 提示用户输入密码，并等待用户输入完成
        #                 device.notification('请输入您的支付密码，以完成货币基金的转移')
        #                 transfer_seccess_view = ui.wait_view(f'成功转投界面', timeout=1*60)
        #                 if transfer_seccess_view:  # 用户顺利输入完成了密码
        #                     transfer_seccess_view.locate_view('查看资产按钮').click()
        #                     ui.root.locate_view('账户金额下面的金额展示按钮').click()
        #                     # TODO: 这块因为转移完自动退出了，这里重新回到已购基金的展示界面。如果买的货币种类比较多，界面发生了滑动，这里是否会对最外层的 for 循环产生影响?
        #                 else:  # 用户在 60s 内没有输入密码，返回，并检查下一个用户已买的货币基金
        #                     for i in range(5):
        #                         ui.back()
