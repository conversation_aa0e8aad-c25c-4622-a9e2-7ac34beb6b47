from ruyi.task import RuyiTask


class Create_contact_and_scroll(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            创建一个名为Ruyi，email为*******************的联系人。"""

    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('微信')
        ui.root.scroll(direction='down')
        ui.root.scroll(direction='up')
        # ui.root.scroll(direction='left')
        # ui.root.scroll(direction='right')
