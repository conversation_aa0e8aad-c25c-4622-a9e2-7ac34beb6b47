import base64
import logging
import queue
import threading
from copy import deepcopy
from datetime import datetime
from typing import TypedDict, cast, MutableMapping, Any
import io

import requests
import structlog
from PIL import Image

from ruyi.config import RuyiConfig
from .processors import DEFAULT_LOG_PROCESSORS, CUSTOM_LOG_PROCESSORS

logger = structlog.get_logger(__name__)


_console_renderer_for_exceptions = structlog.dev.ConsoleRenderer()


def _custom_raw_renderer(_, __, event_dict: structlog.types.EventDict) -> str:
    """
    A custom renderer that formats the event message and a rich traceback into a
    single string, without any other logging metadata.
    """
    message = event_dict.get("event", "")
    exc_info = event_dict.get("exc_info")

    if not exc_info:
        return message

    tb_str = ""
    try:
        from rich.console import Console
        from rich.traceback import Traceback
        from io import StringIO

        # Render the rich traceback into a string
        string_io = StringIO()
        console = Console(file=string_io, force_terminal=True)
        console.print(Traceback(show_locals=False))
        tb_str = string_io.getvalue()
    except ImportError:
        import traceback
        tb_str = "".join(traceback.format_exception(*exc_info))

    return f"{message}\n{tb_str}"


def simple_console_renderer(_, __, event_dict):
    """Simple renderer that only outputs the message content"""
    if event_dict.pop("raw_output", False):
        return _custom_raw_renderer(_, __, event_dict)

    # if event_dict.get("exc_info"):
    #     return _console_renderer_for_exceptions(_, __, event_dict)
    return event_dict.get("event", "")


class LogData(TypedDict):
    timestamp: str
    level: str
    message: str
    metadata: MutableMapping[str, Any]
    has_image: bool
    image_data: str | None


class RuyiLogManager:
    def __init__(self, config: RuyiConfig) -> None:
        self._server_url = cast(str, config.log_server_url) # Temporary default
        self._version = cast(str, config.version)
        self._opened = False
        self._level = cast(int, config.log_level)
        manual_enabled_signal = config.log_server_enable

        self._queue = cast(queue.Queue[LogData], None)
        self._work_thread = cast(threading.Thread, None)

        # Check if logging is enabled
        self._enabled = manual_enabled_signal and self._check_server_health()

    def _check_server_health(self) -> bool:
        """Check if the log server is healthy"""
        try:
            response = requests.get(f"{self._server_url}/api/v2/health")
            if response.status_code == 200:
                logger.info(
                    "Log server is healthy",
                    action="check server health",
                    status="success",
                )
                return True
        except Exception as e:
            logger.warning(
                "Log server is not available",
                action="check server health",
                status="error",
                error=str(e),
            )
        return False

    def start(self):
        if self._enabled:
            self._queue = queue.Queue()
            self._work_thread = threading.Thread(target=self._worker)
            self._work_thread.start()
            self._opened = True
        else:
            # logger.warning(
            #     "Log client is not configured or enabled, logs will not be sent to server but only printed to console",
            #     action="start client",
            #     status="skip",
            # )
            pass
        self._reset_logging_settings(level=self._level)

    def _worker(self):
        while True:
            try:
                data: LogData = self._queue.get()
                if data is None:
                    break

                # Add version to metadata
                if "metadata" not in data:
                    data["metadata"] = {}
                data["metadata"]["version"] = self._version

                # Send log to server
                response = requests.post(
                    f"{self._server_url}/api/v2/logs",
                    json=data,
                    headers={"Content-Type": "application/json"},
                )
                response.raise_for_status()
                self._queue.task_done()
            except Exception as e:
                logger.error("Error sending log to server", error=str(e))

    def _queue_processor(self, _, __, event_dict: structlog.types.EventDict):
        """Process structlog events and handle image data"""
        dict_copy = deepcopy(event_dict)

        # Extract and process image if present
        image = dict_copy.pop("image", None)
        has_image = False
        image_data = None

        # 检查是否是任何类型的 PIL 图像（包括 PngImageFile, JpegImageFile 等）
        if image and (isinstance(image, Image.Image) or (hasattr(image, '__module__') and 
           image.__module__ and 'PIL' in image.__module__ and hasattr(image, 'save'))):
            has_image = True
            # Convert PIL Image to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format=image.format or "PNG")
            image_data = base64.b64encode(img_byte_arr.getvalue()).decode("utf-8")

        # Prepare log data
        log_data: LogData = {
            "timestamp": dict_copy.pop("timestamp", datetime.now().isoformat()),
            "level": dict_copy.pop("level", "info"),
            "message": dict_copy.pop("event", ""),
            "metadata": dict_copy,
            "has_image": has_image,
            "image_data": image_data,
        }

        if log_data["level"] != "info":
            self._queue.put(log_data)
        return event_dict

    def _reset_logging_settings(
        self, target_module: str = "ruyi", level: int = logging.DEBUG
    ):
        if self._opened:
            formatter = structlog.stdlib.ProcessorFormatter(
                processors=[
                    *DEFAULT_LOG_PROCESSORS,
                    self._queue_processor,
                    *CUSTOM_LOG_PROCESSORS,
                    # structlog.dev.ConsoleRenderer(),
                    simple_console_renderer if self._level > 10 else structlog.dev.ConsoleRenderer(),
                ],
            )
        else:
            formatter = structlog.stdlib.ProcessorFormatter(
                processors=[
                    *DEFAULT_LOG_PROCESSORS,
                    *CUSTOM_LOG_PROCESSORS,
                    # structlog.dev.ConsoleRenderer(),
                    simple_console_renderer if self._level > 10 else structlog.dev.ConsoleRenderer(),
                ],
            )
        structlog.configure(
            logger_factory=structlog.stdlib.LoggerFactory(),
            cache_logger_on_first_use=True,
            processors=[structlog.stdlib.ProcessorFormatter.wrap_for_formatter],
        )
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logging.basicConfig(handlers=[console_handler], level=logging.WARNING)
        logging.getLogger(target_module).setLevel(level)

    def stop(self):
        self._opened = False
        if self._enabled:
            self._queue.put(None)  # type: ignore
            self._work_thread.join()
        self._reset_logging_settings()
