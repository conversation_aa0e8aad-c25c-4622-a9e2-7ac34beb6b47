import structlog
from datetime import datetime
from zoneinfo import ZoneInfo

def timestamp_processor(_, __, event_dict: structlog.typing.EventDict):
    localized_time = datetime.now(ZoneInfo("Asia/Shanghai")).strftime(
        "%Y-%m-%d %H:%M:%S"
    )
    event_dict["timestamp"] = localized_time
    return event_dict


def remove_meta_in_console_renderer(_, __, event_dict: structlog.typing.EventDict):
    for key in ["filename", "func_name", "lineno", "task_id", "image"]:
        if key in event_dict:
            del event_dict[key]
    if "metadata" in event_dict:
        if "state" in event_dict["metadata"]:
            del event_dict["metadata"]["state"]
    return event_dict


DEFAULT_LOG_PROCESSORS = [
    structlog.contextvars.merge_contextvars,
    structlog.stdlib.filter_by_level,
    structlog.stdlib.add_log_level,
    structlog.processors.StackInfoRenderer(),
    structlog.dev.set_exc_info,
    structlog.stdlib.add_logger_name,
    structlog.processors.CallsiteParameterAdder(
        parameters=[
            structlog.processors.CallsiteParameter.FILENAME,
            structlog.processors.CallsiteParameter.FUNC_NAME,
            structlog.processors.CallsiteParameter.LINENO,
        ]
    ),
    structlog.stdlib.ProcessorFormatter.remove_processors_meta,
]

CUSTOM_LOG_PROCESSORS = [
    timestamp_processor,
    remove_meta_in_console_renderer,
]
