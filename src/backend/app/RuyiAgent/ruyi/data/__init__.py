"""
The interfaces to use common data structures and common data resources (photos, sensors, etc.) on the device.
Can potentially reuse many from the chainstream project.
"""
from .common import LiveTable, Image, Text
from ..utils.interface import RuyiInterface

import os
from markitdown import MarkItDown
import tiktoken

class Data_Interface(RuyiInterface):
    """
    Interface for handling common data structures and resources on the device.

    This class provides methods to create and manage various data types such as
    LiveTable, Image, and Text. It inherits from RuyiInterface and is designed
    to be used within the RuyiAgent ecosystem.

    Attributes:
        _tag (str): A string identifier for the interface, set to 'data'.

    """

    def __init__(self, agent):
        """
        Initialize the Data_Interface.

        Args:
            agent: The RuyiAgent instance this interface is associated with.
        """
        super().__init__(agent)
        self._tag = 'data'
        self.encoding = tiktoken.encoding_for_model("gpt-4o")

    def live_table(self, *args, **kwargs):
        """
        Create and return a LiveTable instance.

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            LiveTable: A new LiveTable instance associated with the agent.
        """
        return LiveTable(self.agent, *args, **kwargs)

    def image(self, *args, **kwargs):
        """
        Create and return an Image instance.

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            Image: A new Image instance associated with the agent.
        """
        return Image(self.agent, *args, **kwargs)

    def text(self, *args, **kwargs):
        """
        Create and return a Text instance.

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            Text: A new Text instance associated with the agent.
        """
        return Text(self.agent, *args, **kwargs)

    def read_document(self, file_name: str) -> str:
        """
        Read a document and return the text content.
        """
        file_path = os.path.join(self.config.data_dir_path, file_name)

        md = MarkItDown(enable_plugins=False)
        result = md.convert(file_path)

        return result.text_content

    def retrieve_document(self, document: str, query: str, length=None) -> str:
        """
        Retrieve a document and return the text content.
        Automatically handles long documents by splitting them into chunks.
        """
        
        # 计算query的token长度
        query_tokens = len(self.encoding.encode(query))
        
        # 计算prompt模板的基础token长度（不包含document部分）
        base_prompt = """# Your job
You are a helpful assistant that can retrieve information from a document.
Now, you need to retrieve information from the document that meets the user's query.

# Document


# Query


# Output Instructions
Only reply with the information you retrieved from the document. If you cannot retrieve any information, reply with "No information found".
        """
        base_prompt_tokens = len(self.encoding.encode(base_prompt))
        
        # 计算document的token长度
        document_tokens = len(self.encoding.encode(document))

        # 获取最大context window，为输出和安全边界预留空间
        max_context_window = self.agent.config.fm_default_max_context_window
        # 为输出预留2000个token，为安全边界预留20%空间
        safe_limit = int(max_context_window * 0.8) - 2000
        
        # 计算单个片段可用的最大token数
        max_chunk_tokens = safe_limit - base_prompt_tokens - query_tokens
        
        # 如果文档不需要分片
        if document_tokens <= max_chunk_tokens:
            # 构建长度限制指令
            length_instruction = ""
            if length is not None:
                length_instruction = f" Keep your response within {length} characters."
            
            prompt = f"""# Your job
You are a helpful assistant that can retrieve information from a document.
Now, you need to retrieve information from the document that meets the user's query.

# Document
{document}

# Query
{query}

# Output Instructions
Only reply with the information you retrieved from the document. If you cannot retrieve any information, reply with "No information found".{length_instruction}
            """
            
            res = self.agent.fm.query(prompt, returns=[("Retrieved Information", str)])
            
            if res == "No information found":
                return ""
            
            return res
        
        # 需要分片处理
        print(f"文档过长 ({document_tokens} tokens), 正在将文档分割成多个片段...")
        
        # 将文档分割成合适大小的片段
        chunks = self._split_document_into_chunks(document, max_chunk_tokens)
        
        print(f"已将文档分割为 {len(chunks)} 个片段")
        
        # 对每个片段调用fm.query
        results = []
        for i, chunk in enumerate(chunks):
            print(f"处理第 {i+1} 个片段...")

            prompt = f"""# Your job
You are a helpful assistant that can retrieve information from a document.
Now, you need to retrieve information from the document that meets the user's query.

# Document
{chunk}

# Query
{query}

# Output Instructions
Only reply with the information you retrieved from the document. If you cannot retrieve any information, reply with "No information found".
            """
            
            res = self.agent.fm.query(prompt, returns=[("Retrieved Information", str)])
            
            if res and res != "No information found":
                results.append(res)
        
        # 合并结果
        if not results:
            return ""
        
        if len(results) == 1:
            return results[0]
        
        # 多个结果需要进一步合并和去重
        combined_text = "\n\n".join(results)
        
        # 如果合并后的结果仍然很长，直接返回
        if len(self.encoding.encode(combined_text)) > max_chunk_tokens:
            return combined_text
        
        # 使用LLM来合并和总结多个片段的结果
        # 构建长度限制指令
        length_instruction = ""
        if length is not None:
            length_instruction = f" Keep your response within {length} characters."
        
        merge_prompt = f"""# Your job
You are a helpful assistant that needs to merge and consolidate information from multiple document chunks.

# Query
{query}

# Information from multiple chunks
{combined_text}

# Instructions
Please consolidate the above information to provide a comprehensive answer to the query. 
Remove duplicates and organize the information logically. If the information is contradictory, mention that.
If no relevant information was found, reply with "No information found".{length_instruction}
        """
        
        final_result = self.agent.fm.query(merge_prompt, returns=[("Consolidated Information", str)])
        
        if final_result == "No information found":
            return ""
        
        return final_result

    def _split_document_into_chunks(self, document: str, max_tokens: int) -> list[str]:
        """
        Split document into chunks that fit within token limits.
        Tries to split at natural boundaries (paragraphs, sentences).
        """
        chunks = []
        
        # 首先尝试按段落分割
        paragraphs = document.split('\n\n')
        current_chunk = ""
        
        for paragraph in paragraphs:
            # 测试加入这个段落后是否超过限制
            test_chunk = current_chunk + ("\n\n" if current_chunk else "") + paragraph
            test_tokens = len(self.encoding.encode(test_chunk))
            
            if test_tokens <= max_tokens:
                current_chunk = test_chunk
            else:
                # 如果当前chunk不为空，保存它
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = ""
                
                # 检查单个段落是否太长
                paragraph_tokens = len(self.encoding.encode(paragraph))
                if paragraph_tokens <= max_tokens:
                    current_chunk = paragraph
                else:
                    # 段落太长，需要按句子分割
                    sentence_chunks = self._split_paragraph_into_sentences(paragraph, max_tokens)
                    chunks.extend(sentence_chunks[:-1])  # 添加所有完整的句子块
                    current_chunk = sentence_chunks[-1] if sentence_chunks else ""
        
        # 添加最后一个chunk
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks

    def _split_paragraph_into_sentences(self, paragraph: str, max_tokens: int) -> list[str]:
        """
        Split a paragraph into sentence-based chunks.
        """
        import re
        
        # 简单的句子分割（可以根据需要改进）
        sentences = re.split(r'[.!?。！？]\s+', paragraph)
        
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if not sentence.strip():
                continue
                
            test_chunk = current_chunk + (" " if current_chunk else "") + sentence
            test_tokens = len(self.encoding.encode(test_chunk))
            
            if test_tokens <= max_tokens:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = sentence
                else:
                    # 单个句子太长，强制分割
                    word_chunks = self._split_by_words(sentence, max_tokens)
                    chunks.extend(word_chunks[:-1])
                    current_chunk = word_chunks[-1] if word_chunks else ""
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks

    def _split_by_words(self, text: str, max_tokens: int) -> list[str]:
        """
        Split text by words when sentence-level splitting is not enough.
        """
        words = text.split()
        chunks = []
        current_chunk = ""
        
        for word in words:
            test_chunk = current_chunk + (" " if current_chunk else "") + word
            test_tokens = len(self.encoding.encode(test_chunk))
            
            if test_tokens <= max_tokens:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = word
                else:
                    # 单个词太长，强制截断
                    chunks.append(word[:max_tokens//2])  # 简单截断
                    current_chunk = ""
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks