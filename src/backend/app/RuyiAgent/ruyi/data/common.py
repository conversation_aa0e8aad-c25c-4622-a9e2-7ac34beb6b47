# Common data structures, eg. Table, Text, Image
from ..utils.interface import RuyiInterface
from ..agent import RuyiAgent
import pandas as pd
from typing import Any, overload, Callable
from PIL.ImageFile import ImageFile 
from PIL import Image as PILImage
from rich.console import Console
from rich.table import Table
import numpy as np
import re
import io
import os
import base64

class LiveTable(RuyiInterface):
    def __init__(self, agent: RuyiAgent | None = None, name: str | None = None):
        super().__init__(agent)
        self._name = name
        self._data: list[dict[str, Any]] = []
        self._columns: list[str] = []

        self.save_to_file()
    
    def _add_column(self, column_name: str):
        if column_name in self._columns:
            return
        self._columns.append(column_name)
        for row in self._data:
            row[column_name] = None

    def _validate(self):
        existing_columns = set(self._columns)
        for row in self._data:
            row_columns = set(row.keys())
            missing_columns = existing_columns - row_columns
            new_columns = row_columns - existing_columns

            for column in missing_columns:
                row[column] = None

            if new_columns:
                self._columns.extend(new_columns)
                existing_columns.update(new_columns)
                for r in self._data:
                    for column in new_columns:
                        if column not in r:
                            r[column] = None
            
    def add_row(self, row_dict: dict[str, Any]):
        """
        Adds a row to the data structure.

        If the columns are not yet defined, they will be initialized with the keys of the provided row_dict.
        If the columns are already defined, it checks for any missing columns in the row_dict and raises a ValueError if any are found.
        It also adds any new columns found in the row_dict to the existing columns.

        Args:
            row_dict (dict[str, Any]): A dictionary representing a row, where keys are column names and values are the corresponding data.

        Raises:
            ValueError: If row_dict is missing any columns that are already defined in the data structure.
        """
        if not self._columns:
            self._columns = list(row_dict.keys())
        else:
            missing_columns = set(self._columns) - set(row_dict.keys())
            if missing_columns:
                raise ValueError(f'row_dict is missing columns: {missing_columns}')
            add_columns = set(row_dict.keys()) - set(self._columns)
            for column in add_columns:
                self._add_column(column)
        self._data.append(row_dict)

        self.save_to_file()

    def __len__(self):
        return len(self._data)
    
    @overload
    def __getitem__(self, key: int)-> dict[str, Any]: ...

    @overload
    def __getitem__(self, key: tuple[int, str])-> Any: ...

    def __getitem__(self, key: int | tuple[int, str]):
        """
        Retrieve an item from the data structure.

        Parameters:
        key (int | tuple[int, str]): The key to access the data. It can be either:
            - An integer to access the data by index.
            - A tuple containing an integer and a string to access a specific column in the data at the given index.

        Returns:
        The data at the specified index or the specific column in the data at the given index.

        Raises:
        TypeError: If the key is not an int or a tuple[int, str].
        """
        if isinstance(key, int):
            return self._data[key]
        elif isinstance(key, tuple):
            index, column = key
            return self._data[index][column]
        else:
            raise TypeError('key must be int or tuple[int, str]')
    
    def iterrows(self):
        """
        Iterate over DataFrame rows as (index, row) pairs.

        Yields:
            tuple: A tuple containing the index and the row data.
        """
        for index, row in enumerate(self._data):
            yield index, row
    
    def __contains__(self, key):
        """
        Check if the given key is in the _columns attribute.

        Args:
            key: The key to check for existence in the _columns attribute.

        Returns:
            bool: True if the key is in the _columns attribute, False otherwise.
        """
        return key in self._columns
    
    def to_dataframe(self):
        """
        Converts the internal data to a pandas DataFrame.

        Returns:
            pd.DataFrame: A DataFrame containing the data stored in the instance.
        """
        return pd.DataFrame(self._data, columns=self._columns)
    
    def to_list(self):
        """
        Converts the internal data to a list.

        Returns:
            list: The internal data as a list.
        """
        return self._data

    def update_row(self, index: int, row_dict: dict[str, Any]):
        """
        Update a row in the data at the specified index with the provided dictionary.

        Args:
            index (int): The index of the row to update.
            row_dict (dict[str, Any]): A dictionary containing the new row data. The keys should match the column names.

        Raises:
            IndexError: If the provided index is out of range.
            ValueError: If the provided dictionary is missing columns or contains extra columns not in the original data.
        """
        if index >= len(self._data):
            raise IndexError('Index out of range')
        missing_columns = set(self._columns) - set(row_dict.keys())
        if missing_columns:
            raise ValueError(f'row_dict is missing columns: {missing_columns}')
        add_columns = set(row_dict.keys()) - set(self._columns)
        for column in add_columns:
            self._add_column(column)
        self._data[index] = row_dict

        self.save_to_file()

    def delete_row(self, index: int):
        """
        Deletes a row from the data at the specified index.

        Args:
            index (int): The index of the row to delete.

        Raises:
            IndexError: If the index is out of range.
        """
        if index >= len(self._data):
            raise IndexError('Index out of range')
        del self._data[index]
        
        self.save_to_file()

    def rename_columns(self, column_mapping: dict[str, str]):
        """
        Renames columns in the dataset based on the provided mapping.

        Args:
            column_mapping (dict[str, str]): A dictionary where keys are the current column names 
                                             and values are the new column names.

        Raises:
            ValueError: If a new column name already exists in the dataset.

        Example:
            column_mapping = {'old_name1': 'new_name1', 'old_name2': 'new_name2'}
            rename_columns(column_mapping)
        """
        for old_name, new_name in column_mapping.items():
            if old_name not in self._columns:
                continue
            if new_name in self._columns:
                raise ValueError(f'Column {new_name} already exists')
            self._columns[self._columns.index(old_name)] = new_name
            for row in self._data:
                row[new_name] = row.pop(old_name)

        self.save_to_file()

    def clear(self):
        """
        Clears the data and columns of the object.

        This method resets the internal data and columns to empty lists.
        """
        self._data = []
        self._columns = []
        
        self.save_to_file()

    def __add__(self, other):
        """
        Add two LiveTable instances together.

        This method allows the use of the `+` operator to concatenate the data of two LiveTable instances.
        It ensures that the operand is of type LiveTable and that both tables have matching columns.

        Args:
            other (LiveTable): The other LiveTable instance to add.

        Returns:
            LiveTable: A new LiveTable instance with concatenated data from both tables.

        Raises:
            TypeError: If the operand is not a LiveTable instance.
            ValueError: If the columns of the two tables do not match.
        """
        if not isinstance(other, LiveTable):
            raise TypeError(f'unsupported operand type(s) for +: LiveTable and {type(other)}')
        if self._columns != other._columns:
            raise ValueError(f'Columns mismatch between {self._columns} and {other._columns}')
        new_table = LiveTable(name=self.name, agent=self.agent)
        new_table._columns = self._columns
        new_table._data = self._data + other._data
        return new_table

    @property
    def columns(self):
        """
        Returns the columns of the dataset.

        Returns:
            list: A list of column names.
        """
        return self._columns
    
    @property
    def name(self):
        """
        Returns the name attribute of the instance.

        Returns:
            str: The name attribute of the instance.
        """
        return self._name

    def to_dict(self):
        """
        Converts the object attributes to a dictionary.

        Returns:
            dict: A dictionary containing the object's name, columns, and data.
        """
        # 处理包含图像对象的数据
        serialized_data = []
        for row in self._data:
            serialized_row = {}
            for key, value in row.items():
                # 检查是否是任何类型的 PIL 图像（包括 PngImageFile, JpegImageFile 等）
                if isinstance(value, PILImage.Image) or (hasattr(value, '__module__') and 
                   value.__module__ and 'PIL' in value.__module__ and hasattr(value, 'save')):
                    # 如果是 PIL Image 类型，转换为 common.py 中的 Image 对象，然后序列化
                    image_obj = Image(self.agent, value)
                    serialized_row[key] = image_obj.to_dict()
                elif isinstance(value, Image):
                    # 如果是 common.py 中的 Image 对象，直接序列化
                    serialized_row[key] = value.to_dict()
                else:
                    # 其他类型的数据保持不变
                    serialized_row[key] = value
            serialized_data.append(serialized_row)
        
        return {'name': self._name, 'columns': self._columns, 'data': serialized_data}
    
    @classmethod
    def from_dict(cls, data: dict[str, Any], agent: RuyiAgent):
        """
        Create an instance of the class from a dictionary.

        Args:
            data (dict[str, Any]): A dictionary containing the data to initialize the instance.
            agent (RuyiAgent): An instance of RuyiAgent associated with the class.

        Returns:
            An instance of the class initialized with the provided data.
        """
        table = cls(name=data['name'], agent=agent)
        table._columns = data['columns']
        
        # 处理包含序列化图像对象的数据
        deserialized_data = []
        for row in data['data']:
            deserialized_row = {}
            for key, value in row.items():
                if isinstance(value, dict) and 'image_data' in value and 'metadata' in value:
                    # 如果是序列化的图像数据，重建为 Image 对象
                    # 从 base64 字符串中提取图像数据
                    image_data_str = value['image_data']
                    if image_data_str.startswith('data:image/png;base64,'):
                        base64_str = image_data_str.split(',', 1)[1]
                        image_bytes = base64.b64decode(base64_str)
                        pil_image = PILImage.open(io.BytesIO(image_bytes))
                        image_obj = Image(agent, pil_image, **value['metadata'])
                        deserialized_row[key] = image_obj
                    else:
                        deserialized_row[key] = value
                else:
                    # 其他类型的数据保持不变
                    deserialized_row[key] = value
            deserialized_data.append(deserialized_row)
        
        table._data = deserialized_data
        table._validate()
        return table

    def filter_rows(self, condition: Callable[[dict], bool]):
        """
        Filters the rows of the table based on a given condition.

        Args:
            condition (Callable[[dict], bool]): A function that takes a row (as a dictionary) 
                                                 and returns True if the row should be included 
                                                 in the filtered results, and False otherwise.

        Returns:
            LiveTable: A new LiveTable instance containing only the rows that satisfy the condition.
        """
        filtered_data = [row for row in self._data if condition(row)]
        new_table = LiveTable(name=self._name, agent=self.agent)
        new_table._columns = self._columns
        new_table._data = filtered_data
        return new_table

    def sort_rows(self, column_name: str, reverse: bool = False):
        """
        Sorts the rows of the data based on the specified column.

        Args:
            column_name (str): The name of the column to sort by.
            reverse (bool, optional): If True, sorts the data in descending order. Defaults to False.

        Raises:
            ValueError: If the specified column does not exist in the data.
        """
        if column_name not in self._columns:
            raise ValueError(f'Column {column_name} does not exist')
        self._data.sort(key=lambda row: row[column_name], reverse=reverse)
        
        self.save_to_file()

    def get_column(self, column_name: str):
        """
        Retrieve the values of a specified column from the data.

        Args:
            column_name (str): The name of the column to retrieve.

        Returns:
            list: A list of values from the specified column.

        Raises:
            ValueError: If the specified column does not exist in the data.
        """
        if column_name not in self._columns:
            raise ValueError(f'Column {column_name} does not exist')
        return [row[column_name] for row in self._data]

    def drop_column(self, column_name: str):
        """
        Removes a column from the dataset.

        Args:
            column_name (str): The name of the column to be removed.

        Raises:
            ValueError: If the specified column does not exist in the dataset.
        """
        if column_name not in self._columns:
            raise ValueError(f'Column {column_name} does not exist')
        self._columns.remove(column_name)
        for row in self._data:
            del row[column_name]
            
        self.save_to_file()

    def rich_console_display(self):
        console = Console()
        table = Table(title=self._name)
        for column in self._columns:
            table.add_column(column, justify="center", no_wrap=True)
        for row in self._data:
            table.add_row(*[str(row[column]) for column in self._columns])
        console.print(table)

    def load_from_file(self, file_name: str):
        """
        Load data from an excel or csv file.

        Args:
            file_name (str): The name of the file.
        """
        self._name = file_name
        file_path = os.path.join(self.config.data_dir_path, file_name)
        
        file_extension = os.path.splitext(file_name)[1].lower()

        if file_extension in ['.xls', '.xlsx']:
            df = pd.read_excel(file_path)
        elif file_extension == '.csv':
            df = pd.read_csv(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}. Please use .xls, .xlsx, or .csv")
            
        self._data = df.to_dict(orient='records')
        self._columns = list(df.columns)

    def save_to_file(self, file_name: str = ""):
        """
        Save data to an excel or csv file.

        Args:
            file_name (str): The name of the file.
        """
        if file_name == "":
            file_name = f'{self.name}.csv'
        
        file_path = os.path.join(self.config.data_dir_path, file_name)
        df = pd.DataFrame(self._data, columns=self._columns)
        
        file_extension = os.path.splitext(file_name)[1].lower()

        if file_extension in ['.xls', '.xlsx']:
            df.to_excel(file_path, index=False)
        elif file_extension == '.csv':
            df.to_csv(file_path, index=False)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}. Please use .xls, .xlsx, or .csv")


class Image(RuyiInterface):
    def __init__(self, agent, img: PILImage.Image, **metadata):
        super().__init__(agent)
        self._img = img
        self._metadata = metadata

    def to_dict(self):
        buffered = io.BytesIO()
        self._img.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
        return {
            'metadata': self._metadata,
            'image_data': f"data:image/png;base64,{img_str}"
        }

    @property
    def size(self):
        """
        Returns the size of the image.

        Returns:
            tuple: A tuple representing the width and height of the image.
        """
        return self._img.size
    
    @property
    def mode(self):
        """
        Returns the mode of the image.

        The mode defines the number and names of the bands in the image, 
        and also the pixel type and depth. Common modes include "L" 
        (luminance), "RGB" (true color), and "CMYK" (pre-press).

        Returns:
            str: The mode of the image.
        """
        return self._img.mode
    
    @property
    def metadata(self):
        """
        Retrieve the metadata associated with this instance.

        Returns:
            dict: The metadata dictionary.
        """
        return self._metadata
    
    def update_metadata(self, metadata: dict[str, Any]):
        """
        Updates the metadata dictionary with the provided key-value pairs.

        Args:
            metadata (dict[str, Any]): A dictionary containing the metadata to update.
        """
        self._metadata.update(metadata)
    
    def show(self):
        """
        Displays the image stored in the _img attribute using the default image viewer.
        """
        self._img.show()
    
    def save(self, path: str, format: str):
        """
        Save the image to the specified path with the given format.

        Args:
            path (str): The file path where the image will be saved.
            format (str): The format in which the image will be saved (e.g., 'JPEG', 'PNG').

        Returns:
            None
        """
        self._img.save(path, format=format)
    
    @classmethod
    def from_path(cls, agent, path: str):
        """
        Create an instance of the class from a given file path.

        Args:
            cls: The class itself.
            agent: An instance of the agent.
            path (str): The file path to the image.

        Returns:
            An instance of the class initialized with the agent and the image.
        """
        img = ImageFile(path)
        return cls(agent, img)
    
    def to_numpy(self):
        """
        Converts the internal image representation to a NumPy array.

        Returns:
            numpy.ndarray: The image represented as a NumPy array.
        """
        return np.array(self._img)
    
    def rotate(self, angle: float):
        """
        Rotates the image by the specified angle.

        Args:
            angle (float): The angle to rotate the image, in degrees. Positive values 
                           will rotate the image counter-clockwise, and negative values 
                           will rotate it clockwise.

        Returns:
            None
        """
        self._img = self._img.rotate(angle)
    
    def crop(self, box: tuple[int, int, int, int]):
        """
        Crops the image to the specified bounding box.

        Args:
            box (tuple[int, int, int, int]): A tuple of four integers specifying 
                                             the left, upper, right, and lower pixel 
                                             coordinate.

        Returns:
            None
        """
        self._img = self._img.crop(box)
    
    def resize(self, size: tuple[int, int]):
        """
        Resize the image to the specified dimensions.

        Args:
            size (tuple[int, int]): A tuple containing the new width and height of the image.

        Returns:
            None
        """
        self._img = self._img.resize(size)
    
    def convert(self, mode: str):
        """
        Converts the image to the specified mode.

        Args:
            mode (str): The mode to convert the image to. For example, 'L' for grayscale, 'RGB' for true color, etc.

        Returns:
            None
        """
        self._img = self._img.convert(mode)
    
    @classmethod
    def from_numpy(cls, agent: RuyiAgent, array: np.ndarray):
        """
        Create an instance of the class from a NumPy array.

        Args:
            agent (RuyiAgent): The agent associated with the instance.
            array (np.ndarray): The NumPy array to convert into an image.

        Returns:
            An instance of the class initialized with the given agent and image.
        """
        img = PILImage.fromarray(array)
        return cls(agent, img)


class Text(RuyiInterface):
    def __init__(self, agent, text: str):
        super().__init__(agent)
        self._text = text
    
    def __str__(self) -> str:
        """
        Returns the string representation of the object.

        Returns:
            str: The string representation of the object.
        """
        return self._text
    
    def __repr__(self) -> str:
        """
        Return a string representation of the Text object.

        Returns:
            str: A string that represents the Text object, including the agent and text attributes.
        """
        return f'Text(agent={self.agent}, text={self._text})'
    
    def __bool__(self):
        """
        Returns the boolean value of the instance.

        This method is called to evaluate the truth value of the instance.
        It returns True if the instance's `_text` attribute is non-empty,
        otherwise it returns False.

        Returns:
            bool: True if `_text` is non-empty, False otherwise.
        """
        return bool(self._text)
    
    def upper(self, **kwargs):
        """
        Convert the text to uppercase.

        Args:
            **kwargs: Additional keyword arguments to pass to the `upper` method.

        Returns:
            str: The text converted to uppercase.
        """
        return self._text.upper(**kwargs)
    
    def lower(self, **kwargs):
        """
        Convert the text to lowercase.

        Parameters:
        **kwargs: Additional keyword arguments to pass to the lower() method.

        Returns:
        str: The text converted to lowercase.
        """
        return self._text.lower(**kwargs)

    def title(self, **kwargs):
        """
        Convert the text to title case.

        This method capitalizes the first letter of each word in the text.

        Args:
            **kwargs: Additional keyword arguments to pass to the title method.

        Returns:
            str: The text converted to title case.
        """
        return self._text.title(**kwargs)
    
    def capitalize(self, **kwargs):
        """
        Capitalizes the first character of the text.

        Parameters:
        **kwargs: Additional keyword arguments to pass to the capitalize method.

        Returns:
        str: The capitalized text.
        """
        return self._text.capitalize(**kwargs)
    
    def swapcase(self, **kwargs):
        """
        Convert uppercase characters to lowercase and lowercase characters to uppercase in the text.

        Args:
            **kwargs: Additional keyword arguments to pass to the underlying string method.

        Returns:
            str: The text with uppercase characters converted to lowercase and vice versa.
        """
        return self._text.swapcase(**kwargs)
    
    def count(self, sub: str, **kwargs):
        """
        Count the occurrences of a substring in the text.

        Args:
            sub (str): The substring to be counted.
            **kwargs: Additional keyword arguments to be passed to the count method.

        Returns:
            int: The number of non-overlapping occurrences of the substring in the text.
        """
        return self._text.count(sub, **kwargs)

    def find(self, sub: str, **kwargs):
        """
        Find the first occurrence of a substring in the text.

        Args:
            sub (str): The substring to search for.
            **kwargs: Additional keyword arguments to pass to the underlying string `find` method.

        Returns:
            int: The lowest index in the text where substring `sub` is found. Returns -1 if the substring is not found.
        """
        return self._text.find(sub, **kwargs)
    
    def __len__(self):
        """
        Returns the length of the text.

        Returns:
            int: The number of characters in the text.
        """
        return len(self._text)
    
    def __contains__(self, sub: str):
        """
        Check if a substring is present in the text.

        Args:
            sub (str): The substring to check for.

        Returns:
            bool: True if the substring is found in the text, False otherwise.
        """
        return sub in self._text
    
    def __iter__(self):
        """
        Returns an iterator over the elements in the '_text' attribute.

        Yields:
            Iterator: An iterator over the elements in the '_text' attribute.
        """
        return iter(self._text)
    
    def __getitem__(self, key: int):
        """
        Retrieve the item at the specified index from the _text attribute.

        Args:
            key (int): The index of the item to retrieve.

        Returns:
            The item at the specified index.
        """
        return self._text[key]
    
    def __add__(self, other):
        """
        Concatenates the current Text object with another Text object or a string.

        Args:
            other (Text or str): The other object to concatenate with. It can be an instance of Text or a string.

        Returns:
            Text: A new Text object with the concatenated result.

        Raises:
            TypeError: If the other object is not an instance of Text or a string.
        """
        if isinstance(other, Text):
            return Text(self.agent, self._text + other._text)
        elif isinstance(other, str):
            return Text(self.agent, self._text + other)
        else:
            raise TypeError(f'unsupported operand type(s) for +: Text and {type(other)}')
    
    def __sub__(self, other):
        """
        Subtracts a Text object or a string from the current Text object.

        Args:
            other (Text or str): The Text object or string to be subtracted.

        Returns:
            Text: A new Text object with the specified text removed.

        Raises:
            TypeError: If the operand is not a Text object or a string.
        """
        if isinstance(other, Text):
            return Text(self.agent, self._text.replace(other._text, ''))
        elif isinstance(other, str):
            return Text(self.agent, self._text.replace(other, ''))
        else:
            raise TypeError(f'unsupported operand type(s) for -: Text and {type(other)}')
    
    def __mul__(self, other):
        """
        Multiply the Text object by an integer.

        Parameters:
        other (int): The integer to multiply the Text object by.

        Returns:
        Text: A new Text object with the text repeated 'other' times.

        Raises:
        TypeError: If 'other' is not an integer.
        """
        if isinstance(other, int):
            return Text(self.agent, self._text * other)
        else:
            raise TypeError(f'unsupported operand type(s) for *: Text and {type(other)}')
    
    def __eq__(self, value: object) -> bool:
        """
        Check if this Text object is equal to another object.

        Args:
            value (object): The object to compare with this Text object. It can be another Text object or a string.

        Returns:
            bool: True if the objects are equal, False otherwise.
        """
        if isinstance(value, Text):
            return self._text == value._text
        elif isinstance(value, str):
            return self._text == value
        else:
            return False
    
    def split(self, sep: str | None = None, regex: bool = False, **kwargs):
        """
        Splits the text into a list of Text objects based on the specified separator.

        Args:
            sep (str | None): The separator to use for splitting the text. If None, splits on any whitespace.
            regex (bool): If True, treats the separator as a regular expression. Defaults to False.
            **kwargs: Additional keyword arguments to pass to the split function.

        Returns:
            list[Text]: A list of Text objects resulting from the split operation.

        Raises:
            ValueError: If regex is True and sep is None.
        """
        if regex:
            if sep is None:
                raise ValueError("split() requires a separator when regex is True")
            return [Text(self.agent, s) for s in re.split(sep, self._text, **kwargs)]
        else:
            return [Text(self.agent, s) for s in self._text.split(sep, **kwargs)]
    
    def strip(self, chars: str | None = None):
        """
        Removes leading and trailing characters from the text.

        Args:
            chars (str | None): A string specifying the set of characters to be removed. 
                                If None, whitespace characters are removed.

        Returns:
            Text: A new Text object with the specified characters removed from both ends.
        """
        return Text(self.agent, self._text.strip(chars))

    def replace(self, old: str, new: str, regex: bool = False, **kwargs):
        """
        Replace occurrences of a substring within the text.

        Args:
            old (str): The substring to be replaced.
            new (str): The substring to replace with.
            regex (bool, optional): If True, treat `old` as a regular expression. Defaults to False.
            **kwargs: Additional keyword arguments to pass to `re.sub` or `str.replace`.

        Returns:
            Text: A new Text object with the replaced content.
        """
        if regex:
            return Text(self.agent, re.sub(old, new, self._text, **kwargs))
        return Text(self.agent, self._text.replace(old, new, **kwargs))
    
    def join(self, iterable):
        """
        Joins an iterable of strings into a single string using the current text as the separator.

        Args:
            iterable (iterable): An iterable of strings to be joined.

        Returns:
            Text: A new Text object with the joined string.
        """
        return Text(self.agent, self._text.join(iterable))
    
    def startswith(self, prefix: str, **kwargs):
        """
        Check if the text starts with the given prefix.

        Args:
            prefix (str): The prefix to check for at the start of the text.
            **kwargs: Additional arguments to pass to the underlying str.startswith method.

        Returns:
            bool: True if the text starts with the specified prefix, False otherwise.
        """
        return self._text.startswith(prefix, **kwargs)
    
    def endswith(self, suffix: str, **kwargs):
        """
        Check if the text ends with the specified suffix.

        Args:
            suffix (str): The suffix to check for at the end of the text.
            **kwargs: Additional arguments to pass to the underlying str.endswith method.

        Returns:
            bool: True if the text ends with the specified suffix, False otherwise.
        """
        return self._text.endswith(suffix, **kwargs)
    
    def isalnum(self):
        """
        Check if the text is alphanumeric.

        Returns:
            bool: True if the text is alphanumeric, False otherwise.
        """
        return self._text.isalnum()
    
    def rich_console_display(self, style: str | None = None):
        """
        Displays the text using the rich Console with an optional style.

        Args:
            style (str | None): The style to apply to the text. If None, no style is applied.
        """
        console = Console()
        console.print(self._text, style=style)
