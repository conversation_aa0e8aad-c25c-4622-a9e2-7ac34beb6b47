"""
The interfaces to get answers from foundation models.
"""
import io
import os
from PIL import Image
from datetime import datetime
import random

from ruyi.utils.interface import RuyiInterface
from ruyi.utils import debug

class Tools_Interface(RuyiInterface):
    """
    The tools that may be used by the agent.
    Agent developers can also use these tools to build their own agents.
    """
    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'tools'

    def time_now(self):
        return datetime.now()
    
    def time_tag(self, time=None):
        if time is None:
            time = datetime.now()
        return time.strftime("%Y-%m-%d_%H:%M:%S")

    def get_temp_file_path(self, suffix='.png'):
        tag = self.time_tag()
        randi = random.randint(0, 10000)
        output_dir = self.agent.config.output_dir
        if output_dir is None:
            output_dir = ''
        file_path = os.path.join(output_dir, 'temp', f'{tag}_{randi:5d}{suffix}')
        return file_path

    def open_image_url(self, image_url):
        # TODO use zulip chat session client
        r = self.agent.chat.client.session.get(image_url)
        img = Image.open(io.BytesIO(r.content))
        return img
    
    def image_to_base64_url(self, image):
        image_format = image.format.upper()
        if image_format not in ['JPEG', 'JPG', 'PNG', 'WEBP']:
            image_format = 'JPEG'  # Default to JPEG if format is not one of the common types
        
        # Convert image to RGB if it's not already in a compatible format
        if image.mode == 'P' or image.mode == 'RGBA' and image_format in ['JPEG', 'JPG']:
            image = image.convert('RGB')
        
        image_stream = io.BytesIO()
        image.save(image_stream, format=image_format)
        import base64
        image_base64 = base64.b64encode(image_stream.getvalue()).decode("utf-8")
        base64_url = f'data:image/{image_format.lower()};base64,{image_base64}'
        return base64_url

