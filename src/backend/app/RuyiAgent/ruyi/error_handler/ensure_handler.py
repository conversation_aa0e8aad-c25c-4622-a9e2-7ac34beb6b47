import structlog

from ruyi.task.task_session import TaskSession
from ruyi.data import Image
from typing import Union

from ruyi.error_handler.generator_prompt import get_ensure_prompt_for_tree, get_ensure_prompt_for_vision

logger = structlog.get_logger(__name__)


class EnsureHandler:
    def __init__(self,
                 handler_type: str,
                 ensure_target: str,
                 ensure_view: Union[str, Image],
                 ensure_reason: str,
                 ensure_type: str,
                 task_session: TaskSession
                 ):
        self.handler_type = handler_type
        self.task_session = task_session
        self.ensure_target = ensure_target
        self.ensure_view = ensure_view
        self.ensure_type = ensure_type
        self.ensure_reason = ensure_reason

        self.is_execute_code = True
        self.is_check_result = True

        self.code = None
        self.action = None

        self.ensure_code_history: list[tuple[str, str]] = []  # 修复代码历史记录 (code, err)

    def handle(self) -> bool:
        loop_count = 0
        while self.task_session.agent.config.max_ensure_loop_count > loop_count:
            loop_count += 1
            logger.debug(
                f'In ensure loop, current loop count: {loop_count}, max loop count: {self.task_session.agent.config.max_ensure_loop_count}',
                action='ensure loop', status='start')

            logger.debug(f'Generating code for ensure target: {self.ensure_target}',
                        action='ensure_handler_generate_code',
                        status='start'
                        )

            self.code = self._generate_code()

            logger.debug(f'Generated code: {self.code}',
                        action='ensure_handler_generate_code',
                        status='done')

            if self.is_execute_code:
                try:
                    logger.debug(f'Executing code {self.code}',
                                action='ensure_handler_execute_code',
                                status='start')
                    self._execute_code(self.code)
                    logger.debug(f'Code execution succeeded: {self.code}',
                                action='ensure_handler_execute_code',
                                status='done')
                except Exception as e:
                    logger.error(f'Code execution failed for ensure target: {self.ensure_target}, error: {e}',
                                 action='ensure_handler_execute_code',
                                 status='failed')
                    self.ensure_code_history.append((self.ensure_target, "Code execution failed: " + str(e)))
            else:
                logger.debug(f'Skip code execution for ensure target: {self.ensure_target}',
                            action='skip_ensure_handler_execute_code',
                            status='done')

            if self.is_check_result:
                logger.debug(f'Checking result for ensure target: {self.ensure_target}',
                            action='ensure_handler_check_result',
                            status='start')
                ans, reason = self._check_result()
                logger.debug(f'Result checking succeeded for ensure target: {self.ensure_target}, result: {ans}, reason: {reason}',
                            action='ensure_handler_check_result',
                            status='done')
                if ans:
                    self.task_session.record_ensure(self.ensure_target, True, self.code)
                    return True
                else:
                    self.ensure_code_history.append(
                        (self.ensure_target, "Code execution succeeded but result is not expected: " + reason))
            else:
                logger.debug(f'Skip result checking for ensure target: {self.ensure_target}',
                            action='skip_ensure_handler_check_result',
                            status='done')
                return True

        logger.debug(f'Max ensure loop count reached for ensure target: {self.ensure_target}',
                    action='max_ensure_loop_count_reached',
                    status='done')
        return False

    def _generate_code(self) -> str:
        raise NotImplementedError("Subclass must implement this method")

    def _execute_code(self, code: str):
        # 执行生成的代码
        device, ui, data, fm = self.task_session.agent.device, self.task_session.agent.ui, self.task_session.agent.data, self.task_session.agent.fm
        exec(code)

        return True

    def _check_result(self) -> tuple[bool, str]:
        ans, reason, view_description = self.task_session.agent.ui.check(self.ensure_target)

        return ans, reason

    def query_user_instruction(self) -> str:
        # feedback = input("Please provide some information about this exception: ")
        feedback = ""
        return feedback

    def dump_to_file(self, file_path: str):
        pass


class LLMHandler(EnsureHandler):
    def __init__(self,
                 ensure_target: str,
                 ensure_view: Union[str, Image],
                 ensure_reason: str,
                 ensure_type: str,
                 task_session: TaskSession
                 ):
        super().__init__("llm",
                         ensure_target,
                         ensure_view,
                         ensure_reason,
                         ensure_type,
                         task_session)

    def _generate_code(self) -> str:
        # 获取历史动作
        actions = self.task_session.get_actions_since_last_ensure()
        action_history = "\n".join([f"- {action}" for action in actions])

        if self.task_session.agent.config.ensure_query_user_instruction:
            feedback = self.query_user_instruction()
        else:
            feedback = None

        ensure_code_history = self.ensure_code_history
        # TODO: use ensure_code_history in the ensure loop

        if self.ensure_type == "tree":
            prompt = get_ensure_prompt_for_tree(
                ensure_view=self.ensure_view,
                current_code=self.task_session.get_current_code(),
                ensure_target=self.ensure_target,
                ensure_reason=self.ensure_reason,
                action_history=action_history,
                feedback=feedback
            )

            # 调用LLM生成修复代码
            code, generated_reason = self.task_session.agent.fm.llm(prompt, returns=[
                ("Python code can help me achieve the ensure target, "
                 "no need any function or class definition.", "str"),
                ("explanation of the code", "str")])
        elif self.ensure_type == "vision":
            prompt_before_img, prompt_after_img = get_ensure_prompt_for_vision(
                current_code=self.task_session.get_current_code(),
                ensure_target=self.ensure_target,
                ensure_reason=self.ensure_reason,
                action_history=action_history,
                feedback=feedback
            )
            code, generated_reason = self.task_session.agent.fm.vlm(
                prompt_before_img,
                self.ensure_view,
                prompt_after_img,
                returns=[(
                    "Python code can help me achieve the ensure target, no need any function or class definition.",
                    "str"),
                    ("explanation of the code", "str")])

        else:
            raise ValueError(f"Invalid ensure type: {self.ensure_type}")

        if code.startswith("```python") and code.endswith("```"):
            code = code[9:-3]

        return code


class UIHandler(EnsureHandler):
    def __init__(self,
                 ensure_target: str,
                 ensure_view: Union[str, Image],
                 ensure_reason: str,
                 ensure_type: str,
                 task_session: TaskSession):
        super().__init__("ui",
                         ensure_target,
                         ensure_view,
                         ensure_reason,
                         ensure_type,
                         task_session)

        self.is_execute_code = False
        self.is_check_result = False

    def _generate_code(self) -> str:
        ui_action = self._wait_for_ui_action()
        self.action = ui_action
        code = self._interpret_action(ui_action)
        return code

    def _wait_for_ui_action(self) -> str:
        # FIXME: guohong help add a new log here
        # self.agent.log(f'Please manually navigate the device to the expected description and press [ENTER].', tag='ERROR')
        # input(f'Please manually achieve the expected description: {self.ensure_target}\nPress [ENTER] after manual '
        #       f'demonstration...')

        # TODO: support action recording in client
        return ""

    def _interpret_action(self, action) -> str:
        # 记录手动操作的代码表示
        return f"# Manual UI operation performed: {action}"
