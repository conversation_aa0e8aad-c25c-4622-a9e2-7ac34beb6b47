from ruyi.task.task_session import TaskSession
from ruyi.data import Image
from typing import Union

from .ensure_handler import LLMHandler, UIHand<PERSON>


def get_ensure_handler(handler_type: str,
                       ensure_description: str,
                       ensure_view: Union[str, Image],
                       ensure_reason: str,
                       ensure_type: str,
                       task_session: TaskSession):
    if handler_type == "llm":
        return LLMHandler(ensure_description, ensure_view, ensure_reason, ensure_type, task_session)
    elif handler_type == "ui":
        return UIHandler(ensure_description, ensure_view, ensure_reason, ensure_type, task_session)
    else:
        raise ValueError(f"Invalid handler type: {handler_type}")


