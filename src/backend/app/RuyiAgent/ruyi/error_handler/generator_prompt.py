RUYI_FRAMEWORK_PROMPT = """
This framework provides the following interfaces:
- agent.device: The interfaces to call system-level APIs of the target device.
    - `device.start_app(app_name: str)` : Open app named `app_name`.
    - `device.kill_app(app_name: str)` : Kill app named `app_name`.
    - `device.click(x: float, y: float, duration: int = 200)`: Click at `(x, y)` with `duration` milliseconds.
    - `device.long_click(x: float, y: float, duration: int = 1000)`: Long click at `(x, y)` with `duration` milliseconds.
    - `device.input(text: str)`: Input text into the current focused input field.
    - `device.clear_and_input(text: str)`: Clear the current focused input field and input text into it.
    - `device.get_input_field_text() -> str`: Get the text from the current focused input field.
    - `device.ask_question(question: str)`: Ask user a question and return the answer.
    - `device.notify_message(message: str)`: Notify user with a message.
    - `device.get_clipboard()`: Get the text from the clipboard.
    - `device.set_clipboard(text: str)`: Set the text to the clipboard.
    - `device.expand_notification_panel()`: Expand the notification panel.
    - `device.take_screenshot() -> PIL.Image`: Take a screenshot of the current screen, and return a PIL image.
    - `device.set_device(device_name: str) -> bool`: Set the connected device to the device named `device_name`.
- agent.ui: The interfaces to interact with the device GUI.
    - `ui.back`: Navigate back from current screen.
    - `ui.home`: Navigate to the home screen.
    - `ui.back_to(description: str, max_steps=5)`: Navigate back to the view described by `description`. `max_steps` is the maximum number of steps to navigate.
    - `ui.scroll_up(distance=None)`: scroll up this view.
    - `ui.scroll_down(distance=None)`: scroll down this view.
    - `ui.scroll_left(distance=None)`: scroll left this view.
    - `ui.scroll_right(distance=None)`: scroll right this view.
    - `ui.scroll_until(desc: str, direction: float | str = "down", distance=None, duration=1000, max_retry: int = 10) -> bool` : scroll this view, until desc is fulfilled. If the desired view appears, return True; otherwise, return False.
    - `ui.wait_until(description, waitInterval:float=0.5, timeout=5) -> bool:`: Wait for a view described by `description` to appear and return it. `timeout` is the time limit (in seconds) of waiting. `-1` means unlimited. If the desired view appears, return True; otherwise, return False.
    - `ui.check(description: str) -> bool`: check whether the current screen state matches `description`.
    - `ui.ensure(description: str) -> bool`: Ensure the view described by `description` is present. If the desired view presents, return True; otherwise, return False.
    - `ui.snapshot()->Image`: Get snapshot UI_View instance of this view.
    - `ui.hide_highlight()`: Hide all highlights on the screen.
    - `ui.locate_view(description: str)`-> `UI_View`: Locate the view described by `description` inside the view. Note that only `UI_View` instances can be located, so you need to call `ui.root` first to get the root view.
    - `ui.iterate_views(description: str, limit=10, direction="up")`: Yield the views that match `description` inside the current view. After enumerating all views in the current interface, the screen will automatically scroll in the direction specified by `direction` (default is upward). You can also specify `direction` as one of "left", "down", or "right". `limit` is the maximum number of views to enumerate, default is -1 (no limit).
    - `ui.fetch_information(description: str, returns: list[str | tuple[str, TypeAlias]] | None)`: Get information from the current screen by `description`. The returns parameter specifies the expected return type and description for function outputs. See `fm.query` for more details.
    - `ui.fetch_image(description: str, save_path: Optional[str] = None)` -> `PIL.Image`: Get image from the current screen by `description`. The image will be saved to the specified path if save_path is provided.
    - `UI_View.content()->str`: Fetch content from this view.
    - `UI_View.click()`: Click the view.
    - `UI_View.input(text: str)`: Clear the input field text and input given text into this view.
    - `UI_View.input_by_pasting(text: str)`: Input text into this view by pasting. Use input_by_pasting API when UI_View.input doesn't work.
    - `UI_View.long_click()`: Long click the view for 1 second.
    - `UI_View.scroll_up(distance=None)`: scroll up this view.
    - `UI_View.scroll_down(distance=None)`: scroll down this view.
    - `UI_View.scroll_left(distance=None)`: scroll left this view.
    - `UI_View.scroll_right(distance=None)`: scroll right this view.
    - `UI_View.scroll_until(desc: str, direction: float | str = "down", distance=None, duration=1000, max_retry: int = 10) -> bool` : scroll this view, until desc is fulfilled. If the desired view appears, return True; otherwise, return False.
    - `UI_View.get_input_field_text() -> str`: Get the text from this input field view.
    - `UI_View.show_highlight()`: Show a highlight on this view with a radius of 100 pixels.
- agent.tools: The tools that may be used by the agent.
    - `tools.time_now()->datetime.now()`: Get the current time.
    - `tools.time_tag(time=None)`: Get the time tag of the current time.
    - `tools.get_temp_file_path(suffix='.png')->str`: Get the path of a temporary file.
    - `tools.open_image_url(image_url)->img`: Open an image from a URL.
    - `tools.image_to_base64_url()->str`:  Convert an image to a base64 URL.
- agent.fm: The interfaces to get answers from foundation models.
    - `fm.llm(prompt: str, returns: list[str] | list[(str, type)] | None, **kwargs)->str`: Query large language model.
    - `fm.vlm(prompt: str, returns: list[str] | list[(str, type)] | None, **kwargs)->str`: Query vision language model.
- agent.data: The interfaces to use common data structures and common data resources (photos, sensors, etc.) on the device.
    - `data.live_table("table name")`: Storing information as a table, `table name` is the name of the table. It provides pandas-like APIs.
        - `live_table.add_row(row_dict: dict[str, Any])`: Add a row.
        - `live_table.update_row(index: int, row_dict: dict[str, Any])`: Update a row.
        - `live_table.delete_row(index: int)`: Delete a row.
        - `len(live_table)`: Get number of rows.
        - `live_table[index]`: Get a row by index.
        - `live_table[index, 'col_name']`: Get a cell value.
        - `live_table.iterrows()`: Iterate over rows.
        - `live_table.columns`: Get list of column names.
        - `live_table.sort_rows('col_name', reverse=False)`: Sort rows by a column.
        - `live_table.filter_rows(lambda row: row['col_name'] > 0)`: Filter rows with a function.
        - `live_table.clear()`: Clear all data in the table.
    - `data.image(PIL.Image)`: Wrap a PIL image as a data.image, `PIL.Image` is the PIL image.
    - `data.read_document(file_name: str) -> str`: Read a document and return the text content.
    - `data.retrieve_document(document: str, query: str, length=None) -> str`: Retrieve document information based on the query and return the text content. If length is not None, the response will be limited to the specified length.
"""


FEEDBACK_PROMPT = """We asked the current user how to resolve this issue, and they provided the following feedback: 
```
{user_feedback}
```

Please refer to this feedback, along with your understanding of the current task and the error, to generate the code 
to fix the issue. """

ENSURE_PROMPT_FORMAT_FOR_TREE = """You are an Ruyi Framework Agent debugging engineer. The Ruyi Framework is used for 
creating software automation task Agents. Currently, the Agent is encountering some issues during execution. Please 
write a Ruyi script to restore the Agent's functionality.

Here’s an overview of the Ruyi Framework's basic usage:
{framework_prompt}

The current UI page is: 
```
{current_page}
```

The current code is:
```python
{current_code}
```

The actions taken since the last successful checkpoint:
```
{action_history}
```

The last `ensure` target was: {ensure_target}, but it failed to pass because: {ensure_reason}.

{feedback_prompt}Please generate Python code to fix the current state and meet the expected outcome. The code should:

1. Use standard UI automation operations (e.g., `click`, `input`, etc.). 2. Return executable Python code as a 
string. 3. Exclude any comments. 4. Focus solely on restoring the current `ensure_target` to pass its check. Avoid 
extra actions, as the main function will continue executing once this `ensure` node passes. 5.Ensure that you are confident in the generated code and carefully evaluate whether the generated code might introduce new issues. The generated code will be executed, and the results will be returned to you for further confirmation. If you are uncertain about any specific step in the code, generate only up to that step. This allows you to analyze the execution results and determine how to proceed with the next steps accordingly."""


def get_ensure_prompt_for_tree(ensure_view: str,
                               current_code: str,
                               ensure_target: str,
                               ensure_reason: str,
                               action_history: str,
                               feedback: str = None) -> str:
    prompt = ENSURE_PROMPT_FORMAT_FOR_TREE.format(
        framework_prompt=RUYI_FRAMEWORK_PROMPT,
        current_page=ensure_view,
        current_code=current_code,
        ensure_target=ensure_target,
        ensure_reason=ensure_reason,
        action_history=action_history,
        feedback_prompt=FEEDBACK_PROMPT.format(user_feedback=feedback) if feedback else ""
    )
    return prompt


def format_actions(actions):
    """格式化动作列表"""
    return "\n".join([f"- {action}" for _, action in actions])


ENSURE_PROMPT_FORMAT_FOR_VISION_1 = """Please act as a debugging engineer for the Ruyi framework Agent. The Ruyi 
framework is designed for creating software automation task Agents. Currently, the Agent is encountering issues 
during its execution. Please write a Ruyi script to restore the Agent's functionality.

First, let me introduce the basic usage of the Ruyi framework. The Ruyi framework provides the following interfaces: 
{framework_prompt}

The current page is shown in the image below:"""

ENSURE_PROMPT_FORMAT_FOR_VISION_2 = """
The current code is:
```python
{current_code}
```

The actions taken since the last successful checkpoint:
{action_history}

The last `ensure` target was: **{ensure_target}**, but it failed to pass because: **{reason}**.

{feedback_prompt}Please generate Python code to fix the current state and achieve the desired outcome. The code should:

1. Use standard UI automation operations (e.g., `click`, `input`, etc.).
2. Return the Python code as an executable string.
3. Do not include any comments.
4. Focus solely on restoring the current `ensure_target` to pass its check. Avoid 
extra actions, as the main function will continue executing once this `ensure` node passes.
5. Ensure that you are confident in the generated code and carefully evaluate whether the generated code might introduce new issues. The generated code will be executed, and the results will be returned to you for further confirmation. If you are uncertain about any specific step in the code, generate only up to that step. This allows you to analyze the execution results and determine how to proceed with the next steps accordingly.
"""


def get_ensure_prompt_for_vision(
        current_code: str,
        ensure_target: str,
        ensure_reason: str,
        action_history: str,
        feedback: str = None
) -> tuple[str, str]:
    prompt1 = ENSURE_PROMPT_FORMAT_FOR_VISION_1.format(
        framework_prompt=RUYI_FRAMEWORK_PROMPT,
    )
    prompt2 = ENSURE_PROMPT_FORMAT_FOR_VISION_2.format(
        current_code=current_code,
        ensure_target=ensure_target,
        reason=ensure_reason,
        action_history=action_history,
        feedback_prompt=FEEDBACK_PROMPT.format(user_feedback=feedback) if feedback else ""
    )
    return prompt1, prompt2
