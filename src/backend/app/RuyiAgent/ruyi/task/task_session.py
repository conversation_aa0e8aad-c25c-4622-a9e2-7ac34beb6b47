import os
import json
import time
import datetime
import inspect
import sys
import threading
import re
import structlog
import requests

logger = structlog.get_logger(__name__)


class LineTracer:
    """行级跟踪器，用于监测函数执行的具体行数"""
    
    def __init__(self, task_session, config):
        self.task_session = task_session
        self.config = config
        self.target_function = None
        self.target_filename = None
        self.is_tracing = False
        self.current_line = None
        self.execution_lines = []  # 记录执行过的行数
        # self.show_code = getattr(config, 'line_tracing_show_code', False)
        
        # 新增：精确的行号映射
        self.line_number_to_label = {}  # main函数行号 -> 标号
        self.label_to_nl_mapping = {}  # 标号 -> 自然语言描述的映射
        
        # 新增：记录哪些行在code_script_labeled中有标号注释
        self.labeled_code_with_comment = {}  # 标号 -> 带注释的代码行
        self.code_script_lines = []  # 所有code_script_labeled的行
        self.line_to_original_code = {}  # main函数行号 -> code_script_labeled中的原始代码
        
        # 新增：记录上一次输出的 NL_script 标号，避免重复输出
        self.last_output_label = None
        
    def _parse_labeled_scripts(self, task):
        """解析任务的 code_script_labeled 和 NL_script_labeled，建立精确的行号映射"""
        try:
            # 首先解析 NL_script_labeled，建立标号到自然语言的映射
            if hasattr(task, 'NL_script_labeled') and task.NL_script_labeled:
                nl_lines = task.NL_script_labeled.strip().split('\n')
                for line in nl_lines:
                    line = line.strip()
                    if line and line.startswith('[') and ']' in line:
                        # 提取标号和描述，如 "[1]打开 "Contacts" 应用。"
                        bracket_end = line.find(']')
                        if bracket_end > 0:
                            label = line[1:bracket_end]  # 提取标号，如 "1"
                            description = line[bracket_end+1:].strip()  # 提取描述
                            if label and description:
                                self.label_to_nl_mapping[label] = description
            
            # 然后解析 main 函数和 code_script_labeled，建立精确的行号映射
            if hasattr(task, 'code_script_labeled') and task.code_script_labeled:
                # 获取 main 函数的源代码
                try:
                    source_lines, start_line_number = inspect.getsourcelines(task.main)
                    
                    # 去掉第一行（函数定义行）和第二行（通常是 device, ui, data, fm = agent.device, ... 这一行）
                    main_code_lines = []
                    for i, line in enumerate(source_lines):
                        # 跳过函数定义行
                        if i == 0:
                            continue
                        
                        # 跳过 device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm 这一行
                        stripped_line = line.strip()
                        # if 'device, ui, data, fm' in stripped_line and 'agent.device' in stripped_line:
                        if 'agent.device' in stripped_line and 'agent.ui' in stripped_line and 'agent.data' in stripped_line and 'agent.fm' in stripped_line:
                            continue
                            
                        # 跳过空行和注释行
                        if not stripped_line or stripped_line.startswith('#'):
                            continue
                            
                        main_code_lines.append((start_line_number + i, stripped_line))
                    
                    # 解析 code_script_labeled，处理新的标号规则
                    code_lines = task.code_script_labeled.strip().split('\n')
                    self.code_script_lines = code_lines
                    
                    # 第一遍：找出所有带标号注释的行，记录标号和代码的对应关系
                    labeled_code_with_labels = []  # [(代码, 标号, 是否有注释), ...]
                    current_label = None
                    
                    for line in code_lines:
                        line = line.strip()
                        if not line:  # 跳过空行
                            continue
                            
                        # 检查是否有标号注释
                        has_comment = False
                        label = current_label  # 默认使用当前标号
                        code_part = line
                        
                        if '#' in line and '[' in line and ']' in line:
                            # 提取标号和代码，如 "device.start_app('Contacts')  # [1]"
                            comment_start = line.find('#')
                            code_part = line[:comment_start].strip()
                            comment_part = line[comment_start:].strip()
                            
                            # 从注释中提取标号
                            if comment_part.startswith('#') and '[' in comment_part and ']' in comment_part:
                                bracket_start = comment_part.find('[')
                                bracket_end = comment_part.find(']', bracket_start)
                                if bracket_start >= 0 and bracket_end > bracket_start:
                                    new_label = comment_part[bracket_start+1:bracket_end]  # 提取标号，如 "1"
                                    if new_label:
                                        current_label = new_label
                                        label = new_label
                                        has_comment = True
                                        # 记录带注释的代码行
                                        self.labeled_code_with_comment[label] = line
                        
                        if code_part and label:  # 只有在有代码内容和标号的情况下才记录
                            labeled_code_with_labels.append((code_part, label, has_comment))
                    
                    # 第二遍：建立精确的行号映射
                    main_index = 0
                    
                    # 按照code_script_labeled中的顺序匹配
                    for code_part, label, has_comment in labeled_code_with_labels:
                        found_match = False
                        
                        # 从当前位置开始搜索匹配的行
                        search_start = main_index
                        while search_start < len(main_code_lines):
                            line_number, main_code = main_code_lines[search_start]
                            
                            # 尝试多种匹配策略
                            if self._code_matches(main_code, code_part):
                                # 找到匹配，建立映射
                                self.line_number_to_label[line_number] = label
                                # 记录原始代码（带或不带注释）
                                if has_comment:
                                    self.line_to_original_code[line_number] = self.labeled_code_with_comment[label]
                                else:
                                    self.line_to_original_code[line_number] = code_part
                                main_index = search_start + 1  # 更新搜索起点
                                found_match = True
                                break
                            else:
                                search_start += 1
                        
                        if not found_match:
                            if not "agent.device" in code_part and not "agent.ui" in code_part and not "agent.data" in code_part and not "agent.fm" in code_part:
                                logger.debug(f"无法找到标号 {label} 对应的代码行: {code_part}", 
                                            action='parse_labeled_scripts', status='warning')
                                
                except Exception as e:
                    logger.error(f"解析 main 函数源代码失败: {str(e)}", 
                               action='parse_labeled_scripts', status='error')
                    
        except Exception as e:
            logger.warning(f"解析标注脚本失败: {str(e)}", 
                          action='parse_labeled_scripts', status='error')
    
    def _code_matches(self, main_code, labeled_code):
        """检查两段代码是否匹配，支持多种匹配策略"""
        # 1. 精确匹配（去掉空格和引号差异）
        normalized_main = re.sub(r'\s+', '', main_code.replace('"', "'"))
        normalized_labeled = re.sub(r'\s+', '', labeled_code.replace('"', "'"))
        
        if normalized_main == normalized_labeled:
            return True
        
        # 2. 容错匹配：处理常见的文本差异
        # 去掉所有标点符号和空格，只保留字母数字
        clean_main = re.sub(r'[^\w\u4e00-\u9fff]', '', main_code.lower())
        clean_labeled = re.sub(r'[^\w\u4e00-\u9fff]', '', labeled_code.lower())
        
        if clean_main == clean_labeled:
            return True
        
        # 3. 子串匹配：检查核心代码是否匹配（用于处理细微差异，如 "Successfully locate view" vs "Successfully locate name view"）
        if len(clean_labeled) > 10:  # 只对较长的代码行应用子串匹配
            # 计算相似度
            if clean_labeled in clean_main or clean_main in clean_labeled:
                # 计算相似度，如果相似度足够高则认为匹配
                similarity = min(len(clean_labeled), len(clean_main)) / max(len(clean_labeled), len(clean_main))
                if similarity > 0.8:
                    return True
        
        return False

    def _find_NL_script_for_line(self, line_number):
        """根据行号查找对应的自然语言脚本"""
        # 通过行号找到标号
        label = self.line_number_to_label.get(line_number)
        if label:
            # 通过标号找到自然语言脚本
            return self.label_to_nl_mapping.get(label)
        return None

    def _find_labeled_code_for_line(self, line_number):
        """根据行号查找对应的代码行，区分带标号和不带标号的情况"""
        # 直接从记录中获取该行的原始代码
        return self.line_to_original_code.get(line_number, None)
    
    def _find_labeled_NL_for_line(self, line_number):
        """根据行号查找对应的带标号的自然语言脚本"""
        label = self.line_number_to_label.get(line_number)
        if label and hasattr(self.task_session.task, 'NL_script_labeled'):
            nl_lines = self.task_session.task.NL_script_labeled.strip().split('\n')
            for line in nl_lines:
                line = line.strip()
                if line and line.startswith(f'[{label}]'):
                    return line
        return None

    def start_tracing(self, target_function):
        """开始跟踪指定函数"""
        self.target_function = target_function
        try:
            self.target_filename = inspect.getfile(target_function)
        except:
            self.target_filename = None
        
        # 解析标注脚本
        if self.task_session and self.task_session.task:
            self._parse_labeled_scripts(self.task_session.task)
        
        self.is_tracing = True
        self.execution_lines = []
        
        # 重置最后输出的标号，确保新任务能正常输出第一个 NL_script
        self.last_output_label = None
        
        # 设置系统跟踪器
        sys.settrace(self._trace_calls)
        # logger.info(f"开始跟踪函数 {target_function.__name__}", 
        #            action='start_line_tracing', status='start')
    
    def stop_tracing(self):
        """停止跟踪"""
        self.is_tracing = False
        sys.settrace(None)
        # logger.info(f"停止跟踪，共执行了 {len(self.execution_lines)} 行代码", 
        #            action='stop_line_tracing', status='done')
    
    def _trace_calls(self, frame, event, arg):
        """跟踪函数调用"""
        if not self.is_tracing:
            return None
            
        # 检查是否是我们要跟踪的函数
        if (self.target_filename and 
            frame.f_code.co_filename == self.target_filename and
            frame.f_code.co_name == self.target_function.__name__):
            return self._trace_lines
        
        return None
    
    def _trace_lines(self, frame, event, arg):
        """跟踪行级执行"""
        if event == 'line':
            line_no = frame.f_lineno
            self.current_line = line_no
            
            # 记录执行的行数（先记录，后续会添加代码内容）
            execution_info = {
                'line_number': line_no,
                'timestamp': time.time(),
                'filename': frame.f_code.co_filename,
                'function_name': frame.f_code.co_name
            }
            self.execution_lines.append(execution_info)
            
            # 获取当前行的代码内容
            current_code = "无法获取代码内容"
            try:
                source_lines = inspect.getsourcelines(self.target_function)[0]
                func_start_line = inspect.getsourcelines(self.target_function)[1]
                relative_line = line_no - func_start_line
                if 0 <= relative_line < len(source_lines):
                    current_code = source_lines[relative_line].strip()
                    if not current_code:  # 如果是空行，显示为空行标记
                        current_code = "[空行]"
            except Exception as e:
                current_code = f"[获取代码失败: {str(e)[:50]}]"
            
            # 将代码内容添加到执行记录中
            execution_info['code_content'] = current_code
            
            # 使用精确的行号匹配查找对应的自然语言脚本
            NL_script = self._find_NL_script_for_line(line_no)
            execution_info['NL_script'] = NL_script
            
            # 获取当前行对应的标号
            current_label = self.line_number_to_label.get(line_no)
            
            # 实时输出当前执行行数和代码内容（仅当标号与上次不同时）
            if NL_script and current_label != self.last_output_label:
                # logger.info(f"正在执行: {NL_script}",
                #            action='line_execution', status='running',)
                print(f"正在执行: {NL_script}")
                # 更新最后输出的标号
                self.last_output_label = current_label

            
            # 以 debug 模式额外输出格式化的代码和自然语言脚本
            if not 'agent.device' in current_code and not 'agent.ui' in current_code and not 'agent.data' in current_code and not 'agent.fm' in current_code:
                logger.debug(f"    └─ 代码内容: {current_code}",
                        action='line_code_detail', status='info',
                        metadata={'line_number': line_no, 'code': current_code})

            try:
                # 获取带标号的代码和自然语言脚本
                labeled_code_script = self._find_labeled_code_for_line(line_no)
                labeled_NL_script = self._find_labeled_NL_for_line(line_no)
                
                response = requests.post(f'http://localhost:{self.config.flask_port}/set_executing_script_info', 
                                        json={'labeled_code_script': labeled_code_script, 
                                              'labeled_NL_script': labeled_NL_script},
                                        headers={'Content-Type': 'application/json'})
            except Exception as e:
                logger.error(f"Exception in setting executing script info: {str(e)}")

        return self._trace_lines
    
    def get_current_line(self):
        """获取当前执行的行数"""
        return self.current_line
    
    def get_execution_summary(self):
        """获取执行摘要"""
        return {
            'total_lines_executed': len(self.execution_lines),
            'current_line': self.current_line,
            'execution_lines': self.execution_lines
        }


class TaskSession:
    def __init__(self, agent, task_id, task, config):
        self.agent = agent
        self.task_id = task_id
        self.task = task
        self.config = config
        self.task_description = task.description
        self.start_time = time.time()
        self.end_time = None
        self.action_history = []
        self.status = 'running'

        # 添加行级跟踪器
        self.line_tracer = LineTracer(self, config)

        try:
            self.main_code = inspect.getsource(task.main)
        except Exception as e:
            try:
                self.main_code = task.__class__.source_code
            except Exception as e:
                self.main_code = 'Unknown'

        self.ensure_history = []
        self.last_passed_ensure = None

    def start_line_tracing(self):
        """开始行级跟踪 main 函数"""
        self.line_tracer.start_tracing(self.task.main)
    
    def stop_line_tracing(self):
        """停止行级跟踪"""
        self.line_tracer.stop_tracing()
    
    def get_current_executing_line(self):
        """获取当前正在执行的行数"""
        return self.line_tracer.get_current_line()
    
    def get_line_execution_summary(self):
        """获取行级执行摘要"""
        return self.line_tracer.get_execution_summary()

    def load_from_json(self, json_file):
        """
        Load task session from json file
        :param json_file:
        :return:
        """
        pass

    def dump_to_json(self, json_file):
        """
        将task session导出为json文件
        """
        # 添加行级跟踪信息
        line_summary = self.get_line_execution_summary()
        
        session_data = {
            'task_id': self.task_id,
            'task_name': self.task.name,
            'task_description': self.task_description,
            'start_time': datetime.datetime.fromtimestamp(self.start_time).isoformat(),
            'end_time': datetime.datetime.fromtimestamp(self.end_time).isoformat() if self.end_time else None,
            'status': self.status,
            'action_history': [
                {
                    'timestamp': datetime.datetime.fromtimestamp(t).isoformat(),
                    'action': a
                }
                for t, a in self.action_history
            ],
            'line_execution_summary': line_summary  # 添加行级执行信息
        }
        
        with open(json_file, 'w') as f:
            json.dump(session_data, f, indent=2)

    def dump_to_ensure_handler(self, json_file):
        pass

    def record_action(self, action_time, action_str):
        """记录动作到当前session"""
        self.action_history.append((action_time, action_str))

    def get_current_page(self):
        """获取当前页面状态"""
        return self.agent.ui.root.description()

    def get_current_code(self):
        """获取当前task的main函数代码"""
        return self.main_code

    def record_ensure(self, ensure_target, passed, action_code=None):
        """记录ensure检查点状态"""
        ensure_record = {
            'timestamp': time.time(),
            'target': ensure_target,
            'passed': passed,
            'action_code': action_code,
            'page_state': self.get_current_page()
        }
        self.ensure_history.append(ensure_record)
        if passed:
            self.last_passed_ensure = ensure_record

    def get_actions_since_last_ensure(self):
        """获取从上一个成功的ensure到现在的所有动作"""
        if not self.last_passed_ensure:
            return self.action_history
        
        last_ensure_time = self.last_passed_ensure['timestamp']
        return [action for time, action in self.action_history if time > last_ensure_time]



