"""
The interfaces to get answers from foundation models.
"""
from collections import OrderedDict

from ..agent import <PERSON>uyi<PERSON>gent
from ..utils.interface import <PERSON><PERSON><PERSON><PERSON>nterface
from ..utils import debug
import structlog
import uuid
import base64
import requests

from .task_session import TaskSession

logger = structlog.get_logger(__name__)


class RuyiTask:
    """
    This class defines the interfaces of a task that can be execute by the agent.
    TODO Each task should be serializable so that it can be transferred between different agents (e.g. generated by a agent and executed by another).
    """

    def __init__(self):
        self.name = self.__class__.__name__  # the short name of this task
        self.description = None  # The text description of this task
        self.status = None  # One of [Pending, Running, Waiting, Failed, Finished]
        self.permissions = None  # TODO permissions of this task. Future work.
        self.results = OrderedDict()  # A dictionary to store the results of this task
        self.status = "not started"

        self._ensure_patches: dict[str, dict[str, callable]] = {}

        self.code_script_labeled = ""
        self.NL_script_labeled = ""

    def main(self, agent:RuyiAgent):
        """
        The entry of this task.
        This method will be called by the agent when starting this task
        """
        pass

    def ensure_patches(self):
        pass

    def register_patch(self, ensure_id, description=""):
        """注册补丁函数的装饰器"""
        def decorator(func):
            if ensure_id not in self.ensure_patches:
                self.ensure_patches[ensure_id] = {}
            if description in self.ensure_patches[ensure_id]:
                if description == "":
                    raise ValueError("Default description already exists in ensure_patches")
                else:
                    raise ValueError(f"Description {description} already exists in ensure_patches")
            self.ensure_patches[ensure_id][description] = func
            return func

        return decorator


class Task_Interface(RuyiInterface):
    """
    The interfaces to manage tasks.
    """

    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'task'
        self._internal_tasks = []
        self._system_tasks = []
        self._task_queue = []
        self._task_sessions = {}  # 新增: task_id -> TaskSession 映射

        self.current_task_session: TaskSession = None  # 当前正在执行的 task session

    @property
    def all_tasks(self) -> list[RuyiTask]:
        return self._internal_tasks + self._system_tasks

    def _open(self):
        from .sample_tasks import Create_contact
        self._internal_tasks.extend([
            Create_contact()
        ])
        # TODO add other internal tasks

    def submit_task(self, task_TaskCls_taskDesc, reply_msg=None, **kwargs):
        self._task_queue.append((task_TaskCls_taskDesc, reply_msg, kwargs))

    def _serve(self):
        while self.agent._enabled:
            self.agent._sleep(1)
            if len(self._task_queue) == 0:
                continue
            task_TaskCls_taskDesc, reply_msg, kwargs = self._task_queue.pop(0)
            self.execute_task(task_TaskCls_taskDesc, reply_msg, **kwargs)

    def _match_task(self, description):
        """
        Find a task that matches the description.
        # TODO better matching method using LLM.
        """
        description = description.strip()
        for task in self.all_tasks:
            if task.name.strip() == description or (task.description and task.description.strip() == description):
                return task
        raise ValueError(f'No task matches the description: {description}')

    def execute_task(self, task_TaskCls_taskDesc: RuyiTask | str | type[RuyiTask], reply_msg=None, **kwargs):
        """
        execute a task defined by `task_or_TaskCls` with the current RuyiAgent instance
        the TaskCls should have a main function that uses an agent instance as its first parameter
        """
        try:
            if isinstance(task_TaskCls_taskDesc, str):
                task = self._match_task(description=task_TaskCls_taskDesc)
            elif isinstance(task_TaskCls_taskDesc, RuyiTask):
                task = task_TaskCls_taskDesc
            elif issubclass(task_TaskCls_taskDesc, RuyiTask):
                task = task_TaskCls_taskDesc(**kwargs)
            else:
                raise ValueError(f'Invalid task type: {type(task_TaskCls_taskDesc)}')

            task_id = base64.urlsafe_b64encode(uuid.uuid4().bytes).decode('utf-8').rstrip('=')
            # 创建新的 task session
            task_session = TaskSession(self.agent, task_id, task, self.agent.config)
            self._task_sessions[task_id] = task_session
            self.current_task_session = task_session
            structlog.contextvars.bind_contextvars(task_id=task_id)
            logger.debug(f'Start task {task.name}', action='start task', status='start', metadata={'task': task.name})
            task.status = 'Running'
            task.ensure_patches()
            
            # 启动行级跟踪（如果配置启用）
            if getattr(self.agent.config, 'enable_line_tracing', True):
                task_session.start_line_tracing()
                # logger.info(f'开始行级跟踪任务 {task.name}', action='start_line_tracing', status='start')
            else:
                # logger.info(f'行级跟踪已禁用，跳过任务 {task.name}', action='skip_line_tracing', status='skip')
                pass
            
            try:
                task.main(self.agent)
            finally:
                if getattr(self.agent.config, 'enable_line_tracing', True):
                    task_session.stop_line_tracing()
                    try:
                        response = requests.post(f'http://localhost:{self.config.flask_port}/set_executing_script_info', 
                                                json={'labeled_code_script': "[-1]", 
                                                    'labeled_NL_script': "[-1]"},
                                                headers={'Content-Type': 'application/json'})
                    except Exception as e:
                        logger.error(f"Exception in setting executing script info: {str(e)}")
                    # logger.info(f'停止行级跟踪任务 {task.name}', action='stop_line_tracing', status='done')
                
            logger.debug(f'Finish task {task.name}', action='finish task', status='done', metadata={'task': task.name})
            task.status = 'Finished'
        except Exception as e:
            # 获取当前执行行的自然语言描述
            error_context = ""
            if getattr(self.agent.config, 'enable_line_tracing', True) and task_session:
                current_line = task_session.get_current_executing_line()
                line_summary = task_session.get_line_execution_summary()
                
                if current_line and line_summary and line_summary.get('execution_lines'):
                    # 查找当前行或最后执行的行的信息
                    target_line = current_line
                    line_info = None
                    
                    # 优先查找当前行
                    for exec_line in reversed(line_summary['execution_lines']):
                        if exec_line['line_number'] == target_line:
                            line_info = exec_line
                            break
                    
                    # 如果没找到当前行，使用最后执行的行
                    if not line_info and line_summary['execution_lines']:
                        line_info = line_summary['execution_lines'][-1]
                        target_line = line_info['line_number']
                    
                    if line_info:
                        code_script = line_info.get('code_script', '未知代码')
                        NL_script = line_info.get('NL_script', '无自然语言描述')
                        
                        error_context += f"{'='*60}\n"
                        error_context += f"❌ 任务执行出错:\n"
                        # error_context += f"   出错行号: {target_line}\n"
                        # error_context += f"   出错代码: {code_script}\n"
                        if NL_script and NL_script != '无自然语言描述':
                            error_context += f"    └─ 任务步骤: {NL_script}\n"
                        error_context += f"    └─ 错误信息: {str(e)}\n"
                        error_context += f"{'='*60}\n"

                    try:
                        # 获取带标号的代码和自然语言脚本
                        labeled_code_script = task_session.line_tracer._find_labeled_code_for_line(target_line)
                        labeled_NL_script = task_session.line_tracer._find_labeled_NL_for_line(target_line)
                        
                        response = requests.post(f'http://localhost:{self.config.flask_port}/set_executing_script_info', 
                                                json={'labeled_code_script': labeled_code_script, 
                                                    'labeled_NL_script': labeled_NL_script,
                                                    'error': True},
                                                headers={'Content-Type': 'application/json'})
                    except Exception as e:
                        logger.error(f"Exception in setting executing script info: {str(e)}")

            logger.exception(error_context, action='finish task', status='failed', raw_output=True)
            
            try:
                task.status = 'Failed'
            except Exception as e:
                logger.exception(f'Failed to set task status to Failed: {e}', action='set task status', status='failed',
                                 metadata={'task': task.name})
        finally:
            if (getattr(self.agent.config, 'enable_line_tracing', True) and 
                task_session and hasattr(task_session, 'line_tracer') and 
                task_session.line_tracer.is_tracing):
                task_session.stop_line_tracing()
            structlog.contextvars.clear_contextvars()
        return task

    def execute_file(self, fpath):
        """
        execute a file specified by `fpath` with the current RuyiAgent instance
        the file should describe a RuyiTask class
        TODO implement this (not urgent)
        """
        debug.print_method_name_with_message('not implemented')
        pass

    def get_current_task_session(self):
        """获取当前正在执行的 task session"""
        task_id = structlog.contextvars.get_contextvars().get('task_id')
        return self._task_sessions.get(task_id)
