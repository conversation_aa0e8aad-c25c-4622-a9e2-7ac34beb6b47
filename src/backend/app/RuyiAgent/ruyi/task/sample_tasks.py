from ruyi.task import RuyiTask

# A very simple example for debugging
class Create_contact(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = """
            创建一个名为Ruyi，email为*******************的联系人。"""
    
    def main(self, agent):
        device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
        device.start_app('Contacts')
        ui.root.locate_view('Create Contact').click()
        ui.root.locate_view('First Name').input('Ruyi')
        ui.root.locate_view('Email').input('<EMAIL>')
        ui.root.locate_view('Save').click()

