import time

from .config import RuyiConfig
from .log import Ruy<PERSON>LogManager
import structlog
from typing import cast

logger = structlog.get_logger(__name__)


class RuyiAgent:
    def __init__(self, config: RuyiConfig):
        self.config = config
        from . import device, data, ui, fm, tools, chat, task
        self.device = device.get_device_controller(agent=self, config=config)
        self.version = config.version
        self.data = data.Data_Interface(self)
        self.ui = ui.UI_Interface(self)
        self.fm = fm.FM_Interface(self)
        self.tools = tools.Tools_Interface(self)
        self.chat = chat.Chat_Interface(self)
        self.task = task.Task_Interface(self)
        self.last_action_time = cast(float, None)
        self.last_action_time = None
        self._action_history: list[tuple[float, str]] = []
        self._enabled = True
        self.log_manager = RuyiLogManager(config)
        self.start()

        self.step_count = 0

    def start(self):
        self.log_manager.start()
        self.device._open()
        self.data._open()
        self.ui._open()
        self.fm._open()
        self.tools._open()
        self.chat._open()
        self.task._open()

    def stop(self):
        self._enabled = False
        self.device._close()
        self.data._close()
        self.ui._close()
        self.fm._close()
        self.tools._close()
        self.chat._close()
        self.task._close()

    def serve(self):
        """
        Start agent serving.
        """
        self.task._serve()

    def _record_action(self, action, seconds: int=1):
        action_time = self.tools.time_now().timestamp()
        self.last_action_time = action_time

        current_session = self.task.get_current_task_session()
        if current_session:
            current_session.record_action(action_time, str(action))

        self._sleep(seconds)

    def sleep(self, seconds: int):
        self._sleep(seconds)

    def _sleep(self, seconds: int):
        """
        let the agent sleep for several seconds
        # TODO freeze all actions during sleeping
        """
        time.sleep(seconds)

    def log(self, message, tag='INFO', reply_msg=None):
        """
        Log a message that can be seen by agent users and developers.
        TODO better logging mechanism.
        """
        log_msg = f'RuyiAgent [{tag}]: {message}'
        print(log_msg)
        if reply_msg:
            self.chat.send_reply(log_msg, reply_msg)

    def get_step_count(self):
        """
        Get the current state of the agent.
        """
        return self.step_count

    def increment_step_count(self):
        """
        Increment the current state of the agent.
        """
        self.step_count += 1
        if self.config.max_steps != -1 and self.step_count > self.config.max_steps:
            raise Exception("Agent has reached maximum number of steps")

    def set_step_count_zero(self):
        """
        Set the current state of the agent to zero.
        """
        self.step_count = 0
    
    def get_current_task_line(self):
        """
        获取当前任务正在执行的行数
        """
        current_session = self.task.get_current_task_session()
        if current_session:
            return current_session.get_current_executing_line()
        return None
    
    def get_task_execution_summary(self):
        """
        获取当前任务的执行摘要
        """
        current_session = self.task.get_current_task_session()
        if current_session:
            return current_session.get_line_execution_summary()
        return None