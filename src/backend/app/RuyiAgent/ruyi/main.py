import os
import sys
from ruyi.agent import RuyiAgent
from ruyi.config import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RuyiArgParser
from ruyi.task import RuyiTask

class Create_contact(RuyiTask):
    """
    A very simple example for debugging
    """
    def __init__(self):
        super().__init__()
        self.description = '创建联系人'

    def main(self, agent):
        print('task running!')
        device, ui = agent.device, agent.ui
        device.start_app('Contacts')
        ui.root.locate_view('Create Contact').click()
        # ui.ensure('A page of email inbox.')  # For debugging `ensure` function
        ui.ensure('A page for creating a contact, including text fields for First Name, Last Name, Email, and a Save button.')
        ui.root.locate_view('First Name').input('Ruyi')
        ui.root.locate_view('Email').input('<EMAIL>')
        ui.root.locate_view('Save').click()


def main():
    parser = RuyiArgParser((RuyiConfig,))
    if len(sys.argv) == 2 and sys.argv[1].endswith('.json'):
        config = parser.parse_json_file(json_file=os.path.abspath(sys.argv[1]))
    elif len(sys.argv) == 2 and sys.argv[1].endswith('.yaml'):
        config = parser.parse_yaml_file(yaml_file=os.path.abspath(sys.argv[1]))
    else:
        config = parser.parse_args_into_dataclasses()
    config = config[0]
    agent = RuyiAgent(config)
    # agent.task.submit_task(Create_contact)
    agent.serve()
    # agent.execute_function(func=monitor_health)
    agent.stop()


if __name__ == '__main__':
    main()

