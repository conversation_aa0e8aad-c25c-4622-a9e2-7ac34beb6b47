# APIs related to apps, eg. open_app, kill_app
from ruyi.utils.interface import RuyiInterface


class Apps_Interface(RuyiInterface):
    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'device.apps'
        self.app_name_to_package = {}

    def start(self, app_name):
        raise DeprecationWarning('This method is deprecated, use device.start_app instead')

    def kill(self, app_name):
        raise DeprecationWarning('This method is deprecated, use device.stop_app instead')
