"""
The interfaces to call system-level APIs of the target device.
Can reuse many from the droidbot library.
"""

from ruyi.device.websocket_device import WebsocketController
from ruyi.device.adb_device import AdbDeviceController
from ruyi.device.browser_device import BrowserDev<PERSON><PERSON><PERSON>roller


def get_device_controller(agent, config):
    if config.device_type == "browser":
        return BrowserDeviceController(agent)
    elif config.device_type == "websocket":
        # res = input(
        #     f"使用无线方式连接手机，请确认RuyiClient打开。当前配置ip为{agent.config.device_url}，按 N/n 更改ip，按其他任意键继续："
        # )
        # if res.lower() == "n":
        #     agent.config.device_url = input(
        #         "请输入新的ip地址，格式为 'xxx.xxx.xxx.xxx:xxx'："
        #     )
        #     agent.config.device_url = f"ws://{agent.config.device_url}"

        return WebsocketController(agent, agent.config.device_url, config.output_dir)
    elif config.device_type == "adb":
        return AdbDeviceController(agent, config.output_dir)
    else:
        raise ValueError(f"Unsupported device type: {config.device_type}")
