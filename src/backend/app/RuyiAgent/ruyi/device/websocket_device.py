import os
import io
import json
import base64
import PIL.Image
import time
import structlog
import requests
import tempfile
import subprocess

from ruyi.device.device_base import DeviceControllerBase
from ruyi.device.websocket_client import WebSocketClient
from ruyi.ui.device_state import DeviceState
from ruyi.data.common import LiveTable, Image

logger = structlog.get_logger(__name__)
class WebsocketController(DeviceControllerBase):
    """
    this class describes a connected device
    """

    def __init__(self, agent, server_address, output_dir):
        super().__init__(agent, "websocket")
        self.server_address = server_address
        self.output_dir = output_dir
        self.websocket_client = WebSocketClient(server_address=server_address)
        self.device_serial_id = self.config.device_mappings[self.config.device_name]

    def _open(self):
        self.apps._open()
        self._open_device()
        width, height = self.get_width_height()
        self.device_bound = (0, 0, width, height)
        self.width = width
        self.height = height

    def _open_device(self):
        self.websocket_client.start()

    def _close_device(self):
        """
        disconnect current device
        :return:
        """
        if self.websocket_client:
            self.websocket_client.close()

    def start_app(self, app) -> bool:
        try:
            res = self._send_command('open_app,' + app)
            self.agent.increment_step_count()
            self.agent.sleep(2)
        except Exception as e:
            # logger.error(f"Failed to start app {app}: {str(e)}")
            if "Failed to find app" in str(e):
                logger.info(f"未找到名为 \"{app}\" 的应用")
                raise RuntimeError(f"未找到名为 \"{app}\" 的应用，请检查 app 名称是否正确以及 app 是否已安装")
            else:
                raise e
        return True

    def kill_app(self, app) -> bool:
        try:
            res = self._send_command('get_app_package_name,' + app)
            app_package_name = res['message']
            if app_package_name:
                os.system(f"adb shell am force-stop {app_package_name}")
                return True
            else:
                return False
        except Exception as e:
            return False
    
    def push_file(self, local_path, remote_path):
        pass

    def pull_file(self, local_path, remote_path):
        pass

    def key_press(self, key):
        pass

    def back(self) -> bool:
        res = self._send_command('back')
        self.agent.increment_step_count()
        return True

    def home(self) -> bool:
        res = self._send_command('home')
        self.agent.increment_step_count()
        return True

    def long_touch(self, x, y, duration=None) -> bool:
        # TODO: support duration
        res = self._send_command('click,' + str(x) + ',' + str(y) + ',' + str(duration))
        self.agent.sleep(duration * 0.001)
        return True

    def click(self, x, y, duration=200) -> bool:
        logger.debug(
            f'Click action done at ({x}, {y})',
            action='click',
            status='done',
            metadata={'coordinates': (x, y)}
        )
        return self.long_touch(x, y, duration)

    def long_click(self, x, y, duration=1000) -> bool:
        logger.debug(
            f'Long click action done at ({x}, {y})',
            action='long_click',
            status='done',
            metadata={'coordinates': (x, y)}
        )
        return self.long_touch(x, y, duration)

    def input(self, text):
        logger.debug(
            f'Input action done with text: {text}',
            action='input',
            status='done',
            metadata={'text': text}
        )
        self.view_append_text(text)

    def clear(self):
        logger.info(
            f'Clear text action done',
            action='clear',
            status='done',
        )
        self.view_clear_text()

    def clear_and_input(self, text):
        logger.debug(
            f'Clear and input action done with text: {text}',
            action='clear_and_input',
            status='done',
            metadata={'text': text}
        )
        self.view_set_text(text)

    def _do_drag(self, start_xy, end_xy, duration=None) -> bool:
        # TODO: support duration
        res = self._send_command(f"drag,{start_xy[0]},{start_xy[1]},{end_xy[0]},{end_xy[1]},{duration}")
        self.agent.sleep(duration * 0.001)
        self.agent.increment_step_count()
        return True

    def get_current_state(self):
        logger.info(
            'Get current state',
            action='get_current_state',
            status='start',
        )
        # raise Exception('Not implemented')
    
        res = self._send_command('view_hierarchy')

        if 'message' not in res or 'height' not in res or 'width' not in res:
            raise Exception('Invalid response, missing message or height or width while getting current state, please check the device recording premissions')
        views = res['message']
        height = res['height']
        width = res['width']
        views = json.loads(views) if isinstance(views, str) else views
        device_state = DeviceState(views, width, height)
        return device_state

    def get_width_height(self):
        res = self._send_command('width_height')
        return res['width'], res['height']
    
    def view_set_text(self, text) -> bool:
        res = self._send_command('clear,' + text)
        res = self._send_command('input,' + text)
        return True
    
    def view_append_text(self, text) -> bool:
        res = self._send_command('input,' + text)
        return True
    
    def view_clear_text(self) -> bool:
        res = self._send_command('clear')
        return True
    
    def get_input_field_text(self) -> str:
        self.agent.increment_step_count()
        res = self._send_command('get_input_field_text')
        return res['message']

    def _send_command(self, command: str):
        res = self.websocket_client.send_message(command)
        # TODO: add a timeout here
        if res is None or res is False:
            raise Exception('command failed')
        res = json.loads(res)
        if res['status'] != 'success':
            raise Exception(f'Websocket meets error while sending command: {command}, {res}')
        return res

    def take_screenshot_adb(self):
        """
        使用 ADB 命令进行截图
        Args:
            device_serial_id: 设备序列号
        Returns:
            PIL.Image: 截图图像
        """
        # 生成临时截图文件路径
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            screenshot_path = temp_file.name
        
        try:
            # 使用 ADB 命令截图
            result = subprocess.run(
                f"adb -s {self.device_serial_id} exec-out screencap -p > {screenshot_path}",
                shell=True,
                check=True
            )
            
            # 读取截图文件并返回 PIL.Image 对象
            screenshot_image = PIL.Image.open(screenshot_path)
            
            # 清理临时文件
            os.unlink(screenshot_path)
            
            return screenshot_image
            
        except subprocess.CalledProcessError as e:
            # 清理临时文件
            if os.path.exists(screenshot_path):
                os.unlink(screenshot_path)
            raise Exception(f"ADB 截图命令执行失败: {str(e)}")
        except Exception as e:
            # 清理临时文件
            if os.path.exists(screenshot_path):
                os.unlink(screenshot_path)
            raise Exception(f"截图过程中发生错误: {str(e)}")

    def take_screenshot(self, save_path=None) -> PIL.Image:
        """
        由于 Ruyi Assistant 录屏权限不稳定
        临时使用 ADB 命令进行截图（使用 exec-out 直接输出）
        Args:
            device_serial_id: 设备序列号
        Returns:
            PIL.Image: 截图图像
        """
        try:
            # screenshot_base64 = self._send_command('screenshot')

            # screenshot_base64 = screenshot_base64['data']

            # screenshot_bytes = decode_base64(screenshot_base64)

            # # 尝试在保存之前验证数据
            # if len(screenshot_bytes) == 0:
            #     raise ValueError("Received empty screenshot data")

            # screenshot_image = PIL.Image.open(io.BytesIO(screenshot_bytes))

            screenshot_image = self.take_screenshot_adb()
            return screenshot_image

        except Exception as e:
            print(f"Screenshot error: {str(e)}")
            # 可以选择记录原始数据用于调试
            # print(f"First 100 chars of base64 data: {screenshot_base64[:100]}")
            raise

    def start_screen_record(self) -> bool:
        res = self._send_command('start_screen_record')
        return True

    def stop_screen_record(self) -> bool:
        res = self._send_command('stop_screen_record')
        return True
    
    def ask_question(self, question) -> str:
        if self.config.user_interaction_type == 'client':
            res = self._send_command('question,' + question)
            return res['message']
        else:
            try:
                response = requests.post(f'http://localhost:{self.config.flask_port}/ask_question', 
                                        json={'question': question},
                                        headers={'Content-Type': 'application/json'})
                return response.json().get('answer', '')
            except Exception as e:
                logger.error(f"Exception when asking question: {str(e)}")
                return f"Error: {str(e)}"

    def notify_message(self, message) -> bool:
        if self.config.user_interaction_type == 'client':
            res = self._send_command('notify_message,' + message)
            return True
        else:
            try:
                response = requests.post(f'http://localhost:{self.config.flask_port}/notify_message', 
                                        json={'message': message},
                                        headers={'Content-Type': 'application/json'})
                return response.status_code == 200
            except Exception as e:
                logger.error(f"Exception when sending notification: {str(e)}")
                return False

    def notify_table(self, table: LiveTable) -> bool:
        try:
            response = requests.post(f'http://localhost:{self.config.flask_port}/notify_table', 
                                    json={'table': table.to_dict()},
                                    headers={'Content-Type': 'application/json'})
            print("SEND response:", response)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Exception when sending notification: {str(e)}")
            return False
    
    def notify_image(self, image) -> bool:
        # 检测传入的 image 是否是 PIL.Image，如果是的话转换成 common.py 中的 Image
        if isinstance(image, PIL.Image.Image):
            image = self.agent.data.image(image)
        
        try:
            response = requests.post(f'http://localhost:{self.config.flask_port}/notify_image', 
                                    json={'image': image.to_dict()},
                                    headers={'Content-Type': 'application/json'})
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Exception when sending notification: {str(e)}")
            return False

    def show_highlight(self, x, y, radius) -> bool:
        res = self._send_command(f'show_highlight,{x},{y},{radius}')
        return True

    def hide_highlight(self) -> bool:
        res = self._send_command('hide_highlight')
        return True

    def log(self, message) -> bool:
        res = self._send_command('log,' + message)
        return True

    def shell(self, cmd):
        raise NotImplementedError("Websoket device does not support shell command")

    def set_clipboard(self, text) -> bool:
        _ = self._send_command('set_clipboard,' + text)
        self.agent.increment_step_count()
        return True

    def get_clipboard(self) -> str:
        res = self._send_command('get_clipboard')
        return res['message']

    def expand_notification_panel(self):
        self.agent.increment_step_count()
        _ = self._send_command('expand_notification')
        return True

    def get_ui_tree(self) -> str:
        try:
            # 获取当前设备状态
            device_state = self.get_current_state()

            if not device_state or not device_state.views:
                raise RuntimeError("Failed to get UI hierarchy from device")
            return device_state.views

        except Exception as e:
            logger.error(f"Error getting UI tree: {str(e)}")
            raise RuntimeError(f"Failed to get UI tree: {str(e)}")

    def _do_device_switch(self, device_name: str, device_id: str) -> bool:
        """执行WebSocket设备的切换操作"""
        try:
            # 关闭现有连接
            self._close_device()

            # 更新设备序列号
            self.device_serial_id = device_id
            
            # 转发端口
            os.system(f"adb -s {device_id} forward tcp:{self.config.device_port} tcp:6666")
            device_url = f"ws://localhost:{self.config.device_port}"
            self.server_address = device_url

            # 重新建立WebSocket连接
            self.websocket_client = WebSocketClient(self.server_address)
            self.websocket_client.start()
            
            return True
        except Exception as e:
            print(f"Failed to switch WebSocket device to '{device_name}': {str(e)}")
            return False

def decode_base64(data):
    """Decode base64, padding being optional.

    :param data: Base64 data as an ASCII byte string
    :returns: The decoded byte string.

    """
    # missing_padding = len(data) % 4
    # if missing_padding != 0:
    #     data += b'=' * (4 - missing_padding)
    return base64.b64decode(data)


if __name__ == '__main__':
    device = WebsocketController('ws://**************:6666', 'output')
    device._open()
    print(device.get_current_state())

    device.start_app('Contacts')

    ans = device.ask_question('What is your name?')
    print(ans)

    device.show_highlight(10, 10, 10)
    time.sleep(2)
    device.hide_highlight()

    # device.long_touch(1000, 210, 1000)
    # device.view_set_text('hello world')
    # device.back()
    # device.home()
    # time.sleep(1)
    # device.take_screenshot()
    # device.disconnect()
