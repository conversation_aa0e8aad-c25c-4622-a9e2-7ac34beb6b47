import asyncio
import websockets
import json

class WebSocketServer:
    def __init__(self, host="0.0.0.0", port=8765):
        self.host = host
        self.port = port
        self.client = None
        self.reconnect_timeout = 3  # 重连等待时间（秒）

    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        try:
            if self.client is not None:
                # 如果已经有客户端连接，拒绝新的连接
                await websocket.close()
                return

            self.client = websocket
            print("客户端已连接")
            
            # 发送连接成功消息
            await websocket.send(json.dumps({
                "type": "connection_response",
                "status": "success",
                "message": "连接成功"
            }))

            # 持续监听客户端消息
            async for message in websocket:
                try:
                    data = json.loads(message)
                    if data.get("type") == "command_response":
                        await self.process_message(data)
                except json.JSONDecodeError:
                    print("收到无效的JSON格式消息")

        except websockets.exceptions.ConnectionClosed:
            print("客户端断开连接，等待重连...")
            self.client = None
            
            # 创建重连超时任务
            reconnect_timeout = asyncio.create_task(asyncio.sleep(self.reconnect_timeout))
            
            while True:
                if self.client is not None:
                    # 新的客户端已连接
                    reconnect_timeout.cancel()
                    return
                
                try:
                    await asyncio.shield(reconnect_timeout)
                    # 如果到达这里，说明超时了还没有新的连接
                    raise ConnectionError("客户端重连失败")
                except asyncio.CancelledError:
                    # 超时任务被取消，说明新的客户端已连接
                    return

    async def send_command(self, command):
        """发送命令并等待响应"""
        if self.client is None:
            raise ConnectionError("没有客户端连接")

        command_message = {
            "type": "command",
            "command": command
        }
        await self.client.send(json.dumps(command_message))
        
        # 等待响应
        try:
            response = await self.client.recv()
            return json.loads(response)
        except Exception as e:
            print(f"等待响应时发生错误: {e}")
            raise

    async def process_message(self, data):
        """处理来自客户端的响应"""
        print(f"收到客户端响应: {data}")
        return data

    def start(self):
        """启动WebSocket服务器"""
        server = websockets.serve(self.handle_client, self.host, self.port)
        print(f"WebSocket 服务器启动在 ws://{self.host}:{self.port}")
        return server

# 使用示例
async def main():
    server = WebSocketServer()
    await server.start()
    
    try:
        while True:
            if server.client:
                # 发送命令示例
                try:
                    response = await server.send_command("test_command")
                    print(f"收到命令响应: {response}")
                except Exception as e:
                    print(f"命令执行失败: {e}")
            await asyncio.sleep(1)
    except asyncio.CancelledError:
        pass

if __name__ == "__main__":
    asyncio.run(main())
