from .device_base import DeviceControllerBase
import requests
from typing import Optional, Tuple, Any
# from config import config
from PIL import Image
import io
import base64
import urllib.parse

# Search Engine Configuration
SEARCH_ENGINES = {
    'google': {
        'id': 'google',
        'name': 'Google',
        'searchUrl': 'https://www.google.com/search?q=',
        'homepage': 'https://www.google.com'
    },
    'baidu': {
        'id': 'baidu',
        'name': '百度',
        'searchUrl': 'https://www.baidu.com/s?wd=',
        'homepage': 'https://www.baidu.com'
    },
    'bing': {
        'id': 'bing',
        'name': 'Bing',
        'searchUrl': 'https://www.bing.com/search?q=',
        'homepage': 'https://www.bing.com'
    },
    'sougou': {
        'id': 'sougou',
        'name': '搜狗',
        'searchUrl': 'https://www.sogou.com/web?query=',
        'homepage': 'https://www.sogou.com'
    }
}

def get_search_engine(engine_id: str) -> dict:
    """获取搜索引擎配置"""
    return SEARCH_ENGINES.get(engine_id, SEARCH_ENGINES['baidu'])

def generate_search_url(query: str, engine_id: str = 'baidu') -> str:
    """生成搜索URL"""
    engine = get_search_engine(engine_id)
    return engine['searchUrl'] + urllib.parse.quote(query)

class BrowserDeviceController(DeviceControllerBase):
    def __init__(self, agent):
        super().__init__(agent, device_type="browser")
        self._setup_browser_communication()
        self.device_bound = (0, 0, 0, 0)

    def _setup_browser_communication(self):
        """设置浏览器通信"""
        # 通过 Flask API 与 Electron 通信
        self.api_base_url = f"http://localhost:{self.config.flask_port}"

    def _open(self):
        """打开浏览器设备"""
        pass

    def _close_device(self):
        """关闭浏览器设备"""
        try:
            self._send_command("destroy")
        except Exception as e:
            print(f"Error closing browser device: {e}")

    def take_screenshot(self, save_path: Optional[str] = None) -> Image.Image:
        """截取浏览器视图的截图，返回PIL Image对象"""
        # 通过 API 获取截图
        response = self._send_command("capturePage")
        screenshot_data_url = response["data"]
        screenshot_data = screenshot_data_url.split(',')[1]
        screenshot_bytes = base64.b64decode(screenshot_data)
        image = Image.open(io.BytesIO(screenshot_bytes))
        
        if save_path:
            image.save(save_path)
        return image

    def key_press(self, key: str):
        """模拟键盘按键"""
        self._send_command("keyPress", {"key": key})

    def back(self):
        """模拟返回操作"""
        self._send_command("goBack")

    def home(self):
        """模拟主页操作"""
        # 浏览器设备不需要实现此方法
        pass

    def open_url(self, url: str) -> bool:
        """打开指定的URL

        Args:
            url: 要打开的URL地址

        Returns:
            bool: 操作是否成功

        Raises:
            RuntimeError: 当URL加载失败时
        """
        if not url:
            raise ValueError("URL cannot be empty")

        # 确保URL有协议前缀
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        try:
            result = self._send_command("loadURL", {"url": url})

            if result and result.get("status") == "success":
                print(f"Successfully loaded URL: {url}")
                return True
            else:
                error_msg = result.get("message", "Unknown error") if result else "No response from loadURL command"
                print(f"Failed to load URL {url}: {error_msg}")
                return False

        except Exception as e:
            print(f"Error loading URL {url}: {str(e)}")
            raise RuntimeError(f"Failed to load URL {url}: {str(e)}")

    def get_url(self) -> str:
        """获取当前页面的URL

        Returns:
            str: 当前页面的URL地址

        Raises:
            RuntimeError: 当获取URL失败时
        """
        try:
            result = self._send_command("getCurrentURL")

            if result and result.get("status") == "success":
                current_url = result.get("url", "")
                print(f"Current URL: {current_url}")
                return current_url
            else:
                error_msg = result.get("message", "Unknown error") if result else "No response from getCurrentURL command"
                print(f"Failed to get current URL: {error_msg}")
                raise RuntimeError(f"Failed to get current URL: {error_msg}")

        except Exception as e:
            print(f"Error getting current URL: {str(e)}")
            raise RuntimeError(f"Failed to get current URL: {str(e)}")

    def web_search(self, query: str) -> bool:
        """使用配置的默认搜索引擎执行网络搜索

        Args:
            query: 搜索查询词

        Returns:
            bool: 搜索操作是否成功

        Raises:
            ValueError: 当查询词为空时
            RuntimeError: 当搜索失败时
        """
        if not query or not query.strip():
            raise ValueError("Search query cannot be empty")

        query = query.strip()
        
        try:
            # 获取当前配置的默认搜索引擎
            search_engine_response = requests.get(f"{self.api_base_url}/get_search_engine_config", timeout=5)
            
            if search_engine_response.status_code == 200:
                search_engine_data = search_engine_response.json()
                search_engine = search_engine_data.get('search_engine', 'baidu')
            else:
                print("Failed to get search engine config, using default (baidu)")
                search_engine = 'baidu'
                
        except Exception as e:
            print(f"Error getting search engine config: {e}, using default (baidu)")
            search_engine = 'baidu'

        # 生成搜索URL
        search_url = generate_search_url(query, search_engine)
        print(f"Generated search URL: {search_url} for query: '{query}' using engine: {search_engine}")

        # 使用现有的open_url方法执行搜索
        try:
            result = self.open_url(search_url)
            if result:
                print(f"Successfully executed web search for query: '{query}'")
                return True
            else:
                print(f"Failed to execute web search for query: '{query}'")
                return False
        except Exception as e:
            print(f"Error executing web search for query '{query}': {str(e)}")
            raise RuntimeError(f"Failed to execute web search for query '{query}': {str(e)}")

    def long_touch(self, x: int, y: int, duration: Optional[float] = None):
        """模拟长按操作
        Args:
            x: 水平坐标（像素）
            y: 垂直坐标（像素）
            duration: 长按持续时间（毫秒）
        """
        self._send_command("longTouch", {
            "x": int(x / 2),
            "y": int(y / 2),
            "duration": int(duration) if duration else 1000
        })
        print(f"Long touch at ({x}, {y}) for {duration}ms")
    
    def click(self, x: int, y: int):
        """模拟点击操作"""
        self._send_command("click", {
            "x": int(x / 2),
            "y": int(y / 2),
            "duration": 200,
        })

    def _do_drag(
        self,
        start_xy: Tuple[int, int],
        end_xy: Tuple[int, int],
        duration: Optional[float] = None,
    ):
        """执行拖拽操作
        Args:
            start_xy: 起始坐标 (x, y)
            end_xy: 结束坐标 (x, y)
            duration: 拖拽持续时间（毫秒）
        Returns:
            拖拽操作的结果
        """
        result = self._send_command(
            "drag",
            {
                "startX": int(start_xy[0] / 2),
                "startY": int(start_xy[1] / 2),
                "endX": int(end_xy[0] / 2),
                "endY": int(end_xy[1] / 2),
                "duration": int(duration) if duration else 1000,
            },
        )
        
        if result.get("status") != "success":
            raise RuntimeError(f"Failed to perform drag: {result.get('message', 'Unknown error')}")
        
        print(f"Drag from ({start_xy[0]}, {start_xy[1]}) to ({end_xy[0]}, {end_xy[1]}) completed")
        return result

    def scroll(
        self,
        start_xy: Tuple[int, int],
        end_xy: Tuple[int, int],
        duration: int = 1000,
    ):
        """执行滚动操作 - 使用浏览器原生滚动
        Args:
            start_xy: 滚动起始坐标 (x, y)
            end_xy: 滚动结束坐标 (x, y)
            duration: 滚动持续时间（毫秒）
        Returns:
            滚动操作的结果
        """
        start_x, start_y = start_xy
        end_x, end_y = end_xy
        
        # Calculate scroll delta from start and end coordinates
        deltaX = end_x - start_x
        deltaY = end_y - start_y
        
        # Use the center point between start and end as the scroll position
        scroll_x = (start_x + end_x) // 2
        scroll_y = (start_y + end_y) // 2
        
        params = {
            "x": int(scroll_x / 2),  # Apply coordinate scaling
            "y": int(scroll_y / 2),
            "deltaX": deltaX,
            "deltaY": deltaY,
            "duration": duration
        }
        
        result = self._send_command("scroll", params)
        
        if result.get("status") != "success":
            raise RuntimeError(f"Failed to perform scroll: {result.get('message', 'Unknown error')}")
        
        print(f"Scroll from {start_xy} to {end_xy} over {duration}ms completed")
        return result

    def get_current_state(self):
        """获取当前页面状态"""
        return self._send_command("getPageState")

    def view_set_text(self, text: str, x: int = None, y: int = None):
        """设置输入框文本"""
        params = {"text": text}
        if x is not None and y is not None:
            # 坐标需要缩放
            params["x"] = int(x / 2)
            params["y"] = int(y / 2)
        
        result = self._send_command("setText", params)
        if result.get("status") != "success":
            raise RuntimeError(f"Failed to set text: {result.get('message', 'Unknown error')}")
        return result

    def view_append_text(self, text: str, x: int = None, y: int = None):
        """追加文本到输入框
        Args:
            text: 要追加的文本
            x: 目标输入框的水平坐标（像素，可选）
            y: 目标输入框的垂直坐标（像素，可选）
        Returns:
            追加操作的结果
        """
        params = {"text": text}
        if x is not None and y is not None:
            # 坐标需要缩放
            params["x"] = int(x / 2)
            params["y"] = int(y / 2)
        
        result = self._send_command("appendText", params)
        if result.get("status") != "success":
            raise RuntimeError(f"Failed to append text: {result.get('message', 'Unknown error')}")
        
        print(f"Appended text '{text}' at ({x or 'current focus'}, {y or 'current focus'})")
        return result

    def check_focus(self):
        """检查当前焦点状态"""
        return self._send_command("checkFocus")

    def shell(self, cmd: str):
        """执行 shell 命令（浏览器设备不支持）"""
        raise NotImplementedError("Browser device does not support shell commands")

    def start_screen_record(self):
        """开始屏幕录制"""
        self._send_command("startRecording")

    def stop_screen_record(self):
        """停止屏幕录制"""
        return self._send_command("stopRecording")

    def sms(self, phone_num: str, msg: str):
        """发送短信（浏览器设备不支持）"""
        raise NotImplementedError("Browser device does not support SMS")

    def ask_question(self, question: str) -> str:
        """向用户提问并获取回答"""
        try:
            response = requests.post(f'http://localhost:{self.config.flask_port}/ask_question', 
                                    json={'question': question},
                                    headers={'Content-Type': 'application/json'})
            return response.json().get('answer', '')
        except Exception as e:
            print(f"Error asking question: {str(e)}")
            return f"Error: {str(e)}"

    def notify_message(self, message: str) -> bool:
        """向前端发送通知消息"""
        try:
            response = requests.post(f'http://localhost:{self.config.flask_port}/notify_message', 
                                    json={'message': message},
                                    headers={'Content-Type': 'application/json'})
            return response.status_code == 200
        except Exception as e:
            print(f"Error sending notification: {str(e)}")
            return False

    def notify_table(self, table) -> bool:
        """向前端发送表格数据"""
        try:
            # 如果传入的是LiveTable对象，转换为字典
            if hasattr(table, 'to_dict'):
                table_data = table.to_dict()
            else:
                table_data = table
                
            response = requests.post(f'http://localhost:{self.config.flask_port}/notify_table', 
                                    json={'table': table_data},
                                    headers={'Content-Type': 'application/json'})
            return response.status_code == 200
        except Exception as e:
            print(f"Error sending table notification: {str(e)}")
            return False
    
    def notify_image(self, image) -> bool:
        """向前端发送图片数据"""
        try:
            # 检测传入的 image 是否是 PIL.Image，如果是的话转换成 common.py 中的 Image
            if hasattr(image, 'mode') and hasattr(image, 'size'):  # PIL.Image check
                # 假设有agent.data.image方法来转换PIL.Image
                if hasattr(self.agent, 'data') and hasattr(self.agent.data, 'image'):
                    image = self.agent.data.image(image)
                else:
                    # 如果没有转换方法，创建基本的图片数据结构
                    import base64
                    import io
                    buffer = io.BytesIO()
                    image.save(buffer, format='PNG')
                    image_base64 = base64.b64encode(buffer.getvalue()).decode()
                    image = {
                        'image_data': image_base64,
                        'metadata': {
                            'width': image.size[0],
                            'height': image.size[1],
                            'format': 'PNG'
                        }
                    }
            
            # 如果image有to_dict方法，使用它
            if hasattr(image, 'to_dict'):
                image_data = image.to_dict()
            else:
                image_data = image
                
            response = requests.post(f'http://localhost:{self.config.flask_port}/notify_image', 
                                    json={'image': image_data},
                                    headers={'Content-Type': 'application/json'})
            return response.status_code == 200
        except Exception as e:
            print(f"Error sending image notification: {str(e)}")
            return False

    def show_highlight(self, x: int, y: int, radius: int):
        """显示高亮标记"""
        self._send_command("showHighlight", {
            "x": int(x / 2),
            "y": int(y / 2),
            "radius": radius
        })

    def hide_highlight(self):
        """隐藏高亮标记"""
        self._send_command("hideHighlight")

    def log(self, message: str) -> bool:
        """记录日志"""
        return self._send_command("log", {"message": message})

    def get_clipboard(self) -> str:
        """获取剪贴板内容"""
        return self._send_command("getClipboard")

    def set_clipboard(self, text: str) -> bool:
        """设置剪贴板内容"""
        return self._send_command("setClipboard", {"text": text})

    def get_ui_tree(self, mode: str = "full") -> str:
        """获取当前页面的HTML内容

        Args:
            mode: 提取模式
                - "full": 完整的HTML文档 (默认)
                - "body": 仅body标签内容
                - "visible": 仅可见元素
                - "text": 纯文本内容
                - "structured": 结构化的元素树

        Returns:
            str: 根据指定模式返回的HTML内容或结构化数据
        """
        result = self._send_command("getUITree", {"mode": mode})

        if result.get("status") != "success":
            raise RuntimeError(f"Failed to get UI tree: {result.get('message', 'Unknown error')}")

        return result.get("content", "")

    def get_width_height(self) -> Tuple[int, int]:
        """获取浏览器视图的宽度和高度
        
        Returns:
            Tuple[int, int]: (width, height) 浏览器视图的宽度和高度
        """
        bounds = self._send_command("getBounds")
        width = bounds.get('width', 0)
        height = bounds.get('height', 0)
        self.device_bound = (0, 0, width, height)
        return (width, height)

    def expand_notification_panel(self):
        """展开通知面板（浏览器设备不支持）"""
        raise NotImplementedError("Browser device does not support notification panel")

    def _do_device_switch(self, device_name: str, device_id: str) -> bool:
        """执行浏览器设备的切换操作"""
        try:
            print(f"[BrowserDevice] 开始切换到设备: {device_name} (ID: {device_id})")
            
            # 通过browser_client切换到目标设备的BrowserView
            # switchDevice命令会自动创建BrowserView（如果不存在）
            result = self._send_command("switchDevice", {
                "deviceId": device_id,
                "deviceName": device_name
            })
            
            print(f"[BrowserDevice] switchDevice命令结果: {result}")
            
            # 检查切换结果
            if result and result.get("status") == "success":
                print(f"[BrowserDevice] 成功切换到浏览器设备: {device_name} (ID: {device_id})")
                return True
            else:
                error_msg = result.get("message", "Unknown error") if result else "No response from switchDevice command"
                print(f"[BrowserDevice] 设备切换失败: {error_msg}")
                return False
            
        except Exception as e:
            print(f"[BrowserDevice] 设备切换过程中出现异常: {str(e)}")
            
            # 如果是连接错误，可能是因为BrowserView还没初始化，尝试等待一下再重试
            if "connection" in str(e).lower() or "browser view not initialized" in str(e).lower():
                print("[BrowserDevice] 检测到初始化相关错误，尝试重试...")
                try:
                    import time
                    time.sleep(1)  # 等待1秒
                    
                    # 重试switchDevice命令
                    result = self._send_command("switchDevice", {
                        "deviceId": device_id,
                        "deviceName": device_name
                    })
                    
                    if result and result.get("status") == "success":
                        print(f"[BrowserDevice] 重试成功，设备切换完成: {device_name}")
                        return True
                    else:
                        print(f"[BrowserDevice] 重试失败: {result}")
                        return False
                        
                except Exception as retry_error:
                    print(f"[BrowserDevice] 重试也失败了: {str(retry_error)}")
                    return False
            
            return False

    def _send_command(self, command: str, params: Optional[dict] = None) -> Any:
        """发送命令到浏览器视图"""
        # 构建命令消息
        message = {"command": command, "params": params or {}}
        
        # 添加设备ID支持 - 从config中获取当前设备名称
        if hasattr(self.config, 'device_name') and self.config.device_name:
            message["deviceId"] = self.config.device_name

        # 通过 Flask API 发送命令到 Electron
        response = requests.post(f"{self.api_base_url}/browser/command", json=message)

        if response.status_code != 200:
            raise RuntimeError(f"Failed to send command: {response.text}")

        # 尝试解析 JSON 响应
        try:
            return response.json()
        except ValueError:
            # 如果不是 JSON 格式，返回原始文本
            return response.text
