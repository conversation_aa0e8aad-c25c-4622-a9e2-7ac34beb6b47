from ruyi.utils.interface import RuyiInterface
from ruyi.device.api_apps import Apps_Interface
from typing import Optional
from PIL import Image


class DeviceControllerBase(RuyiInterface):
    def __init__(self, agent, device_type):
        super().__init__(agent)
        self._tag = 'device'
        self.apps = Apps_Interface(agent)
        self.device_type = device_type
        self.device_bound = None

        self.width = 0
        self.height = 0

    def _open(self):
        self.apps._open()
        self._open_device()
        root_view = self.get_current_state()
        self.device_bound = (0, 0, root_view.width, root_view.height)
        self.width = root_view.width
        self.height = root_view.height

    def _open_device(self):
        raise NotImplementedError("open_device not implemented")

    def _close(self):
        self.apps._close()
        self._close_device()

    def _close_device(self):
        raise NotImplementedError("close_device not implemented")

    def take_picture(self, save_path=None):
        raise NotImplementedError("take_picture not implemented")

    def take_screenshot(self, save_path=None):
        raise NotImplementedError("take_screenshot not implemented")

    def start_app(self, app_name):
        raise NotImplementedError("start_app not implemented")

    def stop_app(self, app_name):
        raise NotImplementedError("stop_app not implemented")

    def push_file(self, local_file_path, remote_file_path):
        raise NotImplementedError("push_file not implemented")

    def pull_file(self, remote_file_path, local_file_path):
        raise NotImplementedError("pull_file not implemented")

    def key_press(self, key):
        raise NotImplementedError("key_press not implemented")

    def back(self):
        raise NotImplementedError("back not implemented")

    def home(self):
        raise NotImplementedError("home not implemented")

    def long_touch(self, x, y, duration=None):
        raise NotImplementedError("long_touch not implemented")

    def drag(self, start_xy, end_xy, duration=None):
        # check if the drag is within the device bound
        start_xy, end_xy = self._check_drag_bound(start_xy, end_xy)
        self._do_drag(start_xy, end_xy, duration)

    def _check_drag_bound(self, start_xy, end_xy):
        # 获取设备边界
        x_min, y_min, x_max, y_max = self.device_bound

        def is_inside(xy):
            x, y = xy
            return x_min <= x <= x_max and y_min <= y <= y_max

        def line_intersection(p1, p2, q1, q2):
            # 计算两条线段的交点
            def det(a, b, c, d):
                return a * d - b * c

            x1, y1 = p1
            x2, y2 = p2
            x3, y3 = q1
            x4, y4 = q2

            denom = det(x1 - x2, y1 - y2, x3 - x4, y3 - y4)
            if denom == 0:
                return None  # 平行或重合

            det1 = det(x1, y1, x2, y2)
            det2 = det(x3, y3, x4, y4)
            x = det(det1, x1 - x2, det2, x3 - x4) / denom
            y = det(det1, y1 - y2, det2, y3 - y4) / denom

            if (min(x1, x2) <= x <= max(x1, x2) and min(y1, y2) <= y <= max(y1, y2) and
                    min(x3, x4) <= x <= max(x3, x4) and min(y3, y4) <= y <= max(y3, y4)):
                return (x, y)
            return None

        # 矩形的四条边
        edges = [
            ((x_min, y_min), (x_max, y_min)),  # 上边
            ((x_max, y_min), (x_max, y_max)),  # 右边
            ((x_max, y_max), (x_min, y_max)),  # 下边
            ((x_min, y_max), (x_min, y_min))   # 左边
        ]

        if is_inside(start_xy) and is_inside(end_xy):
            return start_xy, end_xy

        intersections = []
        for edge in edges:
            intersection = line_intersection(start_xy, end_xy, *edge)
            if intersection:
                intersections.append(intersection)

        if len(intersections) == 2:
            return intersections[0], intersections[1]
        elif len(intersections) == 1:
            if is_inside(start_xy):
                return start_xy, intersections[0]
            elif is_inside(end_xy):
                return intersections[0], end_xy
        else:
            return start_xy, end_xy

    def _do_drag(self, start_xy, end_xy, duration=None):
        raise NotImplementedError("_do_drag not implemented")

    def get_current_state(self):
        raise NotImplementedError("get_current_state not implemented")

    def view_set_text(self, text):
        raise NotImplementedError("view_set_text not implemented")

    def view_append_text(self, text):
        raise NotImplementedError("view_append_text not implemented")

    def shell(self, cmd):
        raise NotImplementedError("shell not implemented")

    def start_screen_record(self):
        raise NotImplementedError("start_screen_record not implemented")

    def stop_screen_record(self):
        raise NotImplementedError("stop_screen_record not implemented")

    def sms(self, phone_num, msg):
        raise NotImplementedError("sms not implemented")
    
    def ask_question(self, question) -> str:
        raise NotImplementedError("ask_question not implemented")

    def show_highlight(self, x, y, radius):
        raise NotImplementedError("show_highlight not implemented")

    def hide_highlight(self):
        raise NotImplementedError("hide_highlight not implemented")

    def log(self, message) -> bool:
        raise NotImplementedError("log not implemented")

    def get_clipboard(self) -> str:
        raise NotImplementedError("get_clipboard not implemented")

    def set_clipboard(self, text: str) -> bool:
        raise NotImplementedError("set_clipboard not implemented")

    def expand_notification_panel(self):
        raise NotImplementedError("expand_notification_panel not implemented")

    def set_device(self, device_name: str) -> bool:
        """公共方法，会在所有设备类型中查找指定的设备名称，
        并调用子类实现的 _do_device_switch 方法来执行实际的设备切换。
        
        Args:
            device_name: 设备名称，用于从device_mappings中查找对应的设备ID
            
        Returns:
            bool: 切换是否成功
            
        Raises:
            ValueError: 当设备名称不存在于device_mappings中时
            DeviceTypeMismatchError: 当设备类型不匹配时（未来扩展）
        """
        try:
            # 1. 检查设备是否存在于映射表中
            if not hasattr(self.config, 'device_mappings') or not self.config.device_mappings:
                raise ValueError("No device mappings configured")
                
            if device_name not in self.config.device_mappings:
                # 提供更详细的错误信息，列出所有可用设备
                available_devices = list(self.config.device_mappings.keys())
                raise ValueError(
                    f"Device '{device_name}' not found in device mappings. "
                    f"Available devices: {available_devices}"
                )
            
            # 2. 获取目标设备ID
            target_device_id = self.config.device_mappings[device_name]
            
            # 3. 调用子类实现的设备切换方法
            success = self._do_device_switch(device_name, target_device_id)
            
            if success:
                # 4. 更新配置中的当前设备名称
                self.config.device_name = device_name
                print(f"Successfully switched to device: {device_name} (ID: {target_device_id}, Type: {self.device_type})")
            else:
                print(f"Failed to switch to device: {device_name}")
                
            return success
            
        except Exception as e:
            print(f"Error in set_device for '{device_name}': {str(e)}")
            return False

    def _do_device_switch(self, device_name: str, device_id: str) -> bool:
        """执行具体的设备切换操作
        
        这是一个抽象方法，需要由子类实现具体的设备切换逻辑。
        每种设备类型都有不同的切换方式：
        - WebSocket设备需要关闭连接、设置端口转发、重新连接
        - Browser设备需要切换BrowserView
        
        Args:
            device_name: 设备名称
            device_id: 设备ID（从device_mappings中获取）
            
        Returns:
            bool: 切换是否成功
        """
        raise NotImplementedError("_do_device_switch not implemented by subclass")

    def take_screenshot_by_description(self, description: str, save_path: Optional[str] = None) -> Image.Image:
        """根据描述定位元素并截取该区域的截图

        Args:
            description: 要定位的元素的自然语言描述，例如 "搜索按钮" 或 "页面顶部的导航栏"
            save_path: 可选的保存路径

        Returns:
            PIL.Image.Image: 裁剪后的图像对象

        Raises:
            RuntimeError: 当无法定位到描述的元素时
            ValueError: 当描述为空时
        """
        if not description:
            raise ValueError("Description cannot be empty")

        try:
            # 首先获取完整截图
            full_screenshot = self.take_screenshot()

            # 使用UI接口定位元素
            located_view = self.agent.ui.root.locate_view(description, mode="bbox")

            # 获取元素的边界框
            bound = located_view._get_bound()

            if bound is None or bound == (None, None, None, None):
                raise RuntimeError(f"Failed to locate element with description: '{description}'")

            x0, y0, x1, y1 = bound

            # 确保坐标有效
            if x0 is None or y0 is None or x1 is None or y1 is None:
                raise RuntimeError(f"Invalid bounding box for element: '{description}'")

            # 确保边界框在图像范围内
            img_width, img_height = full_screenshot.size
            x0 = max(0, min(int(x0), img_width))
            y0 = max(0, min(int(y0), img_height))
            x1 = max(0, min(int(x1), img_width))
            y1 = max(0, min(int(y1), img_height))

            # 确保x1 > x0 和 y1 > y0
            if x1 <= x0 or y1 <= y0:
                # 如果边界框是点坐标，扩展为小区域
                center_x, center_y = (x0 + x1) // 2, (y0 + y1) // 2
                padding = 50  # 50像素的边距
                x0 = max(0, center_x - padding)
                y0 = max(0, center_y - padding)
                x1 = min(img_width, center_x + padding)
                y1 = min(img_height, center_y + padding)

            # 裁剪图像
            cropped_image = full_screenshot.crop((x0, y0, x1, y1))

            # 保存图像（如果指定了路径）
            if save_path:
                cropped_image.save(save_path)

            print(f"Successfully captured screenshot of '{description}' at bounds ({x0}, {y0}, {x1}, {y1})")

            # 关闭原始截图以释放内存
            full_screenshot.close()

            return cropped_image

        except Exception as e:
            print(f"Error taking screenshot by description '{description}': {str(e)}")
            raise RuntimeError(f"Failed to take screenshot by description '{description}': {str(e)}")