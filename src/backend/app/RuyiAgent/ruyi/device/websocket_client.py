import websocket
import threading
import time
from ruyi.log.manager import logger

def default_on_error(ws, error):
    logger.debug(f"WebSocket错误发生: {error}")
    # print(f"WebSocket错误发生: {error}")


def default_on_close(ws, close_status_code, close_msg):
    logger.debug("WebSocket连接已关闭")
    # print("WebSocket连接已关闭")


class WebSocketClient:
    def __init__(self, server_address=None, on_start_message=None, on_error=default_on_error,
                 on_close=default_on_close):
        self.on_start_message = on_start_message
        self.server_address = server_address
        self.response_event = threading.Event()  # 用于同步
        self.is_running = threading.Event()
        self.response_message = None  # 用于存储服务器返回的信息
        self.ws = websocket.WebSocketApp(server_address,
                                         on_open=self.on_open,
                                         on_message=self.on_message,
                                         on_error=on_error,
                                         on_close=on_close)
        self.ws.client_instance = self  # 将当前实例传递给 WebSocketApp
        self.thread = threading.Thread(target=self.ws.run_forever)

    def on_message(self, ws, message):
        # print(f"收到服务器消息: {message}")
        # 将消息存入 WebSocketClient 实例中
        self.response_message = message
        # 解除阻塞
        self.response_event.set()

    def on_open(self, ws):
        # print("WebSocket连接已打开")
        self.is_running.set()

    def start(self):
        # print(f"连接到服务器: {self.server_address}")
        self.thread.start()
        self.is_running.wait()

    def send_message(self, message):
        # 发送消息并等待服务器的响应
        # print("重置事件...")
        self.response_event.clear()  # 重置事件
        self.response_message = None  # 清空之前的响应消息
        # print(f"发送消息: {message}")
        self.ws.send(message)
        # print("等待服务器响应...")
        self.response_event.wait()  # 阻塞，直到事件被设置
        # print("收到服务器响应")
        return self.response_message  # 返回服务器的响应

    def close(self):
        self.ws.close()


if __name__ == "__main__":
    import PIL.Image
    import base64
    import io
    import json
    # 替换成你的服务器地址
    # server_address = "ws://*************:6666"
    server_address = "ws://127.0.0.1:51825"

    # 创建WebSocket客户端实例
    client = WebSocketClient(server_address)

    try:
        # 启动WebSocket连接
        client.start()

        # res = client.send_message("view_hierarchy")
        # print(res)
        
        res = client.send_message("notify_message,ruyi")
        print(res)

        # res = client.send_message("open_app,Contacts")
        # print(res)
        # input()
        #
        # res = client.send_message("question,你好，请问你是谁？")
        # print(res)
        # input()
        #
        # res = client.send_message("log,你好，我是ruyi")
        # print(res)
        # input()
        #
        # client.send_message("show_highlight,500,500,300")
        # input()
        # client.send_message("hide_highlight")
        # input()

        # client.send_message("expand_notification")
        # input()

        # client.send_message("set_clipboard,haha")
        # input()
        # res = client.send_message("get_clipboard")
        # print(res)
        # input()



        # 发送命令并等待响应
        # res = client.send_message("click,1000,210")
        # time.sleep(1)
        # res = client.send_message("back")
        # time.sleep(1)
        # res = client.send_message("home")
        # time.sleep(1)
        # res = client.send_message("drag, 500, 500, 1000, 500")
        # time.sleep(1)
        # res = client.send_message("drag, 500, 500, 1000, 500")
        # time.sleep(1)
        # res = client.send_message("drag, 500, 500, 1000, 500")
        # time.sleep(1)
        # res = client.send_message("drag, 500, 500, 1000, 500")
        # time.sleep(1)
        # res = client.send_message("drag, 500, 500, 1000, 500")
        # time.sleep(1)
        # res = client.send_message("click,500, 430")
        # time.sleep(1)
        # res = client.send_message("input,haha")
        # time.sleep(1)
        # res = client.send_message("input,lala")
        # time.sleep(1)
        # res = client.send_message("clear")
        # time.sleep(1)
        # res = client.send_message("screenshot")
        # res = json.loads(res)["data"]
        # res = base64.b64decode(res)

        # img = PIL.Image.open(io.BytesIO(res))
        # img.show()
        # print(f"收到的响应: {res}")
    except Exception as e:
        raise e
