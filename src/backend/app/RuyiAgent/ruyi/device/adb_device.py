from ruyi.device.device_base import DeviceControllerBase
from droidbot.droidbot import Device
import time
import os
from ruyi.utils import debug
import PIL.Image as Image
from datetime import datetime
import warnings


class AdbDeviceController(DeviceControllerBase):
    def __init__(self, agent, output_dir):
        super().__init__(agent, "adb")
        self._output_dir = output_dir
        self.droidbot = None

        self.app_name_to_package = {
            "WeChat": "com.tencent.mm",
            "Contacts": "com.simplemobiletools.contacts.pro/.activities.MainActivity",
            "Contacts Edit": "com.simplemobiletools.contacts.pro/.activities.EditContactActivity",
            "Xiecheng": "ctrip.android.view/ctrip.business.splash.CtripSplashActivity",
            "联系人": "com.simplemobiletools.contacts.pro/.activities.MainActivity",
            "QQ": "com.tencent.mobileqq/.activity.SplashActivity",
            "微信": "com.tencent.mm/com.tencent.mm.ui.LauncherUI",
            "天气": "com.miui.weather2/.ActivityWeatherMain",
            "支付宝": "com.eg.android.AlipayGphone/.AlipayLogin",
            "淘宝": "com.taobao.taobao/com.taobao.tao.welcome.Welcome",
            "小红书": "com.xingin.xhs/.index.v2.IndexActivityV2",
            "京东": "com.jingdong.app.mall/.main.MainActivity",
            "携程旅行": "ctrip.android.view/ctrip.business.splash.CtripSplashActivity",
            "相册": "com.miui.gallery/.activity.HomePageActivity",
            "美团": "com.sankuai.meituan/com.meituan.android.pt.homepage.activity.MainActivity",
            "相机": "com.android.camera/com.android.camera.Camera",
            "Lemon8": "com.bd.nproject/com.bytedance.nproject.app.MainActivity",
            "Weather": "com.miui.weather2/.ActivityWeatherMain",
            "TikTok": "com.zhiliaoapp.musically/com.ss.android.ugc.aweme.splash.SplashActivity",
            "eBay": "com.ebay.mobile/com.ebay.mobile.home.impl.main.MainActivity",
            "闲鱼": "com.taobao.idlefish/.maincontainer.activity.MainActivity",
        }

    def _open_device(self):
        droidbot = Device(
            grant_perm=True,
            output_dir=self.config.output_dir
        )
        droidbot.set_up()
        droidbot.connect()
        self.droidbot = droidbot

    def _close_device(self):
        self.droidbot.disconnect()
        if not self.config.device_keep_env:
            self.droidbot.tear_down()

    def take_picture(self, save_path=None):
        self.droidbot.adb.shell("am start -n com.android.camera/.Camera")
        time.sleep(1)
        self.droidbot.adb.shell("input keyevent 27")
        time.sleep(1)
        remote_image_path = self.droidbot.adb.shell("ls -t /sdcard/DCIM/Camera/").split('\n')[0]
        remote_image_path ="/sdcard/DCIM/Camera/" + remote_image_path
        local_image_path = os.path.join(self.agent.config.output_dir, "screen_%s.png" % datetime.now().strftime("%Y-%m-%d_%H%M%S"))
        self.droidbot.pull_file(remote_image_path, local_image_path)
        self.droidbot.adb.shell("rm %s" % remote_image_path)
        self.droidbot.adb.shell("input keyevent 4")

        image = Image.open(local_image_path)
        os.remove(local_image_path)

        debug.print_method_name_with_message('picture saved to %s' % local_image_path)

        # TODO: guohong decied image revice a file path or a Image object
        return image

    def take_screenshot(self, save_path=None):
        file_path = self.droidbot.take_screenshot()
        debug.print_method_name_with_message('screenshot saved to %s' % file_path)
        image = Image.open(file_path)
        try:
            os.remove(file_path)
        except PermissionError:
            self.agent.log(f'Failed on deleting cache image at '+file_path, tag='WARN')
        # TODO: guohong decied image revice a file path or a Image object
        # return self.agent.data.image(img=None)
        return image

    def _get_package_by_name(self, app_name, exclude_activity=False):
        """
        get the package name of an app based on its app name
        package name can be used to start/stop apps
        """
        if app_name in self.app_name_to_package:
            pkg_name = self.app_name_to_package[app_name]
        elif app_name in self.app_name_to_package.values():
            pkg_name = app_name
        else:
            self.agent.log(f'_get_package_by_name cannot find an app named {app_name}', tag='WARN')
            pkg_name = app_name
        if exclude_activity and '/' in pkg_name:
            pkg_name = pkg_name[:pkg_name.index('/')]
        return pkg_name

    def start_app(self, app_name):
        pkg_name = self._get_package_by_name(app_name=app_name)
        self.droidbot.adb.shell(f'am start {pkg_name}')
        self.agent._record_action(f'Start [{app_name}]')

    def stop_app(self, app_name):
        pkg_name = self._get_package_by_name(app_name=app_name, exclude_activity=True)
        self.droidbot.adb.shell(f'am force-stop {pkg_name}')
        self.agent._record_action(f'Kill [{app_name}]')

    def push_file(self, local_file_path, remote_file_path):
        self.droidbot.push_file(local_file_path, remote_file_path)

    def pull_file(self, remote_file_path, local_file_path):
        self.droidbot.pull_file(remote_file_path, local_file_path)

    def back(self):
        self.droidbot.key_press('BACK')

    def home(self):
        self.droidbot.key_press('HOME')

    def enter(self):
        self.droidbot.key_press('ENTER')

    def view_set_text(self, text):
        self.droidbot.view_set_text(text)

    def view_append_text(self, text):
        self.droidbot.view_append_text(text)

    def long_touch(self, x, y, duration=None):
        self.droidbot.adb.long_touch(x, y, duration=200)

    def _do_drag(self, start_xy, end_xy, duration=None):
        self.droidbot.view_drag(start_xy, end_xy, duration=duration)

    def get_current_state(self):
        res = self.droidbot.get_current_state()

        return res

    def shell(self, cmd):
        return self.droidbot.adb.shell(cmd)






