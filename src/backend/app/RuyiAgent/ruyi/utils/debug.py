import inspect


def print_method_name_with_message(message='null'):
    """
    The method is intended for framework developers.
    If you want to log something in the agent (which can be seen by agent users), use agent.log method.
    """
    func_name = inspect.stack()[1][3]
    file_name = inspect.stack()[1][1]
    print(f'[DEBUG] >> {file_name} - {func_name} called. message: {message}')



def _save_result(**kwargs):
    pass
