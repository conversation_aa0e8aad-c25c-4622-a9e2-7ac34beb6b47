#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
@Project : RuyiAgent 
@File    : ConstantControl
<AUTHOR> <PERSON><PERSON>
@Date    : 2024/11/27
'''
import inspect
import json
import os
import importlib.resources as pkg_resources
from ruyi import resources


class PromptManager:
    SUPPORTED_LANGUAGES = ['zh_CN', 'en_US']

    def __init__(self, defaultLanguage: str = None):
        self.defaultLanguage = PromptManager.SUPPORTED_LANGUAGES
        if defaultLanguage is not None:
            self.defaultLanguage = [defaultLanguage]
        with pkg_resources.files(resources).joinpath('existing_prompts.json').open('r', encoding='utf-8') as f:
            self.prompts = json.load(f)

    def get_string(self, type: str, name: str, params: list):
        """
        获取字符串。

        根据传入的名称和参数，获取对应的字符串

        Parameters
        ----------
        name : str
            要获取的变量名。
        params : list
            对应于v1,v2,v3...vn的变量，可以不一定是字符串，会被强制传换成字符串
            关于v1,v2,v3...vn的解释，请查阅existingPrompts.json中对应的描述
        Returns
        -------
        str
            格式化后的字符串。

        Notes
        -----
        - 该方法通过检查调用栈来确定调用者模块。
        - 默认语言设置用于选择适合的语言键。

        """
        format_dict = {f'v{i + 1}': value for i, value in enumerate(params)}
        target = self.prompts[type]
        selectedKey = None
        if len(target.keys()) > 1:
            for l in self.defaultLanguage:
                if l in target.keys():
                    selectedKey = l
                    break
            if selectedKey is None:
                selectedKey = list(target.keys())[0]
        else:
            selectedKey = list(target.keys())[0]
        target = target[selectedKey]
        return target[name][0].format(**format_dict)

    def add_constant(self, fileName: str, language: str, variableName: str, formatString: str, paramsDesc: list[str],
                     ) -> dict:
        """
            添加一个字符串常量到存储文件中。

            Parameters
            ----------
            fileName : str
                文件名（不带后缀）
            language : str
                代码语言，需要在支持的语言列表中。
            variableName : str
                要添加或更新的变量名。（不一定非要和真正的变量名一样，反正你存的时候的名字和取的时候一样就行）
            formatString : str
                格式化字符串，用于描述变量的输出格式。（请用v1,v2,v3...来代表第一个，第二个第三个变量（全是字符串））
            paramsDesc : list of str
                格式化字符串的参数描述列表。

            Returns
            -------
            dict
                一个包含文件名、语言、变量名以及其格式化信息的字典。

            Raises
            ------
            Exception
                如果所选的语言不在支持的语言列表中，则引发异常。
            """
        if fileName.endswith('.py'):
            fileName = fileName[:-3]
        format_dict = {f'v{i + 1}': value for i, value in enumerate(paramsDesc)}
        formatString.format(**format_dict)
        if language not in PromptManager.SUPPORTED_LANGUAGES:
            raise Exception("暂时不支持这种语言！\n支持的语言是" + str(PromptManager.SUPPORTED_LANGUAGES))
        answer = {fileName: {language: {variableName: [formatString, paramsDesc]}}}
        if fileName in self.prompts.keys():
            if language in self.prompts[fileName].keys():
                if variableName in self.prompts[fileName][language].keys():
                    print("更新已有变量！变量名" + variableName)
                else:
                    self.prompts[fileName][language][variableName] = [formatString, paramsDesc]
            else:
                self.prompts[fileName][language] = {variableName: [formatString, paramsDesc]}
        else:
            self.prompts[fileName] = {language: {variableName: [formatString, paramsDesc]}}
        return answer

    def save(self):
        with open('../resources/existing_prompts.json', 'w', encoding='utf-8') as f:
            json.dump(self.prompts, f, ensure_ascii=False, indent=4)

prompt_manager = PromptManager()

if __name__ == '__main__':
    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='tree_view_locator_locate_view_prompt',
        formatString='Given the GUI view tree below, which view best-matches the description "{v1}"?',
        paramsDesc=['description']
    )

    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='tree_view_locator_get_views_by_list_prompt',
        formatString='Given the GUI view tree below, which views best-match the description "{v1}"? \nNote that the description describes a list of views, you should return the list of view descriptions that match this description. For example, if the description is "Restuarant Listings", you should return: ["McCafe, 4.5 stars, $5, 28 min, 4.95 discount rate.", "Burger King, 4.2 stars, $4, 20 min, 4.50 discount rate.", "Subway, 4.1 stars, $3, 15 min, 4.25 discount rate."].{v2} \n Note: 1. You should respond only with a list of string. Example Output: [\"a\", \"b\", \"c\"]. 2. Don\'t output \"```python!\". 3. Don\'t output any other things.',
        paramsDesc=['description', 'parent_view_desc']
    )

    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='tree_view_locator_deduplicate_prompt',
        formatString='''You are going to remove duplicated elements from the current element list if it already exists in the history element list. If an element from current element list already exists in the history element list, please remove it from current element list. `Exists` means the descriptions between history and current ones are semantically similar.

    Example:
    [Example Start]
    input:
    history element list: ['Green Button with text `Hello` in the left-top corner', 'Black Search Icon beside the `Search` text']
    current element list: ['Black Search Icon beside the `Search` text', 'Yellow User Icon at the bottom']

    output:  ['Yellow User Icon at the bottom']
    [Example End]

    Note:
    1. You should output a list that can be parsed. Don't output any other things!
    2. Response Example: ['Green Button with text `Hello` in the left-top corner', 'Black Search Icon beside the `Search` text']. You should respond like this. Don't output any other things!

    current element list: {v1}
    history element list: {v2}

    Now please start!''',
        paramsDesc=['element_descriptions', 'history_elements']
    )

    # 添加Molmo_Vision_View_Locator相关的prompt
    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='molmo_vision_view_locator_format_locate_view_inputs',
        formatString='''You are going to refer an element on the UI interface of the image, following the instruction.
    Please point out the element you want to refer to complete the instruction.

    Your instruction:
    where is {v1}?

    Note:
    1. Remember always to give a point to refer an element!
    2. Your response should be like this: ' <point x="?" y="?" alt="?">?</point>' 
    Begin!''',
        paramsDesc=['instruction']
    )

    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='molmo_vision_view_locator_content_prompt',
        formatString='''What is the text inside the element described as `{v1}` on the screenshot from the computer screen? Only output the content.

    For example:
    [Example Start]
    input:
    What is the text inside the element described as `name`?
    output:
    John Smith
    [Example End]

    Now please begin!''',
        paramsDesc=['description']
    )

    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='molmo_vision_view_locator_deduplicate_prompt',
        formatString='''You are going to remove duplicated elements from the current element list if it already exists in the history element list. If an element from current element list already exists in the history element list, please remove it from current element list. `Exists` means the descriptions between history and current ones are semantically similar.

    Example:
    [Example Start]
    input:
    history element list: ['Green Button with text `Hello` in the left-top corner', 'Black Search Icon beside the `Search` text']
    current element list: ['Black Search Icon beside the `Search` text', 'Yellow User Icon at the bottom']

    output:  ['Yellow User Icon at the bottom']
    [Example End]

    Note:
    1. You should output a list that can be parsed. Don't output any other things!
    2. Response Example: ['Green Button with text `Hello` in the left-top corner', 'Black Search Icon beside the `Search` text']. You should respond like this. Don't output any other things!

    current element list: {v1}
    history element list: {v2}

    Now please start!''',
        paramsDesc=['element_descriptions', 'history_elements']
    )

    # 添加Claude_Vision_View_Locator相关的prompt
    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='claude_vision_view_locator_format_locate_view_inputs',
        formatString='''You are going to move mouse onto the UI interface of the image, following the instruction.

    Your instruction:
    find the {v1}. (Do not take screenshot, I have already sent it to you)''',
        paramsDesc=['instruction']
    )

    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='claude_vision_view_locator_format_iterate_view_inputs',
        formatString='''You are an expert in UI interface analysis. Please analyze a given UI interface based on the user's instructions and respond in the required format as specified in the instruction below.

    Instruction: This UI interface includes several instances of content related to "{v1}", distributed evenly across various positions. Extract each instance of this content and return them in Python list format.

    Guidelines:
    1. Output a list that is directly usable in Python code. Do not include any additional text.
    2. Format Example: ['Content1', 'Content2']. Your response should follow this format.''',
        paramsDesc=['instruction']
    )

    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='claude_vision_view_locator_content_prompt',
        formatString='''What is the text inside the element described as `{v1}` on the screenshot from the computer screen? Only output the content.

    For example:
    [Example Start]
    input:
    What is the text inside the element described as `name`?
    output:
    John Smith
    [Example End]

    Now please begin!''',
        paramsDesc=['description']
    )

    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='claude_vision_view_locator_deduplicate_prompt',
        formatString='''You are going to remove duplicated elements from the current element list if it already exists in the history element list. If an element from current element list already exists in the history element list, please remove it from current element list. `Exists` means the descriptions between history and current ones are semantically similar.

    Example:
    [Example Start]
    input:
    history element list: ['Green Button with text `Hello` in the left-top corner', 'Black Search Icon beside the `Search` text']
    current element list: ['Black Search Icon beside the `Search` text', 'Yellow User Icon at the bottom']

    output:  ['Yellow User Icon at the bottom']
    [Example End]

    Note:
    1. You should output a list that can be parsed. Don't output any other things!
    2. Response Example: ['Green Button with text `Hello` in the left-top corner', 'Black Search Icon beside the `Search` text']. You should respond like this. Don't output any other things!

    current element list: {v1}
    history element list: {v2}

    Now please start!''',
        paramsDesc=['element_descriptions', 'history_elements']
    )
    prompt_manager.add_constant(
        fileName='view_locator.py',
        language='en_US',
        variableName='molmo_vision_view_locator_get_views_by_list_prompt',
        formatString="""
        Please return all views that matches following description: {v1}
        """,
        paramsDesc=['description']
    )
    prompt_manager.save()