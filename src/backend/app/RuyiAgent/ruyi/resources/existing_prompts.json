{"returns_parser": {"zh_CN": {"listPrompt": ["数组，其中每一项是一个{v1}", ["后续的prompt"]], "dictPrompt": ["字典，其中，它的值是{v1}", ["类型列表转换后的字符串"]], "tupleSpecial": ["元组（tuple），你将以list的形式返回它，但是你应当明白，这个元组的长度是固定为{v1}的，其中{v2}", ["元组长度", "每个项的描述列表"]], "tupleElement": ["第{v1}项是一个{v2}", ["第几个变量", "类型列表转换后的字符串"]]}}, "anonTest": {"zh_CN": {"a": ["数组，其aaaaa项是一个{v1}", ["a"]]}}, "view_locator": {"en_US": {"tree_view_locator_locate_view_prompt": ["Given the GUI view tree below, which view best-matches the description \"{v1}\"?", ["description"]], "tree_view_locator_get_views_by_list_prompt": ["Given the GUI view tree below, which views best-match the description \"{v1}\"? \nNote that the description describes a list of views, you should return the list of view descriptions that match this description. For example, if the description is \"Restuarant Listings\", you should return: [\"McCafe, 4.5 stars, $5, 28 min, 4.95 discount rate.\", \"<PERSON> King, 4.2 stars, $4, 20 min, 4.50 discount rate.\", \"Subway, 4.1 stars, $3, 15 min, 4.25 discount rate.\"].{v2} \n Note: 1. You should respond only with a list of string. Example Output: [\"a\", \"b\", \"c\"]. 2. Don't output \"```python!\". 3. Don't output any other things.", ["description", "parent_view_desc"]], "tree_view_locator_deduplicate_prompt": ["You are going to remove duplicated elements from the current element list if it already exists in the history element list. If an element from current element list already exists in the history element list, please remove it from current element list. `Exists` means the descriptions between history and current ones are semantically similar.\n\n    Example:\n    [Example Start]\n    input:\n    history element list: ['<PERSON> Button with text `Hello` in the left-top corner', 'Black Search Icon beside the `Search` text']\n    current element list: ['Black Search Icon beside the `Search` text', 'Yellow User Icon at the bottom']\n\n    output:  ['Yellow User Icon at the bottom']\n    [Example End]\n\n    Note:\n    1. You should output a list that can be parsed. Don't output any other things!\n    2. Response Example: ['<PERSON> Button with text `Hello` in the left-top corner', 'Black Search Icon beside the `Search` text']. You should respond like this. Don't output any other things!\n\n    current element list: {v1}\n    history element list: {v2}\n\n    Now please start!", ["element_descriptions", "history_elements"]], "molmo_vision_view_locator_format_locate_view_inputs": ["You are a GUI agent.\nGiven a screenshot and an element description, you need to locate the element.\nIf the element does not exist, return <|box_start|>null null null null<|box_end|>.\nOutput Format:\n<|box_start|>x1, y1, x2, y2<|box_end|>\nDescription: {v1}\nScreenshot: ", ["description"]], "molmo_vision_view_locator_content_prompt": ["You are a GUI agent.\nGiven a screenshot and a coordinate, you need to describe the element at the coordinate.\nOutput Format:\nA description of the element at the coordinate.\nCoordinate: <|box_start|>x, y<|box_end|>\nScreenshot: ", ["x", "y"]], "molmo_vision_view_locator_get_views_by_list_prompt": ["You are a GUI agent.\nGiven a screenshot, a general description of a set of elements, and a list of matched elements, you need to return the description of elements that are not included in the list.\nOutput Format:\n[description1, description2, ...]\nGeneral description: {v1}\nExisting elements: {v2}\nScreenshot: ", ["description", "history_list"]], "molmo_vision_view_locator_get_views_text_by_list_prompt": ["You are a GUI agent.\nGiven a screenshot, a general description of a set of elements, and a list of matched elements, you need to return the description of elements that are not included in the list.\nOutput Format:\n[description1, description2, ...]\nGeneral description: {v1}\nExisting elements: {v2}\nScreenshot: ", ["description", "history_list"]], "molmo_vision_view_locator_checklist_prompt": ["You are a GUI agent.\nGiven a screenshot and several descriptions, you need to check if the descriptions are correct.\nOutput Format (A list of boolean values):\nTrue/False, True/False, ...\nDescriptions: {v1}\nScreenshot: ", ["condition_list"]], "molmo_vision_view_locator_ensure_prompt": ["You are a GUI agent.\nGiven a screenshot and a description of the target GUI state, you need to return the bbox to click.\nIf the current GUI state is already the target state, return <|box_start|>null null null null<|box_end|>.\nOutput Format:\n<|box_start|>x1, y1, x2, y2<|box_end|>\nDescription: {v1}\nScreenshot: ", ["description"]], "claude_vision_view_locator_format_locate_view_inputs": ["You are going to move mouse onto the UI interface of the image, following the instruction.\n\n    Your instruction:\n    find the {v1}. (Do not take screenshot, I have already sent it to you)", ["instruction"]], "claude_vision_view_locator_format_iterate_view_inputs": ["You are an expert in UI interface analysis. Please analyze a given UI interface based on the user's instructions and respond in the required format as specified in the instruction below.\n\n    Instruction: This UI interface includes several instances of content related to \"{v1}\", distributed evenly across various positions. Extract each instance of this content and return them in Python list format.\n\n    Guidelines:\n    1. Output a list that is directly usable in Python code. Do not include any additional text.\n    2. Format Example: ['Content1', 'Content2']. Your response should follow this format.", ["instruction"]], "claude_vision_view_locator_content_prompt": ["What is the text inside the element described as `{v1}` on the screenshot from the computer screen? Only output the content.\n\n    For example:\n    [Example Start]\n    input:\n    What is the text inside the element described as `name`?\n    output:\n    <PERSON>\n    [Example End]\n\n    Now please begin!", ["description"]], "claude_vision_view_locator_deduplicate_prompt": ["You are going to remove duplicated elements from the current element list if it already exists in the history element list. If an element from current element list already exists in the history element list, please remove it from current element list. `Exists` means the descriptions between history and current ones are semantically similar.\n\n    Example:\n    [Example Start]\n    input:\n    history element list: ['<PERSON> Button with text `Hello` in the left-top corner', 'Black Search Icon beside the `Search` text']\n    current element list: ['Black Search Icon beside the `Search` text', 'Yellow User Icon at the bottom']\n\n    output:  ['Yellow User Icon at the bottom']\n    [Example End]\n\n    Note:\n    1. You should output a list that can be parsed. Don't output any other things!\n    2. Response Example: ['<PERSON> Button with text `Hello` in the left-top corner', 'Black Search Icon beside the `Search` text']. You should respond like this. Don't output any other things!\n\n    current element list: {v1}\n    history element list: {v2}\n\n    Now please start!", ["element_descriptions", "history_elements"]]}}}