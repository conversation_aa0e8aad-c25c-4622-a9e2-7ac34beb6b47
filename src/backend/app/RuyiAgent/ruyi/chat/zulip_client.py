"""
The interfaces to chat with users or other agents.
"""
import zulip
import re
import io
import random
import requests
import base64
from PIL import Image
from threading import Thread
import structlog

from ruyi.utils.interface import RuyiInterface
from .chat_utils import Chat_Client, Chat_Message

logger = structlog.get_logger(__name__)

HELP_MESSAGE = """# RuyiAgent developed by MobileLLM Team
## How to use
To use the bot, simply mention it in a message, e.g. @**{bot}** hello!. The bot will then generate a response and send it back to you.
You can also write a private message to the bot without mentioning it.

## Subcommands
Subcommands are words starting with an exclamation mark, e.g. `!new`.
You can use the following subcommands to control the bot:
- `!help` - show this help message
- `!continue` - continue the previous conversation (the bot will treat each request as a new conversation by default)
- `!gen_img` - generate an image
- `!task` - execute a task
- `!bind` - bind agent to this thread (this thread will be notified when the agent reports something)

## Example usage
- `@{bot} I have a question...` - start a new conversation (previous messages will be ignored)
- `@{bot} !continue Can you summarise previous messages?` - use context from the current conversation
"""
CONTEXT_CLEARED = "context cleared!"


class Zulip_Client(Chat_Client):
    def __init__(self, agent):
        super().__init__(agent)
        from ruyi.agent import RuyiAgent
        assert isinstance(agent, RuyiAgent)
        self._tag = 'chat.client'
        self.bind_user = None
        self.bind_type = None
        self.bind_subject = None
        self._serving_thread = None

    def _open(self):
        try:
            self.client = zulip.Client(
                email=self.agent.config.chat_zulip_email,
                api_key=self.agent.config.chat_zulip_key,
                site=self.agent.config.chat_zulip_site
            )
            self.server_settings = self.client.get_server_settings()
            self.server_url = self.server_settings['realm_uri']
            self.profile = self.client.get_profile()
            if (self.profile.get('code') == 'UNAUTHORIZED'):
                logger.error('Zulip_Client UNAUTHORIZED', action='start zulip client', status='failed')
                self.client = None
            else:
                logger.debug('Zulip_Client started', action='start zulip client', status='done')
                self._serving_thread = Thread(target=self._start_serving)
                self._serving_thread.start()
        except Exception as e:
            logger.exception(f'Zulip_Client not started: {e}', action='start zulip client', status='failed')
            self.client = None

    def _close(self):
        if self._serving_thread is not None:
            self._serving_thread.join()

    def _start_serving(self):
        self.client.call_on_each_event(self._handle_event, event_types=['message'])

    def send_message(self, content, to, subject=None):
        msg = {
            'type': 'stream',
            'to': to,
            'subject': subject,
            'content': content,
        }
        self.client.send_message(msg)

    def send_reply(self, content, previous_message):
        if previous_message['type'] == 'private':
            msg = {
                'type': 'private',
                'to': previous_message['sender_email'],
                'content': content,
            }
        else:
            msg = {
                'type': 'stream',
                'to': previous_message['display_recipient'],
                'subject': previous_message['subject'],
                'content': content,
            }
        self.client.send_message(msg)
    
    def _bind_user(self, msg):
        if msg['type'] == 'private':
            self.bind_type = 'private'
            self.bind_user = msg['sender_email'],
        else:
            self.bind_type = 'stream',
            self.bind_user = msg['display_recipient'],
            self.bind_subject = msg['subject']

    def send_to_user(self, msg_content):
        msg = {
            'type': self.bind_type,
            'to': self.bind_user,
            'subject': self.bind_subject,
            'content': msg_content,
        }
        self.client.send_message(msg)

    def _handle_event(self, event):
        event_type = event['type']
        if event_type != 'message':
            return

        msg = event['message']
        content = msg['content'].strip()

        if msg['sender_email'] == self.client.email:
            # Ignoring message sent by myself
            return

        agent_name = self.agent.config.name
        if msg['type'] != 'private' and not re.search(fr"@\*\*{agent_name}\*\*", content) and not re.search(fr"@{agent_name}", content):
            # Ignoring message not mentioning the bot or sent in private"
            return

        # get subcommands (words starting with exclamation mark)
        subcommands = get_subcommands(content)

        current_time = self.agent.tools.time_tag()
        sender_email = msg['sender_email']
        sender_id = msg['sender_id']
        sender_name = msg['sender_full_name']
        # print("%s (%s); subcommands: %s; content: %s", str(sender_email), str(sender_name), ",".join(subcommands), content)

        # first get rid of the command or mention trigger
        content = re.sub(fr"@\*\*{agent_name}\*\*", "", content)
        content = re.sub(fr"@{agent_name}", "", content)
        content = content.strip()
        content = remove_subcommands(content, subcommands)

        if subcommands and "help" in subcommands:
            help_msg = HELP_MESSAGE.format(bot=agent_name)
            self.send_reply(help_msg, msg)
            return
        
        openai_client = self.agent.fm._openai_client
        model_name = self.agent.config.fm_default_llm
        agent_role = self.agent.config.role_description
        max_previous_messages = self.agent.config.chat_max_previous_messages

        if self.bind_user is None:
            self._bind_user(msg)

        if 'gen_img' in subcommands:
            model_name = 'dall-e-3'

        if 'task' in subcommands:
            task_desc = content
            self.agent.task.submit_task(task_desc, reply_msg=msg)
            self.send_reply(f'task [{task_desc}] submitted', msg)
            return

        if 'bind' in subcommands:
            self._bind_user(msg)
            self.send_reply('binded to this thread.', msg)
            return

        messages = [
            {"role": "system", "content": f"{agent_role}"},
            {"role": "user", "content": f"{content}"},
        ]

        if "clear" in subcommands:
            self.send_reply(CONTEXT_CLEARED, msg)
            return
        
        if "continue" in subcommands:
            messages = self.with_previous_messages(msg, messages, subcommands, max_previous_messages)

        if len(content) > 100:
            content_brief = content[:50] + ' ... ' + content[-50:]
        else:
            content_brief = content
        content_brief = content_brief.replace('\n', '<br>')

        try:
            if model_name == 'dall-e-3':
                img_prompt = []
                for row in messages:
                    if row['role'] == 'user':
                        img_prompt.append(row['content'])
                img_prompt = '\n'.join(img_prompt)
                
                img_quality = 'hd' if 'hd' in subcommands else 'standard'
                img_style = 'natural' if 'natural' in subcommands else 'vivid'
                img_size = '1024x1024'
                if '1024x1792' in subcommands:
                    img_size = '1024x1792'
                if '1792x1024' in subcommands:
                    img_size = '1792x1024'
                response = openai_client.images.generate(
                    model=model_name,
                    prompt=img_prompt,
                    size=img_size,
                    quality=img_quality,
                    style=img_style,
                    n=1,
                )
                
                image_url = response.data[0].url
                # print(f'{sender_id} ({sender_name}); {model_name}; {img_quality}; {img_style}; {img_size}; {content_brief}')

                try:
                    response = requests.get(image_url)
                    if response.status_code == 200:
                        image_bytes = io.BytesIO(response.content)
                        setattr(image_bytes, 'name', f'{current_time}_{sender_id}.png')
                        upload_result = self.client.upload_file(image_bytes)
                        image_url = upload_result['uri']
                        logger.debug(f'Image uploaded: {self.server_url}/{image_url}', action='send_reply', status='success', metadata={'sender_id': sender_id, 'sender_name': sender_name})
                except Exception as e:
                    logger.exception(f'Failed to upload image: {e}', action='send_reply', status='failed', metadata={'sender_id': sender_id, 'sender_name': sender_name})
        
                reply = f'An image generated with prompt `{img_prompt}`:\n[IMG]({image_url})'
            
            else:
                messages = self.convert_messages_vision(messages)
                max_tokens = self.agent.config.chat_max_reply_token
                completion = openai_client.chat.completions.create(
                    messages=messages,
                    model=model_name,
                    max_tokens=max_tokens
                )
                response = completion.choices[0].message.content
                prompt_tokens = completion.usage.prompt_tokens
                completion_tokens = completion.usage.completion_tokens
                # return response, prompt_tokens, completion_tokens
                reply = f'{response}\n(tokens: prompt={prompt_tokens}, completion={completion_tokens}, model={model_name})'
                # print(f'{sender_id} ({sender_name}); {model_name}; prompt_tokens={prompt_tokens}; completion_tokens={completion_tokens}; {content_brief}')
        except Exception as e:
            logger.exception(f'API error: {e}', action='send_reply', status='failed', metadata={'sender_id': sender_id, 'sender_name': sender_name})
            reply = f"API error: {e}"
            
        self.send_reply(reply, msg)

    def with_previous_messages(self, msg, messages, subcommands, max_previous_messages=100):
        client = self.client
        if msg['type'] == 'private':
            query = {
                'anchor': msg['id'],
                'num_before': 100,  # adjust this value as needed
                'num_after': 0,
                'apply_markdown': False,
                'include_anchor': False,
                'narrow': [{'operand': msg['sender_email'], 'operator': 'pm-with'}],
            }
        else:
            narrow = [
                {'operand': msg['display_recipient'], 'operator': 'stream'},
            ]

            # filter to topic by default
            if ("stream" not in subcommands):
                narrow.append({'operand': msg['subject'], 'operator': 'topic'})

            query = {
                'anchor': msg['id'],
                'num_before': 100,  # adjust this value as needed
                'num_after': 0,
                'apply_markdown': False,
                'include_anchor': False,
                'narrow': narrow,
            }

        previous_messages = client.get_messages(query)['messages']
        previous_messages.reverse()
        # print(previous_messages)

        new_messages = messages.copy()

        for i, msg in enumerate(previous_messages):
            if i >= max_previous_messages:
                break
            content = msg['content'].strip()

            # remove mentions of the bot
            content = re.sub(fr"@\*\*{self.agent.config.name}\*\*", "", content)
            # remove token statistics
            content = re.sub(fr"\n\(tokens: prompt=.+\)$", "", content)
            content = content.strip()

            # get subcommands (words starting with exclamation mark)
            subcommands = get_subcommands(content)

            if 'clear' in subcommands:
                break

            # don't remove in previous messages for now, as it breaks with some code blocks
            # content = remove_subcommands(content, subcommands)
            if client.email == msg['sender_email']:
                role = "assistant"
                if content == CONTEXT_CLEARED:
                    break
            else:
                role = "user"

            new_messages.insert(1, {"role": role, "content": content.strip()})
        return new_messages

    # Function to convert messages to gpt4v format
    def convert_messages_vision(self, messages):
        new_messages = []
        # Updated pattern to match file paths with image extensions
        # url_pattern = r'\[IMG\]\(([^\s]+)\)'
        # url_pattern = r'\[\]\(([^\s]+\.(?:jpg|jpeg|png|gif|webp))\)'
        url_pattern = r'\[IMG\]\(([^\s]+)\)|\[.*?\]\(([^\s]+\.(?:jpg|jpeg|png|webp))\)'

        for message in messages:
            new_content = []
            last_index = 0
            for match in re.finditer(url_pattern, message["content"]):
                # Add text before the image URL
                if match.start() != last_index:
                    new_content.append({"type": "text", "text": message["content"][last_index:match.start()]})
                # Add image URL
                image_url = match.group(1) if match.group(1) else match.group(2)
                if image_url.startswith('/user_uploads'):   # user-uploaded images
                    try:
                        server_image_url = f'{self.server_url}/{image_url}'
                        r = self.client.session.get(server_image_url)
                        with Image.open(io.BytesIO(r.content)) as image:
                            image_format = image.format.upper()
                            if image_format not in ['JPEG', 'JPG', 'PNG', 'WEBP']:
                                image_format = 'JPEG'  # Default to JPEG if format is not one of the common types
                            
                            # Convert image to RGB if it's not already in a compatible format
                            if image.mode == 'P' or image.mode == 'RGBA' and image_format in ['JPEG', 'JPG']:
                                image = image.convert('RGB')
                            
                            image_stream = io.BytesIO()
                            image.save(image_stream, format=image_format)
                            image_base64 = base64.b64encode(image_stream.getvalue()).decode("utf-8")
                            image_url = f'data:image/{image_format.lower()};base64,{image_base64}'
                    except Exception as e:
                        logger.exception(f'Failed to convert image: {e}', action='send_reply', status='failed')
                        continue
                new_content.append({"type": "image_url", "image_url": {"url": image_url}})
                last_index = match.end()
            # Add any remaining text after the last image URL
            if last_index != len(message["content"]):
                new_content.append({"type": "text", "text": message["content"][last_index:]})
            new_messages.append({"role": message["role"], "content": new_content})
        return new_messages

    def is_admin(self, msg):
        member = self.client.get_user_by_id(msg['sender_id'])
        return member.get("user", {}).get("is_admin")


def get_subcommands(content):
    content_chunks = content.strip().split()
    subcommands = [word.lower().replace("!", "")
                   for word in content_chunks if word.startswith("!")]
    return subcommands


def remove_subcommands(content, subcommands):
    for subcommand in subcommands:
        content = re.sub(f"!{subcommand} ", "", content,
                         flags=re.IGNORECASE).strip()
        content = re.sub(f"!{subcommand}", "", content,
                         flags=re.IGNORECASE).strip()
    return content

