"""
The interfaces to chat with users or other agents.
"""
from ruyi.utils.interface import RuyiInterface

class Chat_Interface(RuyiInterface):
    def __init__(self, agent):
        super().__init__(agent)
        from ruyi.agent import Ruyi<PERSON>gent
        assert isinstance(agent, RuyiAgent)
        self._tag = 'chat'
        self.backend = agent.config.chat_backend
        self.client = None

    def _open(self):
        if self.agent.config.chat_enable and self.backend == 'zulip':
            from .zulip_client import Zulip_Client
            self.client = Zulip_Client(self.agent)
            self.client._open()
    
    def _close(self):
        if self.client is not None:
            self.client._close()

    def send_to_user(self, message):
        if self.client is not None:
            self.client.send_to_user(message)

    def send_reply(self, message, previous_message):
        if self.client is not None:
            self.client.send_reply(message, previous_message)

