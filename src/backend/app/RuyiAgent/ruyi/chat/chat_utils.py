"""
The interfaces to chat with users or other agents.
"""
from ruyi.utils.interface import RuyiInterface
from ruyi.utils import debug


class Chat_Message:
    def __init__(self, content=None, timestamp=None, sender=None, recipient=None, **kwargs):
        self.content = content
        self.timestamp = timestamp
        self.sender = sender
        self.recipient = recipient


class Chat_Handler(RuyiInterface):
    def __init__(self, agent):
        super().__init__(agent)
    
    def _handle_message(self, message_in):
        debug.print_method_name_with_message('not implemented')


class Chat_Client(RuyiInterface):
    """
    Each chat client should implement:
    - `_send` method, which sends a message
    """
    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'chat.client'
        self.owner_id = None
        self.self_id = None
        self.handlers = []

    def _compose_message(self, content=None, timestamp=None, sender=None, recipient=None, **kwargs):
        return Chat_Message(content=content, timestamp=timestamp, sender=sender, recipient=recipient, **kwargs)
    
    def _send(self, message):
        """
        The function to actually send the message
        """
        debug.print_method_name_with_message('not implemented')
        pass

    def notify_owner(self, content):
        message_out = self._compose_message(
            content = content,
            timestamp = self.agent.tools.time_now(),
            sender=self.self_id,
            recipient=self.owner_id
        )
        self._send(message_out)

    def send_message(self, content, recipient):
        message_out = self._compose_message(
            content = content,
            timestamp = self.agent.tools.time_now(),
            sender=self.self_id,
            recipient=recipient
        )
        self._send(message_out)

    def register_handler(self, handler):
        self.handlers.append(handler)

