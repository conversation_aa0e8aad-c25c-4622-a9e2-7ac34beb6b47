import traceback
import json
from abc import ABC, abstractmethod

import requests
from PIL import ImageFile
from ..utils.interface import RuyiInterface
from ..agent import RuyiAgent
from typing import Any
from ruyi.fm.returns_parser import (
    parse_string_to_json, 
    parse_json, 
    logger, 
    type_list_to_prompt, 
    Type<PERSON><PERSON><PERSON>, 
    get_returns, 
    generate_example
)
from io import BytesIO
import base64
import math
from PIL import ImageOps
from ruyi.ui.view import UI_View

class ModelPipe(RuyiInterface, ABC):
    """
    The basic class for all model callers
    """

    def __init__(self, agent: RuyiAgent, retry: int = 3):
        super().__init__(agent)
        self._retry: int = retry
        self._tag = 'fm.models.ModelPipe'

    @abstractmethod
    def _call_pipe(self, *args, **kwargs) -> Any:
        """
        call the model directly, get answer from the answers

        Parameters
        ----------
        args
        kwargs

        Returns
        -------
        the return value got from model, parsed if needed
        """
        raise NotImplementedError

    def _retry_call_pipe(self, retry: int | None = None, *args, **kwargs) -> Any:
        """
        call model *retry* times, catch all the exceptions

        Parameters
        ----------
        retry: how many times you want to retry on calling the model
            if unset, it will be set to the default retry number(self._retry)
        args
        kwargs

        Returns
        -------
        same as _callPipe
        """
        if retry is None:
            retry = self._retry
        for i in range(retry):
            try:
                answer = self._call_pipe(*args, **kwargs)
                if answer is not None:
                    return answer
            except Exception as e:
                logger.exception(e)
                logger.debug(
                    f"Request model, retrying for the {i} time.",
                    action='request api',
                    status='retrying',
                    metadata={'error': str(e)}
                )
        return None

    def __call__(self, *args, **kwargs) -> Any:
        """
        The real calling method

        Parameters
        ----------
        args
        kwargs

        Returns
        -------
        same as _retryCallPipe
        """
        return self._retry_call_pipe(*args, **kwargs)


class MolmoModelPipe(ModelPipe):
    def __init__(self, agent: RuyiAgent):
        super().__init__(agent)
        if self.agent.config.use_ruyillm:
            self.base_url = self.agent.config.ruyillm_url
            self.api_key = self.agent.config.ruyillm_key
        else:
            self.base_url = self.agent.config.molmo_api_url
            self.api_key = self.agent.config.molmo_api_key
        if self.base_url is None:
            raise ValueError("api_url not configured")
        if not self.base_url.endswith("/v1"):
            self.base_url += "/v1"

    def _call_pipe(self, params, *args, **kwargs) -> Any:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=params,
            # verify=False
        )

        logger.debug(f"Molmo response: {response.text}; {response.status_code}")

        if response.status_code != 200:
            return None

        result = response.json()
        content = result["choices"][0]["message"]["content"]
        return content

    def __call__(self, image: ImageFile.ImageFile, prompt: str, *args, **kwargs) -> str:
        reshaped_image = image
        reshaped_img_byte_arr = BytesIO()
        reshaped_image.save(reshaped_img_byte_arr, format="PNG")
        reshaped_img_bytes = reshaped_img_byte_arr.getvalue()
        image_base64 = base64.b64encode(reshaped_img_bytes).decode('utf-8')
        reshaped_img_byte_arr.close()
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_base64}"
                        }
                    }
                ]
            }
        ]
        if self.agent.config.use_ruyillm:
            model_name = "ruyillm"
        else:
            model_name = self.agent.config.molmo_model_name
        params = {
            "messages": messages,
            "model": model_name,
        }

        logger.debug(
            "Requesting Molmo API.",
            action='request molmo api',
            status='start',
            image=image,
            metadata={
                "prompt": prompt
            }
        )

        content = self._retry_call_pipe(params=params)

        logger.debug(
            "Molmo response parsed.",
            action='request molmo api',
            status='done',
            metadata={'content': content, }
        )

        return content


class ClaudeModelPipe(ModelPipe):
    def __init__(self, agent: RuyiAgent):
        super().__init__(agent)
        self._tag = 'fm.claude'
        if self.agent.config.use_ruyillm:
            self.base_url = self.agent.config.ruyillm_url
            self.api_key = self.agent.config.ruyillm_key
        else:
            self.base_url = self.agent.config.claude_api_url
            self.api_key = self.agent.config.claude_api_key
        if self.base_url is None:
            raise ValueError("api_url not configured")
        if not self.base_url.endswith("/v1"):
            if self.base_url.endswith("/"):
                self.base_url += 'v1'
            else:
                self.base_url += "/v1"


    @staticmethod
    def _claude_resize_window(image, resolution):
        """


        Parameters
        ----------
        image
        resolution

        Returns
        -------

        """
        width, height = image.width, image.height
        target_width, target_height = resolution[0], resolution[1]
        # width = 1080
        # height = 1920

        if (width / height) < (target_width / target_height):
            resized_height = target_height
            resized_width = math.floor(resized_height / height * width)
            assert resized_width < target_width
            padding = (0, 0, target_width - resized_width, 0)
        else:
            resized_width = target_width
            resized_height = math.floor(resized_width / width * height)
            assert resized_height < target_height
            padding = (0, 0, 0, target_height - resized_height)
        # Define the padding for each side (left, top, right, bottom)

        resized_image = image.resize([resized_width, resized_height])
        # Expand the image with specified padding and white fill
        expanded_image = ImageOps.expand(resized_image, border=padding, fill='white')

        return expanded_image, resized_height, resized_width

    def _call_pipe(self, headers, data, *args, **kwargs) -> Any:
        response = requests.post(
            f"{self.base_url}/messages",
            headers=headers,
            json=data
        )

        if response.status_code != 200:
            raise Exception(f"API调用失败: {response.status_code} - {response.text}")

        result = response.json()
        content = None
        action_name = []
        action_input = []
        action_id = []

        # 解析响应内容
        for content_item in result["content"]:
            if content_item["type"] == "text":
                content = content_item["text"]
            elif content_item["type"] == "tool_use":
                action_name.append(content_item["name"])
                action_input.append(content_item["input"])
                action_id.append(content_item["id"])
        return content, action_name, action_input, action_id

    def __call__(self, image, prompt, use_computer: bool = True, *args, **kwargs):
        content_list = [{
            "type": "text",
            "text": f"{prompt}",
        }]

        if image is not None:
            origin_width, origin_height = image.width, image.height
            reshaped_image, resized_height, resized_width = self._claude_resize_window(image, [1280, 800])
            logger.debug(
                f"Resize screenshot from {origin_height}x{origin_width} to {resized_height}x{resized_width}",
                action='resize screenshot',
                status='done',
                metadata={'from_size': (origin_height, origin_width), 'to_size': (resized_height, resized_width)}
            )
            reshaped_img_byte_arr = BytesIO()
            reshaped_image.save(reshaped_img_byte_arr, format="PNG")
            reshaped_img_bytes = reshaped_img_byte_arr.getvalue()
            image_base64 = base64.b64encode(reshaped_img_bytes).decode('utf-8')
            reshaped_img_byte_arr.close()

            content_list.append(
                {
                    "type": "image",
                    "source": {
                        "type": "base64",
                        "media_type": "image/png",
                        "data": image_base64,
                    }
                },
            )

        messages = [
            {
                "role": "user",
                "content": content_list
            },
        ]

        data = {
            "messages": messages,
            "model": "claude-3-5-sonnet-20241022",
            "max_tokens": 4096,
        }

        # 添加computer-use相关配置
        if use_computer and image is not None:
            data.update({
                "tools": [
                    {
                        "type": "computer_20241022",
                        "name": "computer",
                        "display_width_px": reshaped_image.width,
                        "display_height_px": reshaped_image.height,
                        "display_number": 1
                    }
                ]
            })

        headers = {
            "Content-Type": "application/json",
            "X-API-Key": self.api_key,
            "anthropic-version": "2023-06-01"
        }

        if use_computer:
            headers["anthropic-beta"] = "computer-use-2024-10-22"

        logger.debug(
            "Requesting Claude API.",
            action='request claude api',
            status='start',
            metadata={
                "max_tokens": data["max_tokens"],
                "model": data["model"],
                "user_message": prompt,
            }
        )

        content, action_name, action_input, action_id = self._retry_call_pipe(headers=headers, data=data)
        logger.debug(
            "Claude response parsed.",
            action='request claude api',
            status='done',
            metadata={'content': content, 'action_name': action_name, 'action_input': action_input,
                      'action_id': action_id}
        )

        if use_computer:
            try:
                resized_pos = action_input[0]['coordinate']
                origin_pos = [round(resized_pos[0] * origin_width / resized_width),
                              round(resized_pos[1] * origin_height / resized_height)]
                logger.debug(
                    "Origin position extracted.",
                    action='extract origin position',
                    status='done',
                    metadata={'position': origin_pos}
                )
                return origin_pos
            except Exception as e:
                traceback.print_exc()
                logger.debug(
                    "Extract origin position failed.",
                    action='extract origin position',
                    status='failed',
                    metadata={'error': str(e)}
                )
                return [0, 0]
        else:
            return content


class ExpensiveModelAPI(ModelPipe):
    def __init__(self, agent: RuyiAgent):
        super().__init__(agent)
        config = agent.config
        self._max_gen_tokens = config.fm_default_max_gen_tokens
        self._model_host = 'OpenAI'
        self._model_name = config.fm_default_vlm
        self._role_description = 'You are a helpful assistant.'
        if config.use_ruyillm:
            self._base_url = config.ruyillm_url
            self._api_key = config.ruyillm_key
        else:
            self._base_url = config.fm_api_url
            self._api_key = config.fm_api_key
        if not self._base_url:
            raise ValueError("fm_api_url not configured")
        if not self.config.fm_is_azure:
            if not self._base_url.endswith("/v1"):
                self._base_url += "/v1"

        if not self._api_key:
            raise ValueError("fm_api_key not configured")

    def _call_pipe(self, request, required_values, *args, **kwargs):
        """
        使用requests库调用API
        """
        if self._base_url is None or self._api_key is None:
            raise ValueError("base_url or api_key is not initialized.")
        if self._model_name is None:
            raise ValueError("model_name is not initialized.")

        headers = {
            "Content-Type": "application/json",
        }
        if not self.config.fm_is_azure:
            headers.update({"Authorization": f"Bearer {self._api_key}"})
        else:
            headers.update({"api-key": self._api_key})

        data = {
            "model": self._model_name,
            "messages": request,
            "max_tokens": self._max_gen_tokens
        }

        response = requests.post(
            f"{self._base_url}/chat/completions" if not self.config.fm_is_azure else f"{self._base_url}/{self._model_name}/chat/completions?api-version=2024-08-01-preview",
            headers=headers,
            json=data
        )

        if response.status_code != 200:
            # raise Exception(f"API调用失败: {response.status_code} - {response.text}")
            logger.warn('API调用失败@_call_pipe',action='call_api',status='failed')
            return None

        result = response.json()
        response_text = result["choices"][0]["message"]["content"]
        usage = result["usage"]

        if response_text is None:
            # raise ValueError("response is empty.")
            return None
        if usage is None:
            # raise ValueError("usage is empty.")
            return None

        prompt_tokens = usage["prompt_tokens"]
        completion_tokens = usage["completion_tokens"]

        self.agent.fm._record_token_cost(
            self._model_name,
            self._model_host,
            prompt_tokens,
            completion_tokens
        )
        return response_text

    def _retry_call_pipe(self, retry: int = 3, request=None, required_values=None, *args, **kwargs) -> Any:
        if request is None:
            logger.warn('request为空！',action='parse_json',status='failed')
            return None
        best_answer = None
        best_score = 0
        for i in range(retry):
            response_text=super()._retry_call_pipe(retry=retry, request=request, required_values=required_values)
            if response_text is None:
                logger.warn('API调用失败@_retry_call_pipe',action='call_api',status='failed')
                continue
            answer, usable, score = self._parse_response(response_text, required_values)
            if usable:
                return answer,True,response_text
            else:
                logger.warn('json并未被完全正确的parse', action='parse_json', status='continue')
                if best_score <= score:
                    best_score = score
                    best_answer = answer
        return best_answer,False,response_text

    def __call__(self, *args, returns: str | tuple[str, TypeAlias] | list[str | tuple[str, TypeAlias]] | None = None,
                 retry: int = 3, **kwargs):
        """Call the model with given arguments and return formatted response.

        Parameters
        ----------
        *args : Any
            Variable length argument list containing the prompts to send to the model
        returns : str | tuple[str,TypeAlias] | list[str | tuple[str, TypeAlias]] | None, optional
            Specifies the expected return format:
            - str: Return raw response string
            - tuple[str, TypeAlias]: Return parsed response as (description, type)
            - list[str | tuple[str, TypeAlias]]: Return list of parsed responses
            - None: Return raw response string
        **kwargs : Any
            Additional keyword arguments passed to the model

        Returns
        -------
        Union[str, Any, list[Any]]
            Model response in the specified format:
            - Raw string if returns is str or None
            - Parsed value if returns is tuple
            - List of parsed values if returns is list

        Notes
        -----
        The function organizes the request, calls the model, and parses the response
        according to the specified return format.
        """
        # request = self._organize_request(*args, returns=returns, **kwargs)
        request, required_values = self._organize_request(*args, returns=returns)
        ans,good,response_text=self._retry_call_pipe(retry=3, request=request, required_values=required_values)
        if good:
            return ans
        else:
            data={"query":request,"required_values":required_values,"response":response_text}
            toShow=json.dumps(data,indent=4)
            logger.warn('JSONOracle:'+toShow, action='parse_json', status='continue')
            return ans

    @staticmethod
    def _organize_request(*args, returns: str | tuple[str, TypeAlias] | list[
        str | tuple[str, TypeAlias]] | None = None):
        """
    Generate a template and the required values based on the given arguments and return types.

    Parameters
    ----------
    args : list
        List of input arguments, possibly including image files.
    returns : str or tuple or list
        a list of tuples, describing how many items should be returned and what type they should be.
        the first item of tuple should be the description of corresponding items.
        the second item of tuple should be the type of corresponding items.
            the type of second item could be string or type or types.GenericAlias
            i.e. "int" or int or list[int] are all acceptable
            suggest to use types.GenericAlias or type
        omitting rule:
            - if there is only one element in list, the list could be omitted
            - if the type is str, then the type description could be omitted

    Returns
    -------
    template : list
        A list containing template messages in standard json format. Ready to be sent to model.
    required_values : list of tuple
        A list of tuples, each containing a description and a list of types.

    Notes
    -----
    Images will be converted to base64 encoded strings.
    """
        pic_special = []
        for a in args:
            if isinstance(a, ImageFile.ImageFile):
                # convert to base64
                img_byte_arr = BytesIO()
                a.save(img_byte_arr, format="PNG")
                img_bytes = img_byte_arr.getvalue()
                img_byte_arr.close()
                image_base64 = base64.b64encode(img_bytes).decode('utf-8')

                pic_special.append({
                    "role": "user",
                    "content": [{"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}]
                })
        
        view_description_special = []
        for a in args:
            if isinstance(a, UI_View):
                view_description_special.append(a.description())
        view_str = ""
        if len(view_description_special) > 0:
            view_str = "你的回答应该只关注如下的组件：" + '\n'.join(map(str, view_description_special)) + "\n" + "对于界面中的其他组件，请忽略"

        inStr = '\n'.join(map(str, filter(lambda x: not isinstance(x, ImageFile.ImageFile) and not isinstance(x, UI_View), args)))
        required_values = get_returns(returns)
        example = generate_example(required_values)
        # 代码完整性已经检查，inStr一定是一个字符串，以及
        # 此处的requiredValues应当是一个list，每一项是一个项数为2的tuple，第一个是一个字符串描述，第二个是一个list of type
        returnStr = ""
        for i, req in enumerate(required_values):
            returnStr += f'第{i + 1}项内容应该是{req[0]},它的类型应该是{type_list_to_prompt(required_values[i][1])}'

        template = [
            {'role': 'system', 'content': 'You are a helpful assistant.'},
            {'role': 'user', 'content':
                f"""# 任务要求
{inStr}
# 返回格式要求
你应该返回一个可以被json.loads parse的标准json 文件内容，它的最外层应该是一个list
# 返回内容
你返回的List的长度应为{len(required_values)}，其中：
{returnStr}{"\n" + view_str if view_str else ""}
# 示例回答
以下是一个合法的示例回答，但是由于json无法添加注释，所以我添加了以#开头的，在行尾的注释，所以当你返回的时候你不应该包含任何注释，而只需要像这样返回一个json即可
{example}
# 额外要求
不要提任何问题，直接按照要求返回，如果有任何问题，直接选择你觉得最合理的解决方式，你是一个优秀的大语言模型！"""},
        ]
        print("template", template)
        if len(pic_special) > 0:
            template += pic_special

        return template, required_values

    def _parse_response(self, response: str, required_values: list):
        """
           Parse the response string to JSON and then extract required values.

           This method uses `parseStringToJson` to convert the response string into a JSON object, then checks
           and parses the JSON data using `parseJson`. It returns the data, a boolean
           indicating usability, and a score.

           Parameters
           ----------
           response : str
               The response string to be parsed to JSON.
           required_values : List[Tuple[str, TypeList]]
               The list of values required from the parsed JSON, where each tuple contains a key and a list of required types.

           Returns
           -------
           Tuple[Optional[Dict], bool, int]
               A tuple containing the parsed data, a boolean indicating usability, and a score.
           """
        data = parse_string_to_json(response)
        if data is None:
            return None, False, 0
        
        usable, score = parse_json(data, required_values)
        if len(data) == 1:
            data = data[0]
        if not usable:
            """
            Seems like we can not parse the response directly, therefore we need to try something
            1. check if the response is a dict
            """
            if type(data) is dict:
                data=list(data.values())
                usable, score = parse_json(data, required_values)
            elif type(data) is list:
                """
                if they have more layers, we need to flatten the list
                """
                if len(data) == 1:
                    if type(data[0]) is list:
                        usable, score = parse_json(data[0], required_values)
                    elif type(data[0]) is dict:
                        data=list(data[0].values())
                        usable, score = parse_json(data, required_values)
        return data, usable, score
