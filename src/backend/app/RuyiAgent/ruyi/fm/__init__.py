"""
The interfaces to get answers from foundation models.
"""
from ..utils.interface import RuyiInterface
from ..utils import debug
from ..agent import RuyiAgent
from .models import ExpensiveModelAPI, MolmoModelPipe, <PERSON><PERSON>odel<PERSON>ipe
from typing import cast


class FM_Interface(RuyiInterface):
    """
    关于如何添加你自己的模型，请参照RuyiDoc中的"如何添加一个新的模型？.md"
    For how to add your model, please search"how to add a new model" in RuyiDoc
    """
    def __init__(self, agent: RuyiAgent):
        super().__init__(agent)
        self._tag = 'fm'
        self.models={"ExpensiveModel": ExpensiveModelAPI(agent),
                     "MolmoModel":<PERSON>lmoModelPipe(agent),
                     "ClaudeModel":<PERSON><PERSON>odel<PERSON><PERSON><PERSON>(agent)}

        # next will remove the llm and vlm api
        self.llm = self.models["ExpensiveModel"]
        self.vlm = self.models["ExpensiveModel"]
        self.query = self.models["ExpensiveModel"]
        self.base_url = None
        self.api_key = None
        self.molmo = self.models["MolmoModel"]
        self.claude = self.models["ClaudeModel"]

    def _open(self):
        if self.agent.config.use_ruyillm:
            self.base_url = self.agent.config.ruyillm_url
            self.api_key = self.agent.config.ruyillm_key
        else:
            self.api_key = self.agent.config.fm_api_key
            self.base_url = self.agent.config.fm_api_url

        if not self.base_url.endswith("/v1"):
            self.base_url += "/v1"

        for models in self.models.values():
            models._open()

    def _close(self):
        for models in self.models.values():
            models._close()

    def _record_token_cost(self, model, url, prompt_tokens, completion_tokens):
        """
        Parameters
        ----------
        model
        url
        prompt_tokens
        completion_tokens

        Returns
        -------
        This function is not implemented yet.
        """
        # debug.print_method_name_with_message('not implemented')
        pass
