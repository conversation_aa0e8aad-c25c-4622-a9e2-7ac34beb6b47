#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
@Project : RuyiAgent 
@File    : operate_input
<AUTHOR> <PERSON><PERSON>
@Date    : 2024/11/20
'''
import json
import types
from typing import List
import structlog
from ruyi.utils.prompt_manager import prompt_manager
logger = structlog.get_logger(__name__)
# 如果需要添加基础类型或者复合类型，请同步修改typeStringGenerate
basic_types = [str, int, float, bool]
complex_types = [list, tuple, dict, set]
basic_types_dict = {t.__name__: t for t in basic_types}
complex_types_dict = {t.__name__: t for t in complex_types}

TypeList = List
# 这里的typeList是一个List，其中，每一项可以是一个type（不可以是types.GenericAlias）也可以是一个typeList（递归）
JsonAnswer = List
# 这里的JsonAnswer是一个Json返回类型，通常是，或者说assume to be List
TypeAlias = str | type | types.GenericAlias

def _generate_exmaple(type_list:TypeList,indent:int)->str:
    """
    Generate an example of the type list
    """
    if type(type_list) is type:
        type_list = [type_list]
    if len(type_list) == 1:
        if type_list[0] in basic_types:
            if type_list[0] is str:
                return '\t'*indent+'"a string"\n'
            elif type_list[0] is int:
                return '\t'*indent+'123\n'
            elif type_list[0] is float:
                return '\t'*indent+'123.456\n'
            elif type_list[0] is bool:
                return '\t'*indent+'true\n'
        else:
            logger.warn("不支持的类型",action='generate_example',status='continue')
            return '\t'*indent+'[]\n'
    else:
        if type_list[0] is list:
            curr_ans=_generate_exmaple(type_list[1:],indent+1)
            curr_ans=curr_ans[:-1]+',\n'
            return '\t'*indent+'[\n'+curr_ans*3+'\t'*(indent+1)+"# 我这里就放了三个元素，具体有几个看情况\n"+'\t'*indent+']\n'
        elif type_list[0] is tuple:
            ans='\t'*indent+'[\n'
            for i in range(len(type_list[1])):
                ans+=_generate_exmaple(type_list[1][i],indent+1)
                ans=ans[:-1]+",\n"
            ans=ans[:-2]+'\n'+'\t'*indent+']\n'
            return ans
        elif type_list[0] is dict:
            ans='\t'*indent+'{\n'
            curr_ans=""
            curr_ans+='\t'*indent+'\"key\":\n'
            curr_ans+=_generate_exmaple(type_list[1:],indent+1)
            curr_ans=curr_ans[:-2]+',\n'
            ans+=curr_ans*3
            ans+='\t'*indent+'# 我这里就放了三个key和value，具体有几个看情况\n'
            ans+='\t'*indent+'}\n'
            return ans
        else:
            logger.warn("不支持的类型",action='generate_example',status='continue')
            return '\t'*indent+'[]\n'


def generate_example(required_values:List[tuple[str,TypeList]])->str:
    """
    Generate an example of the required values
    """
    
    example = "[\n"
    for i in range(len(required_values)):
        example += f'\t# 第{i+1}项内容应该是{required_values[i][0]},它的类型应该是{type_list_to_prompt(required_values[i][1])}\n'
        example += _generate_exmaple(required_values[i][1],indent=1)
    example += "]\n"
    return example

def get_returns(returns:TypeAlias)->TypeList:
    """
    translate types from str, type, types.GenericAlias to list

    Parameters
    ----------
    returns: it could be basic_types(refer to variable with the name) or complex_types(refer to variable with the name),
    if it is complex_types, then it must complete by a basic_types,i.e. list[str]
    - a complex_types with different types inside it is NOT SUPPORTED
    - the key for the dict has to be str

    Returns
    -------
    a list of types, should be used in typeListToPrompt or typeListToString
    """
    requiredValues = []
    if returns is None:
        logger.warn("未接收到返回类型", action='parse_type', status='continue')
        requiredValues = [("", [str])]
    if type(returns) is str:
        # 只有一个字符串，说明这个字符串是描述，默认类型为字符串
        requiredValues = [(returns, [str])]
    elif type(returns) is tuple:
        # 只有一个tuple，说明只有一个期待的返回值，第一个内容是其描述并且跟随了它的类型
        if len(returns) == 2:
            requiredValues = [(returns[0], parse_string_to_type_list(returns[1]))]
        elif len(returns) == 1:
            requiredValues = [(returns[0], [str])]
        else:
            logger.warn("")
            requiredValues = [(str(returns), [str])]
    elif type(returns) is list:
        for ret in returns:
            if type(ret) is str:
                requiredValues.append((ret, [str]))
            elif type(ret) is tuple:
                if len(ret) == 2:
                    requiredValues.append((ret[0], parse_string_to_type_list(ret[1])))
                elif len(ret) == 1:
                    requiredValues.append((ret[0], [str]))
                else:
                    logger.warn("")
                    requiredValues.append((str(ret), [str]))
            else:
                logger.warn("在returns的list中有错误的参数类型！该项将被放入作为描述，返回类型强制为str")
                requiredValues.append((str(ret), [str]))
    else:
        logger.warn("错误的参数类型！将被放入作为描述，返回类型强制为str")
        requiredValues = [(str(returns), [str])]
    return requiredValues

def type_list_to_prompt(typeList: TypeList) -> str:
    """
       Convert typeList to prompt

       Parameters
       ----------
       typeList : TypeList
           The type List to convert.

       Returns
       -------
       str
           a prompt representing the typelist
    """
 
    if len(typeList) == 1:
        return typeList[0].__name__
    if typeList[0] is list:
        return prompt_manager.get_string('returns_parser', 'listPrompt', [type_list_to_prompt(typeList[1:])])
    elif typeList[0] is dict:
        return prompt_manager.get_string('returns_parser', 'dictPrompt', [type_list_to_prompt(typeList[1:])])
    elif typeList[0] is tuple:
        tupleLength = str(len(typeList[1]))
        tupleDetails = ""
        for i in range(len(typeList[1])):
            tupleDetails += prompt_manager.get_string(
                'returns_parser', 'tupleElement', [i + 1, type_list_to_prompt(typeList[1][i])])

        return prompt_manager.get_string('returns_parser', 'tupleSpecial', [tupleLength, tupleDetails])

def _string_to_type_list(typeStr: str) -> TypeList:
    """
   Convert type string to a list of types.

   Parameters
   ----------
   typeStr : str
       The type string to convert.

   Returns
   -------
   TypeList
       A list representation of the type or types.

   Raises
   ------
   Exception
       Raised if the type string is invalid.
   Notes
   -----
   - This function handles both basic and complex types.
   - Complex types include dictionaries, tuples, sets, and lists.
   - The function assumes 'str' for any invalid type string.

   Examples
   --------
   - For a basic type string without brackets:
       >>> _string_to_type_list('int')
       [<class 'int'>]

   - For a complex type string with nested types:
       >>> _string_to_type_list('dict[str,int]')
       [<class 'dict'>, <class 'int'>]
   """
    if '[' not in typeStr:
        typeStr = typeStr.replace(']', '')
        typeStr = typeStr.replace(',', '')
        if typeStr in basic_types_dict.keys():
            return [basic_types_dict[typeStr]]
        elif typeStr in complex_types_dict.keys():
            return [complex_types_dict[typeStr], str]
        else:
            logger.warn("Invalid type: %s, assume to be str" % typeStr, action='parse_type', status='continue')
            return [str]
    else:
        currType = typeStr[:typeStr.index('[')]
        if currType in basic_types_dict.keys():
            logger.warn("基础类型不可以再展开，假设为str", action='parse_type', status='continue')
            return [str]
        elif currType in complex_types_dict.keys():
            if currType == 'dict':
                currType = dict
                # 在json中，只接受string作为key类型！
                # https://www.json.org/json-en.html
                return [dict] + _string_to_type_list(typeStr[typeStr.index(',') + 1:-1])
            elif currType == 'tuple':
                currType = tuple
                typeStr = typeStr[typeStr.index('[') + 1:-1]
                bracketLevel = 0
                tupleElements = []
                lastI = 0
                for i in range(len(typeStr)):
                    c = typeStr[i]
                    if c == ',' and bracketLevel == 0:
                        if lastI < i:
                            tupleElements.append(typeStr[lastI:i])
                            lastI = i + 1
                        else:
                            logger.warn("不要在tuple里连续放两个逗号！", action="tuple检测", status='continue')
                    if c == '[':
                        bracketLevel += 1
                    if c == ']':
                        bracketLevel -= 1
                        if bracketLevel < 0:
                            logger.warn("typeStr层级错误", action='typeStrParse', status='continue')
                            break
                if lastI < len(typeStr):
                    tupleElements.append(typeStr[lastI:])
                if len(tupleElements) == 0:
                    logger.warn("错误！元组中必须有内容！", action="parse元组", status='continue')
                    tupleElements = [str]
                for i in range(len(tupleElements)):
                    tupleElements[i] = _string_to_type_list(tupleElements[i])
                return [tuple, tupleElements]
            else:
                # set 和 list都是list
                return [list] + _string_to_type_list(typeStr[typeStr.index('[') + 1:-1])
        raise Exception


def parse_string_to_type_list(typeStr: TypeAlias) -> TypeList:
    """
    Convert TypeAlias to a list of type

    Parameters
    ----------
    typeStr : TypeAlias
        The string or type to convert. It can be a type name string, a type object, or a generic alias object.

    Returns
    -------
    TypeList
        A list of the converted types.
    Raises
    ------
    None

    Notes
    -----
    - If `typeStr` is a type object, its name will be extracted and converted to a string.
    - If `typeStr` is a generic alias object, it will be converted to a string.
    - If `typeStr` is neither a string, type, nor a generic alias object, a warning will be logged.

    Examples
    --------
    >>> parse_string_to_type_list('int')
    [<class 'int'>]

    >>> parse_string_to_type_list(dict)
    [<class 'dict'>, <class 'str'>]

    >>> parse_string_to_type_list(dict[str,list[tuple[int,str]]])
    [<class 'dict'>, <class 'list'>, <class 'tuple'>, [[<class 'int'>], [<class 'str'>]]]

    """
    if type(typeStr) is type:
        typeStr = typeStr.__name__
    elif type(typeStr) is types.GenericAlias:
        typeStr = str(typeStr)
    elif type(typeStr) is str:
        pass
    else:
        logger.warn("不明确的类型", action='检查类型', status='continue')
        typeStr = str(typeStr)
    typeStr = typeStr.lower()
    typeStr = typeStr.replace(' ', '')
    return _string_to_type_list(typeStr)


def parse_string_to_json(response: str) -> List | None:
    """
    Parse the given response to extract JSON data.

    Parameters
    ----------
    response : str
        The response string that may contain JSON data.

    Returns
    -------
    dict or None
        Parsed JSON data if available, otherwise None.

    Notes
    -----
    The function first checks if the response contains code blocks by looking for '```'.
    If found, it will attempt to extract and parse each segment as JSON.
    If no code blocks are found, it will try to parse the entire response as JSON.
    In both cases, if JSON parsing fails, None is returned.

    Examples
    --------
    >>> response = '```json\\n{"key": "value"}\\n```'
    >>> parse_string_to_json(response)
    {'key': 'value'}

    >>> response = '{"key": "value"}'
    >>> parse_string_to_json(response)
    {'key': 'value'}

    >>> response = 'invalid response'
    >>> parse_string_to_json(response)
    None
    """
    data = None
    if '```' in response:
        result = response.split('```')
        data = None
        for r in result:
            try:
                if r.startswith('json'):
                    r = r[len('json'):]
                data = json.loads(r)

            except json.decoder.JSONDecodeError:
                pass
        if data is None:
            return None
    else:
        try:
            data = json.loads(response)
        except json.decoder.JSONDecodeError:
            return None
    return data


def json_type_check(raw, requiredValues: TypeList) -> tuple[bool, float]:
    """
    Check if the JSON data matches the required types specified in requiredValues.

    Parameters
    ----------
    raw : Any
        The JSON data to be checked.
    requiredValues : TypeList
        A list of types that the JSON data is checked against.

    Returns
    -------
    Tuple[bool, float]
        A tuple containing a boolean indicating if the check passed and a score representing the check's success level.

    Examples
    --------
    >>> json_type_check(123, [int])
    (True, 1)
    >>> json_type_check("string", [str])
    (True, 1)
    >>> json_type_check(123.45, [float])
    (True, 1)
    >>> json_type_check([1, 2, 3], [list, int])
    (True, 2.0)
    >>> json_type_check({"address":["aaa",'bbb']},[dict,list,str])
    (True, 3.0)
    """
    curr = requiredValues[0]
    if curr in basic_types:
        if curr is str:
            if type(raw) is str:
                return True, 1
            else:
                return False, 0
        elif curr is float:
            if type(raw) is float:
                return True, 1
            elif type(raw) is int:
                # 允许int被视作float
                return True, 1
            else:
                return False, 0
        elif curr is int:
            if type(raw) is int:
                return True, 1
            else:
                return False, 0
        elif curr is bool:
            if type(raw) is bool:
                return True, 1
            else:
                return False, 0
        logger.error("你tm怎么来这儿的？")
        return False, 0
    if curr in complex_types:
        if curr is list or curr is set:
            if type(raw) is list:
                good = True
                sumScore = 0
                for i in raw:
                    anonGood, anonScore = json_type_check(i, requiredValues[1:])
                    good &= anonGood
                    sumScore += anonScore
                return good, sumScore / len(raw) + 1
            else:
                return False, 0
        elif curr is dict:
            if type(raw) is dict:
                good = True
                sumScore = 0
                for i in raw.values():
                    anonGood, anonScore = json_type_check(i, requiredValues[1:])
                    good &= anonGood
                    sumScore += anonScore
                return good, sumScore / len(raw) + 1
            else:
                return False, 0
        elif curr is tuple:
            if type(raw) is list:
                if len(requiredValues) == 1:
                    logger.error("这tm不可能啊")
                    return False, 0
                if len(raw) == len(requiredValues[1]):
                    good = True
                    sumScore = 0
                    for i in range(len(raw)):
                        anonGood, anonScore = json_type_check(raw[i], requiredValues[1][i])
                        good &= anonGood
                        sumScore += anonScore
                    return good, sumScore / len(raw) + 1
                else:
                    return False, 0.5
            else:
                return False, 0
        else:
            logger.error("你这又是怎么过来的？？？")
            return False, 0
    logger.error("还有高手？？？")
    return False, 0


def parse_json(answer: JsonAnswer, requiredValues: List[tuple[str, TypeList]]) -> tuple[bool, float]:
    """
      Parse and verify JSON data against a list of required values.

      Parameters
      ----------
      answer : JsonAnswer
          The JSON data to be parsed and verified.
      requiredValues : List[Tuple[str, TypeList]]
          A list of tuples where each tuple contains a key and a list of required types for that key.

      Returns
      -------
      Tuple[bool, float]
          A tuple containing a boolean indicating if the parsing and verification passed and a score representing the success level.
            if the bool is true, then everything matches perfectly, no need to check score.
            if the bool is false, then the one with the highest score means it matches best
      Examples
      --------
      >>> answer = ["John", 30]
      >>> requiredValues = [("name", [str]), ("age", [int])]
      >>> parse_json(answer, requiredValues)
      (True, 2)
      """
    if type(answer) is not list:
        answer = [answer]
    score = 0
    good = True
    if len(answer) != len(requiredValues):
        logger.warn("bad length")
        return False, score
    for i in range(len(answer)):
        anonGood, anonScore = json_type_check(answer[i], requiredValues[i][1])
        good &= anonGood
        score += anonScore
    return good, score


def type_list_to_string(typeList: List):
    """
   Convert a list of types to a string representation.

   Parameters
   ----------
   typeList : List
       A list representing a hierarchy of types.

   Returns
   -------
   str
       A string representation of the nested types in `typeList`.

   Raises
   ------
   Exception
       If an unsupported type is encountered in `typeList`.

   """
    if len(typeList) == 0:
        return "str"
    curr = typeList[0]
    if curr in basic_types:
        return curr.__name__
    if type(curr) is list:
        anon = ""
        for c in curr:
            anon += type_list_to_string(c) + ", "
        return anon[:-2]
    if curr is dict:
        return curr.__name__ + "[str," + type_list_to_string(typeList[1:]) + "]"
    if curr is list or curr is set or curr is tuple:
        return curr.__name__ + "[" + type_list_to_string(typeList[1:]) + "]"
    raise Exception

