import ast
import os
import json
from abc import abstractmethod, ABC
import re

import structlog

from ruyi.utils import debug
from ruyi.utils.interface import RuyiInterface
from .view import UI_View
from ..agent import RuyiAgent
from ruyi.utils.prompt_manager import prompt_manager
logger = structlog.get_logger(__name__)


class View_Locator(RuyiInterface, ABC):

    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'ui.view_locator'

    @abstractmethod
    def locate_view(self, parent_view, description) -> UI_View:
        """
        Determine the location of this view inside the `parent_view` based on the `description`
        return the `bound` and `view_id` (optionally)
        """
        debug.print_method_name_with_message('not implemented')
        pass

    # 原先的方法基本没有implement这个方法
    # @abstractmethod
    def describe_view(self, view) -> str:
        """
        Return a description of the given view.
        """
        return self.content(view)
        # debug.print_method_name_with_message('not implemented')
        # pass

    @abstractmethod
    def content(self, view: UI_View) -> str:
        """
        Fetch content from this view.
        """
        debug.print_method_name_with_message('not implemented')
        pass

    @abstractmethod
    def get_views_by_list(self, parent_view: UI_View, description: str) -> list[str]:
        """
        获取一个list的view，于view_iterator中用到
        """
        debug.print_method_name_with_message('not implemented')
        pass

    @abstractmethod
    def deduplicate(self, history_element_description: list[str], element_description: list[str]) -> list[str]:
        """
        在element_description中去掉已经在history_element_description中出现过的元素
        """
        debug.print_method_name_with_message('not implemented')
        pass


class Tree_View_Locator(View_Locator):

    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'ui.tree_view_locator'

    # llm
    def locate_view(self, parent_view, description):
        """
        locate the view using an LLM
        """
        parent_view_desc = self.describe_view(parent_view)
        try:
            matched_idx = self.agent.fm.llm(
                prompt_manager.get_string('view_locator', 'tree_view_locator_locate_view_prompt', [description]),
                f'{parent_view_desc}',
                returns=('the matched view index in int, -1 if none matched', int)
            )
            matched_idx = int(matched_idx)
            all_view_dicts = parent_view.root.all_view_dicts
            if 0 <= matched_idx < len(all_view_dicts):
                matched_view = all_view_dicts[matched_idx]
                b = matched_view['bounds']
                bound = (b[0][0], b[0][1], b[1][0], b[1][1])
                return bound, matched_idx
        except Exception as e:
            logger.exception(f'{self._tag} _locate_view exception {e}', action='locate view', status='failed')
        return parent_view.bound, parent_view.view_id

    def describe_view(self, view: UI_View):
        """
        get a text description of the given view
        """
        view_dict = view.root.all_view_dicts[view.view_id]
        # TODO using flatten view description currently, change to tree structure
        children_ids = view.root.state.get_all_children(view_dict)
        sorted_view_ids = sorted(set(children_ids + [view.view_id]))
        # description = ''
        # for idx in sorted_view_ids:
        #     view_desc = self._view_dict_to_str(view.root.state, view.root.all_view_dicts[idx])
        #     description += f'{view_desc}\n'
        description = self._get_tree_structure([view.root.all_view_dicts[idx] for idx in sorted_view_ids])
        return description

    def content(self, view: UI_View):
        bound, view_id = self.locate_view(view.parent_view, view.locate_description)
        text = view.root.all_view_dicts[view_id].get('text', None)
        if text:
            return text
        description = view.root.all_view_dicts[view_id].get('content_description', None)
        if description:
            return description
        return 'null'

    # llm
    def get_views_by_list(self, parent_view, description):
        """
        Get the descriptions of a list of views that match the given description
        """
        parent_view_desc = self.describe_view(parent_view)

        try:
            matched_descriptions = self.agent.fm.llm(
                prompt_manager.get_string('view_locator', 'tree_view_locator_get_views_by_list_prompt', [description, parent_view_desc]),
                returns=('the list of view descriptions that match this description', str)
            )
            matched_descriptions = matched_descriptions.replace("```python", '')
            matched_descriptions = matched_descriptions.replace("```", "")

            print(f'matched_descriptions for {description}: {matched_descriptions}')
            return ast.literal_eval(matched_descriptions)
            # matched_idx = int(matched_idx)
            # all_view_dicts = parent_view.root.all_view_dicts
            # if 0 <= matched_idx < len(all_view_dicts):
            #     matched_view = all_view_dicts[matched_idx]
            #     b = matched_view['bounds']
            #     children = [int(child_id) for child_id in matched_view['children']]
            #     # bound = (b[0][0], b[0][1], b[1][0], b[1][1])
            #     return children
        except Exception as e:
            logger.exception(f'{self._tag} get_views_by_list exception {e}', action='get views by list',
                             status='failed')
        return []

    def _get_tree_structure(self, element_dict_list: list):

        def _prettify_element_tree(elements, current_id='0', indent=0):
            element = next(e for e in elements if e['id'] == current_id)

            # Define the HTML tag based on type, and add id as an attribute
            tag = element.get('type', 'div')
            html_output = f"{'  ' * indent}<{tag} id='{element['id']}'"

            # Add resource_id if it exists
            if 'resource_id' in element:
                html_output += f" resource_id='{element['resource_id']}'"

            # Add alternative text or status if applicable
            if 'alt' in element:
                html_output += f" alt='{element['alt']}'"
            if 'status' in element:
                html_output += f" status='{element['status']}'"

            html_output += '>'

            # Check if element contains text content
            if 'text' in element:
                html_output += element['text']

            # Recursively add children elements with increased indentation
            for child_id in element.get('children', []):
                html_output += '\n' + _prettify_element_tree(elements, current_id=child_id, indent=indent + 1)

            # Close the tag
            html_output += f"\n{'  ' * indent}</{tag}>"
            return html_output

        ui_elements = [{"id": str(el.get("temp_id", 0)),
                        "bound_box": el.get("bound_box", []),
                        "children": [str(id) for id in el.get("children", [])],
                        "status": el.get("checked", False) or el.get("selected", False),
                        "type": el.get("class", "div").split('.')[-1],
                        "text": el.get("text", ""),
                        "alt": el.get("content_description", "")
                        } for el in element_dict_list]
        for ui_element_id, ui_element in enumerate(ui_elements):
            if not ui_elements[ui_element_id]['status']:
                ui_elements[ui_element_id].pop('status')
            else:
                ui_elements[ui_element_id]['status'] = 'selected'
            for k in ['text', 'alt']:
                if not ui_elements[ui_element_id][k]:
                    ui_elements[ui_element_id].pop(k)

        return _prettify_element_tree(ui_elements, current_id=ui_elements[0]['id'])

    # llm
    def deduplicate(self, history_elements, element_descriptions) -> list[str]:
        """
        Remove duplicated elements from the current element list if it already exists in the history element list.
        """

        prompt = prompt_manager.get_string('view_locator', 'tree_view_locator_deduplicate_prompt', [element_descriptions, history_elements])
        result = self.agent.fm.llm(prompt)
        result = result.replace("```python", '')
        result = result.replace("```", "")
        return ast.literal_eval(result)


class Vision_View_Locator(View_Locator, ABC):
    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'ui.vision_view_locator'

    def locate_view(self, parent_view, description, mode="point"):
        """
        locate the view using a Vision-Language Model
        如果定位失败，会尝试调用ensure来处理异常情况
        """
        for _ in range(3):
            result = self._locate_view_impl(parent_view, description, mode=mode)
            if result[0] == (None, None, None, None):
                logger.debug(f"Failed to locate view {description}, retrying...", action='locate_view', status='retry')
                logger.info(f"未在当前界面上定位到 \"{description}\"，重试中...")
                continue
            return result

        if result[0] == (None, None, None, None):
            logger.info(f"未在当前界面上定位到 \"{description}\"，尝试导航到 \"{description}\" 所在界面")
            logger.debug(f"Failed to locate view {description}, trying ensure...", action='locate_view', status='ensure')
            if hasattr(self, 'ensure') and self.agent.config.enable_ensure:
                res = self.ensure(description)
                if res:
                    result = self._locate_view_impl(parent_view, description, mode=mode)
                    if result[0] != (None, None, None, None):
                        logger.debug(f"Successfully located view {description} after ensure", action='locate_view', status='done')
                        return result
            # raise RuntimeError(f"Failed to locate view {description}")
            raise RuntimeError(f"在当前界面定位 \"{description}\" 失败。如果 \"{description}\" 在当前界面存在，请点击 \"手工补丁\" 按钮添加补丁。")

        return result

    @abstractmethod 
    def _locate_view_impl(self, parent_view, description, mode="point"):
        """
        实际的定位实现
        """
        debug.print_method_name_with_message('not implemented')
        pass


class Molmo_Vision_View_Locator(Vision_View_Locator):
    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'ui.molmo_vision_view_locator'
        if self.agent.config.use_ruyillm:
            self.base_url = self.agent.config.ruyillm_url
            self.api_key = self.agent.config.ruyillm_key
        else:
            self.base_url = self.agent.config.molmo_api_url
            self.api_key = self.agent.config.molmo_api_key
        self.annotation_locator = Annotation_Vision_View_Locator(agent)
    
    def _extract_bbox(self, response: str):
        try:
            xyxy = response.split()
            if 'null' in xyxy:
                return None, None, None, None
            return map(float, xyxy)
        except Exception:
            return -1, -1, -1, -1

    def _extract_point(self, response: str):
        try:
            xyxy = response.split()
            if 'null' in xyxy:
                return None, None
            x1, y1, x2, y2 = map(float, xyxy)
            return (x1 + x2) / 2, (y1 + y2) / 2
        except Exception:
            return -1, -1

    def _locate_view_impl(self, parent_view, description, mode="point"):
        """
        locate the view using a Vision-Language Model
        """
        annotation_grounding = self.annotation_locator._locate_view_impl(description)
        if annotation_grounding is not None:
            logger.info("Locate view using annotation grounding")
            logger.debug("Locate view using annotation grounding", action='locate_view', status='done')
            return annotation_grounding

        image = self.agent.device.take_screenshot()
        if self.agent.config.device_type == "browser":
            resized_image = image.resize((1080, 2400))
        else:
            resized_image = image
        response = self.agent.fm.molmo(resized_image, prompt_manager.get_string('view_locator', 'molmo_vision_view_locator_format_locate_view_inputs', [description]))
        logger.debug(f"Molmo locate view response: {response}")
        if mode == "point":
            x, y = self._extract_point(response)

            if x is None or y is None:
                logger.debug(f"Failed to parse model response for locating '{description}', model response: {response}")
                # logger.debug(f"错误：未在当前界面上定位到 \"{description}\"")
                return (None, None, None, None), None

            if x < 0 or y < 0:
                logger.debug(f"Failed to parse model response for locating '{description}', model response: {response}")
                # logger.info(f"错误：未在当前界面上定位到 \"{description}\"")
                return (None, None, None, None), None
            
            x, y = x / 1000 * image.width, y / 1000 * image.height
            image.close()
            logger.debug(
                "Molmo locate view parse point",
                status="done",
                metadata={
                    "x y": f"{x} {y}"
                }
            )

            return (x, y, x, y), None
        elif mode == "bbox":
            x1, y1, x2, y2 = self._extract_bbox(response)
            if x1 is None or y1 is None or x2 is None or y2 is None:
                logger.debug(f"Failed to parse model response for locating '{description}', model response: {response}")
                # logger.debug(f"错误：未在当前界面上定位到 \"{description}\"")
                return (None, None, None, None), None
            x1 = x1 / 1000 * image.width
            y1 = y1 / 1000 * image.height
            x2 = x2 / 1000 * image.width
            y2 = y2 / 1000 * image.height
            image.close()
            logger.debug(
                "Molmo locate view parse bbox",
                status="done",
                metadata={
                    "x1 y1 x2 y2": f"{x1} {y1} {x2} {y2}"
                }
            )
            return (x1, y1, x2, y2), None

    # molmo
    def content(self, view: UI_View) -> str:
        image = self.agent.device.take_screenshot()
        # description = view.locate_description
        x, y = view._get_center()
        prompt = prompt_manager.get_string('view_locator', 'molmo_vision_view_locator_content_prompt', [x, y])
        response = self.agent.fm.molmo(image, prompt)
        response = response.strip()
        return response

    # vlm
    def get_views_by_list(self, description, history_list) -> list[str]:
        views_list_annotation = self.annotation_locator.get_views_by_list(description, history_list)
        if views_list_annotation is not None:
            logger.info("Get views by list using annotation")
            logger.debug("Get views by list using annotation",
                        action='get_views_by_list',
                        status='done',
                        metadata={
                            "views_list": views_list_annotation
                        })
            return views_list_annotation
        # response = self.agent.fm.molmo(self.agent.device.take_screenshot(), prompt_manager.get_string('view_locator', 'molmo_vision_view_locator_get_views_by_list_prompt', [description, history_list]))
        response = self.agent.fm.molmo(self.agent.device.take_screenshot(), prompt_manager.get_string('view_locator', 'molmo_vision_view_locator_get_views_text_by_list_prompt', [description, history_list]))
        return re.split(r'\s*,\s*', response)
    
    def deduplicate(self, history_elements, element_descriptions):
        raise RuntimeError("You should not call deduplicate for Molmo Vision View Locator")

    def ensure(self, description):
        if self.check(description):
            return True
        
        ensure_annotation = self.annotation_locator.ensure(description)

        if ensure_annotation is not None:
            x, y = ensure_annotation
            if x < 0 or y < 0:
                logger.info(f"Ensure '{description}' using annotation, current screen is the target screen")
                return True
            logger.info(f"Ensure '{description}' using annotation")
            logger.debug(f"Ensure '{description}' using annotation", action='ensure', status='done',
                        metadata={"x y": f"{x} {y}"})
            self.agent.device.long_touch(x, y, duration=200)
        else:
            image = self.agent.device.take_screenshot()
            if self.agent.config.device_type == "browser":
                image = image.resize((1080, 2400))
            
            prompt = prompt_manager.get_string('view_locator', 'molmo_vision_view_locator_ensure_prompt', [description])
            response = self.agent.fm.molmo(image, prompt)
            if 'null' in response:
                # logger.info(f"Ensure '{description}', current screen is the target screen")
                logger.info(f"调用模型确保 \"{description}\"，模型未找到导航动作，当前界面可能是目标界面")
                logger.debug(f"Failed to ensure '{description}', current screen is the target screen", action='ensure', status='failed')
                return True
            else:
                x, y = self._extract_point(response)
                if x is None or y is None:
                    # logger.info(f"Ensure '{description}', current screen is the target screen")
                    logger.info(f"调用模型确保 \"{description}\"，当前界面已经是目标界面")
                    return True
                if x < 0 or y < 0:
                    logger.info(f"Failed to parse model response for ensuring '{description}', model response: {response}")
                    return True
                
                x, y = x / 1000 * image.width, y / 1000 * image.height
                image.close()
                logger.debug(
                    "Molmo ensure view parse point",
                    status="done",
                    metadata={
                        "x y": f"{x} {y}"
                    }
                )
                self.agent.device.long_touch(x, y, duration=200)

        if self.check(description):
            return True
        return False

    def check(self, description: str | list[str]):
        check_annotation = self.annotation_locator.check(description)
        if check_annotation is not None:
            logger.info(f"Check view {description} using annotation, result: {check_annotation}")
            return check_annotation
        if type(description) is list:
            condition_list = "\n".join(description)
        else:
            condition_list = description
        is_success = False
        for _ in range(3):
            try:
                response = self.agent.fm.molmo(self.agent.device.take_screenshot(), prompt_manager.get_string('view_locator', 'molmo_vision_view_locator_checklist_prompt', [condition_list]))
                check_result = re.split(r'\s*,\s*', response)
                is_success = True
                break
            except Exception as _:
                # logger.exception(f"Failed to check view {condition_list}, retrying...", action='check', status='retry')
                logger.exception(f"在当前界面中检查 \"{condition_list}\" 失败，重试中...", action='check', status='retry')
                continue
        if not is_success:
            # logger.debug(f"Failed to check view {condition_list}, trying ensure...")
            # logger.info(f"在当前界面中检查 \"{condition_list}\" 失败")
            raise RuntimeError(f"在当前界面中检查 \"{condition_list}\" 失败")

        check_result = 'True' in check_result
        
        # logger.info(f"Check result: {check_result}")
        logger.info(f"检查 \"{condition_list}\"，结果为：{check_result}")
        logger.debug(f"Check result: {check_result}", action='check', status='done')
        return check_result


class Claude_Vision_View_Locator(Vision_View_Locator):
    '''
    TODO:
    - locate view (ok)

    '''

    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'ui.claude_vision_view_locator'

    def _locate_view_impl(self, parent_view, description):
        """
        locate the view using a Vision-Language Model
        """
        from .view import UI_View
        assert isinstance(parent_view, UI_View)
        image = self.agent.device.take_screenshot()
        response = self.agent.fm.claude(image, prompt_manager.get_string('view_locator', 'claude_vision_view_locator_format_locate_view_inputs', [description]))
        x, y = response[0], response[1]
        image.close()
        return (x, y, x, y), None

    #claude
    def content(self, view: UI_View) -> str:
        image = self.agent.device.take_screenshot()
        x, y = view._get_center()
        prompt = prompt_manager.get_string('view_locator', 'claude_vision_view_locator_content_prompt', [x, y])
        response = self.agent.fm.claude(image, prompt, use_computer=False)
        response = response.strip()
        return response

    # claude
    def get_views_by_list(self, parent_view, description) -> list[str]:
        from .view import UI_View
        assert isinstance(parent_view, UI_View)
        image = self.agent.device.take_screenshot()
        prompt = prompt_manager.get_string('view_locator', 'claude_vision_view_locator_format_iterate_view_inputs', [description])
        response = self.agent.fm.claude(image, prompt, use_computer=False)
        element_descriptions = eval(response)
        return element_descriptions

    # claude
    def deduplicate(self, history_elements, element_descriptions):
        prompt = prompt_manager.get_string('view_locator', 'claude_vision_view_locator_deduplicate_prompt', [element_descriptions, history_elements])
        response = self.agent.fm.claude(None, prompt, use_computer=False)
        return ast.literal_eval(response)


class Annotation_Vision_View_Locator(Vision_View_Locator):
    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'ui.annotation_vision_view_locator'
        # 获取 documents_path
        # documents_path = os.path.expanduser('~/Documents')
        # self.annotation_path = os.path.join(documents_path, 'ruyi-annotation')
        self.annotation_path = agent.config.annotation_path
        self.use_annotation = agent.config.use_annotation and os.path.exists(self.annotation_path)

    def _locate_view_impl(self, description):
        if not self.use_annotation:
            return None
        for annotation_dir in os.listdir(self.annotation_path):
            if not annotation_dir.startswith('ruyi-annotation'):
                continue
            annotation_path = os.path.join(self.annotation_path, annotation_dir)
            annotation_file = os.path.join(annotation_path, 'annotation.json')
            with open(annotation_file, 'r', encoding='utf-8') as f:
                annotation = json.load(f)
                for item in annotation:
                    if item['mode'] == 'grounding' or item['mode'] == 'enumerate':
                        for item_description in item['description']:
                            if item_description == description:
                                x = item['x'] + item['width'] / 2
                                y = item['y'] + item['height'] / 2
                                return (x, y, x, y), None
        return None

    def content(self, view: UI_View) -> str:
        return view.content

    def get_views_by_list(self, description, history_list) -> list[str]:
        if not self.use_annotation:
            return None
        if len(history_list) > 0:
            return None
        view_list = []
        for annotation_dir in os.listdir(self.annotation_path):
            if not annotation_dir.startswith('ruyi-annotation'):
                continue
            annotation_path = os.path.join(self.annotation_path, annotation_dir)
            annotation_file = os.path.join(annotation_path, 'annotation.json')
            with open(annotation_file, 'r', encoding='utf-8') as f:
                annotation = json.load(f)
                for item in annotation:
                    if item['mode'] == 'enumerate':
                        for groupTitle in item['groupTitle']:
                            if groupTitle == description:
                                view_list.append(item['description'][0])
        
        if len(view_list) > 1:
            return str(view_list)
        return None

    def deduplicate(self, history_elements, element_descriptions):
        return element_descriptions

    def ensure(self, description):
        if not self.use_annotation:
            return None
        for annotation_dir in os.listdir(self.annotation_path):
            if not annotation_dir.startswith('ruyi-annotation'):
                continue
            annotation_path = os.path.join(self.annotation_path, annotation_dir)
            annotation_file = os.path.join(annotation_path, 'annotation.json')
            with open(annotation_file, 'r', encoding='utf-8') as f:
                annotation = json.load(f)
                for item in annotation:
                    if item['mode'] == 'ensure':
                        for item_description in item['description']:
                            if item_description == description:
                                x = item['x'] + item['width'] / 2
                                y = item['y'] + item['height'] / 2
                                return x, y
        return None

    def check(self, description):
        if not self.use_annotation:
            return None
        for annotation_dir in os.listdir(self.annotation_path):
            if not annotation_dir.startswith('ruyi-annotation'):
                continue
            annotation_path = os.path.join(self.annotation_path, annotation_dir)
            annotation_file = os.path.join(annotation_path, 'annotation.json')
            with open(annotation_file, 'r', encoding='utf-8') as f:
                annotation = json.load(f)
                for item in annotation:
                    if item['mode'] == 'check':
                        for item_description in item['description']:
                            if item_description == description:
                                return item['value']
        return None
    

_viewLocatorList = {'tree':
                        {"default": Tree_View_Locator},
                    'vision':
                        {"molmo": Molmo_Vision_View_Locator,
                         "claude": Claude_Vision_View_Locator}}


def get_view_locator(agent: RuyiAgent) -> View_Locator:
    locatorType = agent.config.ui_default_locator.lower().strip()
    backbone = agent.config.ui_vision_locator_backbone.lower().strip()
    if agent.config.use_ruyillm:
        locatorType = 'vision'
        backbone = 'molmo'
    if locatorType not in _viewLocatorList.keys():
        raise Exception("找不到对应的locator")
    typedLocatorList = _viewLocatorList[locatorType]
    if len(list(typedLocatorList.values())) == 1:
        return list(typedLocatorList.values())[0](agent)
    if backbone not in typedLocatorList.keys():
        raise Exception("找不到对应的locator")
    return typedLocatorList[backbone](agent)
