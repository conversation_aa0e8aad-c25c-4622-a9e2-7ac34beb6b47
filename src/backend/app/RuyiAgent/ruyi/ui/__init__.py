"""
The interfaces to manipulate the device GUI.
Can potentially reuse many from the autodroid project.
"""
from ruyi.utils.interface import <PERSON>uy<PERSON><PERSON>nter<PERSON>
from .view import UI_View, UI_Root
from .view_locator import View_Locator,get_view_locator
from typing import cast, Optional
import structlog

logger = structlog.get_logger(__name__)


class UI_Interface(RuyiInterface):
    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'ui'
        self.last_action_time = cast(float, None)
        self.action_history = []
        self.view_locator = cast(View_Locator, None)

    def _open(self):
        self.view_locator=get_view_locator(agent=self.agent)

    @property
    def root(self) -> UI_View:
        """
        Get the current root view of the device
        """
        return UI_Root(self.agent)

    def back(self):
        """
        Navigate back from current screen
        """
        logger.info(
            'Press [BACK]',
        )
        self.agent.device.back()
        self.agent._record_action('Press [BACK]')

    def home(self):
        """
        Navigate to the home screen
        """
        self.agent.device.home()
        self.agent._record_action('Press [HOME]')

    def back_to(self, description: str, max_steps=5):
        """
        Back to the expected UI view
        """
        count_steps = 0
        while not self.agent.ui.view_locator.check(description) and count_steps < max_steps:
            self.back()
            count_steps += 1

    def check(self, description) -> bool:
        res = self.agent.ui.view_locator.check(description)
        return res

    def wait_until(self, description, waitInterval:float=0.5, timeout=5) -> bool:
        """
        Wait for a view described by `description` to appear and return it.
        `timeout` is the time limit (in seconds) of waiting.
        """
        currSec = 0.0
        res = False
        logger.info(f'Waiting for view {description}')
        logger.debug(
            f'Waiting for view {description}',
            action='wait_until',
            status='start',
            metadata={}
        )
        while currSec < timeout:
            res = self.check(description)
            if res:
                logger.info(f'View {description} appeared')
                logger.debug(
                    f'View {description} appeared',
                    action='wait_until',
                    status='done',
                    metadata={}
                )
                return True
            self.agent.sleep(waitInterval)
            currSec += waitInterval
        self.agent.increment_step_count()
        logger.info(f'Timeout waiting for view {description}')
        logger.debug(
            f'Timeout waiting for view {description}',
            action='wait_until',
            status='timeout',
            metadata={}
        )

        return res

    def wait_view(self, description, waitInterval: float = 0.5, timeout=5):
        raise RuntimeError("The method wait_view is deprecated, use wait_until instead")

    def drag(self, source_desc, target_desc, duration=200):
        x1, y1 = self.agent.fm.vlm(source_desc)
        x2, y2 = self.agent.fm.vlm(target_desc)

        logger.info(
            f'Dragging view {source_desc} to {target_desc}',
        )
        logger.debug(
            f'Dragging view {source_desc} to {target_desc}',
            action='drag',
            status='start',
            metadata={'source': source_desc, 'target': target_desc}
        )
        self.agent.device.drag((x1, y1), (x2, y2), duration=duration)
        logger.debug(
            'Dragging done',
            action='drag',
            status='done',
            metadata={'source': source_desc, 'target': target_desc}
        )

    def ensure(self, description: str):
        res = self.agent.ui.view_locator.ensure(description)

        return res

    def ask_question(self, question: str) -> str:
        """
        Ask question to this view.
        """
        logger.info(
            'Asking question'
        )
        logger.debug(
            'Asking question',
            action='ask question',
            status='start',
            metadata={'question': question}
        )
        answer = self.agent.device.ask_question(question)
        logger.debug(
            'Question answered',
            action='ask question',
            status='done',
            metadata={'answer': answer}
        )
        return answer

    def locate_view(self, description):
        """
        Locate the view described by `description` inside the current view (self)
        A `description` is a text description of the view, eg. "the search button at the top-right corner".
        A vision language model (accessed with `fm.vlm`) is used to locate the view.
        """
        return self.root.locate_view(description)

    def iterate_views(self, description, limit=-1,
                      direction: str = 'up', distance=None, duration=1000):
        """
        Yield the views that match `description` inside the current view.
        `limit` is the max number of matched views to be returned. `-1` means unlimited.
        You can scroll the current view if needed (e.g. if the self view is a ListView of many items).
        """
        return self.root.iterate_views(description, limit, direction, distance, duration)

    def content(self, description=None, returns=None) -> str:
        """
        Fetch content from this view.
        """
        return self.root.content(description, returns)

    def image(self):
        """
        Get screenshot image of this view.
        """
        return self.root.image()

    def long_snapshot(self):
        """
        Get a long snapshot image of this view.
        WARNING: The output of this function should NOT be used as parameters to call fm.vlm.
                 Please use iterate_views() instead.
        """
        return self.root.long_snapshot()

    def snapshot(self):
        """
        Get snapshot UI_View instance of this view.
        """
        return self.root.snapshot()

    def fetch_information(self, description: str, returns=None):
        """
        Fetch information from current screen.
        """
        return self.root.fetch_information(description, returns)
    

    def fetch_image(self, description: str, save_path: Optional[str] = None):
        """
        Fetch image from current screen.
        """
        return self.root.fetch_image(description, save_path)

    def description(self):
        """
        Get the description of this view.
        """
        return self.root.description()

    def click(self, duration=200):
        """
        Click this view.
        """
        self.root.click(duration)

    def long_click(self, duration=1000):
        '''
        Long click the view for 1 second.
        '''
        self.root.long_click(duration)

    def draw(self, line_num=1, duration=200):
        """
        Draw this view.
        """
        self.root.draw(line_num, duration)

    def highlight(self, duration: int = 1):
        """
        Highlight this view.
        """
        self.root.highlight(duration)

    def input(self, text: str) -> None:
        """
        Input text into this view.
        """
        self.root.input(text)

    def input_by_pasting(self, text: str) -> None:
        """
        Input text into this view by copy.
        """
        self.root.input_by_pasting(text)

    def get_input_field_text(self):
        """
        Get the text from the input field of this view.
        """
        return self.root.get_input_field_text()

    def scroll_up(self, distance=None):
        """
        scroll up this view
        """
        return self.root.scroll_up(distance)

    def scroll_down(self, distance=None):
        """
        scroll down this view
        :param distance:
        :return:
        """
        return self.root.scroll_down(distance)

    def scroll_left(self, distance=None):
        """
        scroll left this view
        :param distance:
        :return:
        """
        return self.root.scroll_left(distance)

    def scroll_right(self, distance=None):
        """
        scroll right this view
        :param distance:
        :return:
        """
        return self.root.scroll_right(distance)

    def scroll(self, direction: float | str, distance=None, duration=1000):
        """
        scroll this view, direction: up,down,left,right
        """
        return self.root.scroll(direction, distance, duration)

    def scroll_until(self, desc: str, direction: float | str = "down", distance=None, duration=1000, max_retry: int = 10) -> bool:
        """
        1. scroll the screen with param direction,distance, duration
        2. check if current screen match the description from desc
        3. if not, redo 1,2 until the description match at most max_retry times
        Parameters
        ----------
        desc: the description of the screen you want
        direction: the parameter for scroll
        distance: the parameter for scroll
        duration: the parameter for scroll
        max_retry: at most how many times the screen will be scrolled

        Returns
        -------
        if succeeded:
            same return value as scroll
        if failed:
            None
        """
        return self.root.scroll_until(desc, direction, distance, duration, max_retry)

    def ask_user(self, question):
        return self.root.ask_user(question)

    def hide_highlight(self):
        return self.root.hide_highlight()
