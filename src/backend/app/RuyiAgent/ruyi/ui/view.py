import time
from typing import cast, Optional

import structlog
from PIL import ImageDraw

from ruyi.ui.view_iterator import View_Iterator
from ruyi.utils import debug
from ruyi.utils.interface import RuyiInterface
import random

logger = structlog.get_logger(__name__)


class UI_View(RuyiInterface):
    def __init__(self, agent, parent_view: 'UI_View | None', locate_description: str | None, mode="point"):
        super().__init__(agent)
        self._tag = 'ui.view'
        self.parent_view = cast(UI_View, parent_view)  # The parent view of this view (where this view is located from)
        self.locate_description = locate_description  # The description used to locate this view
        self.init_time = self.agent.tools.time_now().timestamp()  # The timestamp when the this view was initialized
        self.bound = cast(tuple[int, int, int, int],
                          None)  # The bound of this view (caution: the bound may be changed, eg. after scrolling)
        self.view_id = cast(int | None,
                            None)  # The temp id of this view inside root.views (caution: the id may be changed after UI action; may be unavailable for image UIs)
        self.last_locate_time = cast(float, None)  # The timestamp of last time when this view was located
        self.last_action_time = cast(float, None)  # The timestamp when the last action was performed on this view
        self.mode = mode

    @property
    def root(self):
        """
        Return the root view node
        """
        return self if self.parent_view is None else self.parent_view.root

    def _get_bound(self):
        if self.bound is not None and self.last_locate_time is not None and self.last_action_time is not None and self.last_locate_time > self.agent.last_action_time:
            return self.bound
        self._do_locate()
        return self.bound

    def _get_center(self):
        bound = self._get_bound()
        center_x = int((bound[0] + bound[2]) / 2)
        center_y = int((bound[1] + bound[3]) / 2)
        return center_x, center_y

    def _get_view_id(self):
        if self.view_id is not None and self.last_action_time and self.last_locate_time and self.last_locate_time > self.agent.last_action_time:
            return self.view_id
        self._do_locate()
        return self.view_id

    def _do_locate(self):
        """
        locate this view based on the locating description inside the parent_view
        """
        logger.debug(
            f"Locate view with {self.agent.ui.view_locator.__class__.__name__}",
            action='call view locator',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__, 'mode': self.mode}
        )
        self.bound, self.view_id = self.agent.ui.view_locator.locate_view(self.parent_view, self.locate_description, mode=self.mode)
        logger.debug(
            'Bound and id parsed',
            action='call view locator',
            status='done',
            metadata={'bound': self.bound, 'description': self.locate_description,
                      "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        self.last_locate_time = self.agent.tools.time_now().timestamp()

    def _after_action(self, action=""):
        """
        necessary operations to perform after each action
        """
        action_time = self.agent.tools.time_now()
        self.last_action_time = action_time
        self.agent._record_action(action)

    def locate_view(self, description, mode="point"):
        """
        Locate the view described by `description` inside the current view (self)
        A `description` is a text description of the view, eg. "the search button at the top-right corner".
        A vision language model (accessed with `fm.vlm`) is used to locate the view.
        """
        view = UI_View(self.agent, parent_view=self, locate_description=description, mode=mode)
        return view

    def iterate_views(self, description, limit=-1,
                      direction: str = 'up', distance=None, duration=1000):
        """
        Yield the views that match `description` inside the current view.
        `limit` is the max number of matched views to be returned. `-1` means unlimited.
        You can scroll the current view if needed (e.g. if the self view is a ListView of many items).
        """
        view_list = UI_View(self.agent, parent_view=self, locate_description=description)

        # TODO: judge whether can scroll, if can, and the current views is not enough for limit, scroll
        return View_Iterator(self.agent, parent_view=view_list.parent_view, description=view_list.locate_description,
                             limit=limit, direction=direction, distance=distance, duration=duration)

    def content(self, description=None, returns=None) -> str:
        """
        Fetch content from this view.
        """
        res = self.agent.ui.view_locator.content(self)
        if description is not None or returns is not None:
            prompt = f"Filter the following content:\n```\n{self.locate_description}\n```\n by {description}\n"
            res = self.agent.fm.llm(prompt, returns)
        return res

    def image(self):
        """
        Get screenshot image of this view.
        """
        debug.print_method_name_with_message('not implemented')
        return self.agent.data.image(img=None)

    def long_snapshot(self):
        """
        Get a long snapshot image of this view.
        WARNING: The output of this function should NOT be used as parameters to call fm.vlm.
                 Please use iterate_views() instead.
        """
        debug.print_method_name_with_message('not implemented')
        return self

    def snapshot(self):
        """
        Get snapshot UI_View instance of this view.
        """
        image = self.agent.device.take_screenshot()
        x, y = self._get_center()
        # 固定高亮范围，可根据需要调整
        highlight_size = 50
        left = x - highlight_size
        top = y - highlight_size
        right = x + highlight_size
        bottom = y + highlight_size

        draw = ImageDraw.Draw(image)
        draw.rectangle([(left, top), (right, bottom)], outline=(255, 0, 0), width=10)  # 红色边框
        
        return image

    def description(self):
        """
        Get the description of this view.
        """
        if self.locate_description:
            return self.locate_description
        
        self._do_locate()
        return self.agent.ui.view_locator.describe_view(self)
    
    def click(self, duration=200):
        """
        Click this view.
        """
        logger.debug(
            'Waiting for click coordinates',
            action='get click coordinates',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        x, y = self._get_center()
        # logger.info(
        #     f'Perform clicking at ({x}, {y})',
        # )
        logger.debug(
            f'Perform clicking at ({x}, {y})',
            action='click',
            status='start',
            metadata={'coordinates': (x, y)}
        )
        self.agent.device.long_touch(x, y, duration=duration)
        self.agent.increment_step_count()
        self._after_action(action=f'click [{self.locate_description}]')  # TODO better action description
        logger.debug(
            f'Click action done at ({x}, {y})',
            action='click',
            status='done',
            metadata={'coordinates': (x, y)}
        )

    def long_click(self, duration=1000):
        '''
        Long click the view for 1 second.
        '''
        return self.click(duration=duration)

    def draw(self, line_num=1, duration=200):
        """
        Draw this view.
        """
        logger.debug(
            'Waiting for draw coordinates',
            action='get draw coordinates',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        x, y = self._get_center()

        for i in range(line_num):
            tmp_x1 = x + random.randint(-200, 200)
            tmp_y1 = y + random.randint(-200, 200)
            tmp_x2 = x + random.randint(-200, 200)
            tmp_y2 = y + random.randint(-200, 200)
            self.agent.device.drag((tmp_x1, tmp_y1), (tmp_x2, tmp_y2), duration=duration)

        self._after_action(action=f'draw [{self.locate_description}]')  # TODO better action description

        logger.debug(
            f'Draw action done at ({x}, {y})',
            action='draw',
            status='done',
            metadata={'coordinates': (x, y)}
        )

    def highlight(self, duration: int = 1):
        """
        Highlight this view.
        """
        logger.debug(
            'Waiting for highlight coordinates',
            action='get highlight coordinates',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        x, y = self._get_center()
        logger.info(
            f'Perform highlighting at ({x}, {y})',
        )
        logger.debug(
            f'Perform highlighting at ({x}, {y})',
            action='highlight',
            status='start',
            metadata={'coordinates': (x, y)}
        )
        self.agent.device.show_highlight(x, y, radius=100)
        self.agent.sleep(duration)
        self.agent.device.hide_highlight()
        self._after_action(action=f'highlight [{self.locate_description}]')  # TODO better action description
        logger.debug(
            f'Highlight action done at ({x}, {y})',
            action='highlight',
            status='done',
            metadata={'coordinates': (x, y)}
        )

    def input(self, text: str) -> None:
        """
        Input text into this view.
        """
        logger.debug(
            'Waiting for input coordinates',
            action='get input coordinates',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        x, y = self._get_center()
        # logger.info(
        #     f'Input text at ({x}, {y})',
        # )
        logger.debug(
            f'Input text at ({x}, {y})',
            action='input',
            status='start',
            metadata={'coordinates': (x, y), 'text': text}
        )
        self.agent.device.long_touch(x, y, duration=200)
        time.sleep(0.5)
        self.agent.device.view_set_text(text)
        self.agent.increment_step_count()
        self.agent.increment_step_count()
        logger.debug(
            f'Input text done at ({x}, {y})',
            action='input',
            status='done',
            metadata={'coordinates': (x, y), 'text': text}
        )
        self._after_action(action=f'input [{self.locate_description}] "{text}"')

    def input_by_pasting(self, text: str) -> None:
        """
        Input text into this view.
        """
        logger.info(
            'Waiting for input coordinates',
        )
        logger.debug(
            'Waiting for input coordinates',
            action='get input coordinates',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        x, y = self._get_center()
        logger.debug(
            f'Input text by pasting at ({x}, {y})',
            action='input',
            status='start',
            metadata={'coordinates': (x, y), 'text': text}
        )
        self.agent.device.long_touch(x, y, duration=1000)
        self.agent.device.set_clipboard(text)
        time.sleep(1)
        self.locate_view("粘贴").click()
        logger.debug(
            'Input text by pasting done.',
            action='input',
            status='done',
            metadata={'coordinates': (x, y), 'text': text}
        )
        self._after_action(action=f'input [{self.locate_description}] "{text}"')

    def get_input_field_text(self):
        """
        Get the text from the input field of this view.
        """
        logger.debug(
            'Waiting for get input field text view coordinates',
            action='get input field text view coordinates',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        x, y = self._get_center()
        logger.info(
            f'Get input field text at ({x}, {y})'
        )
        logger.debug(
            f'Get input field text at ({x}, {y})',
            action='get input field text',
            status='start',
            metadata={'coordinates': (x, y)}
        )
        self.agent.device.long_touch(x, y, duration=200)
        time.sleep(0.5)
        text = self.agent.device.get_input_field_text()
        logger.debug(
            f'Get input field text done at ({x}, {y})',
            action='get input field text',
            status='done',
            metadata={'coordinates': (x, y), 'text': text}
        )
        self._after_action(action=f'get input field text [{self.locate_description}] "{text}"')
        return text

    def scroll_up(self, distance=None):
        """
        scroll up this view
        """
        start_xy, end_xy, duration = self.scroll('up', distance)
        return start_xy, end_xy, duration

    def scroll_down(self, distance=None):
        """
        scroll down this view
        :param distance:
        :return:
        """
        start_xy, end_xy, duration = self.scroll('down', distance)
        return start_xy, end_xy, duration

    def scroll_left(self, distance=None):
        """
        scroll left this view
        :param distance:
        :return:
        """
        start_xy, end_xy, duration = self.scroll('left', distance)
        return start_xy, end_xy, duration

    def scroll_right(self, distance=None):
        """
        scroll right this view
        :param distance:
        :return:
        """
        start_xy, end_xy, duration = self.scroll('right', distance)
        return start_xy, end_xy, duration

    def scroll(self, direction: float | str, distance=None, duration=1000):
        """
        scroll this view, direction: up,down,left,right
        """
        logger.debug(
            f'Waiting for scroll {direction} {distance} coordinates',
            action='get scroll coordinates',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        if distance is None:
            distance = 350
        
        center_xy = self._get_center()
        # start_xy = self._get_center()
        if direction == 'up':
            start_xy = center_xy
        elif direction == 'down':
            start_xy = center_xy
        elif direction == 'left':
            start_xy = (center_xy[0] - distance, center_xy[1])
        elif direction == 'right':
            start_xy = (center_xy[0] + distance, center_xy[1])
        else:
            raise ValueError(f'Invalid direction: {direction}')
        
        if direction == 'up':
            end_xy = (center_xy[0], center_xy[1] + distance)
        elif direction == 'down':
            end_xy = (center_xy[0], center_xy[1] - distance)
        elif direction == 'left':
            end_xy = (center_xy[0] + distance, center_xy[1])
        elif direction == 'right':
            end_xy = (center_xy[0] - distance, center_xy[1])
        else:
            raise ValueError(f'Invalid direction: {direction}')

        logger.info(
            f'Perform scrolling from {start_xy} to {end_xy}'
        )
        logger.debug(
            f'Perform scrolling from {start_xy} to {end_xy}',
            action='scroll',
            status='start',
            metadata={'coordinates': (start_xy, end_xy)}
        )
        if hasattr(self.agent.device, 'scroll'):
            self.agent.device.scroll(start_xy, end_xy, duration=duration)
        else:
            self.agent.device.drag(start_xy, end_xy, duration=duration)
        logger.debug(
            f'Scroll action done from {start_xy} to {end_xy}',
            action='scroll',
            status='done',
            metadata={'coordinates': (start_xy, end_xy)}
        )
        self._after_action(action=f'scroll [{self.locate_description}] {direction} {distance}')

        return start_xy, end_xy, duration

    def scroll_until(self, desc: str, direction: float | str = "down", distance=None, duration=1000, max_retry: int = 10) -> bool:
        """
        1. scroll the screen with param direction,distance, duration
        2. check if current screen match the description from desc
        3. if not, redo 1,2 until the description match at most max_retry times
        Parameters
        ----------
        desc: the description of the screen you want
        direction: the parameter for scroll
        distance: the parameter for scroll
        duration: the parameter for scroll
        max_retry: at most how many times the screen will be scrolled

        Returns
        -------
        if succeeded:
            same return value as scroll
        if failed:
            None
        """
        for i in range(max_retry):
            ret = self.agent.ui.check(desc)
            if ret:
                return True
            self.scroll(direction, distance, duration)
        return False

    def ask_user(self, question):
        logger.debug(
            f'Perform question asking: {question}',
            action='scroll',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        res = self.agent.device.ask_question(question)
        logger.debug(
            f'Question answerd: {res}',
            action='scroll',
            status='done',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        self._after_action(action=f'question [{self.locate_description}] {question} {res}')
        return res

    def show_highlight(self):
        logger.debug(
            'Show highlight',
            action='show_highlight',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        x, y = self._get_center()
        # x1,y1,x2,y2 = self._get_bound()
        # width = x2 - x1
        # height = y2 - y1
        # radius = max(width, height) / 2
        radius = 100

        res = self.agent.device.show_highlight(x, y, radius)

        logger.debug(
            'Show highlight',
            action='show_highlight',
            status='done',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        self._after_action(action=f'show_highlight [{self.locate_description}] {x}, {y}, {radius}')
        return res
    
    def hide_highlight(self):
        logger.debug(
            'Hide highlight',
            action='hide_highlight',
            status='start',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        res = self.agent.device.hide_highlight()
        logger.debug(
            'Hide highlight',
            action='hide_highlight',
            status='done',
            metadata={'description': self.locate_description, "locator": self.agent.ui.view_locator.__class__.__name__}
        )
        self._after_action(action=f'hide_highlight [{self.locate_description}] ')
        return res

    def fetch_information(self, description: str, returns=None):
        """
        Fetch information from this view.
        """
        screenshot = self.agent.device.take_screenshot()
        prompt = f"Please retrieve the information described by \"{description}\" from the view specified by \"{self.description()}\"."

        if returns is None:
            returns = (description, str)
        res = self.agent.fm.query(screenshot, prompt, returns=returns)
        return res

    def fetch_image(self, description: str, save_path: Optional[str] = None):
        """
        Fetch image from this view.
        """
        des = f"\"{description}\" within \"{self.description()}\""
        return self.agent.device.take_screenshot_by_description(des, save_path)

    def locate_view(self, description: str):
        """
        Locate the view described by `description` inside the current view (self)
        A `description` is a text description of the view, eg. "the search button at the top-right corner".
        A vision language model (accessed with `fm.vlm`) is used to locate the view.
        """
        des = f"\"{description}\" within \"{self.description()}\""
        view = UI_View(self.agent, parent_view=self, locate_description=des)
        return view


class UI_Root(UI_View):
    def __init__(self, agent):
        super().__init__(agent, parent_view=None, locate_description='root')
        self._tag = 'ui.root'
        # self.state = cast(DeviceState, None)
        self.width, self.height = 0, 0
        self.all_view_dicts = None

    def _do_locate(self):
        # self.state = self.agent.device.get_current_state()

        self.width, self.height = self.agent.device.get_width_height()
        logger.debug(
            'Locate root view',
            action='locate root view',
            status='done',
            metadata={'width': self.width, 'height': self.height}
        )
        self.all_view_dicts = {}
        # self.bound = (0, 0, self.state.width, self.state.height)
        self.bound = (0, 0, self.width, self.height)
        self.view_id = 0

    def screenshot(self):
        return self.agent.device.take_screenshot()

    def fetch_information(self, description: str, returns=None):
        """
        Fetch information from this view.
        """
        screenshot = self.agent.device.take_screenshot()
        prompt = f"Please retrieve information described by \"{description}\" from the current screen."
        if returns is None:
            returns = (description, str)
        res = self.agent.fm.query(screenshot, prompt, returns=returns)
        return res
    

    def fetch_image(self, description: str, save_path: Optional[str] = None):
        """
        Fetch image from this view.
        """
        return self.agent.device.take_screenshot_by_description(description, save_path)


    def locate_view(self, description: str):
        """
        Locate the view described by `description` inside the current view (self)
        A `description` is a text description of the view, eg. "the search button at the top-right corner".
        A vision language model (accessed with `fm.vlm`) is used to locate the view.
        """
        des = f"\"{description}\""
        view = UI_View(self.agent, parent_view=self, locate_description=des)
        return view
