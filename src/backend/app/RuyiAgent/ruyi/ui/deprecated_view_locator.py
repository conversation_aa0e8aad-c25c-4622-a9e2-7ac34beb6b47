# -*- coding: UTF-8 -*-
"""
@Project:
@File   :deprecated_view_locator
@Date   :2024/12/10
<AUTHOR> Yuan
"""
import ast
import os
import traceback
from ruyi.utils.interface import RuyiInterface
from ruyi.utils import debug
from ruyi.ui.view_locator import Vision_View_Locator,View_Locator
from .view import UI_View
from openai import OpenAI
import re

import structlog
import anthropic
logger = structlog.get_logger(__name__)

class GPT_Vision_View_Locator(View_Locator):
    def __init__(self, agent):
        super().__init__(agent)

        self._tag = 'ui.tree_view_locator'

    def locate_view(self, parent_view, description):
        """
        locate the view using an LLM
        """
        parent_view_id = parent_view._get_view_id()
        from PIL import Image, ImageDraw, ImageFont
        bounding_boxes = self._get_bounding_boxes(parent_view)
        screen = self.agent.device.take_screenshot()
        screen_draw = ImageDraw.Draw(screen)
        # idx_font = ImageFont.truetype(size=24)
        idx_anno_list = []
        for idx, bounds in bounding_boxes.items():
            # print(f"idx: {idx}, bounds: {bounds}")
            # import pdb; pdb.set_trace()
            # invalid case: idx: 37, bounds: [[1476, 377], [1080, 538]]
            def min_max_sort(a, b):
                return min(a, b), max(a, b)
            x0, x1 = min_max_sort(bounds[0][0], bounds[1][0])
            y0, y1 = min_max_sort(bounds[0][1], bounds[1][1])
            if (x0, y0) in idx_anno_list:
                continue
            screen_draw.rectangle([x0, y0, x1, y1], width=3)
            screen_draw.text([x0 + 6, y0 + 6], text=f"{idx}", font_size=24)
            idx_anno_list.append((x0, y0))
        screen.save("temp_processed.png")
        try:
            matched_idx = self.agent.fm.vlm(
                f'Given the GUI view image below (every view is inside a white box, with its index beside the box), which view best-matches the description "{description}"?',
                screen,
                returns=['the matched view index in int, -1 if none matched']
            )
            matched_idx = int(matched_idx)
            all_view_dicts = parent_view.root.all_view_dicts
            if 0 <= matched_idx < len(all_view_dicts):
                matched_view = all_view_dicts[matched_idx]
                b = matched_view['bounds']
                bound = (b[0][0], b[0][1], b[1][0], b[1][1])
                return bound, matched_idx
        except Exception as e:
            logger.exception(f'{self._tag} _locate_view exception {e}', action='locate view', status='failed')
        return parent_view.bound, parent_view.view_id
    def _get_bounding_boxes(self, view):
        view_dict = view.root.all_view_dicts[view.view_id]
        # TODO using flatten view description currently, change to tree structure
        children_ids = view.root.state.get_all_children(view_dict)
        sorted_view_ids = sorted(set(children_ids + [view.view_id]))
        bounding_boxes = {}
        for idx in sorted_view_ids:
            bounding_boxes[idx] = view.root.all_view_dicts[idx]['bounds']
        return bounding_boxes


class Qwen_Vision_View_Locator(Vision_View_Locator):
    '''
    TODO:
    - locate view (ok)
    - iterate view
    '''

    def __init__(self, agent):
        super().__init__(agent)
        self._tag = 'ui.molmo_vision_view_locator'

    def _extract_points(self, response_str):
        debug.print_method_name_with_message('not implemented')

    def _extract_box(self, response_str):
        import re
        pattern = r"\<\|box_start\|\>\(([\d]+),([\d]+)\),\(([\d]+),([\d]+)\)\<\|box_end\|\>"
        matches = re.findall(pattern, response_str)
        if len(matches) > 0:
            # Convert the tuples of strings into tuples of integers
            return int(matches[0][0]) / 1000, int(matches[0][1]) / 1000, int(matches[0][2]) / 1000, int(
                matches[0][3]) / 1000

    def _format_locate_view_inputs(self, instruction):
        return f'''You are going to take an action on the UI interface of the image, following the instruction.\nPlease show the bounding box you want to interact with to complete the instruction.\n\nYour instruction:\nfind the {instruction}. Show me with a box.\n\nNote:\n1. Response Example: <|object_ref_start|>the OK button<|object_ref_end|><|box_start|>(241,429),(536,879)<|box_end|><|im_end|>. You should respond like this.\n2. Remember always to give a box to refer an item!\nBegin!'''

    def _format_iterate_view_inputs(self, instruction):
        return f'''You are going to find all desired items on the UI interface of the image, following the instruction.\nPlease point out all the points that match the instruction.\n\nYour instruction:\nPoint to all {instruction}.\n\nNote:\n1. Response Example: <|object_ref_start|>the OK button<|object_ref_end|><|box_start|>(241,429),(536,879)<|box_end|><|im_end|>. You should respond like this.\n2. Remember always to give all the points to refer the items!\n\nThe UI interface: <image>\n\nBegin!'''

    def _get_response(self, image, prompt):
        return self.agent.fm.cheap.qwen(image, prompt)

    def locate_view(self, parent_view, description):
        """
        locate the view using a Vision-Language Model
        """
        from PIL import Image
        from .view import UI_View
        assert isinstance(parent_view, UI_View)
        # view_image = parent_view.image()
        image = self.agent.device.take_screenshot()
        width, height = image.width, image.height
        response = self._get_response(image, self._format_locate_view_inputs(description))
        x0, y0, x1, y1 = self._extract_box(response)

        # import pdb; pdb.set_trace()
        def get_abs_pos(x, y):
            return x * width, y * height

        x0, y0 = get_abs_pos(x0, y0)
        x1, y1 = get_abs_pos(x1, y1)
        image.close()

        return (x0, y0, x1, y1), None

    def _iterate_views(self, parent_view, description):
        """
        results = ui.view_locator._iterate_views(ui.root, "contacts")
        locate the view using a Vision-Language Model
        """
        debug.print_method_name_with_message('not implemented')

        from .view import UI_View
        assert isinstance(parent_view, UI_View)
        image = self.agent.device.take_screenshot()
        response = self._get_response(image, self._format_iterate_view_inputs(description))
        points_list = self._extract_points(response)
        points_list = [(x / 100 * image.width, y / 100 * image.height) for x, y in points_list]
        image.close()

        return points_list, None
