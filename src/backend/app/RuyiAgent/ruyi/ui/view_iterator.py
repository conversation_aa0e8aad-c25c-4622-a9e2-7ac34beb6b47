from ruyi.utils.interface import RuyiInterface
import structlog
from collections import deque

logger = structlog.getLogger(__name__)


class View_Iterator(RuyiInterface):
    def __init__(self, agent, parent_view, description, limit=-1,
                 direction: str = 'down', distance=None, duration=1000):
        super().__init__(agent)
        self._tag = 'ui.view_iterator'
        self.limit = limit
        self.parent_view = parent_view
        self.description = description
        self.direction = direction
        if distance is None:
            if direction == 'down' or direction == 'up':
                self.distance = self.agent.device.height // 2
            else:
                self.distance = self.agent.device.width // 2

        self.more_button_flag = False  # flag for more button

        self.center_xy = None
        self.duration = duration

    def __iter__(self):
        self.history_element_descriptions = []  # elements visited
        self.elements = deque()  # elements not visited
        self.first_scan = True
        self.center_xy = None
        # self.scrollParam = None
        self.more_button_flag = False  # flag for more button
        return self

    def _cal_image_similarity(self, img1, img2):
        # # 转换为灰度图
        # gray_before = img1.convert("L")
        # gray_after = img2.convert("L")

        # # 转换为 NumPy 数组
        # arr_before = np.array(gray_before)
        # arr_after = np.array(gray_after)

        # # 计算 SSIM
        # score, _ = ssim(arr_before, arr_after, full=True)
        reason, is_different = self.agent.fm.vlm(img1, img2, "这两张照片分别是用户执行滑动操作前后的图片。请合理地判断滑动是否有效果，即使得界面发生了变化？", returns=[("界面变化了吗，请给出你的推理过程", str), ("界面是否发生变化", bool)])
        # print(reason)
        # return score
        return 0.0 if is_different else 1.0

    def __next__(self):
        from ruyi.ui.view import UI_View  # lazy import, this is important to prevent circular import

        if self.limit != -1 and len(self.history_element_descriptions) >= self.limit:
            raise StopIteration  # stop

        if len(self.elements) == 0:
            if not self.first_scan:
                screen_before = self.agent.device.take_screenshot()

                end_xy = (0, 0)
                if self.direction == 'up':
                    end_xy = (self.center_xy[0], self.center_xy[1] + self.distance)
                elif self.direction == 'down':
                    end_xy = (self.center_xy[0], self.center_xy[1] - self.distance)
                elif self.direction == 'right':
                    end_xy = (self.center_xy[0] - self.distance, self.center_xy[1])
                elif self.direction == 'left':
                    end_xy = (self.center_xy[0] + self.distance, self.center_xy[1])

                self.agent.device.drag(self.center_xy, end_xy, duration=self.duration)

                screen_after = self.agent.device.take_screenshot()
                if self._cal_image_similarity(screen_before, screen_after) > 0.88:
                    is_button, button_desc = self.agent.fm.vlm(
                        self.agent.device.take_screenshot(),
                        f"We are currently iterating over elements that match {self.description} on the current page, but it seems that no new elements can be found. Please check if there is a button on the current page labeled “See More” “Expand,” or something similar. Respond with a boolean indicating whether such a button exists. If it does, please provide the exact text on the button and describe its visual characteristics so that I can locate it based on your description.",
                        returns=[("Whether there is a “See More” or “Expand” button on the current page", "bool"),
                                    ("If there is a button, please provide the exact text and describe on the button",
                                    "str")]
                    )
                    if is_button:
                        self.agent.ui.root.locate_view(description=button_desc).click()
                        self.more_button_flag = True
                        # return self.__next__()
                    else:
                        raise StopIteration

            def parse_checklist(list_str):
                if type(list_str) is list:
                    if list_str[0].startswith("[") and list_str[-1].endswith("]"):
                        list_str[0] = list_str[0][1:]
                        list_str[-1] = list_str[-1][:-1]
                    return list_str
                if list_str.strip().startswith("("):
                    start_idx = list_str.find('(') + 1
                    end_idx = list_str.rfind(')')
                    list_str = list_str[start_idx:end_idx]
                    return list(eval(list_str))
                elif list_str.strip().startswith("["):
                    start_idx = list_str.find('[') + 1
                    end_idx = list_str.rfind(']')
                    list_str = list_str[start_idx:end_idx]
                    return list(eval(list_str))
                elif "," in list_str:
                    list_str = list_str.split(",")
                    for i in range(len(list_str)):
                        list_str[i] = list_str[i].strip()
                    return list(list_str)
                else:
                    raise ValueError(f'{list_str} is not a valid list')

            response = self.agent.ui.view_locator.get_views_by_list(self.description, self.history_element_descriptions)
            deduplicated_element_descriptions = parse_checklist(response)
            for element_description in deduplicated_element_descriptions:
                if self.center_xy is None:
                    bound, _ = self.agent.ui.view_locator.locate_view(self.parent_view, element_description)
                    if self.direction == 'down' or self.direction == 'up':
                        self.center_xy = (bound[0], self.agent.device.height // 2)
                    else:
                        self.center_xy = (self.agent.device.width // 2, bound[1])
                if self.more_button_flag:
                    self.more_button_flag = False
                self.elements.append(
                    UI_View(self.agent, parent_view=self.parent_view, locate_description=element_description))
            if self.first_scan:
                self.first_scan = False

        if len(self.elements) == 0:  # still nothing, exit
            if self.more_button_flag:
                raise StopIteration
            else:
                is_button, button_desc = self.agent.fm.vlm(
                    self.agent.ui.screenshot(),
                    f"We are currently iterating over elements that match {self.description} on the current page, but it seems that no new elements can be found. Please check if there is a button on the current page labeled “See More” “Expand,” or something similar. Respond with a boolean indicating whether such a button exists. If it does, please provide the exact text on the button and describe its visual characteristics so that I can locate it based on your description.",
                    returns=[("Whether there is a “See More” or “Expand” button on the current page", "bool"),
                             ("If there is a button, please provide the exact text and describe on the button", "str")]
                )
                if is_button:
                    self.agent.ui.root.locate_view(description=button_desc).click()
                    self.more_button_flag = True
                    return self.__next__()
                else:
                    raise StopIteration

        element = self.elements.popleft()
        assert isinstance(element, UI_View)
        self.history_element_descriptions.append(element.locate_description)
        return element
