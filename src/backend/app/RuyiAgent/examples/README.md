有可能的需求agent

1. 扫描TikTok视频，记录创作者的资料，对其进行分类，并将信息存储到数据库中。 （资料应包括账号、姓名、分类、描述、视频数量、粉丝数量）

2. 请你帮我辅导孩子写作业，用摄像头看着孩子的作业，如果有不会的，及时辅导

3. 制定去西安的三天旅行计划，要求包括拍古装照、享受美食美景，并体验当地文化。要求不坐飞机。

4. 将手机中所有与发票相关的照片整理到一个文件夹里。

5. 对所有的收支进行分析，按照不同的应用（如支付宝、淘宝、中国银行等）进行分类，并进行汇总。

6. 实时追踪某商品在所有购物应用（如淘宝、京东等）的价格，如果价格低于X元，立即提醒我购买。

7. 监控家中的摄像头，如果老人出门或有危险情况发生，立即打电话通知我，如果联系不上我，则立即报警。

8. 根据我司的xxx产品特点，自动在小红书里面寻找适合推广这个产品的达人（粉丝数大于1000且作品内容与产品相关），找到之后聊一聊，如果对方有意向，通知我。

9. 记录余额宝、理财通、以及各大银行的货币基金理财产品的收益率，如果其中有某x产品的收益率连续7天都高于y产品，且用户在y产品中有余额，询问用户是否转移，如果用户同意，则把y产品中的钱都转到x产品。

10. 请搜索附近5km以内的所有粤菜厅，并查询最便宜的2～4人套餐

11. 我即将去东京游玩，日期在10.11-10.22，请你在总价格$1000的预算内帮我推荐机票和住宿方案

12. 在微信上跟踪客户沟通记录，识别每个客户需求和排期，并整理信息到日历的对应议程中。如果需要预定会议，则打开腾讯会议自动设置对应时间的会议。

13. 为 HR 部门自动扫描各大招聘平台（如LinkedIn、Indeed等）上投递的简历，将资料信息存储到数据库中，基于职位要求初步筛选一些符合条件的候选人，并自动发送面试邀请。

14. 请根据天气，给我推荐一下，最近适合去哪里郊游，6人左右