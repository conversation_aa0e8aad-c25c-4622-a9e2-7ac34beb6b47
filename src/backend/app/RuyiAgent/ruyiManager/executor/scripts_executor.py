import sys
import os
import subprocess
import tempfile
import time
import re

# 根据操作系统选择不同的模块
if os.name == 'posix':
    import fcntl
else:
    import msvcrt

class ScriptsExecutor:
    """
    This module is used to execute scripts using RuyiAgent.
    """
    def __init__(self):
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        # 跨平台日志路径设置
        if os.name == 'nt':  # Windows 系统
            self.log_file = os.path.join(os.path.expanduser('~'), 'Documents', 'ruyi_agent.log')
        else:  # macOS/Linux 系统
            self.log_file = os.path.join(os.path.expanduser('~'), 'Desktop', 'ruyi_agent.log')
        
        self.execute_process = None
        self.task_end = False
        self.task_success = False

        # 定义模板字符串
        self.template_content = '''# -*- coding: utf-8 -*-
import sys
import os

# 添加 RuyiAgent 的 Python 路径
sys.path.append("{ruyi_agent_path}")

from ruyi.agent import RuyiAgent
from ruyi.task import RuyiTask
from ruyi.config import RuyiConfig, RuyiArgParser

class DynamicTask(RuyiTask):
    def __init__(self):
        super().__init__()
        self.description = "{task_description}"
        
    def main(self, agent):
        device, ui, data, fm, tool = agent.device, agent.ui, agent.data, agent.fm, agent.tools
{script_content}

if __name__ == '__main__':
    yaml_file = os.path.join("{ruyi_agent_path}", 'config.yaml')
    parser = RuyiArgParser((RuyiConfig,))
    config = parser.parse_yaml_file(yaml_file=yaml_file)[0]
    agent = RuyiAgent(config)
    task = DynamicTask()
    agent.task.execute_task(task)
'''

    def check_task_end(self, log_content):
        """检查任务是否结束"""
        if "error" in log_content:
            self.task_success = False
            return True
        elif "finish task" in log_content:
            self.task_success = True
            return True
        else:
            return False

    def execute_scripts(self, scripts, task="task"):
        self.task_end = False
        self.task_success = False

        # 如果上一次执行进程存在，则杀死进程
        if self.execute_process:
            self.execute_process.kill()
        self.execute_process = None
        print(f"{'='*60}")  # 添加分隔符
        print(f"开始新的任务执行")
        # 对 task 中的双引号进行转义
        escaped_task = task.replace('"', '\\"')
        print(f"任务名称: {escaped_task}")
        print(f"脚本内容:\n{scripts}")
        
        try:
            # 获取 RuyiAgent 路径（修复路径分隔符问题）
            ruyi_agent_path = os.path.dirname(os.path.dirname(self.current_dir))
            # 将路径转换为原始字符串格式
            ruyi_agent_path = os.path.normpath(ruyi_agent_path).replace('\\', '/')  # 统一使用正斜杠
            print(f"RuyiAgent 路径: {ruyi_agent_path}")
            # 准备脚本内容（添加适当的缩进）
            formatted_scripts = '\n'.join(f'        {line}' for line in scripts.splitlines())
            
            # 替换模板中的占位符（使用转义后的路径）
            task_content = self.template_content.format(
                ruyi_agent_path=ruyi_agent_path.replace('\\', r'\\'),  # 双重转义反斜杠
                task_description=escaped_task,
                script_content=formatted_scripts
            )
            
            # 创建临时文件
            try:
                with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_file:
                    temp_file.write(task_content)
                    temp_path = temp_file.name
                    print(f"创建临时文件: {temp_path}")
                    print(f"创建临时文件内容: {task_content}")
            except Exception as e:
                print(f"创建临时文件失败: {str(e)}")
                raise
            
            try:
                print(f"执行临时文件: {temp_path}")
                # 执行临时文件并实时获取输出
                self.execute_process = subprocess.Popen(
                    [sys.executable, "-u", temp_path],  # 添加 -u 参数强制无缓冲输出
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True,
                    env={**os.environ, 'PYTHONUNBUFFERED': '1'},  # 确保子进程无缓冲
                    start_new_session=True  # 创建新的进程组
                )
                print(f"执行临时文件启动成功")

                # 使用文件描述符进行非阻塞读取 RuyiAgent 的 log
                if os.name == 'posix':
                    # Unix/Linux 系统使用 fcntl
                    for fd in [self.execute_process.stdout, self.execute_process.stderr]:
                        fl = fcntl.fcntl(fd, fcntl.F_GETFL)
                        fcntl.fcntl(fd, fcntl.F_SETFL, fl | os.O_NONBLOCK)
                else:
                    # Windows 系统使用 msvcrt
                    for fd in [self.execute_process.stdout, self.execute_process.stderr]:
                        # 设置为非阻塞模式
                        msvcrt.setmode(fd.fileno(), os.O_BINARY)

                # 实时读取循环
                while True:
                    # Windows平台
                    if os.name == 'nt':
                        # 修改后的Windows读取逻辑
                        def win_read_available(fd):
                            try:
                                # 使用二进制模式读取
                                return os.read(fd.fileno(), 4096).decode('utf-8', errors='replace')
                            except BlockingIOError:
                                return ''
                            except Exception as e:
                                return ''

                        # 读取标准输出
                        try:
                            output = win_read_available(self.execute_process.stdout)
                            if output:
                                print(output, end='')
                                if self.check_task_end(output):
                                    self.task_end = True
                                    break
                        except Exception as e:
                            print(f"标准输出读取错误: {str(e)}")

                        # 读取错误输出
                        try:
                            error = win_read_available(self.execute_process.stderr)
                            if error:
                                print(error, end='', file=sys.stderr)
                                if self.check_task_end(error):
                                    self.task_end = True
                                    break
                        except Exception as ƒe:
                            print(f"错误输出读取错误: {str(e)}")

                    # macOS/Linux
                    else:
                        # 读取标准输出
                        output = ''
                        try:
                            chunk = self.execute_process.stdout.read()
                            if chunk:
                                # 统一转换为字符串
                                if isinstance(chunk, bytes):
                                    output = chunk.decode('utf-8', errors='replace')
                                else:
                                    output = str(chunk)
                                print(output, end='')
                                if self.check_task_end(output):
                                    self.task_end = True
                                    break
                        except Exception as e:
                            pass

                        # 读取错误输出
                        error = ''
                        try:
                            err_chunk = self.execute_process.stderr.read()
                            if err_chunk:
                                # 统一转换为字符串
                                if isinstance(err_chunk, bytes):
                                    error = err_chunk.decode('utf-8', errors='replace')
                                else:
                                    error = str(err_chunk)
                                print(error, end='', file=sys.stderr)
                                if self.check_task_end(error):
                                    self.task_end = True
                                    break
                        except Exception as e:
                            pass
                    
                    # 检查进程是否结束
                    if self.execute_process.poll() is not None:
                        break

                    # 适当降低CPU占用
                    time.sleep(0.1)

                success = self.task_success
                return success
                
            except Exception as e:
                raise
            
        except Exception as e:
            raise
            
        finally:
            # 清理临时文件
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except Exception as e:
                print(f"清理临时文件失败: {str(e)}")

if __name__ == '__main__':
    scripts = "device.start_app('Contacts')\nui.root.locate_view('Create Contact').click()"
    scripts = "device.start_app('Contacts')\nui.root.locate_view('Create Contact').click()\nui.root.locate_view('First name').input('Ruyi')\nui.root.locate_view('Number').input('1234567890')\nui.root.locate_view('Save').click()"
    executor = ScriptsExecutor()
    success = executor.execute_scripts(scripts, task="Create Contact")
    print(f"ScriptsExecutor 任务执行{'成功' if success else '失败'}")


