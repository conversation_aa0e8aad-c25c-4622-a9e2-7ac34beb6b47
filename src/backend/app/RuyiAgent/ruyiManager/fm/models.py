from openai import OpenAI
import base64
import os
import time
from ruyi.config import RuyiConfig
from ruyiManager.log.logger import Logger

class BaseModel:
    def __init__(self, config: RuyiConfig):
        self.config = config
        self.base_url = ""
        self.api_key = ""

        if self.config.use_ruyillm:
            self.base_url = self.config.ruyillm_url
            self.api_key = self.config.ruyillm_key
        else:
            self.base_url = self.config.fm_api_url
            self.api_key = self.config.fm_api_key

        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key
        )

    def query(self, *args, **kwargs):
        raise NotImplementedError("Subclasses should implement this method")


class LLM(BaseModel):
    def __init__(self, config: RuyiConfig):
        super().__init__(config)

    def query(self, prompt: str, model_name: str, temperature: float = 0.2, timeout: int = 120, strict_json: bool = False, properties: dict = {}) -> str:
        output_file_name = f"{time.strftime('%Y%m%d_%H%M%S')}_query.html"
        Logger.log_to_html(f"<b>----- Prompt -----</b><br>{prompt}", output_file_name=output_file_name)
        print("LLM query use model:", model_name)

        if not strict_json:
            completion = self.client.chat.completions.create(
                messages=[
                        {
                            "role": "user",
                            "content": prompt,
                        }
                    ],
                model=model_name,
                timeout=timeout, 
                temperature=temperature, 
            )
        else:
            completion = self.client.chat.completions.create(
                model=model_name, 
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                response_format=properties, 
                timeout=timeout
            )
        res = completion.choices[0].message.content
        Logger.log_to_html(f"<b>----- Response -----</b><br>{res}", output_file_name=output_file_name)
        
        return res

class VLM(BaseModel):
    def __init__(self, config: RuyiConfig):
        super().__init__(config)

    def query(self, prompt_txt: str, image_path, model_name: str='gpt-4o'):
        output_file_name = f"{time.strftime('%Y%m%d_%H%M%S')}_query.html"
        Logger.log_to_html(f"<b>----- Prompt -----</b><br>{prompt_txt}", output_file_name=output_file_name)
        prompt = []
        image = self.get_image(image_path)
        if image is not None:
            prompt.append({
                'type': 'image_url',
                'image_url': {
                    'url': f'data:image/jpeg;base64,{image}'
                }
            })
        else:
            print('warning, no image', '*'*50)
        
        prompt.insert(0, {
            'type': 'text',
            'text': prompt_txt
        })
        answer = self.retry_query_gptv2(prompt, model_name)
        
        Logger.log_to_html(f"<b>----- Response -----</b><br>{answer}", output_file_name=output_file_name)
        
        return answer
    def retry_query_gptv2(self, prompt: str, model_name: str='gpt-3.5-turbo-16k', retry_times=12):
        retry = 0

        completion = self.client.chat.completions.create(
        messages=[
                {
                    "role": "user",
                    "content": prompt,
                }
            ],
            model=model_name,
            timeout=15
        )
        res = completion.choices[0].message.content
        return res
    
    def encode_image(self, image_path):
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def get_image(self, image_path):
        if os.path.exists(image_path):
            return self.encode_image(image_path)
        else:
            return None