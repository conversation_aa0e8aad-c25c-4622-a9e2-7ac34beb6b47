class ScriptsExtractor:
    """
    This module is used to extract script from the response of LLM/VLM.
    """
    def __init__(self):
        pass

    def extract_scripts(self, scripts):
        if scripts.startswith("```") and scripts.endswith("```"):
            lines = scripts.splitlines()
            # 去掉第一行和最后一行
            lines = lines[1:-1]
            scripts = "\n".join(lines)
        return scripts

