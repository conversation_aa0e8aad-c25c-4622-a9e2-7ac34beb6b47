import json
import time

from ruyiManager.generator import PromptGenerator
from ruyiManager.utils import ScriptsExtractor
from ruyiManager.fm import LLM
from ruyi.config import RuyiConfig
from ruyiManager.log import Logger

class ScriptsGenerator:
    """
    根据 task, 生成初版的 script --> 包含 NL 和 DSL(code) 两种版本
    """
    def __init__(self, config: RuyiConfig):
        self.llm = LLM(config)
        self.prompt_generator = PromptGenerator()
        self.scripts_extractor = ScriptsExtractor()
    
    def generate_scripts(self, task):
        """
        New Version: 根据 task, 生成初版的 script --> 包含 NL 和 code 两种版本
        """
        init_prompt = self.prompt_generator.get_init_prompt()
        ruyi_introduction_prompt = self.prompt_generator.get_ruyi_introduction_prompt()
        NL_script_design_prompt = self.prompt_generator.get_NL_script_design_prompt()
        few_shot_with_NL_prompt = self.prompt_generator.get_few_shot_with_NL_prompt()
        app_knowledge_prompt = self.prompt_generator.get_app_knowledge_prompt()
        
        """
        {init_prompt}
        {ruyi_introduction_prompt}
        {NL_script_design_prompt}
        {few_shot_with_NL_prompt}

        现在你需要实现一个任务 < { task } >。
        你需要生成一段脚本，这段脚本需要遵循 agent 框架，以便能够完成这个任务。
        注意: 
            1. 对于 code script：
                1.1 `agent` 变量是在上下文中预定义的，你不需要在代码开头通过 `import agent` 导入这个变量。
                1.2 上下文中已经通过 `device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm` 声明了相关包，你可以直接使用，不需要再次声明这些相关包。
                1.3 如果你使用了子函数，那么：
                    1.3.1 这个子函数应该在其调用位置之前就被定义了，否则该子函数将无法被解析。
                    1.3.2 这个子函数的第一个参数应该 `agent`. 然后在调用该子函数的时候，也应该将这个 `agent` 参数传入进去
                    1.3.3 在子函数的开头声明需要的变量 `device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm`，否则会报错。
            2. 对于 NL script：
                2.1 你需要识别用户任务中的所使用的语言（如：中文、英文、日文、韩文等），然后使用对应的语言来写 NL script。
                2.2 你给出的自然语言描述的脚本应该尽量详细，能够和 code script 一一对应。
        请你先给出 python 脚本, 然后再将 python 脚本转换为自然语言描述的脚本。
        你的回复应该遵循如下的 json 格式，并且可以被 json.loads API 解析：
        {
            "code_script": "通过编程语言(python)描述的完成这个任务的脚本",
            "NL_script": "通过自然语言描述的完成这个任务的脚本",
        }
        """
        prompt = init_prompt + ruyi_introduction_prompt + NL_script_design_prompt + few_shot_with_NL_prompt + app_knowledge_prompt + \
                f"Now you need to implement a task named < { task } >.\n" + \
                "You need to generate script for this task according to the agent framework so that it can complete the task.\n" + \
"""Note:
    1. For code script:
        1.1 `agent` is predefined in context, you don't need to import it via `import agent` at the beginning of the script.
        1.2 The related packages are declared in context via `device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm`, you can use them directly without declaring them again.
        1.3 If you use sub-functions:
            1.3.1 The sub-function should be defined before its call position, otherwise it cannot be parsed.
            1.3.2 The first parameter of the sub-function should be 'agent'. When calling the sub-function, you should also pass in this 'agent' parameter
            1.3.3 Reference needed variables at the beginning of the sub-function: 'device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm'
    2. For NL script:
        2.1 You need to identify the language used in the user's task (e.g., Chinese, English, Japanese, Korean, etc.), then write the NL script in that language.
        2.2 Your natural language description script should be as detailed as possible and correspond one-to-one with the code script.
""" + \
                "Please give the python script first, and then translate the python script into a natural language description script." + \
                "Your answer should follow the json format below, and can be parsed by the `json.loads` API:\n" + \
                "{\n" + \
                "    \"code_script\": \"The script described in programming language(python) to complete the task\",\n" + \
                "    \"NL_script\": \"The script described in natural language to complete the task\",\n" + \
                "}"

        print("----- generate_first_script prompt -----")
        print(prompt)
        print("----------------------------------------")

        scripts = self.llm.query(prompt, 'gpt-4o')
        scripts = self.scripts_extractor.extract_scripts(scripts)
        print("----- scripts -----")
        print(scripts)
        print("--------------------")
        scripts = json.loads(scripts)

        output_file_name = f"{time.strftime('%Y%m%d_%H%M%S')}_scripts.html"
        Logger.log_to_html(f"<b>----- NL scripts -----</b><br>{scripts['NL_script']}", output_file_name=output_file_name)
        Logger.log_to_html(f"<b>----- DSL scripts -----</b><br>{scripts['code_script']}", output_file_name=output_file_name)
        return scripts



if __name__ == '__main__':
    print("ok")