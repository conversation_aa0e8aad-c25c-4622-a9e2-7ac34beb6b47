from ruyi.error_handler.generator_prompt import RUYI_FRAMEWORK_PROMPT

class PromptGenerator:
    """
    This module is used to generate prompts for LLM/VLM.
    """
    def __init__(self):
        self.app_knowledge = ""

    def get_init_prompt(self):
        prompt = """You are a designer of an app agent. The agent is designed to automate user operations on the phone interface.
Currently, there is a Python-based agent framework can be used to implement the agent."""
        return prompt
    
    def get_ruyi_introduction_prompt(self):
        return RUYI_FRAMEWORK_PROMPT

    def get_NL_script_design_prompt(self):
        """
        注意：使用这个框架构建 agent 的用户包含很多非技术人员，因此，也需要能够将这套框架对应到自然语言的形式，可以通过自然语言来编写脚本，来实现对 agent 的构建。
        实现自然语言描述脚本时，需要遵循以下原则：
        1. **结构化命令**: 使用结构化的命令格式，每个命令以一个动词开头，指示动作，后跟目标和任何附加参数。这类似于 Python 脚本中的函数调用。
        2. **顺序步骤**: 每个步骤应该清晰地编号或分离，以指示操作的顺序，类似于代码逐行执行。
        3. **动作关键词**: 使用特定的动词，如 "Open", "Click", "Input", "Wait", "Scroll", 等，来指示动作。这些动词直接映射到框架中的方法。
        4. **目标指定**: 明确指定每个动作的目标，例如视图描述或应用程序名称。
        5. **参数化**: 允许参数以一致的格式指定，例如使用引号表示字符串和括号表示附加选项。
        6. **条件逻辑**: 使用简单的 "If...Then..." 语句来处理逻辑。
        7. **循环和迭代**: 使用直接的语言来描述循环，例如 "Repeat X times" 或 "For each..."。
        8. **视图描述**: 利用界面中出现的视图描述作为目标，确保它们易于识别。
        9. **注释或解释**: 允许注释或解释提供上下文或附加指令，类似于代码中的注释。
        """
        prompt = """
Note: The user of this framework includes many non-technical users, so it is also necessary to be able to map this framework to a natural language form, so that the script can be written in natural language to implement the construction of the agent.
When implementing a natural language description script, the following principles should be followed:
1. **Structured Commands**: Use a structured format where each command starts with a verb that indicates the action, followed by the target and any additional parameters. This mirrors the function calls in the Python script.
2. **Sequential Steps**: Each step should be clearly numbered or separated to indicate the sequence of operations, similar to how code executes line by line.
3. **Action Keywords**: Use specific verbs like "Open", "Click", "Input", "Wait", "Scroll", etc., to denote actions. These map directly to methods in the framework.
4. **Target Specification**: Clearly specify the target of an action, such as a view description or an app name.
5. **Parameterization**: Allow parameters to be specified in a consistent format, such as using quotes for strings and parentheses for additional options.
6. **Conditional Logic**: Clearly define conditions and actions using simple "If...Then..." statements.
7. **Loops and Iterations**: Use straightforward language to describe loops, such as "Repeat X times" or "For each...".
8. **Use of Descriptions**: Leverage descriptions for UI elements as they appear in the interface, ensuring they are easily identifiable.
9. **Comments and Explanations**: Allow for comments or explanations to provide context or additional instructions, similar to comments in code."""
        return prompt

    def get_few_shot_with_NL_prompt(self):
        """如下是三个使用该框架编写的 task 用例.
        每个用例都分别包含使用 python 描述的脚本以及对应的使用自然语言描述的脚本。同时每个用例都做了多语种的适配。
        Task 1: “创建一个名为Ruyi，email为*******************的联系人。” :
        Task 2: “扫描微信视频号视频，记录创作者的资料，对其进行分类，并将信息存储到数据库中。（资料应包括名字、作者描述、典型作品描述、作品最多点赞量）。” :
        Task 3: “监控家中的摄像头，如果老人摔倒，立即发消息通知我，如果我5分钟没有回复，则立即发短信给紧急联系电话。” :"""
        
        prompt = """Below are three examples of tasks written using this framework.
Each example includes a Python script and corresponding natural language description scripts. And each example has been adapted to multiple languages:
Task 1: "Create a contact named Ruyi <NAME_EMAIL>." :
Python Language English Version:
```python
device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
device.start_app('Contacts') # Open Contacts app
ui.root.locate_view('Create Contact').click()  # Find 'Create Contact' Button and click on it
ui.ensure('A page for creating a new contact, including text fields for First Name, Last Name, Email, and a Save Icon')  # Use ensure() to ensure the view described by the string is present
ui.root.locate_view('First Name').input('Ruyi')  # Find 'First Name' EditText and input "Ruyi" into it
ui.root.locate_view('Email').input('<EMAIL>')  # Find 'Email' EditText and input "<EMAIL>" into it
ui.root.locate_view('SAVE or Done or Finish or Checkmark button in the top right corner').click()  # Find 'Save' Button and click on it
device.back()
device.back()
```

Python Language Chinese Version:
```python
device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
device.start_app('联系人') # 打开联系人应用
ui.root.locate_view('创建联系人按钮').click()  # 点击创建联系人按钮
ui.ensure('当前界面是一个包含姓名、邮箱、保存按钮的新建联系人页面')  # 确保页面存在
ui.root.locate_view('姓名').input('Ruyi')  # 找到姓名输入框并输入姓名
ui.root.locate_view('邮箱').input('<EMAIL>')  # 找到邮箱输入框并输入邮箱
ui.root.locate_view('右上角的保存按钮').click()  # 找到保存按钮并点击
device.back()
device.back()
```

Natural Language English Version:
```natural language
1. Open the "Contacts" app.
2. Click on "Create Contact".
3. Ensure "the page for creating a new contact, including text fields for First Name, Last Name, Email, and a Save Icon" is present.
4. Input "Ruyi" into "First Name" field.
5. Input "<EMAIL>" into "Email" field.
6. Click on "SAVE or Done or Finish or Checkmark button in the top right corner"
7. Navigate back 2 times.
```

Natural Language Chinese Version:
```natural language
1. 打开 "联系人" 应用。
2. 点击 "创建联系人按钮"。
3. 确保 "当前界面展示的是包含姓名、邮箱、保存按钮的新建联系人页面"。
4. 在 "姓名" 字段中输入 "Ruyi"。
5. 在 "邮箱" 字段中输入 "<EMAIL>"。
6. 点击 "右上角的保存按钮"。
7. 返回 2 次。
```


Task 2: "Scan WeChat video accounts, record creator information, classify them, and store the information in the database (information should include name, author description, typical work description, and the most likes of the works)." :
Python Language English Version:
```python
device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
device.start_app('WeChat')  # Open WeChat app

ui.root.locate_view('Discover').click()
ui.root.locate_view('Channels').click()
ui.root.locate_view('Hot').click()
profiles = data.live_table()  # Create a live table named profiles

for _ in range(2):
    video_desc = fm.vlm(device.take_screenshot(), 'Describe the video', returns=[('Video description', str)])
    num_likes = fm.vlm(device.take_screenshot(), 'The number of likes below the like icon in the bottom right corner of the interface', returns=[('Number of likes', int)])
    ui.root.locate_view('The nickname of the user on the left side of the thumbnail image in the bottom left corner of the interface').click()

    author_name = fm.vlm(device.take_screenshot(), 'The author's nickname on the right of the profile picture.', returns=[('Author nickname', str)])            
    author_desc = fm.vlm(device.take_screenshot(), 'Briefly describe the author based on the information displayed on the interface', returns=[('Author description', str)])
    
    ui.root.locate_view('The center of the thumbnail image in the bottom half of the interface').scroll(direction='down')
    
    for v in ui.root.iterate_views('The brief description of each thumbnail image in the bottom half of the interface', limit=6):
        v_num_likes = fm.vlm(device.take_screenshot(), f"The number of likes in the bottom right corner of the thumbnail image'{v.description}'", returns=[('Number of likes', int)])

        if v_num_likes > num_likes:
            num_likes = v_num_likes
    profiles.add_row(
        {'Author': author_name, 'Author description': author_desc, 'Typical work description': video_desc, 'Number of likes': num_likes})
    ui.back()
    ui.root.scroll(direction='down')
    logger.info(f'{author_name} crawled!', action='crawl wechat channels', status='done')
    agent.sleep(sleep_interval)  # sleep 3 seconds

ui.back()
ui.back()
ui.back()
ui.back()
```

Python Language Chinese Version:
```python
device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
device.start_app('微信')  # 打开微信

ui.root.locate_view('Discover').click()
ui.root.locate_view('Channels').click()
ui.root.locate_view('Hot').click()
profiles = data.live_table()  # 创建一个动态表格，名为profiles

for _ in range(2):
    video_desc = fm.vlm(device.take_screenshot(), '描述一下这个视频', returns=[('视频内容描述', str)])
    num_likes = fm.vlm(device.take_screenshot(), '界面右下角点赞图标下面的数字', returns=[('点赞数', int)])
    ui.root.locate_view('界面左下角的头像右侧的用户昵称').click()

    author_name = fm.vlm(device.take_screenshot(), '头像右边的作者昵称', returns=[('作者昵称', str)])            
    author_desc = fm.vlm(device.take_screenshot(), '根据界面展示的作者信息，简要描述这个作者', returns=[('作者描述', str)])
    
    ui.root.locate_view('The center of the thumbnail image in the bottom half of the interface').scroll(direction='down')
    
    for v in ui.root.iterate_views('屏幕下方每个缩略图的简要描述', limit=6):
        v_num_likes = fm.vlm(device.take_screenshot(), f"缩略图'{v.description}'右下角点赞数字", returns=[('点赞数', int)])

        if v_num_likes > num_likes:
            num_likes = v_num_likes
    profiles.add_row(
        {'作者': author_name, '作者描述': author_desc, '代表作品描述': video_desc, '点赞数': num_likes})
    ui.back()
    ui.root.scroll(direction='down')
    logger.info(f'{author_name} crawled!', action='crawl wechat channels', status='done')
    agent.sleep(sleep_interval)  # sleep 3 seconds

ui.back()
ui.back()
ui.back()
ui.back()
```

Natural Language English Version:
```natural language
1. Open "WeChat" app.
2. Click on "Discover".
3. Click on "Channels".
4. Click on "Hot".
5. Create a table named "profiles".
6. Repeat the following steps 2 times:
   6.1 Use VLM to describe the current video and store as "video_desc".
   6.2 Use VLM to get "The number of likes below the like icon in the bottom right corner of the interface" and store as "num_likes".
   6.3 Click on "The nickname of the user on the left side of the thumbnail image in the bottom left corner of the interface".
   6.4 Use VLM to get "The author's nickname on the right of the profile picture." and store as "author_name".
   6.5 Use VLM to "Briefly describe the author based on the information displayed on the interface" and store as "author_desc".
   6.6 Scroll down through "The center of the thumbnail image in the bottom half of the interface".
   6.7 For each of "The brief description of each thumbnail image in the bottom half of the interface", up to 9:
      6.7.1 Use VLM to get "The number of likes in the bottom right corner of the thumbnail image" and store as "v_num_likes".
      6.7.2 If "v_num_likes" is greater than "num_likes", update "num_likes".
   6.8 Add a row to "profiles" with "author_name", "author_desc", "video_desc", and "num_likes".
   6.9 Navigate back.
   6.10 Scroll down.
   6.11 Log "author_name" as crawled.
   6.12 Wait for 3 seconds.
7. Navigate back 4 times.
```

Natural Language Chinese Version:
```natural language
1. 打开 "微信" 应用。
2. 点击 "发现"。
3. 点击 "视频号"。
4. 点击 "推荐"。
5. 创建一个名为 "创作者信息" 的表格。
6. 重复以下步骤 2 次：
   6.1 使用 VLM 描述当前视频并存储为 "视频内容描述"。
   6.2 使用 VLM 获取 "界面右下角点赞图标下面的数字" 并存储为 "当前最多的点赞数"。
   6.3 点击 "界面左下角的头像右侧的用户昵称"。
   6.4 使用 VLM 获取 "头像右边的作者昵称" 并存储为 "作者昵称"。
   6.5 使用 VLM 获取 "根据界面展示的作者信息，简要描述这个作者" 并存储为 "作者描述"。
   6.6 在 "界面下半部分的缩略图的中心" 向下滚动。
   6.7 对于每个 "屏幕下方每个缩略图的简要描述"，最多 9 个：
      6.7.1 使用 VLM 获取 "缩略图右下角点赞数字" 并存储为 "视频点赞数"。
      6.7.2 如果 "视频点赞数" 大于 "当前最多的点赞数"，更新 "当前最多的点赞数"。
   6.8 添加一行 "创作者信息" 表格，包含 "作者昵称"、"作者描述"、"视频内容描述" 和 "当前最多的点赞数"。
   6.9 返回。
   6.10 向下滚动。
   6.11 记录 "作者昵称" 为已爬取。
   6.12 等待 3 秒。
7. 返回 4 次。
```


Task 3: "Monitor the home camera, if the elderly falls down, immediately send a message to notify me, if I don't reply within 5 minutes, send an SMS to the emergency contact." :
Python Language English Version:
```python
# Create a contact user (the owner of the Agent) and get the reply
def contact_wait(self, agent, name: str, msg: str, photo):
    device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
    device.start_app('WeChat')  # Open WeChat app
    
    ui.root.locate_view('The top right corner magnifying glass icon').click()
    ui.root.locate_view('The top search text box').input(name)
    ui.root.locate_view(f'The first contact in the contact list<{name}>').click()
    ui.root.locate_view('The bottom chat input text box').input(msg)
    ui.root.locate_view('The bottom right send button').click()
    response_view = ui.wait_view(f'The reply message below the message I sent<{msg}>', timeout=5 * 60)  # wait_view() function waits for a view to appear. If the view does not appear within the timeout, it returns False
    ui.back()
    ui.back()
    ui.back()
    return response_view

device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
device.start_app('Camera')
for _ in range(2):
    agent.sleep(sleep_interval)
    photo = device.take_screenshot()
    
    unsafe_flag = fm.vlm(photo, 'Whether there is a fall in the image', returns=[('True or False', bool)])
    if unsafe_flag:
        response = self.contact_wait(agent, 'owner', 'There is a fall in the house', photo)
        if response is None:
            logger.info('Fall detected and no user response!', action='monitor health', status='done')
            device.sms('12345678900', 'There is a fall in the house')
    agent.sleep(sleep_interval)
```

Python Language Chinese Version:
```python
# 创建一个联系用户（Agent的主人）并获得回复
def contact_wait(self, agent, name: str, msg: str, photo):
    device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
    device.start_app('微信')  # 打开微信
    
    ui.root.locate_view('界面右上角放大镜按钮').click()
    ui.root.locate_view('界面上方搜索文本框').input(name)
    ui.root.locate_view(f'联系人列表中的第一个<{name}>').click()
    ui.root.locate_view('底部的聊天输入文本框').input(msg)
    ui.root.locate_view('右下角的发送按钮').click()
    response_view = ui.wait_view(f'我发送的消息<{msg}>下方的回复消息', timeout=5 * 60)  # wait_view()函数等待某个view出现，如果timeout时间之后还没出现，返回 False
    ui.back()
    ui.back()
    ui.back()
    return response_view

device, ui, data, fm = agent.device, agent.ui, agent.data, agent.fm
device.start_app('相机')
for _ in range(2):
    agent.sleep(sleep_interval)
    photo = device.take_screenshot()
    
    unsafe_flag = fm.vlm(photo, '图片中是否有人摔倒', returns=[('True or False', bool)])
    if unsafe_flag:
        response = self.contact_wait(agent, '主人', '家里有人摔倒了', photo)
        if response is None:
            logger.info('Fall detected and no user response!', action='monitor health', status='done')
            device.sms('12345678900', '家里有人摔倒了')
    agent.sleep(sleep_interval)
```

Natural Language English Version:
```natural language
1. Define a function "send message and wait for reply":
   1.1 Open "WeChat" app.
   1.2 Click on "The top right corner magnifying glass icon".
   1.3 Input the contact name into "The top search text box".
   1.4 Click on "The first contact in the contact list".
   1.5 Input the message into "The bottom chat input text box".
   1.6 Click on "The bottom right send button".
   1.7 Wait for a response message for up to 5 minutes.
   1.8 Navigate back 3 times.
2. Open "Camera" app.
3. Repeat the following steps 2 times:
   3.1 Wait for a specified interval.
   3.2 Take a screenshot.
   3.3 Use VLM to check if anyone has fallen in the image.
   3.4 If a fall is detected:
      3.4.1 Send a message to "owner" saying "There is a fall in the house" with the photo.
      3.4.2 If no response is received within 5 minutes, send an SMS to "12345678900" with the message "There is a fall in the house".
   3.5 Wait for a specified interval.
```

Natural Language Chinese Version:
```natural language
1. 定义一个名为 "发送消息并等待回复" 的函数：
   1.1 打开 "微信" 应用。
   1.2 点击 "界面右上角放大镜按钮"。
   1.3 在 "界面上方搜索文本框" 中输入联系人名称。
   1.4 点击 "联系人列表中的第一个对应的联系人"。
   1.5 在 "底部的聊天输入文本框" 中输入消息。
   1.6 点击 "右下角的发送按钮"。
   1.7 等待 "我发送的消息下方的回复消息"，最多等待 5 分钟。
   1.8 返回 3 次。
2. 打开 "相机" 应用。
3. 重复以下步骤 2 次：
   3.1 等待指定时间间隔。
   3.2 截图。
   3.3 使用 VLM 检查图片中是否有人摔倒。
   3.4 如果检测到摔倒：
      3.4.1 向 "主人" 发送消息 "家里有人摔倒了" 并附上图片。
      3.4.2 如果 5 分钟内没有收到回复，向 "12345678900" 发送短信 "家里有人摔倒了"。
   3.5 等待指定时间间隔。
"""
        return prompt

    def set_app_knowledge(self, app_knowledge):
        self.app_knowledge = app_knowledge

    def get_app_knowledge_prompt(self):
        if self.app_knowledge != "":
            return f"""To complete this task, you can refer to the following information:
{self.app_knowledge}
"""
        init_prompt = """To complete this task, you can refer to the following information:"""
        task_prompt = """
Task: Date Picker
Instructions: 
    1. Click on the "Date" button.
    2. Click on the "year" button.
    
    # Find the target year. Since the target year may not be shown in the screen, we need to use VLM to judge whether the target year is shown in the screen. If not, we need to scroll down or scroll up to find it.
    3. Repeat the following steps, until "the 'target year' is shown in the screen":
        3.1 Use VLM to "judge whether the 'target year' is shown in the screen. If not, need to scroll down or scroll up?".
        3.2 If "the 'target year' is not shown in the screen":
            3.2.1 Scroll down(if the "target year" is greater than the current year shown on the screen) or scroll up (if the "target year" is less than the current year shown on the screen).
    4. Click on the "target year".
    
    # Find the target month. Since the target month may not be shown in the screen, we need to use VLM to judge whether the target month is shown in the screen. If not, we need to scroll left or scroll right to find it.
    5. Repeat the following steps, until "the 'target month' is shown in the screen":
        5.1 Use VLM to "judge whether the 'target month' is shown in the screen. If not, need to scroll left or scroll right?".
        5.2 If "the 'target month' is not shown in the screen":
            5.2.1 Scroll left(if the "target month" is greater than the current month shown on the screen) or scroll right (if the "target month" is less than the current month shown on the screen).
    6. Click on the "target month".

    # Find the target day. Since the target day has already been shown in the screen, we can directly click on it.
    7. Click on the "target day".
    
    8. Click on the "OK" button.
"""
        prompt = init_prompt + task_prompt
        return prompt
