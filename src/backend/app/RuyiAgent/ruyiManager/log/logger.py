import os

class Logger:
    @staticmethod
    def log_to_html(content: str, image_path: str = None, output_file_name: str = 'ruyi.html'):
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        output_dir = os.path.join(base_dir, 'output')
        os.makedirs(output_dir, exist_ok=True)
        log_path = os.path.join(output_dir, output_file_name)
        
        mode = 'a' if os.path.exists(log_path) else 'w'
        
        with open(log_path, mode, encoding='utf-8') as f:
            if mode == 'w':
                f.write('''
                <html>
                <head>
                    <style>
                        pre {
                            background-color: #f5f5f5;
                            padding: 10px;
                            border-radius: 5px;
                            border: 1px solid #ddd;
                            white-space: pre-wrap;
                            word-wrap: break-word;
                            font-family: Consolas, monospace;
                            font-size: 14px;
                        }
                    </style>
                </head>
                <body style="font-family: Arial, sans-serif;">
                ''')
            
            f.write(f'<div style="margin: 20px; padding: 15px; border: 1px solid #ccc;">')
            f.write(f'<pre>{content}</pre>')
            
            if image_path:
                abs_path = os.path.abspath(image_path)
                if os.path.exists(abs_path):
                    f.write(f'<br><img src="{abs_path}" style="max-width: 800px; margin-top: 10px;">')
                else:
                    f.write(f'<br><span style="color: red;">Image not found: {abs_path}</span>')
            
            f.write('</div>')
            
            if mode == 'w':
                f.write('</body></html>')

