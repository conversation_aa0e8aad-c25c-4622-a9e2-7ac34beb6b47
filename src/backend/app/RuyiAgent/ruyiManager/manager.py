from ruyiManager.generator import ScriptsGenerator
from ruyiManager.executor import ScriptsExecutor
from ruyi.config import RuyiConfig

class RuyiManager():
    def __init__(self, config: RuyiConfig):
        self.config = config
        self.task = ""
        self.NL_script = ""
        self.DSL_script = ""
        self.scripts_generator = ScriptsGenerator(config)
        self.scripts_executor = ScriptsExecutor()

    def generate_scripts(self, task):
        generated_scripts = self.scripts_generator.generate_scripts(task)
        self.NL_script = generated_scripts["NL_script"]
        self.DSL_script = generated_scripts["code_script"]
        return generated_scripts

    def execute_scripts(self, scripts):
        return self.scripts_executor.execute_scripts(scripts)

    def start_task(self, task):
        self.generate_scripts(task)
        self.execute_scripts(self.DSL_script)
