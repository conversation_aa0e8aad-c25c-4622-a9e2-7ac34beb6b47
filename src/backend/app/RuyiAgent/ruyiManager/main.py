import os
import sys
from ruyiManager.manager import RuyiManager
from ruyi.config import RuyiConfig, RuyiArgParser

if __name__ == '__main__':
    yaml_file = os.path.join(os.path.dirname(__file__), '..', 'config.yaml')
    if not os.path.exists(yaml_file):
        raise FileNotFoundError(f'Config file not found: {yaml_file}, you need copy config_example.yaml to config.yaml and modify it according to your environment.')
    parser = RuyiArgParser((RuyiConfig,))   # type: ignore
    config = parser.parse_yaml_file(yaml_file=yaml_file)
    config = config[0]
    manager = RuyiManager(config)
    
    if len(sys.argv) < 2:
        task = input("请提供任务名称：")
    else:
        task = sys.argv[1]

    manager.start_task(task)
