# RuyiAgent
Framework for building intelligent, reliable and efficient mobile agents.

## Preparation

### Prepare the RuyiAgent
- Install the frameworks:

```
conda create -n ruyi python=3.12
conda activate ruyi
git clone https://github.com/MobileLLM/Droidbot-llm.git
pip install -e Droidbot-llm --use-pep517
git clone https://github.com/MobileLLM/RuyiAgent.git
pip install -e RuyiAgent --use-pep517
```

- Copy `config_example.yaml` to `config.yaml` and fill in the necessary information.
- **Note that do not commit `config.yaml` to version control.**

### Prepare the RuyiClient device

Ruyi supports two types of devices: ADB or RuyiClient App

* For ADB, you need install ADB and connect the device to your computer through wireless or cable.
* For RuyiClient App, you need:
  1. Install the [RuyiClient](ruyi/resources/RuyiClientAPK.apk) App manually on your device.
  2. Let RuyiClient device and RuyiAgent device run in the same LAN or config your own proxy server to forward the traffic.
  3. Start the RuyiClient App on your device and click "Start" button and set the IP address and port of the RuyiAgent device to your RuyiAgent `config.yaml`.

## Run

Run with `ruyi` command:
```
ruyi config.yaml
```
You can also pass arguments through command line. See `ruyi -h`.

Optionally, you can run with `ruyi/main.py`:
```
cd RuyiAgent/ruyi
python main.py <config.yaml>
```

## Development

Follow the standard guidelines to contribute to this project.
Major changes should be made through pull requests.
Before sending a pull request, please make sure to pass the test cases.
```
cd RuyiAgent/ruyiTest
python test.py
```

## Usage

There are four main interfaces that can be used to develop an agent task, including:

- `ruyi.device`: The interfaces to call system-level APIs of the target device.
- `ruyi.ui`: The interfaces to interact with the device GUI.
- `ruyi.tools`: The interfaces to third-party API tools.
- `ruyi.fm`: The interfaces to get answers from foundation models.
- `ruyi.data`: The interfaces to use common data structures and common data resources (photos, sensors, etc.) on the device.
- `ruyi.chat`: The interfaces to communicate with user or other agents.

### Examples:

See `ruyiTest/task_*` for examples of tasks.
