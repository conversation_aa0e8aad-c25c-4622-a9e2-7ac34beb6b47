# set up basic requirements for droidbot
from setuptools import setup, find_packages
import glob
import os

def read_version():
    with open('VERSION', 'r') as f:
        return f.read().strip()

setup(
    name='ruyi',
    packages=find_packages(include=['ruyi']),
    # this must be the same as the name above
    version=read_version(),
    description='Development framework and runtime system for mobile agents.',
    author='RuyiAgent Team',
    license='CUSTOM',
    author_email='<EMAIL>',
    url='https://github.com/MobileLLM/RuyiAgent',  # use the URL to the github repo
    download_url='https://github.com/MobileLLM/RuyiAgent/tarball/0.0.1a1',
    keywords=['AI', 'agent', 'mobile', 'framework', 'LLM'],  # arbitrary keywords
    classifiers=[
        # How mature is this project? Common values are
        #   3 - Alpha
        #   4 - Beta
        #   5 - Production/Stable
        'Development Status :: 3 - Alpha',

        # Indicate who your project is intended for
        'Intended Audience :: Developers',
        'Topic :: Software Development',

        # Specify the Python versions you support here. In particular, ensure
        # that you indicate whether you support Python 2, Python 3 or both.
        'Programming Language :: Python',
    ],
    entry_points={
        'console_scripts': [
            'ruyi=ruyi.main:main',
        ],
    },
    package_data={
        'ruyi': [os.path.relpath(x, 'ruyi') for x in glob.glob('ruyi/resources/**/*', recursive=True)]
    },
    install_requires=[
        'openai', 
        'droidbot @ git+https://github.com/MobileLLM/Droidbot-llm.git#egg=droidbot',
        'deprecated',
        'pandas',
        'rich',
        # 'websocket',
        'websocket-client',
        'Anthropic',
        "structlog",
        "influxdb-client",
    ],
)
