---
hide:
  - navigation
  - toc
---

# RuyiAgent中文文档

> Framework for building intelligent, reliable and efficient mobile agents.
> 
> 构建和支撑更智能、更可靠、更高效的移动端智能体框架。

!!! success "欢迎每个参与者! :hugging:"
    欢迎所有人参与到ChainStream的开发和使用中来，无论是开发者、用户、贡献者、或者其他任何角色。

!!! note "加入我们! :raising_hand:"
    ChainStream 仍在开发中， 我们欢迎所有用户、建议者和贡献者参与。 欢迎通过[Github Repo](https://github.com/MobileLLM/RuyiAgent)与我们联系。
    同样的，本文档仍在开发中，请定期查看更新。ChainStream

## 文档结构

对于每个读者，我们都建议您先阅读：

- [:octicons-book-24: RuyiAgent概览](overview/whats_ruyiagent.md){ .md-button } 了解RuyiAgent的基本概念和功能。

本文档面向四个主要受众：

- **RuyiAgent 用户**：希望使用 RuyiAgent 或将其集成到现有系统中的用户。尚未完成，敬请期待。
- **Agent开发者**：希望了解 RuyiAgent 的内部工作原理并构建自己的智能体的开发者。尚未完成，敬请期待。
- **RuyiAgent系统贡献者**：希望为 RuyiAgent 的开发做出贡献的系统开发者。请阅读 [:fontawesome-solid-code: 系统贡献者者指南](system_developer/api_document.md){ .md-button }
- **学者**：希望复现论文结果，或者使用 RuyiAgent 框架进行研究的学者。尚未完成，敬请期待。


## 更新日志

- 2024.10: RuyiAgent项目正式启动。 

## 版权声明
- 本工作由清华大学智能产业研究院AIoT团队的RuyiAgent项目组推出。
- 后续将发布更详细的引用格式。