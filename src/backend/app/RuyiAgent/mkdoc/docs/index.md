---

hide:
  - navigation
  - toc
---

# RuyiAgent Documentation

> Framework for building intelligent, reliable and efficient mobile agents.
> 
> 构建和支撑更智能、更可靠、更高效的移动端智能体框架。

!!! success "Welcome to Every Participant! :hugging:"
    We welcome everyone to participate in the development and use of RuyiAgent, whether as a developer, user, contributor, or any other role.

!!! note "Join Us! :raising_hand:"
    RuyiAgent is still under development, and we welcome all users, suggesters, and contributors to participate. Feel free to connect with us through the [Github Repo](https://github.com/MobileLLM/RuyiAgent).
    Likewise, this documentation is also under development, so please check back regularly for updates.

## Documentation Structure

For all readers, we recommend starting with:

- [:octicons-book-24: RuyiAgent Overview](overview/whats_ruyiagent.md){ .md-button } to learn about the basic concepts and features of RuyiAgent.

This documentation is intended for four main audiences:

- **RuyiAgent Users**: Users who want to use RuyiAgent or integrate it into existing systems. Not yet completed, stay tuned.
- **Agent Developers**: Developers who wish to understand the internal workings of RuyiAgent and build their own agents. Not yet completed, stay tuned.
- **RuyiAgent System Contributors**: System developers who want to contribute to the development of RuyiAgent. Please refer to the [:fontawesome-solid-code: System Contributor Guide](system_developer/api_document.md){ .md-button }
- **Scholars**: Scholars aiming to replicate paper results or use the RuyiAgent framework for research. Not yet completed, stay tuned.

## Update Log

- 2024.10: The RuyiAgent project officially launched.

## Copyright Notice
- This work is launched by the RuyiAgent project team of the AIoT team at the Institute for AI Industry Research, Tsinghua University.
- More detailed citation formats will be released in the future.