site_name: RuyiAgent
site_description: Build your own agents to help anything!
site_author: RuyiAgent Team
site_url: https://github.com/MobileLLM/RuyiAgent

copyright: Copyright &copy; 2024 AIR-AIoT-RuyiAgent Team. All rights reserved.

repo_name: RuyiAgent
repo_url: https://github.com/MobileLLM/RuyiAgent

nav:
  - Home: index.md
  - Overview: overview/whats_ruyiagent.md
  - API Doc:
      - Main API: system_developer/api_document.md
      - Interface: 
        - RuyiInterface: system_developer/interfaces/ruyi_interface.md
        - Data_Interface: system_developer/interfaces/data_interface.md
      - ruyi.data:
        - LiveTable: system_developer/ruyi.data/live_table.md
        - Text: system_developer/ruyi.data/text.md
        - Image: system_developer/ruyi.data/image.md


extra:
#  alternate:
#    - name: English
#      link: en/
#      lang: en
#    - name: 中文
#      link: /zh/
#      lang: zh
  version:
    provider: mike

plugins:
  - search: # 搜索插件
  - mkdocstrings:
  - minify: # 压缩插件
      minify_html: true
  - i18n: # 多语言插件
      docs_structure: suffix # 抄来的，不太懂
      fallback_to_default: true # 抄来的，不太懂
      reconfigure_material: true # 抄来的，不太懂
      reconfigure_search: true # 抄来的，不太懂
      languages: # 多语言配置 - 需要小心一点
        - locale: en
          default: true # 默认语言
          name: English
          build: true # 是否构建
          # site_name: Infinity
        - locale: zh
          name: 简体中文
          build: true
          nav_translations: # 导航栏翻译，不可以有缩进
            Home: 首页
            API Doc: API文档
            Overview: 概览


theme:
  name: material
  custom_dir: overrides
  language: en
  features:
    - content.tabs.link
    - content.code.annotate
    - content.code.copy
    - announce.dismiss
    - navigation.tabs
    - navigation.instant
    - navigation.instant.prefetch
    - navigation.instant.preview
    - navigation.instant.progress
    - navigation.path
    - navigation.sections
    - navigation.top
    - navigation.tracking
    - search.suggest
    - toc.follow

  palette:

    - scheme: default
      primary: purple
      accent: red
      toggle:
        icon: material/lightbulb-outline
        name: Switch to light mode

    # Toggle dark mode
    - scheme: slate
      primary: blue
      accent: blue
      toggle:
        icon: material/lightbulb
        name: Switch to dark mode

  icon:
    admonition:
      note: octicons/tag-16
      abstract: octicons/checklist-16
      info: octicons/info-16
      tip: octicons/squirrel-16
      success: octicons/check-16
      question: octicons/question-16
      warning: octicons/alert-16
      failure: octicons/x-circle-16
      danger: octicons/zap-16
      bug: octicons/bug-16
      example: octicons/beaker-16
      quote: octicons/quote-16

markdown_extensions:
    - toc:
        permalink:
    - admonition
    - attr_list
    - def_list
    - pymdownx.details
    - pymdownx.superfences
    - md_in_html
    - tables
    - attr_list
    - pymdownx.critic
    - pymdownx.caret
    - pymdownx.keys
    - pymdownx.mark
    - pymdownx.tilde
    - pymdownx.emoji:
        emoji_index: !!python/name:material.extensions.emoji.twemoji
        emoji_generator: !!python/name:material.extensions.emoji.to_svg



