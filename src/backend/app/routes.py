"""
Ruyi Cloud IDE Backend App
"""
import time
import yaml
import os
from multiprocessing import Process, Queue
import psutil
import io
import base64
import uuid
from . import socketio

from flask import Blueprint, request, make_response, jsonify

from config import config
from app.core import ScriptsExecutor
from app.utils import WebSocketManager, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>elper
from app.utils.browser_client import browser_client

# 创建一个蓝图
main = Blueprint('main', __name__)

flask_port = config.flask_port
device_id = ""
device_type = ""
app_helper = AppHelper()
adb_helper = ADBHelper()

scripts_executor = ScriptsExecutor()


# 初始化 Appium
driver = None
websocket_manager = WebSocketManager()

# 初始化记录 vh、录屏 的类
timestamp = time.time()

# 初始化记录的 flag
start_flag = False
record_flag = False  # True: recorder 记录中, False: recorder 没在记录中
generate_flag = False  # True: ScriptsGenerator 正在生成脚本, False: ScriptsGenerator 没有在生成脚本中
execute_flag = False  # True: 正在执行 scripts, False: 没有在执行 scripts

action_group = []  # 最新的一组 action, 包含一个非并发的 action 和可能的多个并发的 action
vh_info_previous = {}  # 用于存储上一次 vh_info, 给 ScriptsGenerator 用
vh_info = {}

global_task = ""
global_NL_script = ""
global_code_script = ""
global_question = ""
global_selected_NL_script = ""
global_selected_code_script = ""
global_start_line = -1
global_end_line = -1

execute_process = None  # 全局的 RuyiAgent 执行进程, 用于记录当前在执行 script 的进程, 保证只有一个 RuyiAgent 进程在执行

# 在全局初始化时创建 Queue, 用于存储获取到的 Ruyi Agent 的日志
log_queue = Queue()

# 用于 ask_question 请求与回答的关联
request_events = {}

@socketio.on('answer_question')
def handle_answer_question(data):
    """处理来自 Electron 客户端的回答"""
    request_id = data.get('request_id')
    answer = data.get('answer')
    if request_id and request_id in request_events:
        result_holder = request_events[request_id]
        result_holder['answer'] = answer

@main.route('/')
def hello_ruyi():
    return 'Backend of Ruyi Studio!'

@main.route('/execute', methods=['GET', 'POST', 'OPTIONS'])
def execute():
    """
    对接 Ruyi Agent, 执行 scripts
    """
    global execute_flag
    global execute_process

    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'

        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'  # 可以缓存预检请求的结果
        return response
    
    # 先检查 websocket 是否连接
    if device_type == 'websocket' and not websocket_manager.ensure_connected():
        return make_response(jsonify({'error': 'WebSocket connection error'}), 500)
    
    # 再处理 GET, POST 请求
    task = request.get_json()['task']
    code_script = request.get_json()['codeScript']
    code_script_labeled = request.get_json()['codeScriptLabeled']
    NL_script_labeled = request.get_json()['NLScriptLabeled']
    # NL_script = request.get_json()['NLScript']
    # selected_NL_script = request.get_json()['selectedNLScript'] if 'selectedNLScript' in request.get_json() else ""
    # selected_code_script = request.get_json()['selectedCodeScript'] if 'selectedCodeScript' in request.get_json() else ""
    # start_line = request.get_json()['startLine'] if 'startLine' in request.get_json() else -1
    # end_line = request.get_json()['endLine'] if 'endLine' in request.get_json() else -1

    execute_flag = True
    # 新开一个进程调用 Ruyi Agent 执行 scripts
    if execute_process and execute_process.is_alive():
        execute_process.terminate()
    execute_process = Process(target=execute_scripts, args=(code_script, code_script_labeled, NL_script_labeled, task, log_queue))
    execute_process.start()

    # 新开一个线程调用 Ruyi Agent 执行 scripts, 此处新开一个线程的话, 只能执行一次代码, 之后就会失败
    # execute_thread = threading.Thread(target=execute_scripts, args=(code_script, task))
    # execute_thread.start()
    # execute_scripts(scripts, task)
    # get_task_activities()

    return make_response('', 200)

def execute_scripts(script, code_script_labeled, NL_script_labeled, task="task", log_queue=None):
    """
    子线程: 调用 Ruyi Agent 执行 script
    """
    global execute_flag
    global scripts_executor

    # 将 log_queue 传递给 scripts_executor
    scripts_executor.set_log_queue(log_queue)
    scripts_executor.execute_scripts(script, code_script_labeled, NL_script_labeled, task)
    
    # 执行完毕，调整 execute_flag
    execute_flag = False

@main.route('/stop_execute', methods=['GET', 'POST', 'OPTIONS'])
def stop_execute():
    """
    停止执行 scripts
    """
    global execute_flag
    global execute_process

    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'

        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'  # 可以缓存预检请求的结果
        return response

    if execute_process:
        try:
            # 获取主进程
            parent = psutil.Process(execute_process.pid)
            
            # 获取所有子进程
            children = parent.children(recursive=True)
            
            # 终止所有子进程
            for child in children:
                try:
                    child.terminate()
                except psutil.NoSuchProcess:
                    pass
            
            # 终止主进程
            execute_process.terminate()
            
            # 等待进程结束
            execute_process.join(timeout=3)
            
            # 如果进程仍然存活，强制结束
            if execute_process.is_alive():
                # 强制终止所有子进程
                for child in children:
                    try:
                        child.kill()
                    except psutil.NoSuchProcess:
                        pass
                # 强制终止主进程
                execute_process.kill()
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
            pass

    execute_flag = False
    return make_response('', 200)

@main.route('/get_adb_path', methods=['GET', 'POST', 'OPTIONS'])
def get_adb_path():
    """
    获取 adb 路径
    """
    adb_path = os.environ['PATH']
    return make_response(jsonify({'adb_path': adb_path}), 200)

@main.route('/get_executable_path', methods=['GET', 'POST', 'OPTIONS'])
def get_executable_path():
    """
    获取可执行文件路径, 也就是调用启动 flask 进程的 python 路径, 由 ScrcpyManager 调用决定
    """
    import sys
    executable_path = sys.executable
    return make_response(jsonify({'executable_path': executable_path}), 200)

@main.route('/set_device', methods=['GET', 'POST', 'OPTIONS'])
def set_device():
    """
    设置设备，传入 device_id 和 device_type
    """
    global device_id
    global device_type

    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'

        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'  # 可以缓存预检请求的结果
        return response
    
    # 再处理 GET, POST 请求
    request_json = request.get_json()
    print("----- set_device -----")
    print(request_json)
    print("-------------------")
    device_id_last = device_id
    device_id = request_json['device_id']
    device_type = request_json.get('device_type', 'websocket')  # 默认为 websocket 类型

    if device_id_last == device_id:
        return make_response('', 200)

    # 将 device_type 写入到 config.yaml 中
    ruyi_config_path = os.path.join(os.path.dirname(__file__), 'RuyiAgent', 'config.yaml')
    with open(ruyi_config_path, 'r') as f:
        ruyi_config = yaml.load(f, Loader=yaml.SafeLoader)
    
    ruyi_config['device_type'] = device_type
    
    # 如果是浏览器设备，不需要设置 device_url
    if device_type == 'browser':
        ruyi_config['device_url'] = None
    else:
        # 修改 adb forward 命令的执行
        device_port = config.device_port
        app_helper.forward_port(device_id, device_port)
        device_url = f"ws://localhost:{device_port}"
        print(device_url)
        ruyi_config['device_url'] = device_url
        
        # 修改 WebSocket 的 server_address
        websocket_manager.change_server_address(device_url)
        websocket_manager.set_device_id(device_id)
    
    with open(ruyi_config_path, 'w') as f:
        yaml.dump(ruyi_config, f)

    return make_response('', 200)

@main.route('/check_ruyi_client_installed', methods=['GET', 'POST', 'OPTIONS'])
def check_ruyi_client_installed():
    """
    判断 Ruyi Client 是否安装
    """
    is_installed = app_helper.is_ruyi_client_installed(device_id)
    # is_installed = False
    # print("----- check_ruyi_client_installed -----")
    # print(f"is_installed: {is_installed}")
    # print("-------------------")
    return make_response(jsonify({'is_ruyi_client_installed': is_installed}), 200)

@main.route('/install_ruyi_client', methods=['GET', 'POST', 'OPTIONS'])
def install_ruyi_client():
    """
    向用户手机上安装 Ruyi Client, 并授予相关权限
    检查是否已安装，未安装则进行安装
    授予必要的权限（无障碍服务和录屏权限）
    """
    print("----- install_ruyi_client -----")
    print(f"device_id: {device_id}")
    app_helper.install_ruyi_client(device_id)
    print("----- install_ruyi_client success -----")
    return make_response('', 200)

@main.route('/start_ruyi_client', methods=['GET', 'POST', 'OPTIONS'])
def start_ruyi_client():
    """
    启动 Ruyi Client
    """
    print("----- start_ruyi_client -----")
    print(f"device_id: {device_id}")
    print("-------------------")
    app_helper.start_ruyi_client(device_id)
    return make_response('', 200)

@main.route('/check_ruyi_client_connected', methods=['GET', 'POST', 'OPTIONS'])
def check_ruyi_client_connected():
    """
    检查 Ruyi Client 是否连接
    """
    connected = websocket_manager.ensure_connected()
    return make_response(jsonify({'is_ruyi_client_connected': connected}), 200)

@main.route('/check_ruyi_client_authorized', methods=['GET', 'POST', 'OPTIONS'])
def check_ruyi_client_authorized():
    """
    检查 Ruyi Client 是否授权
    """
    authorized = app_helper.check_ruyi_client_authorized(device_id)
    return make_response(jsonify({'is_ruyi_client_authorized': authorized}), 200)

@main.route('/check_ruyi_client_version', methods=['GET', 'POST', 'OPTIONS'])
def check_ruyi_client_version():
    """
    检查 Ruyi Client 版本
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response

    request_json = request.get_json()
    check_device_id = request_json.get('device_id', device_id) if request_json else device_id
    
    installed_version = app_helper.get_ruyi_client_version(check_device_id)
    target_version = config.get('ruyi_client_version', '0.2.0.3')
    needs_update = app_helper.should_update_ruyi_client(check_device_id)
    
    return make_response(jsonify({
        'installed_version': installed_version,
        'target_version': target_version,
        'needs_update': needs_update
    }), 200)

@main.route('/check_and_update_ruyi_client', methods=['GET', 'POST', 'OPTIONS'])
def check_and_update_ruyi_client():
    """
    检查并自动更新 Ruyi Client
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response

    request_json = request.get_json()
    check_device_id = request_json.get('device_id', device_id) if request_json else device_id
    
    print(f"----- check_and_update_ruyi_client for device {check_device_id} -----")
    
    result = app_helper.check_and_update_ruyi_client(check_device_id)
    
    print(f"----- check_and_update_ruyi_client result: {result['message']} -----")
    if 'permissions_granted' in result:
        print(f"----- permissions_granted: {result['permissions_granted']} -----")
    
    return make_response(jsonify(result), 200)

@main.route('/health')
def health_check():
    return make_response('', 200)

@main.route('/get_ruyi_agent_log', methods=['GET', 'POST', 'OPTIONS'])
def get_ruyi_agent_log():
    """
    获取 Ruyi Agent 日志
    先尝试每次把所有的日志内容发送，如果前端显示会乱跳的话，就记录一下哪些发了哪些没发
    返回从上次调用后新产生的日志内容
    """
    global scripts_executor

    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response

    # 获取日志内容
    log_content = scripts_executor.get_log()

    # 在 get_ruyi_agent_log 中从 Queue 获取日志
    log_content = []
    while not log_queue.empty():
        log_content.append(log_queue.get())
    # log_content = "line1\nline2\nline3"
    return make_response(jsonify({'ruyi_agent_log': '\n'.join(log_content)}), 200)

@main.route('/get_task_execution_status', methods=['GET', 'POST', 'OPTIONS'])
def get_task_execution_status():
    """
    获取任务执行状态
    """
    global scripts_executor

    executing = scripts_executor.execute_process is not None and not scripts_executor.task_end
    task_success = scripts_executor.task_success

    task_execution_status = {
        'executing': executing,
        'task_success': task_success
    }
    return make_response(jsonify(task_execution_status), 200)

@main.route('/get_example_tasks', methods=['GET', 'POST', 'OPTIONS'])
def get_example_tasks():
    """
    获取示例任务，展示给用户
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response

    # 获取示例任务
    example_tasks = [
        {
            'task_name': '创建联系人',
            'task_description': '创建一个联系人Ruyi，邮箱为*******************',
            'code_script': """device.start_app('联系人') # 打开联系人应用
ui.locate_view('创建联系人按钮').click()  # 点击创建联系人按钮
ui.locate_view('姓名').input('Ruyi')  # 找到姓名输入框并输入姓名
ui.locate_view('邮箱').input('<EMAIL>')  # 找到邮箱输入框并输入邮箱
ui.locate_view('保存').click()  # 找到保存按钮并点击""",
            'NL_script': """打开 "联系人" 应用。
点击 "创建联系人按钮"。
输入 "Ruyi" 到 "姓名输入框"。
输入 "<EMAIL>" 到 "邮箱输入框"。
点击 "保存"。
"""
        },
        {
            'task_name': '小红书搜索',
            'task_description': '搜索小红书上的内容，搜索词为Ruyi',
            'code_script': """device.start_app('小红书')  # 打开小红书
ui.locate_view('搜索图标').click()  # 点击搜索图标
ui.locate_view('搜索输入框').input('Ruyi')  # 输入搜索词
ui.locate_view('搜索按钮').click()  # 点击搜索按钮
""",
            'NL_script': """打开"小红书"应用。
点击 "搜索图标"。
输入 "搜索输入框" 到 "Ruyi"。
点击 "搜索按钮"。
"""
        },
    ]

    return make_response(jsonify(example_tasks), 200)

@main.route('/get_vh', methods=['GET', 'POST', 'OPTIONS'])
def get_vh():
    """
    获取 VH 信息
    """

    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response

    vh = websocket_manager.get_vh()
    return make_response(jsonify(vh), 200)

@main.route('/get_screenshot', methods=['GET', 'POST', 'OPTIONS'])
def get_screenshot():
    """
    获取截图
    """

    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response

    # 获取截图（PIL.Image 对象）
    if device_type == 'browser':
        try:
            screenshot_data = browser_client.capture_page()
            # 如果返回的是 base64 字符串，直接使用
            if isinstance(screenshot_data, str) and screenshot_data.startswith('data:image'):
                return make_response(jsonify({
                    'screenshot': screenshot_data,
                    'device_type': 'browser'
                }), 200)
            # 如果返回的是 bytes，转换为 base64
            elif isinstance(screenshot_data, bytes):
                img_str = base64.b64encode(screenshot_data).decode("utf-8")
                img_data = f"data:image/png;base64,{img_str}"
                return make_response(jsonify({
                    'screenshot': img_data,
                    'device_type': 'browser'
                }), 200)
            else:
                return make_response(jsonify({'error': 'Invalid screenshot data format'}), 500)
        except Exception as e:
            print(f"Error capturing browser screenshot: {str(e)}")
            return make_response(jsonify({'error': str(e)}), 500)
    else:
        # 使用 ADB 进行截图
        screenshot = adb_helper.take_screenshot(device_id)
        buffered = io.BytesIO()
        screenshot.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
        img_data = f"data:image/png;base64,{img_str}"
        return make_response(jsonify({
            'screenshot': img_data,
            'device_type': 'device'
        }), 200)

@main.route('/browser/create', methods=['GET', 'POST', 'OPTIONS'])
def create_browser():
    """
    创建 BrowserView
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response

    try:
        # 检查设备类型
        if device_type != 'browser':
            return make_response(jsonify({'error': 'Current device is not a browser device'}), 400)

        # 获取请求数据
        request_data = request.get_json() or {}
        url = request_data.get('url')
        device_id_param = request_data.get('deviceId', device_id)  # 使用传入的deviceId，如果没有则使用全局device_id

        # 调用 Electron 的 HTTP 客户端创建 BrowserView
        browser_client.create_browser_view(url=url, device_id=device_id_param)
        return make_response('', 200)
    except Exception as e:
        print(f"Error creating browser view: {str(e)}")
        return make_response(jsonify({'error': str(e)}), 500)

@main.route('/browser/command', methods=['GET', 'POST', 'OPTIONS'])
def browser_command():
    """
    处理浏览器命令
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response

    try:
        # 获取命令和参数
        command_data = request.get_json()
        if not command_data:
            return make_response(jsonify({'error': 'No command data provided'}), 400)

        command = command_data.get('command')
        if not command:
            return make_response(jsonify({'error': 'No command specified'}), 400)

        params = command_data.get('params', {})
        target_device_id = command_data.get('deviceId')  # 获取目标设备ID
        
        # 设备切换支持：如果指定了deviceId，临时切换到该设备执行命令
        original_device_id = None
        original_device_type = None
        
        if target_device_id:
            # 保存原设备状态
            original_device_id = device_id
            original_device_type = device_type
            
            # 临时切换到目标设备
            globals()['device_id'] = target_device_id
            globals()['device_type'] = 'browser'
        elif device_type != 'browser':
            return make_response(jsonify({'error': 'Current device is not a browser device'}), 400)

        # 根据不同的命令类型处理
        if command == 'getBounds':
            # 获取 BrowserView 的边界
            bounds = browser_client.get_browser_bounds()
            return make_response(jsonify(bounds), 200)
        elif command == 'capturePage':
            # 获取页面截图
            screenshot_data = browser_client.capture_page()
            return make_response(jsonify({'data': screenshot_data}), 200)
        elif command == 'keyPress':
            # 模拟按键
            key = params.get('key')
            if not key:
                return make_response(jsonify({'error': 'No key specified'}), 400)
            browser_client.key_press(key)
            return make_response('', 200)
        elif command == 'destroy':
            # 销毁 BrowserView
            browser_client.destroy_browser_view()
            return make_response('', 200)
        elif command == 'longTouch':
            # 处理长按操作
            x = params.get('x')
            y = params.get('y')
            duration = params.get('duration', 1000)
            if x is None or y is None:
                return make_response(jsonify({'error': 'Missing x or y coordinates'}), 400)
            browser_client._make_request('POST', '/browser/command', {
                'command': 'longTouch',
                'params': {'x': x, 'y': y, 'duration': duration}
            })
            return make_response('', 200)
        elif command == 'showHighlight':
            # 处理高亮显示
            x = params.get('x')
            y = params.get('y')
            radius = params.get('radius', 20)
            duration = params.get('duration', 500)
            if x is None or y is None:
                return make_response(jsonify({'error': 'Missing x or y coordinates'}), 400)
            browser_client._make_request('POST', '/browser/command', {
                'command': 'showHighlight',
                'params': {'x': x, 'y': y, 'radius': radius, 'duration': duration}
            })
            return make_response('', 200)
        elif command == 'checkFocus':
            # 检查焦点状态
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'checkFocus'
            })
            return make_response(jsonify(result), 200)
        elif command == 'setText':
            # 设置文本
            text = params.get('text')
            if text is None:
                return make_response(jsonify({'error': 'No text specified'}), 400)
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'setText',
                'params': {'text': text}
            })
            return make_response(jsonify(result), 200)
        elif command == 'appendText':
            # 追加文本
            text = params.get('text')
            if text is None:
                return make_response(jsonify({'error': 'No text specified'}), 400)
            x = params.get('x')
            y = params.get('y')
            
            append_params = {'text': text}
            if x is not None:
                append_params['x'] = x
            if y is not None:
                append_params['y'] = y
                
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'appendText',
                'params': append_params
            })
            return make_response(jsonify(result), 200)
        elif command == 'drag':
            # 处理拖拽操作
            startX = params.get('startX')
            startY = params.get('startY')
            endX = params.get('endX')
            endY = params.get('endY')
            duration = params.get('duration', 1000)
            
            if any(coord is None for coord in [startX, startY, endX, endY]):
                return make_response(jsonify({'error': 'Missing drag coordinates'}), 400)
                
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'drag',
                'params': {
                    'startX': startX, 
                    'startY': startY, 
                    'endX': endX, 
                    'endY': endY, 
                    'duration': duration
                }
            })
            return make_response(jsonify(result), 200)
        elif command == 'scroll':
            # 处理滚动操作 - 使用原生滚轮事件
            x = params.get('x')
            y = params.get('y')
            deltaX = params.get('deltaX', 0)
            deltaY = params.get('deltaY', 0)
            direction = params.get('direction')
            distance = params.get('distance', 350)
            duration = params.get('duration', 1000)
            
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'scroll',
                'params': {
                    'x': x,
                    'y': y,
                    'deltaX': deltaX,
                    'deltaY': deltaY,
                    'direction': direction,
                    'distance': distance,
                    'duration': duration
                }
            })
            return make_response(jsonify(result), 200)
        elif command == 'goBack':
            # 处理后退操作
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'goBack'
            })
            return make_response(jsonify(result), 200)
        elif command == 'goForward':
            # 处理前进操作
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'goForward'
            })
            return make_response(jsonify(result), 200)
        elif command == 'reload':
            # 处理重载操作
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'reload'
            })
            return make_response(jsonify(result), 200)
        elif command == 'loadURL':
            # 处理加载URL操作
            url = params.get('url')
            if not url:
                return make_response(jsonify({'error': 'No URL specified'}), 400)
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'loadURL',
                'params': {'url': url}
            })
            return make_response(jsonify(result), 200)
        elif command == 'switchDevice':
            # 处理设备切换操作
            target_device_id_param = params.get('deviceId')
            device_name = params.get('deviceName')
            if not target_device_id_param:
                return make_response(jsonify({'error': 'No deviceId specified'}), 400)
            
            # 通过browser_client切换到指定设备的BrowserView
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'switchDevice',
                'params': {'deviceId': target_device_id_param, 'deviceName': device_name}
            })
            
            # 同时更新后端的全局设备状态
            globals()['device_id'] = target_device_id_param
            globals()['device_type'] = 'browser'
            
            return make_response(jsonify(result), 200)
        elif command == 'getUITree':
            # 处理获取UI树操作
            mode = params.get('mode', 'full')
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'getUITree',
                'params': {'mode': mode}
            })
            return make_response(jsonify(result), 200)
        elif command == 'getCurrentURL':
            # 处理获取当前URL操作
            result = browser_client._make_request('POST', '/browser/command', {
                'command': 'getCurrentURL'
            })
            return make_response(jsonify(result), 200)
        else:
            return make_response(jsonify({'error': f'Unknown command: {command}'}), 400)
    except Exception as e:
        print(f"Error executing browser command: {str(e)}")
        return make_response(jsonify({'error': str(e)}), 500)
    finally:
        # 恢复原设备状态（如果进行了临时切换）
        if original_device_id is not None and original_device_type is not None:
            globals()['device_id'] = original_device_id
            globals()['device_type'] = original_device_type

@main.route('/browser/destroy', methods=['GET', 'POST', 'OPTIONS'])
def destroy_browser():
    """
    销毁 BrowserView
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response

    try:
        # 检查设备类型
        if device_type != 'browser':
            return make_response(jsonify({'error': 'Current device is not a browser device'}), 400)

        browser_client.destroy_browser_view()
        return make_response('', 200)
    except Exception as e:
        print(f"Error destroying browser view: {str(e)}")
        return make_response(jsonify({'error': str(e)}), 500)

@main.route('/ask_question', methods=['GET', 'POST', 'OPTIONS'])
def ask_question():
    """
    向 Ruyi IDE 发送问题，并获取回答
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response
    
    # 处理 POST 请求
    if request.method == 'POST':
        try:
            data = request.get_json()
            question = data.get('question', '')
            print("----- ask_question -----")
            print(f"question: {question}")
            print("-------------------")

            request_id = str(uuid.uuid4())
            result_holder = {}
            request_events[request_id] = result_holder

            socketio.emit('ask_question', {'question': question, 'request_id': request_id})

            # 等待客户端的回答，设置超时时间
            end_time = time.time() + 300  # 5分钟超时
            while 'answer' not in result_holder and time.time() < end_time:
                socketio.sleep(0.1)

            answer = result_holder.get('answer', 'Timeout: No answer received from user.')

            if request_id in request_events:
                del request_events[request_id]

            return make_response(jsonify({'answer': answer}), 200)
        except Exception as e:
            if 'request_id' in locals() and request_id in request_events:
                del request_events[request_id]
            return make_response(jsonify({'error': str(e)}), 500)
    
    # 处理 GET 请求
    answer = "Ruyi IDE 回答：你好，我是 Ruyi IDE，很高兴认识你！"
    return make_response(jsonify({'answer': answer}), 200)

@main.route('/notify_message', methods=['GET', 'POST', 'OPTIONS'])
def notify_message():
    """
    向 Ruyi IDE 发送通知
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response
    
    # 处理 POST 请求
    if request.method == 'POST':
        try:
            data = request.get_json()
            message = data.get('message', '')
            socketio.emit('notify_message', {'message': message})
            return make_response('', 200)
        except Exception as e:
            return make_response(jsonify({'error': str(e)}), 500)
    
    # 处理 GET 请求
    return make_response('', 200)

@main.route('/notify_table', methods=['GET', 'POST', 'OPTIONS'])
def notify_table():
    """
    向 Ruyi IDE 发送表格
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response
    
    # 处理 POST 请求
    if request.method == 'POST':
        try:
            data = request.get_json()
            table = data.get('table', {})
            print("----- notify_table -----")
            print(f"table: {table}")
            print("-------------------")
            socketio.emit('notify_table', {'table': table})
            return make_response('', 200)
        except Exception as e:
            return make_response(jsonify({'error': str(e)}), 500)
        
    # 处理 GET 请求
    return make_response('', 200)

@main.route('/notify_image', methods=['GET', 'POST', 'OPTIONS'])
def notify_image():
    """
    向 Ruyi IDE 发送图片
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response
    
    # 处理 POST 请求
    if request.method == 'POST':
        try:
            data = request.get_json()
            image = data.get('image', {})
            print("----- notify_image -----")
            print("Received image data.")
            print("-------------------")
            socketio.emit('notify_image', {'image': image})
            return make_response('', 200)
        except Exception as e:
            return make_response(jsonify({'error': str(e)}), 500)
        
    # 处理 GET 请求
    return make_response('', 200)


@main.route('/set_executing_script_info', methods=['GET', 'POST', 'OPTIONS'])
def set_executing_script_info():
    """
    设置执行中的脚本信息
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response
    
    # 处理 POST 请求
    if request.method == 'POST':
        try:
            data = request.get_json()
            labeled_code_script = data.get('labeled_code_script', '')
            labeled_NL_script = data.get('labeled_NL_script', '')
            error = data.get('error', False)
            
            print("----- set_executing_script_info -----")
            print(f"labeled_code_script: {labeled_code_script}")
            print(f"labeled_NL_script: {labeled_NL_script}")
            print("-------------------")
            socketio.emit('set_executing_script_info', {'labeled_code_script': labeled_code_script, 'labeled_NL_script': labeled_NL_script, 'error': error})
            return make_response('', 200)
        except Exception as e:
            return make_response(jsonify({'error': str(e)}), 500)
        
    # 处理 GET 请求
    return make_response('', 200)

@main.route('/get_search_engine_config', methods=['GET', 'POST', 'OPTIONS'])
def get_search_engine_config():
    """
    获取默认搜索引擎配置
    """
    # 先处理 OPTION 请求，允许跨域请求
    if request.method == 'OPTIONS':
        response = make_response('', 200)
        response.headers['Allow'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response.headers['Access-Control-Max-Age'] = '1728000'
        return response

    try:
        # 通过HTTP请求获取Electron的搜索引擎配置
        import requests
        electron_response = requests.get('http://localhost:18542/get-search-engine-config', timeout=5)
        if electron_response.status_code == 200:
            search_engine = electron_response.json().get('searchEngine', 'baidu')
        else:
            # 如果无法获取配置，使用默认值
            search_engine = 'baidu'
    except Exception as e:
        print(f"Error getting search engine config: {e}")
        # 如果出错，使用默认值
        search_engine = 'baidu'
    
    return make_response(jsonify({'search_engine': search_engine}), 200)

