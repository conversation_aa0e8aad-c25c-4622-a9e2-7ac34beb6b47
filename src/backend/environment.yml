name: ruyi_ide
channels:
  - conda-forge
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - blinker=1.6.2=py312hca03da5_0
  - bzip2=1.0.8=h80987f9_6
  - ca-certificates=2024.12.14=hf0a4a13_0
  - certifi=2024.12.14=pyhd8ed1ab_0
  - conda-pack=0.8.1=pyhd8ed1ab_1
  - deprecated=1.2.13=py312hca03da5_0
  - expat=2.6.4=h313beb8_0
  - flask=3.0.3=py312hca03da5_0
  - flask_cors=3.0.10=pyhd3eb1b0_0
  - itsdangerous=2.2.0=py312hca03da5_0
  - jedi=0.19.2=py312hca03da5_0
  - jinja2=3.1.4=py312hca03da5_1
  - libcxx=14.0.6=h848a8c0_0
  - libffi=3.4.4=hca03da5_1
  - ncurses=6.4=h313beb8_0
  - openssl=3.4.0=h39f12f2_0
  - pip=24.2=py312hca03da5_0
  - python=3.12.8=h99e199e_0
  - readline=8.2=h1a28f6b_0
  - setuptools=75.1.0=py312hca03da5_0
  - sqlite=3.45.3=h80987f9_0
  - tk=8.6.14=h6ba3021_0
  - werkzeug=3.0.6=py312hca03da5_0
  - wheel=0.44.0=py312hca03da5_0
  - xz=5.4.6=h80987f9_1
  - zlib=1.2.13=h18a0788_1
  - pip:
      - alembic==1.14.0
      - androguard==4.1.2
      - annotated-types==0.7.0
      - anthropic==0.42.0
      - anyio==4.7.0
      - apkinspector==1.3.2
      - asn1crypto==1.5.1
      - asttokens==3.0.0
      - banal==1.0.6
      - beautifulsoup4==4.13.4
      - bidict==0.23.1
      - cffi==1.17.1
      - charset-normalizer==3.4.1
      - click==8.1.8
      - cobble==0.1.4
      - colorama==0.4.6
      - coloredlogs==15.0.1
      - contourpy==1.3.1
      - cryptography==45.0.5
      - cycler==0.12.1
      - dataset==1.6.2
      - decorator==5.1.1
      - defusedxml==0.7.1
      - distro==1.9.0
      - droidbot==1.0.2b4
      - et-xmlfile==2.0.0
      - executing==2.1.0
      - flask-socketio==5.5.1
      - flatbuffers==25.2.10
      - fonttools==4.55.3
      - frida==16.5.9
      - gevent==24.11.1
      - greenlet==3.1.1
      - h11==0.14.0
      - httpcore==1.0.7
      - httpx==0.28.1
      - humanfriendly==10.0
      - idna==3.10
      - influxdb-client==1.48.0
      - ipython==8.31.0
      - jiter==0.8.2
      - kiwisolver==1.4.8
      - loguru==0.7.3
      - lxml==5.3.0
      - magika==0.6.2
      - mako==1.3.8
      - mammoth==1.9.1
      - markdown-it-py==3.0.0
      - markdownify==1.1.0
      - markitdown==0.1.2
      - markupsafe==3.0.2
      - matplotlib==3.10.0
      - matplotlib-inline==0.1.7
      - mdurl==0.1.2
      - mpmath==1.3.0
      - mutf8==1.0.6
      - networkx==3.4.2
      - numpy==2.2.1
      - onnxruntime==1.22.0
      - openai==1.58.1
      - opencv-python==*********
      - openpyxl==3.1.5
      - oscrypto==1.3.0
      - packaging==24.2
      - pandas==2.2.3
      - parso==0.8.4
      - pdfminer-six==20250506
      - pexpect==4.9.0
      - pillow==11.0.0
      - prompt-toolkit==3.0.48
      - psutil==6.1.1
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - pyarmor==9.1.3
      - pyarmor-cli-core==7.6.5
      - pycparser==2.22
      - pydantic==2.10.4
      - pydantic-core==2.27.2
      - pydot==3.0.3
      - pydub==0.25.1
      - pygments==2.18.0
      - pyparsing==3.2.0
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.1.1
      - python-engineio==4.12.2
      - python-pptx==1.0.2
      - python-socketio==5.13.0
      - pytz==2024.2
      - pyyaml==6.0.2
      - reactivex==4.0.4
      - regex==2024.11.6
      - requests==2.32.3
      - rich==13.9.4
      - ruyi==0.0.1a1
      - simple-websocket==1.1.0
      - six==1.17.0
      - sniffio==1.3.1
      - soupsieve==2.7
      - speechrecognition==3.14.3
      - sqlalchemy==1.4.54
      - stack-data==0.6.3
      - structlog==24.4.0
      - sympy==1.14.0
      - tiktoken==0.9.0
      - tqdm==4.67.1
      - traitlets==5.14.3
      - typing-extensions==4.12.2
      - tzdata==2024.2
      - urllib3==2.3.0
      - wcwidth==0.2.13
      - websocket-client==1.8.0
      - websockets==14.1
      - wrapt==1.17.0
      - wsproto==1.2.0
      - xlrd==2.0.2
      - xlsxwriter==3.2.5
      - zope-event==5.0
      - zope-interface==7.2
prefix: /Users/<USER>/miniconda3/envs/ruyi_ide
