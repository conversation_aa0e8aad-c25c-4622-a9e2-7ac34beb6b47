const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
    startWsScrcpy: () => ipcRenderer.invoke('start-ws-scrcpy'),
    stopWsScrcpy: () => ipcRenderer.invoke('stop-ws-scrcpy'),
    updateWsIp: (wsIp = '') => ipcRenderer.invoke('update-ws-ip', wsIp),
    checkForUpdates: () => ipcRenderer.invoke('check-for-updates'),
    downloadUpdate: () => ipcRenderer.invoke('download-update'),
    confirmDownloadUpdate: (confirmed) => ipcRenderer.invoke('confirm-download-update', confirmed),
    quitAndInstall: () => ipcRenderer.invoke('quit-and-install'),
    // Update preferences management
    getUpdatePreferences: () => ipcRenderer.invoke('get-update-preferences'),
    setUpdatePreferences: (preferences) => ipcRenderer.invoke('set-update-preferences', preferences),
    dismissUpdateVersion: (version) => ipcRenderer.invoke('dismiss-update-version', version),
    setUpdateRemindLater: (version, hours) => ipcRenderer.invoke('set-update-remind-later', version, hours),
    getFontPath: (fontName) => ipcRenderer.invoke('get-font-path', fontName),
    onUpdateMessage: (callback) => {
        ipcRenderer.on('update-message', (event, message) => callback(message));
    },
    offUpdateMessage: () => {
        ipcRenderer.removeAllListeners('update-message');
    },
    request: async (method, url, body) => {
        try {
            const response = await ipcRenderer.invoke('api-request', {
                method,
                url,
                body: typeof body === 'string' ? body : JSON.stringify(body)
            });
            return response;
        } catch (error) {
            console.error('IPC request failed:', error);
            throw error;
        }
    },
    getFlaskDetails: () => ipcRenderer.invoke('get-flask-details'),
    startFlaskServer: async () => {
        const result = await ipcRenderer.invoke('start-flask-server');
        return result;
    },
    stopFlaskServer: () => ipcRenderer.invoke('stop-flask-server'),
    runAdbCommand: (command) => ipcRenderer.invoke('run-adb-command', command),
    checkAndStartFlaskServer: () => ipcRenderer.invoke('check-and-start-flask'),

    getConfig: () => ipcRenderer.invoke('get-config'),
    setPortConfig: (config) => ipcRenderer.invoke('set-port-config', config),
    setApiKeyConfig: (apiKey) => ipcRenderer.invoke('set-api-key-config', apiKey),

    getLanguageConfig: () => ipcRenderer.invoke('get-language-config'),
    setLanguageConfig: (language) => ipcRenderer.invoke('set-language-config', language),

    getAppLanguageConfig: () => ipcRenderer.invoke('get-app-language-config'),
    setAppLanguageConfig: (appLanguage) => ipcRenderer.invoke('set-app-language-config', appLanguage),

    getUseAnnotationConfig: () => ipcRenderer.invoke('get-use-annotation-config'),
    setUseAnnotationConfig: (useAnnotation) => ipcRenderer.invoke('set-use-annotation-config', useAnnotation),

    setDataDirPathConfig: (taskName) => ipcRenderer.invoke('set-data-dir-path-config', taskName),

    getDeviceCountConfig: () => ipcRenderer.invoke('get-device-count-config'),
    setDeviceCountConfig: (deviceCounts) => ipcRenderer.invoke('set-device-count-config', deviceCounts),

    getDeviceSerialIdConfig: () => ipcRenderer.invoke('get-device-serial-id-config'),
    setDeviceSerialIdConfig: (deviceSerialId) => ipcRenderer.invoke('set-device-serial-id-config', deviceSerialId),

    getDeviceMappingsConfig: () => ipcRenderer.invoke('get-device-mappings-config'),
    setDeviceMappingsConfig: (deviceMappings) => ipcRenderer.invoke('set-device-mappings-config', deviceMappings),

    setRuyiAgentDeviceConfig: (deviceName, deviceType) => ipcRenderer.invoke('set-ruyi-agent-device-config', deviceName, deviceType),

    getRuyiLLMKeyConfig: () => ipcRenderer.invoke('get-ruyillm-key-config'),

    getBrowserHomepageConfig: () => ipcRenderer.invoke('get-browser-homepage-config'),
    setBrowserHomepageConfig: (homepage) => ipcRenderer.invoke('set-browser-homepage-config', homepage),

    getDefaultSearchEngineConfig: () => ipcRenderer.invoke('get-default-search-engine-config'),
    setDefaultSearchEngineConfig: (searchEngine) => ipcRenderer.invoke('set-default-search-engine-config', searchEngine),

    // 配置验证和迁移 API
    validateConfiguration: () => ipcRenderer.invoke('validate-configuration'),
    getConfigMigrationStatus: () => ipcRenderer.invoke('get-config-migration-status'),
    forceConfigMigration: () => ipcRenderer.invoke('force-config-migration'),

    onToggleScriptLanguage: (callback) => {
        ipcRenderer.on('toggle-script-language', () => callback());
    },
    offToggleScriptLanguage: () => {
        ipcRenderer.removeAllListeners('toggle-script-language');
    },
    createAnnotationDataDir: () => ipcRenderer.invoke('create-annotation-data-dir'),
    saveAnnotationData: (data) => ipcRenderer.invoke('save-annotation-data', data),
    getAnnotations: () => ipcRenderer.invoke('get-annotations'),
    deleteAnnotation: (directory) => ipcRenderer.invoke('delete-annotation', directory),
    deleteAllAnnotations: () => ipcRenderer.invoke('delete-all-annotations'),
    answerQuestion: (data) => ipcRenderer.invoke('answer_question', data),
    onFlaskMessage: (callback) => ipcRenderer.on('flask-message', (_event, ...args) => callback(...args)),
    offFlaskMessage: () => ipcRenderer.removeAllListeners('flask-message'),
    openExternalUrl: (url) => ipcRenderer.invoke('open-external-url', url),
    createBrowserView: (url, deviceId) => ipcRenderer.invoke('create-browser-view', url, deviceId),
    destroyBrowserView: () => ipcRenderer.invoke('destroy-browser-view'),
    hideBrowserView: (temporary) => ipcRenderer.invoke('hide-browser-view', temporary),
    toggleBrowserView: (isVisible) => ipcRenderer.invoke('toggle-browser-view', isVisible),
    goBack: () => ipcRenderer.invoke('browser-go-back'),
    goForward: () => ipcRenderer.invoke('browser-go-forward'),
    reload: () => ipcRenderer.invoke('browser-reload'),
    loadURL: (url) => ipcRenderer.invoke('browser-load-url', url),

    // 添加浏览器事件监听器
    onBrowserLoadingState: (callback) => {
        ipcRenderer.on('browser-loading-state', (event, state) => callback(state));
    },
    offBrowserLoadingState: () => {
        ipcRenderer.removeAllListeners('browser-loading-state');
    },
    onBrowserLoadError: (callback) => {
        ipcRenderer.on('browser-load-error', (event, error) => callback(error));
    },
    offBrowserLoadError: () => {
        ipcRenderer.removeAllListeners('browser-load-error');
    },
    onBrowserNavigationState: (callback) => {
        ipcRenderer.on('browser-navigation-state', (event, state) => callback(state));
    },
    offBrowserNavigationState: () => {
        ipcRenderer.removeAllListeners('browser-navigation-state');
    },
    
    // 监听浏览器设备切换事件
    onBrowserDeviceSwitched: (callback) => {
        ipcRenderer.on('browser-device-switched', (event, data) => callback(data));
    },
    offBrowserDeviceSwitched: () => {
        ipcRenderer.removeAllListeners('browser-device-switched');
    },
    saveImage: (base64Data) => ipcRenderer.invoke('save-image', base64Data),
    
    // 保存任务到本地仓库
    saveTaskToLocalRepo: (taskData) => ipcRenderer.invoke('save-task-to-local-repo', taskData),
    
    // 获取本地仓库任务列表
    getLocalRepoTasks: () => ipcRenderer.invoke('get-local-repo-tasks'),
    
    // 从本地仓库加载任务
    loadTaskFromLocalRepo: (taskName) => ipcRenderer.invoke('load-task-from-local-repo', taskName),
    
    // 从本地仓库删除任务
    deleteTaskFromLocalRepo: (taskName) => ipcRenderer.invoke('delete-task-from-local-repo', taskName),

    // Git 操作相关
    initializeAndPushGitRepo: (data) => ipcRenderer.invoke('initialize-and-push-git-repo', data),
    
    // 保存并推送项目到Git
    saveAndPushProjectToGit: (data) => ipcRenderer.invoke('save-and-push-project-to-git', data),

    // 文件管理器相关操作
    getTaskFiles: (taskName) => ipcRenderer.invoke('get-task-files', taskName),
    getTaskFileTree: (taskName) => ipcRenderer.invoke('get-task-file-tree', taskName),
    refreshTaskFiles: (taskName) => ipcRenderer.invoke('refresh-task-files', taskName),
    createEmptyTaskFile: (data) => ipcRenderer.invoke('create-empty-task-file', data),
    createTaskFile: (data) => ipcRenderer.invoke('create-task-file', data),
    createTaskFolder: (data) => ipcRenderer.invoke('create-task-folder', data),
    readTaskFile: (data) => ipcRenderer.invoke('read-task-file', data),
    deleteTaskFile: (data) => ipcRenderer.invoke('delete-task-file', data),
    deleteTaskFolder: (data) => ipcRenderer.invoke('delete-task-folder', data),
    renameTaskItem: (data) => ipcRenderer.invoke('rename-task-item', data),
    updateTaskFile: (data) => ipcRenderer.invoke('update-task-file', data),
    
    // 聊天消息保存和加载
    saveTaskChatMessages: (data) => ipcRenderer.invoke('save-task-chat-messages', data),
    loadTaskChatMessages: (data) => ipcRenderer.invoke('load-task-chat-messages', data),
    
    // 监听项目文件创建事件
    onProjectFileCreated: (callback) => {
        ipcRenderer.on('project-file-created', (event, data) => callback(data));
    },
    offProjectFileCreated: () => {
        ipcRenderer.removeAllListeners('project-file-created');
    },
    updateKnowledgeFile: (data) => ipcRenderer.invoke('update-knowledge-file', data),

    // Git 同步操作相关
    cloneProjectFromGit: (data) => ipcRenderer.invoke('clone-project-from-git', data),
    pullProjectFromGit: (data) => ipcRenderer.invoke('pull-project-from-git', data),
    forceUseLocalVersion: (data) => ipcRenderer.invoke('force-use-local-version', data),
    forceUseRemoteVersion: (data) => ipcRenderer.invoke('force-use-remote-version', data),
    
    // 自动保存和同步相关
    autoSaveAndSync: (data) => ipcRenderer.invoke('auto-save-and-sync', data),
    
    // 文件置顶功能
    toggleFilePinned: (data) => ipcRenderer.invoke('toggle-file-pinned', data),
    
    // 文件导入功能
    selectAndImportFile: (data) => ipcRenderer.invoke('select-and-import-file', data),
    
    // 读取并解析数据文件
    readAndParseDataFile: (data) => ipcRenderer.invoke('read-and-parse-data-file', data),
    
    // 删除浏览器设备状态
    removeBrowserDeviceState: (deviceId) => ipcRenderer.invoke('remove-browser-device-state', { deviceId }),

    // 加载自定义提示模板
    loadPromptTemplate: () => ipcRenderer.invoke('load-prompt-template'),
    
    // 加载自定义JavaScript模板
    loadJSTemplate: () => ipcRenderer.invoke('load-js-template'),
    
    // 在主进程中处理JavaScript模板
    processJSTemplate: (mechanism, variables) => ipcRenderer.invoke('process-js-template', mechanism, variables),
    
    // 用户管理相关
    setCurrentUser: (userInfo) => ipcRenderer.invoke('set-current-user', userInfo),
    clearUserRepoData: (username) => ipcRenderer.invoke('clear-user-repo-data', username),
    getCurrentUser: () => ipcRenderer.invoke('get-current-user'),
    
    // 用户切换事件监听
    onUserSwitched: (callback) => {
      ipcRenderer.removeAllListeners('user-switched');
      ipcRenderer.on('user-switched', (event, data) => callback(data));
    },
    offUserSwitched: () => ipcRenderer.removeAllListeners('user-switched'),

    // 代码执行高亮监听
    onHighlightExecutingLine: (callback) => {
      ipcRenderer.on('highlight-executing-line', (event, data) => callback(data));
    },
    offHighlightExecutingLine: () => {
      ipcRenderer.removeAllListeners('highlight-executing-line');
    },
    
    // 清除代码执行高亮
    clearExecutingLineHighlight: () => ipcRenderer.invoke('clear-executing-line-highlight'),
    
    // 监听清除高亮事件
    onClearExecutingLineHighlight: (callback) => {
      ipcRenderer.on('clear-executing-line-highlight', () => callback());
    },
    offClearExecutingLineHighlight: () => {
      ipcRenderer.removeAllListeners('clear-executing-line-highlight');
    },

    // 设备连接状态监测相关
    startDeviceMonitoring: (deviceId) => ipcRenderer.invoke('start-device-monitoring', deviceId),
    stopDeviceMonitoring: () => ipcRenderer.invoke('stop-device-monitoring'),
    reloadDeviceIframe: () => ipcRenderer.invoke('reload-device-iframe'),
    checkDeviceConnection: (deviceId) => ipcRenderer.invoke('check-device-connection', deviceId),
    
    // 监听设备连接状态变化事件
    onDeviceConnectionStatusChanged: (callback) => {
      ipcRenderer.on('device-connection-status-changed', (event, data) => callback(data));
    },
    offDeviceConnectionStatusChanged: () => {
      ipcRenderer.removeAllListeners('device-connection-status-changed');
    },
    
    // 监听重新加载iframe事件
    onReloadDeviceIframe: (callback) => {
      ipcRenderer.on('reload-device-iframe', () => callback());
    },
    offReloadDeviceIframe: () => {
      ipcRenderer.removeAllListeners('reload-device-iframe');
    },

    // 添加getFontPath方法的IPC调用
    getFontPath: (fontName) => ipcRenderer.invoke('get-font-path', fontName),
});
