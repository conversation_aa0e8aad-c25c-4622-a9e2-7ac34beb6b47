const { app, BrowserWindow, ipcMain, screen, globalShortcut, shell, BrowserView, dialog } = require('electron');
const { autoUpdater } = require('electron-updater');
const path = require('path');
const isDev = !app.isPackaged;
const ScrcpyManager = require('../services/ScrcpyManager');
const BrowserHttpService = require('../services/BrowserHttpService');
const { net } = require('electron');
const { API_BASE_URL, API_BASE_URL_DATA, GIT_SERVER_URL} = require('../services/config.main');
const fs = require('fs').promises;
const { exec } = require('child_process');
const yaml = require('js-yaml');
const git = require('isomorphic-git');
const http = require('isomorphic-git/http/node');
const tar = require('tar');
const os = require('os');
const FormData = require('form-data');
const fetch = require('node-fetch');
const { io } = require("socket.io-client");
const xlsx = require('xlsx');
const Papa = require('papaparse');

let win;
let scrcpyWindow;
let defaultWindowConfig;
let socket;
let annotationStoragePath;
let repoStoragePath; // 存储本地仓库的路径
let currentUser = null; // 当前登录用户信息

/**
 * 获取用户专属的仓库存储路径
 * @param {string} username 用户名
 * @returns {string} 用户专属路径
 */
function getUserRepoPath(username) {
  if (!username) {
    throw new Error('用户名不能为空');
  }
  // 使用安全的文件名（移除特殊字符）
  const safeUsername = username.replace(/[<>:"/\\|?*]/g, '_');
  return path.join(app.getPath('userData'), 'repositories', safeUsername);
}

/**
 * 清理指定用户的本地仓库数据
 * @param {string} username 要清理的用户名
 */
async function clearUserRepoData(username) {
  try {
    const userRepoPath = getUserRepoPath(username);
    await writeLog(`[用户数据清理] 开始清理用户 ${username} 的本地仓库数据: ${userRepoPath}`);
    
    // 检查目录是否存在
    try {
      await fs.access(userRepoPath);
      // 递归删除用户目录
      await fs.rm(userRepoPath, { recursive: true, force: true });
      await writeLog(`[用户数据清理] ✓ 成功清理用户 ${username} 的本地仓库数据`);
    } catch (error) {
      if (error.code === 'ENOENT') {
        await writeLog(`[用户数据清理] 用户 ${username} 的仓库目录不存在，无需清理`);
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error(`清理用户 ${username} 仓库数据失败:`, error);
    await writeLog(`[用户数据清理] ✗ 清理用户 ${username} 仓库数据失败: ${error.message}`);
    throw error;
  }
}

/**
 * 设置当前用户并更新仓库路径
 * @param {Object} userInfo 用户信息对象
 */
async function setCurrentUser(userInfo) {
  try {
    const username = userInfo.username || userInfo.displayName;
    if (!username) {
      throw new Error('用户信息中缺少用户名');
    }

    // 检测用户切换
    if (currentUser && currentUser.username !== username) {
      await writeLog(`[用户切换] 检测到用户从 ${currentUser.username} 切换到 ${username}`);
      
      // 通知前端清理状态
      if (win && !win.isDestroyed()) {
        win.webContents.send('user-switched', {
          previousUser: currentUser.username,
          newUser: username
        });
        await writeLog(`[用户切换] 已通知前端清理用户 ${currentUser.username} 的状态`);
      }

      // 新增：切换用户时重置浏览器和设备相关状态
      try {
        // 1. 清空 browser-devices-state.json
        if (global.BrowserHttpService && typeof global.BrowserHttpService.clearAllDeviceStates === 'function') {
          await global.BrowserHttpService.clearAllDeviceStates();
        } else if (BrowserHttpService && typeof BrowserHttpService.clearAllDeviceStates === 'function') {
          await BrowserHttpService.clearAllDeviceStates();
        } else {
          // 兼容性处理：直接删除状态文件
          const stateFile = BrowserHttpService && BrowserHttpService.stateFile;
          if (stateFile) {
            try { await fs.unlink(stateFile); } catch {}
          }
        }
        // 2. 重置 device_mapping
        if (global.ScrcpyManager && typeof global.ScrcpyManager.setDeviceMappingsConfig === 'function') {
          global.ScrcpyManager.setDeviceMappingsConfig({});
        } else if (ScrcpyManager && typeof ScrcpyManager.setDeviceMappingsConfig === 'function') {
          ScrcpyManager.setDeviceMappingsConfig({});
        }
        // 3. 重置 device_counts
        if (global.ScrcpyManager && typeof global.ScrcpyManager.setDeviceCountConfig === 'function') {
          global.ScrcpyManager.setDeviceCountConfig({ browserDeviceCount: 0, cloudPhoneCount: 0 });
        } else if (ScrcpyManager && typeof ScrcpyManager.setDeviceCountConfig === 'function') {
          ScrcpyManager.setDeviceCountConfig({ browserDeviceCount: 0, cloudPhoneCount: 0 });
        }
        await writeLog(`[用户切换] 已重置 browser-devices-state.json、device_mapping、device_counts`);
      } catch (error) {
        await writeLog(`[用户切换] 重置 browser-devices-state.json、device_mapping、device_counts 失败: ${error.message}`);
      }
      // 可选：清理前一用户的数据（根据需求决定）
      // await clearUserRepoData(currentUser.username);
    }

    currentUser = { ...userInfo, username };
    
    // 更新仓库路径为用户专属路径
    repoStoragePath = getUserRepoPath(username);
    
    // 确保用户专属目录存在
    await fs.mkdir(repoStoragePath, { recursive: true });
    
    await writeLog(`[用户设置] 当前用户设置为: ${username}, 仓库路径: ${repoStoragePath}`);
  } catch (error) {
    console.error('设置当前用户失败:', error);
    await writeLog(`[用户设置] ✗ 设置当前用户失败: ${error.message}`);
    throw error;
  }
}

/**
 * 全局日志写入函数
 * @param {string} message 要写入的日志消息
 */
async function writeLog(message) {
  let logPath;
  if (process.platform === 'win32') {
    // Windows 系统使用 Documents 目录
    logPath = path.join(os.homedir(), 'Documents', 'ruyi_updater.log');
  } else {
    // 其他系统（macOS/Linux）使用 Desktop 目录
    logPath = path.join(os.homedir(), 'Desktop', 'ruyi_updater.log');
  }

  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  console.log(logMessage.trim());

  try {
    await fs.appendFile(logPath, logMessage);
  } catch (error) {
    console.error('写入日志失败:', error);
  }
}

/**
 * 初始化窗口配置
 */
function initializeWindowConfig() {
  defaultWindowConfig = {
    width: screen.getPrimaryDisplay().workAreaSize.width,
    height: screen.getPrimaryDisplay().workAreaSize.height,
    autoHideMenuBar: true, // 自动隐藏菜单栏
    frame: false, // 隐藏系统标题栏
    titleBarStyle: 'hiddenInset', // macOS风格的标题栏
    titleBarOverlay: {
      color: '#ffffff', // 标题栏背景色
      symbolColor: '#262626', // 窗口控制按钮颜色
      height: 48 // 标题栏高度，与我们的navbar高度一致
    },
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      devTools: true // 临时启用开发者工具用于调试
    }
  };
}

/**
 * 创建主窗口
 */
function createWindow() {
  win = new BrowserWindow({
    ...defaultWindowConfig
  });

  // macOS特定的无边框窗口设置
  if (process.platform === 'darwin') {
    // 设置窗口为可拖拽区域
    win.setWindowButtonVisibility(true);
    
    // 确保窗口在macOS上正确显示
    win.on('ready-to-show', () => {
      win.show();
    });
  }

  if (isDev) {
    win.loadURL('http://localhost:5173');
    win.webContents.openDevTools();
  } else {
    const indexPath = path.join(__dirname, '..', '..', 'dist', 'index.html');
    console.log('Loading index from:', indexPath);
    win.loadFile(indexPath);
  }

  // 启动浏览器 HTTP 服务
  BrowserHttpService.start(ScrcpyManager.getPortConfig().electron_http_port, win);

  // 在窗口关闭时停止 HTTP 服务
  win.on('closed', () => {
    if (BrowserHttpService.server) {
      BrowserHttpService.server.close();
      BrowserHttpService.server = null;
    }
  });
}

/**
 * 仅创建 Scrcpy 窗口, 用于显示 scrcpy 的界面, 不创建 ws-scrcpy 的 frontend 服务
 */
function createScrcpyWindow() {
  scrcpyWindow = new BrowserWindow({
    ...defaultWindowConfig,
    show: false
  });

  scrcpyWindow.on('closed', () => {
    scrcpyWindow = null;
  });

  // scrcpyWindow.loadURL('http://localhost:8000');

  // 添加加载失败重试逻辑
  const maxRetries = 5;  // 最大重试次数
  let retryCount = 0;

  const tryLoadUrl = () => {
    scrcpyWindow.loadURL('http://localhost:8000').catch(error => {
      console.log(`Scrcpy 窗口加载失败 (${retryCount + 1}/${maxRetries}):`, error);

      if (retryCount < maxRetries) {
        retryCount++;
        // 3秒后重试
        setTimeout(tryLoadUrl, 500);
      } else {
        console.error('Scrcpy 窗口加载失败次数超过限制');
        // 可以在这里发送事件通知前端
        if (win) {
          win.webContents.send('scrcpy-window-failed');
        }
      }
    });
  };

  tryLoadUrl();
}

/**
 * 初始化自动更新
 */
function initAutoUpdater() {
  writeLog('开始初始化自动更新...');

  const server = 'http://wisewk.com/updates/ruyi-ide/';
  const platform = process.platform === 'darwin' ? 'mac' : process.platform;
  const url = `${server}`;

  writeLog(`更新服务器地址: ${url}`);
  writeLog(`当前平台: ${platform}`);

  autoUpdater.setFeedURL({
    provider: 'generic',
    url: url,
    channel: 'latest'
  });

  writeLog('已设置更新服务器地址');

  autoUpdater.autoDownload = false;  // 改为 false，禁用自动下载，需要用户确认
  autoUpdater.autoInstallOnAppQuit = true;  // 退出时自动安装
  // 确保不禁用签名验证，让系统正常验证签名
  // autoUpdater.verifyUpdateCodeSignature = false; // 注释掉这行

  writeLog('已配置用户确认下载和退出时安装');

  // 添加错误检查
  autoUpdater.on('checking-for-update', async () => {
    await writeLog('正在检查更新...');
    if (win && !win.isDestroyed()) {
      win.webContents.send('update-message', {
        type: 'checking',
        message: '正在检查更新...'
      });
    } else {
      console.error('主窗口未初始化或已销毁');
      await writeLog('错误：主窗口未初始化或已销毁');
    }
  });

  // 发现新版本
  autoUpdater.on('update-available', async (info) => {
    writeLog(`发现新版本: ${info.version}, 检查用户偏好设置...`);

    try {
      // 检查用户偏好设置
      const store = await initializeStore();
      const prefs = store.get('preferences', {
        dismissedVersions: [],
        remindLater: {}
      });

      // 检查是否已忽略此版本
      if (prefs.dismissedVersions.includes(info.version)) {
        writeLog(`版本 ${info.version} 已被用户忽略，跳过通知`);
        return;
      }

      // 检查是否设置了稍后提醒
      if (prefs.remindLater.version === info.version &&
          prefs.remindLater.remindAfter &&
          Date.now() < prefs.remindLater.remindAfter) {
        writeLog(`版本 ${info.version} 设置了稍后提醒，跳过通知`);
        return;
      }

      // 发送更新通知
      writeLog(`发送版本 ${info.version} 更新通知给用户`);
      win.webContents.send('update-message', {
        type: 'available-confirm',
        version: info.version,
        message: `发现新版本 ${info.version}`,
        needsConfirm: true
      });
    } catch (error) {
      writeLog(`检查用户偏好设置失败: ${error.message}`);
      // 如果偏好设置检查失败，仍然发送通知
      win.webContents.send('update-message', {
        type: 'available-confirm',
        version: info.version,
        message: `发现新版本 ${info.version}`,
        needsConfirm: true
      });
    }
  });

  // 没有新版本
  autoUpdater.on('update-not-available', () => {
    writeLog('检查完成：当前已是最新版本');
    win.webContents.send('update-message', {
      type: 'not-available',
      message: '当前已是最新版本'
    });
  });

  // 下载进度
  autoUpdater.on('download-progress', (progressObj) => {
    writeLog(`更新包下载进度：${Math.floor(progressObj.percent)}%, 速度：${progressObj.bytesPerSecond}字节/秒`);
    win.webContents.send('update-message', {
      type: 'progress',
      percent: progressObj.percent,
      message: `下载进度：${Math.floor(progressObj.percent)}%`
    });
  });

  // 更新下载完成
  autoUpdater.on('update-downloaded', () => {
    writeLog('更新包下载完成，等待用户确认重启进行安装');
    win.webContents.send('update-message', {
      type: 'downloaded',
      message: '更新已下载完成，是否立即重启以安装更新？',
      showRestart: true  // 标志位表示显示重启选项
    });
  });

  // 更新错误
  autoUpdater.on('error', (err) => {
    writeLog(`更新过程出错: ${err.message}`);
    win.webContents.send('update-message', {
      type: 'error',
      error: err.message,
      message: `更新出错：${err.message}`
    });
  });

  writeLog('自动更新初始化完成');

  // 启动时执行一次更新检查
  writeLog('启动时执行一次更新检查');
  autoUpdater.checkForUpdates().catch(err => {
    writeLog(`启动时检查更新失败: ${err.message}`);
    console.error('启动时检查更新失败:', err);
  });
}

/**
 * 设置 adb 路径
 */
async function setAdbPath() {
  const platformToolsPath = app.isPackaged
    ? path.join(process.resourcesPath, 'platform-tools')
    : path.join(__dirname, '../../resources/platform-tools', process.platform);

  // 设置可执行权限（仅在 macOS 和 Linux 上需要）
  if (process.platform !== 'win32') {
    const adbPath = path.join(platformToolsPath, 'adb');
    try {
      await fs.chmod(adbPath, '755');
    } catch (error) {
      console.error('设置 adb 可执行权限失败:', error);
    }
  }

  process.env.PATH = `${platformToolsPath}${path.delimiter}${process.env.PATH}`;
}

/**
 * 初始化应用
 */
app.whenReady().then(async () => {
  // if (!isDev) {
  //   await writeLog('非开发环境，开始解压 Conda 环境');
  //   // 解压 Conda 环境
  //   await extractCondaEnv();

  //   await writeLog('Conda 环境解压完成');
  // }
  // else {
  //   await writeLog('开发环境，跳过 Conda 环境解压');
  // }

  // 首先初始化窗口配置
  initializeWindowConfig();

  // 在生产环境中禁用开发者工具的默认快捷键
  if (!isDev) {
    app.on('web-contents-created', (event, contents) => {
      // 禁用开发者工具
      contents.on('before-input-event', (event, input) => {
        // 拦截 Command+Option+I 或 Ctrl+Shift+I 快捷键
        if ((input.meta && input.alt && input.key === 'i') ||
          (input.control && input.shift && input.key === 'i')) {
          event.preventDefault();
        }
      });
    });
  }

  // 设置 adb 路径
  await setAdbPath();


  // 初始化 ScrcpyManager
  ScrcpyManager.init(process.resourcesPath, app.getAppPath(), isDev);

  // 验证配置完整性
  const configValidation = ScrcpyManager.validateConfiguration();
  if (!configValidation.isValid) {
    console.warn('配置验证失败，将尝试自动修复:');
    console.warn('缺失的配置键:', configValidation.missingKeys);
    console.warn('错误信息:', configValidation.errors);
    
    // 尝试自动修复配置
    try {
      const configPath = ScrcpyManager.getConfigPath();
      ScrcpyManager.ensureConfigMigration(configPath);
      
      const flaskConfigPath = ScrcpyManager.getFlaskConfigPath();
      ScrcpyManager.ensureFlaskConfigMigration(flaskConfigPath);
      
      console.log('配置自动修复完成');
    } catch (error) {
      console.error('配置自动修复失败:', error);
    }
  } else {
    console.log('配置验证通过');
  }

  // 清理所有配置的端口，避免启动时报错
  await ScrcpyManager.cleanupConfiguredPorts();

  // 现在创建窗口（在端口清理之后）
  createWindow();

  // 启动 scrcpy
  ScrcpyManager.startScrcpy();

  await writeLog('当前平台: ' + process.platform);
  await writeLog('当前环境: ' + process.env.PATH);
  await writeLog('当前 isDev: ' + isDev);


  // 设置并创建标注数据存储路径
  annotationStoragePath = path.join(app.getPath('userData'), 'annotations');
  await fs.mkdir(annotationStoragePath, { recursive: true });

  // 设置本地仓库存储基础路径（用户登录后会设置为用户专属路径）
  repoStoragePath = path.join(app.getPath('userData'), 'repositories');
  // 确保基础repositories目录存在，但不创建用户专属目录
  await fs.mkdir(repoStoragePath, { recursive: true });

  // 同步路径到 RuyiAgent 的配置文件中
  ScrcpyManager.setAnnotationPathConfig(annotationStoragePath);

  ScrcpyManager.startFlaskServer();

  // 在初始化自动更新之前记录日志
  await writeLog(`准备初始化自动更新，当前 isDev 值: ${isDev}`);

  if (!isDev) {
    await writeLog('非开发环境，开始调用 initAutoUpdater');
    initAutoUpdater();
    await writeLog('initAutoUpdater 调用完成');
  } else {
    await writeLog('开发环境，跳过自动更新初始化');
  }

  // Connect to Flask-SocketIO
  const flaskPort = ScrcpyManager.getPortConfig().flask_port; // As requested by user
  socket = io(`http://localhost:${flaskPort}`, {
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
  });

  socket.on("connect", () => {
      console.log(`Connected to Flask-SocketIO server on port ${flaskPort}`);
      writeLog(`Connected to Flask-SocketIO server on port ${flaskPort} with socket id ${socket.id}`);
  });

  socket.on("disconnect", (reason) => {
      console.log("Disconnected from Flask-SocketIO server:", reason);
      writeLog(`Disconnected from Flask-SocketIO server: ${reason}`);
  });

  socket.on("connect_error", (err) => {
      console.log(`Connection Error to Flask-SocketIO: ${err.message}`);
      writeLog(`Flask-SocketIO Connection Error: ${err.message}`);
  });

  socket.on('ask_question', (data) => {
      console.log('Received ask_question event:', data);
      writeLog(`Received ask_question event: ${JSON.stringify(data)}`);
      if (win) {
          win.webContents.send('flask-message', { type: 'ask_question', payload: data });
      }
  });

  socket.on('notify_message', (data) => {
      console.log('Received notify_message event:', data);
      writeLog(`Received notify_message event: ${JSON.stringify(data)}`);
      if (win) {
          win.webContents.send('flask-message', { type: 'notify_message', payload: data });
      }
  });

  socket.on('notify_table', (data) => {
    console.log('Received notify_table event:', data);
    writeLog(`Received notify_table event: ${JSON.stringify(data)}`);
    if (win) {
        win.webContents.send('flask-message', { type: 'notify_table', payload: data });
    }
  });

  socket.on('notify_image', (data) => {
      console.log('Received notify_image event');
      writeLog(`Received notify_image event`);
      if (win) {
          win.webContents.send('flask-message', { type: 'notify_image', payload: data });
      }
  });

  socket.on('set_executing_script_info', (data) => {
    console.log('Received set_executing_script_info event:', data);
    writeLog(`Received set_executing_script_info event: ${JSON.stringify(data)}`);
    if (win) {
      win.webContents.send('flask-message', { type: 'set_executing_script_info', payload: data });
      // 同时发送专门的高亮事件给渲染进程
      win.webContents.send('highlight-executing-line', data);
    }
  });

  ipcMain.handle('answer_question', (event, data) => {
    console.log('Forwarding answer to Flask:', data);
    writeLog(`Forwarding answer to Flask: ${JSON.stringify(data)}`);
    if (socket && socket.connected) {
      socket.emit('answer_question', data);
    }
  });

  ipcMain.handle('start-ws-scrcpy', async () => {
    ScrcpyManager.startScrcpy();
    console.log('ws-scrcpy 启动成功');

    if (!scrcpyWindow) {
      createScrcpyWindow();
      console.log('scrcpy 窗口创建成功');
    }

    scrcpyWindow.show();
    console.log('scrcpy 窗口显示成功');

    ScrcpyManager.startFlaskServer();
    win.webContents.send('notification', { type: 'success', message: 'flask 服务启动成功' });
  });

  ipcMain.handle('stop-ws-scrcpy', () => {
    ScrcpyManager.stopScrcpy();

    if (scrcpyWindow) {
      scrcpyWindow.hide();
    }
  });

  ipcMain.handle('update-ws-ip', async (event, wsIp = '') => {
    return ScrcpyManager.updateWsIp(wsIp);
  });

  // 监听浏览器设备切换事件
  if (win) {
    win.webContents.on('browser-device-switched', (event, data) => {
      console.log('Browser device switched:', data);
      writeLog(`Browser device switched: ${JSON.stringify(data)}`);
      // 转发给渲染进程
      win.webContents.send('browser-device-switched', data);
    });
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });

  // 注册全局快捷键，用于 DSL 和 NL 之间的转换
  let isHandlingShortcut = false;  // 添加处理状态标识
  globalShortcut.register('CommandOrControl+Shift+L', () => {
    if (isHandlingShortcut) return;  // 如果正在处理，直接返回
    isHandlingShortcut = true;  // 设置为正在处理状态

    try {
      // 向渲染进程发送切换语言的消息
      if (win) {
        win.webContents.send('toggle-script-language');
      }
    } catch (error) {
      writeLog(`全局快捷键执行失败: ${error.message}`);
      console.error('全局快捷键执行失败:', error);
    } finally {
      isHandlingShortcut = false;  // 处理完成后重置状态
    }
  });

  // 在合适的位置添加以下函数，例如在其他IPC处理函数附近

  /**
   * 获取字体文件路径
   * 在开发环境和生产环境中返回不同的路径
   * @param {string} fontName 字体文件名
   * @returns {string} 字体文件的完整路径
   */
  function getFontPath(fontName) {
    if (isDev) {
      // 开发环境，使用相对路径
      return `../renderer/pages/fonts/${fontName}`;
    } else {
      // 生产环境，使用resources目录中的字体
      return path.join(process.resourcesPath, 'fonts', fontName);
    }
  }

  // 在app.whenReady().then()中添加IPC处理函数
  ipcMain.handle('get-font-path', (event, fontName) => {
    return getFontPath(fontName);
  });

  // 添加清除代码执行高亮的IPC处理器
  ipcMain.handle('clear-executing-line-highlight', () => {
    if (win) {
      win.webContents.send('clear-executing-line-highlight');
    }
  });
});

/**
 * 处理关闭窗口事件
 */
app.on('window-all-closed', async () => {
  if (socket && socket.connected) {
    socket.disconnect();
  }
  
  // 停止设备监测
  if (deviceMonitor) {
    deviceMonitor.stopMonitoring();
  }
  
  // 确保先停止 Flask 服务
  ScrcpyManager.stopFlaskServer();
  // 然后停止 Scrcpy
  ScrcpyManager.stopScrcpy();

  // 清理所有配置的端口
  await ScrcpyManager.cleanupConfiguredPorts();

  // 添加一个小延迟确保进程被清理
  setTimeout(() => {
    if (process.platform !== 'darwin') {
      app.quit();
    }
  }, 1000);
});

/**
 * 处理新窗口创建
 */
app.on('web-contents-created', (event, contents) => {
  contents.setWindowOpenHandler((details) => {
    // 获取当前焦点窗口(父窗口)
    const parentWindow = BrowserWindow.getFocusedWindow();
    const [width, height] = parentWindow.getSize(); // 获取父窗口的尺寸

    return {
      action: 'allow',
      overrideBrowserWindowOptions: {
        ...defaultWindowConfig,
        width: width,  // 使用父窗口的宽度
        height: height,  // 使用父窗口的高度
        parent: parentWindow // 设置父窗口
      }
    };
  });
});

/**
 * 添加 before-quit 事件处理
 */
app.on('before-quit', async (event) => {
  if (BrowserHttpService.browserView && win && !win.isDestroyed()) {
    BrowserHttpService.destroyBrowserView();
  }
  // 再次确保 Flask 服务被停止
  ScrcpyManager.stopFlaskServer();
  // 清理所有配置的端口
  await ScrcpyManager.cleanupConfiguredPorts();
});

/**
 * 确保在应用退出时注销快捷键
 */
app.on('will-quit', () => {
  globalShortcut.unregisterAll();
});

/**
 * 添加 IPC 处理程序
 */
ipcMain.handle('check-for-updates', () => {
  // 允许开发环境进行手动检查更新
  return autoUpdater.checkForUpdates();
});

ipcMain.handle('download-update', () => {
  if (!isDev) {
    return autoUpdater.downloadUpdate();
  }
});

ipcMain.handle('quit-and-install', () => {
  autoUpdater.quitAndInstall();
});

ipcMain.handle('confirm-download-update', (event, confirmed) => {
  if (confirmed) {
    writeLog('用户确认下载更新');
    return autoUpdater.downloadUpdate();
  }
  if (!confirmed) {
    writeLog('用户取消下载更新');
  }
});

// Update preferences management IPC handlers
let updatePreferencesStore = null;

// Initialize electron-store asynchronously
async function initializeStore() {
  if (!updatePreferencesStore) {
    const Store = (await import('electron-store')).default;
    updatePreferencesStore = new Store({ name: 'update-preferences' });
  }
  return updatePreferencesStore;
}

ipcMain.handle('get-update-preferences', async () => {
  const store = await initializeStore();
  return store.get('preferences', {
    dismissedVersions: [],
    remindLater: {},
    showBadgeOnButton: true,
    showBadgeOnAvatar: true,
    notificationMethod: 'notification'
  });
});

ipcMain.handle('set-update-preferences', async (event, preferences) => {
  const store = await initializeStore();
  store.set('preferences', preferences);
  return true;
});

ipcMain.handle('dismiss-update-version', async (event, version) => {
  const store = await initializeStore();
  const prefs = store.get('preferences', { dismissedVersions: [] });
  if (!prefs.dismissedVersions.includes(version)) {
    prefs.dismissedVersions.push(version);
    store.set('preferences', prefs);
  }
  return true;
});

ipcMain.handle('set-update-remind-later', async (event, version, hours) => {
  const store = await initializeStore();
  const prefs = store.get('preferences', { remindLater: {} });
  prefs.remindLater = {
    version,
    remindAfter: Date.now() + (hours * 60 * 60 * 1000)
  };
  store.set('preferences', prefs);
  return true;
});

/**
 * 处理API请求的函数
 */
async function handleApiRequest(request) {
  return new Promise((resolve, reject) => {
    const apiUrl = API_BASE_URL + request.url;

    try {
      const netRequest = net.request({
        method: request.method,
        url: apiUrl,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      let body = '';
      netRequest.on('response', (response) => {
        response.on('data', (chunk) => {
          body += chunk.toString();
        });

        response.on('end', () => {
          // 简化返回的对象结构
          resolve({
            ok: response.statusCode >= 200 && response.statusCode < 300,
            status: response.statusCode,
            data: body
          });
        });
      });

      netRequest.on('error', (error) => {
        reject(new Error(error.message));
      });

      // 确保 request.body 是字符串
      if (request.body) {
        netRequest.write(request.body);
      }
      netRequest.end();
    } catch (error) {
      reject(new Error('Request failed: ' + error.message));
    }
  });
}

/**
 * 注册IPC处理器
 */
ipcMain.handle('api-request', async (event, request) => {
  try {
    const response = await handleApiRequest(request);
    return response;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
});

ipcMain.handle('get-flask-details', () => {
  return ScrcpyManager.getFlaskDetails();
});

ipcMain.handle('start-flask-server', () => {
  return ScrcpyManager.startFlaskServer();
});

ipcMain.handle('stop-flask-server', () => {
  return ScrcpyManager.stopFlaskServer();
});

ipcMain.handle('run-adb-command', async (event, command) => {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing command: ${error}`);
        reject(stderr);
      } else {
        resolve(stdout);
      }
    });
  });
});

/**
 * 设备连接状态监测器
 */
class DeviceMonitor {
  constructor() {
    this.monitoredDevice = null;
    this.isMonitoring = false;
    this.monitorInterval = null;
    this.monitorIntervalDuration = 1500; // 每1.5秒检查一次，提高检测频率
    this.lastConnectionStatus = null;
  }

  /**
   * 开始监测指定设备的连接状态
   * @param {string} deviceId - 设备ID
   */
  async startMonitoring(deviceId) {
    console.log(`开始监测设备连接状态: ${deviceId}`);
    
    // 如果已经在监测其他设备，先停止
    if (this.isMonitoring) {
      this.stopMonitoring();
    }

    this.monitoredDevice = deviceId;
    this.isMonitoring = true;

    // 先获取初始状态，不发送变化通知
    try {
      const result = await this.runAdbCommand('adb devices');
      const initialStatus = this.parseDeviceConnection(result, deviceId);
      this.lastConnectionStatus = initialStatus;
      console.log(`设备 ${deviceId} 监测开始，初始状态: ${initialStatus}`);
    } catch (error) {
      console.error('获取设备初始状态失败:', error);
      this.lastConnectionStatus = false;
    }

    // 设置定时检查
    this.monitorInterval = setInterval(() => {
      this.checkDeviceConnection();
    }, this.monitorIntervalDuration);
  }

  /**
   * 停止监测设备连接状态
   */
  stopMonitoring() {
    console.log('停止设备连接状态监测');
    this.isMonitoring = false;
    this.monitoredDevice = null;
    this.lastConnectionStatus = null;

    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
  }

  /**
   * 检查设备连接状态
   */
  async checkDeviceConnection() {
    if (!this.monitoredDevice || !this.isMonitoring) {
      return;
    }

    try {
      // 使用adb devices命令检查设备连接状态
      const result = await this.runAdbCommand('adb devices');
      const isConnected = this.parseDeviceConnection(result, this.monitoredDevice);

      // 如果连接状态发生变化，通知渲染进程
      if (this.lastConnectionStatus !== isConnected) {
        console.log(`设备 ${this.monitoredDevice} 连接状态变化: ${this.lastConnectionStatus} -> ${isConnected}`);
        this.lastConnectionStatus = isConnected;
        
        if (win && !win.isDestroyed()) {
          win.webContents.send('device-connection-status-changed', {
            deviceId: this.monitoredDevice,
            isConnected: isConnected
          });
        }
      }
    } catch (error) {
      console.error('检查设备连接状态失败:', error);
      // 如果ADB命令失败，认为设备未连接
      if (this.lastConnectionStatus !== false) {
        this.lastConnectionStatus = false;
        if (win && !win.isDestroyed()) {
          win.webContents.send('device-connection-status-changed', {
            deviceId: this.monitoredDevice,
            isConnected: false
          });
        }
      }
    }
  }

  /**
   * 解析adb devices命令的输出，检查指定设备是否连接
   * @param {string} adbOutput - adb devices命令的输出
   * @param {string} deviceId - 要检查的设备ID
   * @returns {boolean} - 设备是否连接
   */
  parseDeviceConnection(adbOutput, deviceId) {
    const lines = adbOutput.split('\n');
    for (const line of lines) {
      if (line.includes(deviceId) && line.includes('device')) {
        return true;
      }
    }
    return false;
  }

  /**
   * 执行ADB命令
   * @param {string} command - 要执行的ADB命令
   * @returns {Promise<string>} - 命令输出
   */
  runAdbCommand(command) {
    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          resolve(stdout);
        }
      });
    });
  }
}

// 创建设备监测器实例
const deviceMonitor = new DeviceMonitor();

// 添加IPC处理器用于控制设备监测
ipcMain.handle('start-device-monitoring', async (event, deviceId) => {
  try {
    await deviceMonitor.startMonitoring(deviceId);
    return { success: true };
  } catch (error) {
    console.error('启动设备监测失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('stop-device-monitoring', async () => {
  try {
    deviceMonitor.stopMonitoring();
    return { success: true };
  } catch (error) {
    console.error('停止设备监测失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('reload-device-iframe', async () => {
  try {
    // 通知渲染进程重新加载iframe
    if (win && !win.isDestroyed()) {
      win.webContents.send('reload-device-iframe');
    }
    return { success: true };
  } catch (error) {
    console.error('重新加载设备iframe失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('check-device-connection', async (event, deviceId) => {
  try {
    // 立即检查指定设备的连接状态
    const result = await deviceMonitor.runAdbCommand('adb devices');
    const isConnected = deviceMonitor.parseDeviceConnection(result, deviceId);
    console.log(`设备 ${deviceId} 当前连接状态: ${isConnected}`);
    return { success: true, isConnected };
  } catch (error) {
    console.error('检查设备连接状态失败:', error);
    return { success: false, isConnected: false, error: error.message };
  }
});

ipcMain.handle('check-and-start-flask', async () => {
  return await ScrcpyManager.checkAndStartFlaskServer();
});

ipcMain.handle('get-config-path', async () => {
  return ScrcpyManager.getConfigPath();
});

ipcMain.handle('get-config', async () => {
  try {
    const configPath = ScrcpyManager.getConfigPath();
    const configFile = await fs.readFile(configPath, 'utf8');
    return yaml.load(configFile);
  } catch (error) {
    console.error('读取端口配置文件失败:', error);
    throw error;
  }
});

// 添加配置验证和迁移相关的 IPC 处理器
ipcMain.handle('validate-configuration', async () => {
  try {
    return ScrcpyManager.validateConfiguration();
  } catch (error) {
    console.error('验证配置失败:', error);
    throw error;
  }
});

ipcMain.handle('get-config-migration-status', async () => {
  try {
    return ScrcpyManager.getConfigMigrationStatus();
  } catch (error) {
    console.error('获取配置迁移状态失败:', error);
    throw error;
  }
});

ipcMain.handle('force-config-migration', async () => {
  try {
    // 强制重新迁移配置文件
    const configPath = ScrcpyManager.getConfigPath();
    ScrcpyManager.ensureConfigMigration(configPath);
    
    const flaskConfigPath = ScrcpyManager.getFlaskConfigPath();
    ScrcpyManager.ensureFlaskConfigMigration(flaskConfigPath);
    
    return { success: true, message: '配置迁移完成' };
  } catch (error) {
    console.error('强制配置迁移失败:', error);
    return { success: false, message: error.message };
  }
});

/**
 * 设置 Electron app 和 flask app 中的端口配置文件 config.yaml
 */
ipcMain.handle('set-port-config', async (event, config) => {
  return ScrcpyManager.setPortConfig(config);
});

/**
 * 设置 flask app 和 Ruyi Agent 中的配置文件 config.yaml 中的 API KEY 的值
 */
ipcMain.handle('set-api-key-config', async (event, apiKey) => {
  return ScrcpyManager.setApiKeyConfig(apiKey);
});

/**
 * 获取 Electron app 的配置文件 config.yaml 中的 language 的值
 */
ipcMain.handle('get-language-config', async () => {
  return ScrcpyManager.getLanguageConfig();
});

/**
 * 设置 Electron app 的配置文件 config.yaml 中的 language 的值
 */
ipcMain.handle('set-language-config', async (event, language) => {
  return ScrcpyManager.setLanguageConfig(language);
});

/**
 * 获取 Electron app 的配置文件 config.yaml 中的 app_language 的值
 */
ipcMain.handle('get-app-language-config', async () => {
  return ScrcpyManager.getAppLanguageConfig();
});

/**
 * 设置 Electron app 的配置文件 config.yaml 中的 app_language 的值
 */
ipcMain.handle('set-app-language-config', async (event, appLanguage) => {
  return ScrcpyManager.setAppLanguageConfig(appLanguage);
});

/**
 * 获取 Electron app 的配置文件 config.yaml 中的 use_annotation 的值
 */
ipcMain.handle('get-use-annotation-config', async () => {
  return ScrcpyManager.getUseAnnotationConfig();
});

/**
 * 设置 Electron app 的配置文件 config.yaml 中的 use_annotation 的值
 */
ipcMain.handle('set-use-annotation-config', async (event, useAnnotation) => {
  return ScrcpyManager.setUseAnnotationConfig(useAnnotation);
});

/**
 * 设置 Ruyi Agent 的配置文件 config.yaml 中的 data_dir_path 的值
 */
ipcMain.handle('set-data-dir-path-config', async (event, taskName) => {
  const dataDirPath = path.join(repoStoragePath, taskName);
  return ScrcpyManager.setDataDirPathConfig(dataDirPath);
});

/**
 * 获取设备数量配置
 */
ipcMain.handle('get-device-count-config', async () => {
  return ScrcpyManager.getDeviceCountConfig();
});

/**
 * 设置设备数量配置
 */
ipcMain.handle('set-device-count-config', async (event, deviceCounts) => {
  return ScrcpyManager.setDeviceCountConfig(deviceCounts);
});

/**
 * 获取设备序列号配置
 */
ipcMain.handle('get-device-serial-id-config', async () => {
  return ScrcpyManager.getDeviceSerialIdConfig();
});

/**
 * 设置设备序列号配置
 */
// ipcMain.handle('set-device-serial-id-config', async (event, deviceSerialId) => {
//   return ScrcpyManager.setDeviceSerialIdConfig(deviceSerialId);
// });

/**
 * 设置设备映射配置
 */
ipcMain.handle('set-device-mappings-config', async (event, deviceMappings) => {
  return ScrcpyManager.setDeviceMappingsConfig(deviceMappings);
});

/**
 * 获取设备映射配置
 */
ipcMain.handle('get-device-mappings-config', async (event) => {
  return ScrcpyManager.getDeviceMappingsConfig();
});

/**
 * 设置 RuyiAgent 设备配置
 */
ipcMain.handle('set-ruyi-agent-device-config', async (event, deviceName, deviceType) => {
  return ScrcpyManager.setRuyiAgentDeviceConfig(deviceName, deviceType);
});

/**
 * 解压 Conda 环境
 */
async function extractCondaEnv() {
  await writeLog('开始解压 Conda 环境...');
  const isDev = !app.isPackaged;
  const resourcesPath = isDev ? path.join(__dirname, '..') : path.join(process.resourcesPath);
  const envTarPath = path.join(resourcesPath, 'ruyi-ide-backend', 'ruyi_ide_env.tar.gz');
  const extractPath = path.join(resourcesPath, 'ruyi-ide-backend', 'env');

  try {
    await writeLog(`开始检查 Conda 环境... 路径: ${extractPath}`);

    // 检查是否已经解压过
    const exists = await fs.access(extractPath).then(() => true).catch(() => false);
    if (exists) {
      await writeLog('Conda 环境目录已存在，跳过解压步骤');
      return;
    }

    // 确保目标文件夹存在
    await fs.mkdir(extractPath, { recursive: true });
    await writeLog(`已创建目标文件夹: ${extractPath}`);

    await writeLog(`准备解压 Conda 环境... 源文件: ${envTarPath}`);
    await writeLog(`解压目标路径: ${extractPath}`);

    await tar.x({
      file: envTarPath,
      cwd: extractPath,
      sync: true
    });

    await writeLog('Conda 环境解压完成');

    // await writeLog('开始启动 flask 服务');
    // ScrcpyManager.startFlaskServer();
    // await writeLog('flask 服务启动成功');
  } catch (error) {
    await writeLog(`解压 Conda 环境失败: ${error.message}`);
    console.error('解压 Conda 环境失败:', error);
    throw error;
  }
}

ipcMain.handle('create-annotation-data-dir', async () => {
  // 目录在应用启动时已创建，这里确保它存在
  await fs.mkdir(annotationStoragePath, { recursive: true });
  // 同步路径到 RuyiAgent 的配置文件中
  ScrcpyManager.setAnnotationPathConfig(annotationStoragePath);
  return annotationStoragePath;
});

ipcMain.handle('save-annotation-data', async (event, data) => {
  writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE START');
  try {
    // 标注数据保存到应用的用户数据目录
    writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE documentsPath: ' + annotationStoragePath);

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const saveDir = path.join(annotationStoragePath, `ruyi-annotation-${timestamp}`);
    await fs.mkdir(saveDir, { recursive: true });
    writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE saveDir: ' + saveDir);

    // 保存原始截图
    const screenshotPath = path.join(saveDir, 'original.png');
    await fs.writeFile(screenshotPath, data.screenshot.replace(/^data:image\/\w+;base64,/, ''), 'base64');
    writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE screenshotPath: ' + screenshotPath);

    // 保存标注后的图片
    const annotatedPath = path.join(saveDir, 'annotated.png');
    await fs.writeFile(annotatedPath, data.annotatedImage.replace(/^data:image\/\w+;base64,/, ''), 'base64');

    // 保存视图层级
    const hierarchyPath = path.join(saveDir, 'view-hierarchy.json');
    await fs.writeFile(hierarchyPath, data.viewHierarchy);
    writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE hierarchyPath: ' + hierarchyPath);

    // 保存标注数据
    const annotationPath = path.join(saveDir, 'annotation.json');
    await fs.writeFile(annotationPath, JSON.stringify(data.annotations, null, 2));
    writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE annotationPath: ' + annotationPath);

    // 上传到云端
    const form = new FormData();
    form.append('label', timestamp); // 使用时间戳作为标签

    // 读取并添加文件到 FormData
    form.append('annotation_json', await fs.readFile(annotationPath), {
      filename: 'annotation.json',
      contentType: 'application/json'
    });

    form.append('view_hierarchy_json', await fs.readFile(hierarchyPath), {
      filename: 'view-hierarchy.json',
      contentType: 'application/json'
    });

    form.append('annotated_png', await fs.readFile(annotatedPath), {
      filename: 'annotated.png',
      contentType: 'image/png'
    });

    form.append('original_png', await fs.readFile(screenshotPath), {
      filename: 'original.png',
      contentType: 'image/png'
    });
    writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE form: ' + form);

    // 发送上传请求
    const uploadUrl = `${API_BASE_URL_DATA}/api/anno/upload`;
    writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE uploadUrl: ' + uploadUrl);

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: form,
      headers: form.getHeaders()
    });
    writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE response: ' + response);

    if (!response.ok) {
      throw new Error(`Upload failed with status: ${response.status}`);
    }

    const uploadResult = await response.json();
    writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE uploadResult: ' + uploadResult);

    return uploadResult;

  } catch (error) {
    console.error('保存或上传标注数据失败:', error);
    writeLog('[MAIN] SAVE-ANNOTATION-DATA-SAVE error: ' + error);
    throw error;
  }
});

// 添加获取标注的 IPC 处理函数
ipcMain.handle('get-annotations', async () => {
  try {
    // 检查目录是否存在
    try {
      await fs.access(annotationStoragePath);
    } catch {
      return []; // 如果目录不存在，返回空数组
    }

    // 获取所有标注目录
    const dirs = await fs.readdir(annotationStoragePath);
    const annotationGroups = [];

    // 遍历每个目录，读取标注数据
    for (const dir of dirs) {
      const annotationPath = path.join(annotationStoragePath, dir, 'annotation.json');
      const screenshotPath = path.join(annotationStoragePath, dir, 'annotated.png');
      try {
        // 读取标注数据
        const data = await fs.readFile(annotationPath, 'utf8');
        const annotations = JSON.parse(data);

        // 读取截图
        const screenshotData = await fs.readFile(screenshotPath, 'base64');
        const screenshot = `data:image/png;base64,${screenshotData}`;

        // 获取标注模式（从第一个标注中获取）
        const mode = annotations[0]?.mode || 'grounding';

        annotationGroups.push({
          mode,
          screenshot,
          annotations,
          directory: dir
        });
      } catch (error) {
        console.error(`读取标注文件失败 ${annotationPath}:`, error);
      }
    }

    return annotationGroups;
  } catch (error) {
    console.error('获取标注失败:', error);
    throw error;
  }
});

// 添加删除标注的 IPC 处理函数
ipcMain.handle('delete-annotation', async (event, directory) => {
  try {
    // 检查目录是否存在
    try {
      await fs.access(annotationStoragePath);
    } catch (error) {
      console.error('标注目录不存在:', annotationStoragePath);
      throw new Error('标注目录不存在');
    }

    // 删除指定目录
    const dirPath = path.join(annotationStoragePath, directory);
    console.log('正在删除标注目录:', dirPath);
    await fs.rm(dirPath, { recursive: true, force: true });
    console.log('标注目录删除成功:', dirPath);

    return true;
  } catch (error) {
    console.error('删除标注失败:', error);
    throw error;
  }
});

// 添加一键删除所有标注的 IPC 处理函数
ipcMain.handle('delete-all-annotations', async () => {
  try {
    // 检查 annotations 目录是否存在
    try {
      await fs.access(annotationStoragePath);
    } catch {
      // 目录不存在，无需任何操作
      return true;
    }

    // 读取目录中的所有文件和子目录
    const dirents = await fs.readdir(annotationStoragePath, { withFileTypes: true });

    // 遍历并删除所有子目录
    for (const dirent of dirents) {
      if (dirent.isDirectory()) {
        const dirPath = path.join(annotationStoragePath, dirent.name);
        await fs.rm(dirPath, { recursive: true, force: true });
        console.log('已删除标注目录:', dirPath);
      }
    }
    
    return true;
  } catch (error) {
    console.error('删除全部标注数据失败:', error);
    throw error;
  }
});

ipcMain.handle('get-ruyillm-key-config', async () => {
  return ScrcpyManager.getRuyiLLMKeyConfig();
});

// Browser homepage configuration handlers
ipcMain.handle('get-browser-homepage-config', async () => {
  try {
    return ScrcpyManager.getBrowserHomepageConfig();
  } catch (error) {
    console.error('获取浏览器首页配置失败:', error);
    throw error;
  }
});

ipcMain.handle('set-browser-homepage-config', async (event, homepage) => {
  try {
    ScrcpyManager.setBrowserHomepageConfig(homepage);
    return true;
  } catch (error) {
    console.error('设置浏览器首页配置失败:', error);
    throw error;
  }
});

// Default search engine configuration handlers
ipcMain.handle('get-default-search-engine-config', async () => {
  try {
    return ScrcpyManager.getDefaultSearchEngineConfig();
  } catch (error) {
    console.error('获取默认搜索引擎配置失败:', error);
    throw error;
  }
});

ipcMain.handle('set-default-search-engine-config', async (event, searchEngine) => {
  try {
    ScrcpyManager.setDefaultSearchEngineConfig(searchEngine);
    return true;
  } catch (error) {
    console.error('设置默认搜索引擎配置失败:', error);
    throw error;
  }
});

/**
 * 打开外部 URL
 */
ipcMain.handle('open-external-url', async (event, url) => {
  try {
    await shell.openExternal(url);
    return true;
  } catch (error) {
    console.error('Failed to open external URL:', error);
    return false;
  }
});

// 修改 IPC 处理函数
ipcMain.handle('create-browser-view', async (event, url, deviceId) => {
    try {
        const response = await fetch(`http://localhost:${ScrcpyManager.getPortConfig().electron_http_port}/browser/create`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ url, deviceId })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
    } catch (error) {
        console.error('Error creating browser view:', error);
        throw error;
    }
});

ipcMain.handle('toggle-browser-view', async (event, isVisible) => {
    try {
        let response;
        if (isVisible) {
            // 显示浏览器视图
            response = await fetch(`http://localhost:${ScrcpyManager.getPortConfig().electron_http_port}/browser/show`, {
                method: 'POST'
            });
        } else {
            // 临时隐藏浏览器视图（用于模态框）
            response = await fetch(`http://localhost:${ScrcpyManager.getPortConfig().electron_http_port}/browser/hide`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ temporary: true })
            });
        }
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
    } catch (error) {
        console.error('Error toggling browser view:', error);
        throw error;
    }
});

ipcMain.handle('destroy-browser-view', async () => {
    try {
        const response = await fetch(`http://localhost:${ScrcpyManager.getPortConfig().electron_http_port}/browser/destroy`, {
            method: 'POST'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
    } catch (error) {
        console.error('Error destroying browser view:', error);
        throw error;
    }
});

ipcMain.handle('hide-browser-view', async (event, temporary = true) => {
    try {
        const response = await fetch(`http://localhost:${ScrcpyManager.getPortConfig().electron_http_port}/browser/hide`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ temporary })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
    } catch (error) {
        console.error('Error hiding browser view:', error);
        throw error;
    }
});

ipcMain.handle('browser-go-back', async () => {
    try {
        const response = await fetch(`http://localhost:${ScrcpyManager.getPortConfig().electron_http_port}/browser/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ command: 'goBack' })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
    } catch (error) {
        console.error('Error going back:', error);
        throw error;
    }
});

ipcMain.handle('browser-go-forward', async () => {
    try {
        const response = await fetch(`http://localhost:${ScrcpyManager.getPortConfig().electron_http_port}/browser/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ command: 'goForward' })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
    } catch (error) {
        console.error('Error going forward:', error);
        throw error;
    }
});

ipcMain.handle('browser-reload', async () => {
    try {
        const response = await fetch(`http://localhost:${ScrcpyManager.getPortConfig().electron_http_port}/browser/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ command: 'reload' })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
    } catch (error) {
        console.error('Error reloading page:', error);
        throw error;
    }
});

ipcMain.handle('browser-load-url', async (event, url) => {
    try {
        const response = await fetch(`http://localhost:${ScrcpyManager.getPortConfig().electron_http_port}/browser/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ command: 'loadURL', params: { url } })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
    } catch (error) {
        console.error('Error loading URL:', error);
        throw error;
    }
});

ipcMain.handle('save-image', async (event, base64Data) => {
  const { canceled, filePath } = await dialog.showSaveDialog({
    // title: '保存图片',
    defaultPath: `ruyi-image-${Date.now()}.png`,
    filters: [
      { name: 'Images', extensions: ['png', 'jpg', 'gif'] }
    ]
  });

  if (!canceled && filePath) {
    try {
      const data = base64Data.replace(/^data:image\/\w+;base64,/, '');
      const buffer = Buffer.from(data, 'base64');
      await fs.writeFile(filePath, buffer);
      return { success: true, path: filePath };
    } catch (error) {
      console.error('Failed to save image:', error);
      return { success: false, error: error.message };
    }
  }
  return { success: false, reason: 'cancelled' };
});

/**
 * 保存任务信息到本地仓库
 * @param {string} taskName 任务名称
 * @param {string} taskDescription 任务描述
 * @param {string} codeScript 代码脚本
 * @param {string} NLScript 自然语言脚本
 * @returns {Promise<{success: boolean, message: string, path?: string}>} 保存结果
 */
async function saveTaskToLocalRepo(taskName, taskDescription, codeScript, NLScript) {
  try {
    if (!taskName) {
      return { success: false, message: 'taskNameEmpty' };
    }

    // 创建任务目录
    const taskDir = path.join(repoStoragePath, taskName);
    await fs.mkdir(taskDir, { recursive: true });

    // 创建任务信息JSON文件
    const taskData = {
      name: taskName,
      description: taskDescription || '',
      code_script: codeScript || '',
      NL_script: NLScript || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const taskFilePath = path.join(taskDir, `${taskName}.json`);
    await fs.writeFile(taskFilePath, JSON.stringify(taskData, null, 2), 'utf8');

    return {
      success: true,
      message: 'taskSaveSuccess',
      path: taskFilePath
    };
  } catch (error) {
    console.error('保存任务到本地仓库失败:', error);
    return {
      success: false,
      message: `taskSaveFailed: ${error.message}`
    };
  }
}

// 注册IPC处理程序 - 任务管理
ipcMain.handle('save-task-to-local-repo', async (event, { taskName, taskDescription, codeScript, NLScript }) => {
  return await saveTaskToLocalRepo(taskName, taskDescription, codeScript, NLScript);
});

// 注册IPC处理程序 - 用户管理
ipcMain.handle('set-current-user', async (event, userInfo) => {
  try {
    await setCurrentUser(userInfo);
    return { success: true, message: '用户设置成功' };
  } catch (error) {
    return { success: false, message: error.message };
  }
});

ipcMain.handle('clear-user-repo-data', async (event, username) => {
  try {
    await clearUserRepoData(username);
    return { success: true, message: '用户数据清理成功' };
  } catch (error) {
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-current-user', async () => {
  return currentUser;
});

/**
 * 获取本地仓库中的任务列表
 * @returns {Promise<Array<{name: string, description: string, path: string, created_at: string, updated_at: string}>>} 任务列表
 */
async function getLocalRepoTasks() {
  try {
    // 确保仓库目录存在
    await fs.mkdir(repoStoragePath, { recursive: true });

    // 读取仓库目录下的所有文件夹
    const dirs = await fs.readdir(repoStoragePath, { withFileTypes: true });
    const tasks = [];

    // 遍历每个目录，读取任务信息
    for (const dir of dirs) {
      if (dir.isDirectory()) {
        const taskDir = path.join(repoStoragePath, dir.name);
        const taskFilePath = path.join(taskDir, `${dir.name}.json`);
        
        try {
          // 检查任务文件是否存在
          await fs.access(taskFilePath);
          
          // 读取任务信息
          const taskData = JSON.parse(await fs.readFile(taskFilePath, 'utf8'));
          tasks.push({
            name: taskData.name,
            description: taskData.description,
            path: taskFilePath,
            created_at: taskData.created_at,
            updated_at: taskData.updated_at
          });
        } catch (error) {
          console.error(`读取任务文件失败: ${taskFilePath}`, error);
          // 跳过错误的任务文件
          continue;
        }
      }
    }

    // 按更新时间排序，最新的在前面
    return tasks.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));
  } catch (error) {
    console.error('获取本地仓库任务列表失败:', error);
    return [];
  }
}

/**
 * 从本地仓库加载任务
 * @param {string} taskName 任务名称
 * @returns {Promise<{success: boolean, message: string, task?: any}>} 加载结果
 */
async function loadTaskFromLocalRepo(taskName) {
  try {
    if (!taskName) {
      return { success: false, message: '任务名称不能为空' };
    }

    const taskDir = path.join(repoStoragePath, taskName);
    const taskFilePath = path.join(taskDir, `${taskName}.json`);

    // 检查任务文件是否存在
    try {
      await fs.access(taskFilePath);
    } catch (error) {
      return { success: false, message: '任务文件不存在' };
    }

    // 读取任务信息
    const taskData = JSON.parse(await fs.readFile(taskFilePath, 'utf8'));
    
    return {
      success: true,
      message: '任务加载成功',
      task: taskData
    };
  } catch (error) {
    console.error('加载任务失败:', error);
    return {
      success: false,
      message: `加载任务失败: ${error.message}`
    };
  }
}

// 注册IPC处理程序
ipcMain.handle('get-local-repo-tasks', async () => {
  return await getLocalRepoTasks();
});

ipcMain.handle('load-task-from-local-repo', async (event, taskName) => {
  return await loadTaskFromLocalRepo(taskName);
});

/**
 * 从本地仓库删除任务
 * @param {string} taskName 任务名称
 * @returns {Promise<{success: boolean, message: string}>} 删除结果
 */
async function deleteTaskFromLocalRepo(taskName) {
  try {
    if (!taskName) {
      return { success: false, message: 'taskNameEmpty' };
    }

    const taskDir = path.join(repoStoragePath, taskName);
    
    // 检查任务目录是否存在
    try {
      await fs.access(taskDir);
    } catch (error) {
      // 任务目录不存在时，直接返回成功（认为删除目标已达成）
      return { success: true, message: 'taskDeleteSuccess' };
    }

    // 删除任务目录及其中的所有文件
    await fs.rm(taskDir, { recursive: true, force: true });
    
    return {
      success: true,
      message: 'taskDeleteSuccess'
    };
  } catch (error) {
    console.error('删除任务失败:', error);
    return {
      success: false,
      message: `taskDeleteFailed: ${error.message}`
    };
  }
}

// 注册IPC处理程序
ipcMain.handle('delete-task-from-local-repo', async (event, taskName) => {
  return await deleteTaskFromLocalRepo(taskName);
});

/**
 * 获取指定任务文件夹下的所有文件
 * @param {string} taskName 任务名称
 * @returns {Promise<Array<{name: string, path: string, size: number, modified: string}>>} 文件列表
 */
async function getTaskFiles(taskName) {
  try {
    console.log(`[getTaskFiles] 开始获取任务文件列表，taskName: ${taskName}`);
    
    if (!taskName) {
      console.log('[getTaskFiles] taskName 为空，返回空数组');
      return [];
    }

    const taskDir = path.join(repoStoragePath, taskName);
    console.log(`[getTaskFiles] 任务目录路径: ${taskDir}`);
    
    // 检查任务目录是否存在
    try {
      await fs.access(taskDir);
      console.log(`[getTaskFiles] 任务目录存在: ${taskDir}`);
    } catch (error) {
      console.log(`[getTaskFiles] 任务目录不存在: ${taskDir}`);
      return [];
    }

    // 读取置顶配置
    const pinnedConfig = await getFilePinnedConfig(taskName);

    // 读取目录下的所有文件
    const files = await fs.readdir(taskDir, { withFileTypes: true });
    console.log(`[getTaskFiles] 目录下原始文件列表:`, files.map(f => `${f.name} (${f.isFile() ? 'file' : 'dir'})`));
    
    const fileList = [];

    for (const file of files) {
      if (file.isFile() && file.name !== '.git' && !file.name.startsWith('.') && file.name !== '.file_pinned_config.json') {
        const filePath = path.join(taskDir, file.name);
        const stats = await fs.stat(filePath);
        
        // 获取置顶信息
        const pinnedInfo = pinnedConfig[file.name];
        
        // 确定文件类型
        let fileType = 'data'; // 默认为数据文件
        
        if (file.name === 'Knowledge.json') {
          fileType = 'knowledge';
        } else if (file.name.toLowerCase().endsWith('.json')) {
          // 对于JSON文件，尝试读取内容来判断是否是任务文件
          try {
            const content = await fs.readFile(filePath, 'utf8');
            const jsonData = JSON.parse(content);
            if ('task_name' in jsonData && 'NL_script' in jsonData && 'code_script' in jsonData) {
              fileType = 'task';
            }
          } catch (error) {
            // JSON解析失败，保持为data类型
            console.warn(`[getTaskFiles] 无法解析JSON文件 ${file.name}:`, error);
          }
        }
        
        fileList.push({
          name: file.name,
          path: filePath,
          size: stats.size,
          modified: stats.mtime.toISOString(),
          created: stats.birthtime.toISOString(), // 添加创建时间
          isPinned: !!pinnedInfo,
          pinnedTime: pinnedInfo ? pinnedInfo.pinnedTime : null,
          fileType: fileType
        });
        console.log(`[getTaskFiles] 添加文件: ${file.name}, 大小: ${stats.size} bytes, 创建时间: ${stats.birthtime.toISOString()}, 置顶: ${!!pinnedInfo}, 类型: ${fileType}`);
      }
    }

    // 排序逻辑：
    // 1. 置顶的文件在前面，按置顶时间倒序（最新置顶的在最上面）
    // 2. 非置顶的文件在后面，按创建时间倒序（最新创建的在前面，最老的在下面）
    const sortedFiles = fileList.sort((a, b) => {
      // 如果都是置顶文件，按置顶时间倒序
      if (a.isPinned && b.isPinned) {
        return new Date(b.pinnedTime) - new Date(a.pinnedTime);
      }
      // 置顶文件在前面
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      // 都不是置顶文件，按创建时间倒序
      return new Date(b.created) - new Date(a.created);
    });
    
    console.log(`[getTaskFiles] 最终文件列表 (共${sortedFiles.length}个文件):`, sortedFiles.map(f => `${f.name} (置顶: ${f.isPinned})`));
    
    return sortedFiles;
  } catch (error) {
    console.error('[getTaskFiles] 获取任务文件列表失败:', error);
    return [];
  }
}

/**
 * 创建空的任务文件
 * @param {string} taskName 任务名称
 * @param {string} taskShortName 任务简称
 * @param {string} description 任务描述
 * @param {string} projectName 项目名称
 * @returns {Promise<{success: boolean, message: string, fileName?: string}>} 创建结果
 */
async function createEmptyTaskFile(taskName, taskShortName, description, projectName) {
  try {
    if (!taskName || !projectName) {
      return { success: false, message: '任务名称和项目名称不能为空' };
    }

    const taskDir = path.join(repoStoragePath, taskName);
    
    // 确保任务目录存在
    await fs.mkdir(taskDir, { recursive: true });

    // 生成任务文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = taskShortName 
      ? `${taskShortName}_${timestamp}.json`
      : `Task_${timestamp}.json`;
    
    const filePath = path.join(taskDir, fileName);
    
    // 检查文件是否已存在
    try {
      await fs.access(filePath);
      return { success: false, message: '文件已存在' };
    } catch (error) {
      // 文件不存在，可以创建
    }

    // 创建空的任务文件数据结构
    const emptyTaskData = {
      project_name: projectName,
      project_description: description || '',
      task_name: taskShortName || '新任务',
      task_description: description || '',
      NL_script: '', // 空的自然语言脚本
      code_script: '', // 空的代码脚本
      NL_script_labeled: '', // 空的带标号自然语言脚本
      code_script_labeled: '', // 空的带标号代码脚本
      NL_script_version: 0, // 版本标志位
      code_script_version: 0, // 版本标志位
      messages: [], // 空的消息列表
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // 创建文件
    await fs.writeFile(filePath, JSON.stringify(emptyTaskData, null, 2), 'utf8');
    
    console.log(`[createEmptyTaskFile] 空任务文件创建成功: ${fileName}`);
    
    return {
      success: true,
      message: '空任务文件创建成功',
      fileName: fileName
    };
  } catch (error) {
    console.error('创建空任务文件失败:', error);
    return {
      success: false,
      message: `创建空任务文件失败: ${error.message}`
    };
  }
}

/**
 * 在任务文件夹中创建新文件
 * @param {string} taskName 任务名称
 * @param {string} fileName 文件名称
 * @param {string} content 文件内容
 * @returns {Promise<{success: boolean, message: string}>} 创建结果
 */
async function createTaskFile(taskName, fileName, content) {
  try {
    if (!taskName || !fileName) {
      return { success: false, message: 'taskNameAndFileNameEmpty' };
    }

    const taskDir = path.join(repoStoragePath, taskName);
    
    // 确保任务目录存在
    await fs.mkdir(taskDir, { recursive: true });

    const filePath = path.join(taskDir, fileName);
    
    // 检查文件是否已存在
    try {
      await fs.access(filePath);
      return { success: false, message: 'fileExists' };
    } catch (error) {
      // 文件不存在，可以创建
    }

    // 创建文件
    await fs.writeFile(filePath, content || '', 'utf8');
    
    return {
      success: true,
      message: 'fileCreateSuccess'
    };
  } catch (error) {
    console.error('创建文件失败:', error);
    return {
      success: false,
      message: `fileCreateFailed: ${error.message}`
    };
  }
}

/**
 * 读取任务文件夹中的文件内容
 * @param {string} taskName 任务名称
 * @param {string} fileName 文件名称
 * @returns {Promise<{success: boolean, content?: string, message: string}>} 读取结果
 */
async function readTaskFile(taskName, fileName) {
  try {
    if (!taskName || !fileName) {
      return { success: false, message: '任务名称和文件名称不能为空' };
    }

    const filePath = path.join(repoStoragePath, taskName, fileName);
    
    // 检查文件是否存在
    try {
      await fs.access(filePath);
    } catch (error) {
      return { success: false, message: '文件不存在' };
    }

    // 读取文件内容
    const content = await fs.readFile(filePath, 'utf8');
    
    return {
      success: true,
      content,
      message: '文件读取成功'
    };
  } catch (error) {
    console.error('读取文件失败:', error);
    return {
      success: false,
      message: `读取文件失败: ${error.message}`
    };
  }
}

/**
 * 删除任务文件夹中的文件
 * @param {string} taskName 任务名称
 * @param {string} fileName 文件名称
 * @returns {Promise<{success: boolean, message: string}>} 删除结果
 */
async function deleteTaskFile(taskName, fileName, repoToken = '', userInfo = {}) {
  try {
    if (!taskName || !fileName) {
      return { success: false, message: 'taskNameAndFileNameEmpty' };
    }

    const filePath = path.join(repoStoragePath, taskName, fileName);
    
    // 检查文件是否存在
    try {
      await fs.access(filePath);
    } catch (error) {
      // 文件不存在时，直接返回成功（认为删除目标已达成）
      return { success: true, message: 'fileDeleteSuccess' };
    }

    // 删除文件
    await fs.unlink(filePath);

    // 新增：自动 push 到 git server
    let pushResult = null;
    try {
      if (repoToken) {
        pushResult = await saveAndPushProjectToGit(taskName, repoToken, `delete file: ${fileName}`, userInfo);
      }
    } catch (pushError) {
      pushResult = { success: false, message: `Git push failed: ${pushError.message}` };
    }
    
    return {
      success: true,
      message: 'fileDeleteSuccess',
      gitPushed: !!pushResult,
      gitPushSuccess: pushResult ? pushResult.success : undefined,
      gitPushMessage: pushResult ? pushResult.message : undefined
    };
  } catch (error) {
    console.error('删除文件失败:', error);
    return {
      success: false,
      message: `fileDeleteFailed: ${error.message}`
    };
  }
}

// 注册IPC处理程序
ipcMain.handle('delete-task-file', async (event, data) => {
  // 兼容老参数格式
  const { taskName, fileName, repoToken, userInfo } = data || {};
  return await deleteTaskFile(taskName, fileName, repoToken, userInfo);
});

/**
 * 更新任务文件
 * @param {string} taskName 任务名称
 * @param {string} fileName 文件名称
 * @param {string} content 文件内容
 * @returns {Promise<{success: boolean, message: string}>} 更新结果
 */
async function updateTaskFile(taskName, fileName, content) {
  try {
    if (!taskName || !fileName) {
      return { success: false, message: '任务名称和文件名称不能为空' };
    }

    const taskDir = path.join(repoStoragePath, taskName);
    const filePath = path.join(taskDir, fileName);
    
    // 确保目录存在
    await fs.mkdir(taskDir, { recursive: true });
    
    // 写入文件内容
    await fs.writeFile(filePath, content, 'utf8');
    
    return {
      success: true,
      message: '文件更新成功'
    };
  } catch (error) {
    console.error('更新文件失败:', error);
    return {
      success: false,
      message: `更新文件失败: ${error.message}`
    };
  }
}

/**
 * 保存任务的聊天消息
 * @param {string} taskName 任务名称
 * @param {string} fileName 任务文件名称
 * @param {Array} messages 聊天消息数组
 * @returns {Promise<{success: boolean, message: string}>} 保存结果
 */
async function saveTaskChatMessages(taskName, fileName, messages) {
  try {
    if (!taskName || !fileName) {
      return { success: false, message: '任务名称和文件名称不能为空' };
    }

    // 读取当前任务文件内容
    const readResult = await readTaskFile(taskName, fileName);
    if (!readResult.success) {
      return { success: false, message: `读取任务文件失败: ${readResult.message}` };
    }

    // 解析任务文件JSON
    let taskData;
    try {
      taskData = JSON.parse(readResult.content);
    } catch (error) {
      return { success: false, message: `解析任务文件JSON失败: ${error.message}` };
    }

    // 添加或更新messages字段
    taskData.messages = messages;

    // 将更新后的数据写回文件
    const updateResult = await updateTaskFile(taskName, fileName, JSON.stringify(taskData, null, 2));
    if (!updateResult.success) {
      return { success: false, message: `更新任务文件失败: ${updateResult.message}` };
    }

    return {
      success: true,
      message: '聊天消息保存成功'
    };
  } catch (error) {
    console.error('保存聊天消息失败:', error);
    return {
      success: false,
      message: `保存聊天消息失败: ${error.message}`
    };
  }
}

/**
 * 加载任务的聊天消息
 * @param {string} taskName 任务名称
 * @param {string} fileName 任务文件名称
 * @returns {Promise<{success: boolean, messages?: Array, message: string}>} 加载结果
 */
async function loadTaskChatMessages(taskName, fileName) {
  try {
    if (!taskName || !fileName) {
      return { success: false, message: '任务名称和文件名称不能为空' };
    }

    // 读取任务文件内容
    const readResult = await readTaskFile(taskName, fileName);
    if (!readResult.success) {
      return { success: false, message: `读取任务文件失败: ${readResult.message}` };
    }

    // 解析任务文件JSON
    let taskData;
    try {
      taskData = JSON.parse(readResult.content);
    } catch (error) {
      return { success: false, message: `解析任务文件JSON失败: ${error.message}` };
    }

    // 返回消息数组，如果不存在则返回空数组
    return {
      success: true,
      messages: taskData.messages || [],
      message: '聊天消息加载成功'
    };
  } catch (error) {
    console.error('加载聊天消息失败:', error);
    return {
      success: false,
      messages: [],
      message: `加载聊天消息失败: ${error.message}`
    };
  }
}

// 注册IPC处理程序
ipcMain.handle('save-task-chat-messages', async (event, data) => {
  const { taskName, fileName, messages } = data || {};
  return await saveTaskChatMessages(taskName, fileName, messages);
});

ipcMain.handle('load-task-chat-messages', async (event, data) => {
  const { taskName, fileName } = data || {};
  return await loadTaskChatMessages(taskName, fileName);
});

/**
 * 获取任务文件树结构（支持目录）
 * @param {string} taskName 任务名称
 * @returns {Promise<{success: boolean, tree?: Array, message?: string}>} 文件树结构
 */
async function getTaskFileTree(taskName) {
  try {
    // console.log(`[getTaskFileTree] 开始获取任务文件树，taskName: ${taskName}`);
    
    if (!taskName) {
      console.log('[getTaskFileTree] taskName 为空，返回失败');
      return { success: false, message: '任务名称不能为空' };
    }

    const taskDir = path.join(repoStoragePath, taskName);
    // console.log(`[getTaskFileTree] 任务目录路径: ${taskDir}`);
    
    // 检查任务目录是否存在
    try {
      await fs.access(taskDir);
      // console.log(`[getTaskFileTree] 任务目录存在: ${taskDir}`);
    } catch (error) {
      console.log(`[getTaskFileTree] 任务目录不存在: ${taskDir}`);
      return { success: true, tree: [] };
    }

    // 读取置顶配置
    const pinnedConfig = await getFilePinnedConfig(taskName);

    // 递归读取目录结构
    const buildTree = async (dirPath, relativePath = '', depth = 0) => {
      const items = [];
      try {
        const files = await fs.readdir(dirPath, { withFileTypes: true });
        
        for (const file of files) {
          // 跳过隐藏文件和git目录
          if (file.name.startsWith('.') || file.name === '.file_pinned_config.json') {
            continue;
          }
          
          const fullPath = path.join(dirPath, file.name);
          const itemRelativePath = relativePath ? path.join(relativePath, file.name) : file.name;
          const stats = await fs.stat(fullPath);
          
          // 获取置顶信息
          const pinnedInfo = pinnedConfig[itemRelativePath];
          
          if (file.isDirectory()) {
            // 目录
            const children = await buildTree(fullPath, itemRelativePath, depth + 1);
            items.push({
              name: file.name,
              path: itemRelativePath,
              isDirectory: true,
              modified: stats.mtime.toISOString(),
              created: stats.birthtime.toISOString(),
              isPinned: !!pinnedInfo,
              pinnedTime: pinnedInfo ? pinnedInfo.pinnedTime : null,
              fileType: 'folder',
              children: children,
              depth: depth
            });
          } else {
            // 文件
            let fileType = 'data'; // 默认为数据文件
            
            if (file.name === 'Knowledge.json') {
              fileType = 'knowledge';
            } else if (file.name.toLowerCase().endsWith('.json')) {
              // 对于JSON文件，尝试读取内容来判断是否是任务文件
              try {
                const content = await fs.readFile(fullPath, 'utf8');
                const jsonData = JSON.parse(content);
                if ('task_name' in jsonData && 'NL_script' in jsonData && 'code_script' in jsonData) {
                  fileType = 'task';
                }
              } catch (error) {
                console.warn(`[getTaskFileTree] 无法解析JSON文件 ${file.name}:`, error);
              }
            }
            
            items.push({
              name: file.name,
              path: itemRelativePath,
              isDirectory: false,
              size: stats.size,
              modified: stats.mtime.toISOString(),
              created: stats.birthtime.toISOString(),
              isPinned: !!pinnedInfo,
              pinnedTime: pinnedInfo ? pinnedInfo.pinnedTime : null,
              fileType: fileType,
              depth: depth
            });
          }
        }
      } catch (error) {
        console.error(`[getTaskFileTree] 读取目录失败 ${dirPath}:`, error);
      }
      
      // 排序：目录在前，文件在后，然后按置顶状态和时间排序
      return items.sort((a, b) => {
        // 目录优先
        if (a.isDirectory && !b.isDirectory) return -1;
        if (!a.isDirectory && b.isDirectory) return 1;
        
        // 同类型内部，置顶优先
        if (a.isPinned && !b.isPinned) return -1;
        if (!a.isPinned && b.isPinned) return 1;
        
        // 置顶文件按置顶时间倒序，非置顶文件按创建时间倒序
        if (a.isPinned && b.isPinned) {
          return new Date(b.pinnedTime) - new Date(a.pinnedTime);
        } else {
          return new Date(b.created) - new Date(a.created);
        }
      });
    };

    const tree = await buildTree(taskDir);
    // console.log(`[getTaskFileTree] 文件树构建完成，根节点数量: ${tree.length}`);
    
    return { success: true, tree };
  } catch (error) {
    console.error('[getTaskFileTree] 获取任务文件树失败:', error);
    return { success: false, message: `获取文件树失败: ${error.message}` };
  }
}

/**
 * 创建目录
 * @param {string} taskName 任务名称
 * @param {string} folderName 目录名称
 * @param {string} parentPath 父目录路径（可选）
 * @returns {Promise<{success: boolean, message: string}>} 创建结果
 */
async function createTaskFolder(taskName, folderName, parentPath = '') {
  try {
    if (!taskName || !folderName) {
      return { success: false, message: '任务名称和目录名称不能为空' };
    }

    const taskDir = path.join(repoStoragePath, taskName);
    
    // 确保任务目录存在
    await fs.mkdir(taskDir, { recursive: true });

    // 构建目录路径
    const fullFolderPath = parentPath 
      ? path.join(taskDir, parentPath, folderName)
      : path.join(taskDir, folderName);
    
    // 检查目录是否已存在
    try {
      await fs.access(fullFolderPath);
      return { success: false, message: '目录已存在' };
    } catch (error) {
      // 目录不存在，可以创建
    }

    // 创建目录
    await fs.mkdir(fullFolderPath, { recursive: true });
    
    return {
      success: true,
      message: '目录创建成功'
    };
  } catch (error) {
    console.error('创建目录失败:', error);
    return {
      success: false,
      message: `创建目录失败: ${error.message}`
    };
  }
}

/**
 * 删除目录
 * @param {string} taskName 任务名称
 * @param {string} folderPath 目录路径
 * @param {string} repoToken 仓库访问令牌
 * @param {Object} userInfo 用户信息
 * @returns {Promise<{success: boolean, message: string}>} 删除结果
 */
async function deleteTaskFolder(taskName, folderPath, repoToken = '', userInfo = {}) {
  try {
    if (!taskName || !folderPath) {
      return { success: false, message: '任务名称和目录路径不能为空' };
    }

    const fullFolderPath = path.join(repoStoragePath, taskName, folderPath);
    
    // 检查目录是否存在
    try {
      const stats = await fs.stat(fullFolderPath);
      if (!stats.isDirectory()) {
        return { success: false, message: '指定路径不是目录' };
      }
    } catch (error) {
      return { success: false, message: '目录不存在' };
    }

    // 删除目录及其所有内容
    await fs.rm(fullFolderPath, { recursive: true, force: true });

    // 新增：自动 push 到 git server
    let pushResult = null;
    try {
      if (repoToken) {
        pushResult = await saveAndPushProjectToGit(taskName, repoToken, `delete folder: ${folderPath}`, userInfo);
      }
    } catch (pushError) {
      pushResult = { success: false, message: `Git push failed: ${pushError.message}` };
    }
    
    return {
      success: true,
      message: '目录删除成功',
      gitPushed: !!pushResult,
      gitPushSuccess: pushResult ? pushResult.success : undefined,
      gitPushMessage: pushResult ? pushResult.message : undefined
    };
  } catch (error) {
    console.error('删除目录失败:', error);
    return {
      success: false,
      message: `删除目录失败: ${error.message}`
    };
  }
}

/**
 * 重命名文件或目录
 * @param {string} taskName 任务名称
 * @param {string} oldPath 旧路径
 * @param {string} newName 新名称
 * @returns {Promise<{success: boolean, message: string}>} 重命名结果
 */
async function renameTaskItem(taskName, oldPath, newName) {
  try {
    if (!taskName || !oldPath || !newName) {
      return { success: false, message: '参数不能为空' };
    }

    const oldFullPath = path.join(repoStoragePath, taskName, oldPath);
    const parentDir = path.dirname(oldFullPath);
    const newFullPath = path.join(parentDir, newName);
    
    // 检查原文件/目录是否存在
    try {
      await fs.access(oldFullPath);
    } catch (error) {
      return { success: false, message: '原文件或目录不存在' };
    }

    // 检查新名称是否已存在
    try {
      await fs.access(newFullPath);
      return { success: false, message: '新名称已存在' };
    } catch (error) {
      // 新名称不存在，可以重命名
    }

    // 重命名
    await fs.rename(oldFullPath, newFullPath);
    
    return {
      success: true,
      message: '重命名成功'
    };
  } catch (error) {
    console.error('重命名失败:', error);
    return {
      success: false,
      message: `重命名失败: ${error.message}`
    };
  }
}

/**
 * 读取并解析数据文件（CSV/Excel）
 * @param {string} taskName 任务名称
 * @param {string} fileName 文件名称
 * @returns {Promise<{success: boolean, message: string, data?: {name: string, columns: string[], data: Record<string, any>[]}}>}
 */
async function readAndParseDataFile(taskName, fileName) {
  try {
    if (!taskName || !fileName) {
      return { success: false, message: '任务名称和文件名称不能为空' };
    }

    const filePath = path.join(repoStoragePath, taskName, fileName);
    
    // 检查文件是否存在
    try {
      await fs.access(filePath);
    } catch (error) {
      return { success: false, message: '文件不存在' };
    }

    const fileExtension = path.extname(fileName).toLowerCase();
    const buffer = await fs.readFile(filePath);

    let data, columns;

    if (fileExtension === '.csv') {
      const csvContent = buffer.toString('utf8');
      const parsed = Papa.parse(csvContent, {
        header: true,
        skipEmptyLines: true,
      });
      columns = parsed.meta.fields;
      data = parsed.data;
    } else if (fileExtension === '.xlsx' || fileExtension === '.xls') {
      const workbook = xlsx.read(buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      data = xlsx.utils.sheet_to_json(worksheet);
      if (data.length > 0) {
        columns = Object.keys(data[0]);
      } else {
        columns = [];
      }
    } else {
      return { success: false, message: '不支持的文件类型' };
    }

    return {
      success: true,
      message: '文件解析成功',
      data: {
        name: fileName,
        columns,
        data,
      }
    };
  } catch (error) {
    console.error('解析数据文件失败:', error);
    return {
      success: false,
      message: `解析数据文件失败: ${error.message}`
    };
  }
}

// 注册新的IPC处理程序
ipcMain.handle('get-task-files', async (event, taskName) => {
  return await getTaskFiles(taskName);
});

ipcMain.handle('get-task-file-tree', async (event, taskName) => {
  return await getTaskFileTree(taskName);
});

ipcMain.handle('create-empty-task-file', async (event, { taskName, taskShortName, description, projectName }) => {
  return await createEmptyTaskFile(taskName, taskShortName, description, projectName);
});

ipcMain.handle('create-task-file', async (event, { taskName, fileName, content, folderPath }) => {
  // 如果指定了文件夹路径，在文件夹中创建文件
  if (folderPath) {
    const fullFileName = path.join(folderPath, fileName);
    return await createTaskFile(taskName, fullFileName, content);
  }
  return await createTaskFile(taskName, fileName, content);
});

ipcMain.handle('create-task-folder', async (event, { taskName, folderName, parentPath }) => {
  return await createTaskFolder(taskName, folderName, parentPath);
});

ipcMain.handle('read-task-file', async (event, { taskName, fileName }) => {
  return await readTaskFile(taskName, fileName);
});


ipcMain.handle('delete-task-folder', async (event, { taskName, folderPath, repoToken, userInfo }) => {
  return await deleteTaskFolder(taskName, folderPath, repoToken, userInfo);
});

ipcMain.handle('rename-task-item', async (event, { taskName, oldPath, newName }) => {
  return await renameTaskItem(taskName, oldPath, newName);
});

ipcMain.handle('update-task-file', async (event, { taskName, fileName, content }) => {
  return await updateTaskFile(taskName, fileName, content);
});

ipcMain.handle('read-and-parse-data-file', async (event, { taskName, fileName }) => {
  return await readAndParseDataFile(taskName, fileName);
});

// 添加强制刷新文件列表的处理程序
ipcMain.handle('refresh-task-files', async (event, taskName) => {
  console.log(`[refresh-task-files] 强制刷新任务文件列表: ${taskName}`);
  // 添加小延迟确保文件系统操作完成
  await new Promise(resolve => setTimeout(resolve, 500));
  return await getTaskFiles(taskName);
});


/**
 * 初始化本地 git 仓库并推送到远程
 * @param {string} taskName 任务名称
 * @param {string} repoToken 仓库访问令牌
 * @param {string} projectDescription 项目描述
 * @param {Object} userInfo 用户信息 {username, email}
 * @returns {Promise<{success: boolean, message: string}>} 操作结果
 */
async function initializeAndPushGitRepo(taskName, repoToken, projectDescription = '', userInfo = {}) {
  try {
    const uuid = currentUser?.uuid;
    if (!uuid) {
      const errorMsg = '[Git Init] ✗ 未找到用户UUID，请重新登录';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '未找到用户UUID，请重新登录' };
    }
    // 使用配置中的服务器地址
    const gitServerUrl = GIT_SERVER_URL;
    const taskDir = path.join(repoStoragePath, taskName);
    console.log(`[Git Init] 项目目录: ${taskDir}`);
    console.log(`[Git Init] Git服务器地址: ${gitServerUrl}`);
    await writeLog(`[Git Init] 项目目录: ${taskDir}`);
    await writeLog(`[Git Init] Git服务器地址: ${gitServerUrl}`);
    
    // 确保任务目录存在
    console.log('[Git Init] 创建项目目录...');
    await writeLog('[Git Init] 创建项目目录...');
    await fs.mkdir(taskDir, { recursive: true });
    console.log('[Git Init] ✓ 项目目录创建完成');
    await writeLog('[Git Init] ✓ 项目目录创建完成');
    
    // 检查是否已经是 git 仓库
    let isRepo = false;
    try {
      await fs.access(path.join(taskDir, '.git'));
      isRepo = true;
      console.log('[Git Init] ✓ Git仓库已存在');
      await writeLog('[Git Init] ✓ Git仓库已存在');
    } catch (error) {
      console.log('[Git Init] 需要初始化新的Git仓库');
      await writeLog('[Git Init] 需要初始化新的Git仓库');
    }
    
    // 如果不是 git 仓库，初始化
    if (!isRepo) {
      console.log('[Git Init] 正在初始化Git仓库...');
      await writeLog('[Git Init] 正在初始化Git仓库...');
      await git.init({ fs, dir: taskDir, defaultBranch: 'main' });
      console.log('[Git Init] ✓ Git仓库初始化完成，默认分支: main');
      await writeLog('[Git Init] ✓ Git仓库初始化完成，默认分支: main');
    }
    
    // 检查是否有文件需要添加
    console.log('[Git Init] 检查项目文件...');
    await writeLog('[Git Init] 检查项目文件...');
    const files = await fs.readdir(taskDir);
    const taskFiles = files.filter(file => file !== '.git');
    console.log(`[Git Init] 发现 ${taskFiles.length} 个文件: ${taskFiles.join(', ')}`);
    await writeLog(`[Git Init] 发现 ${taskFiles.length} 个文件: ${taskFiles.join(', ')}`);
    
    if (taskFiles.length === 0) {
      console.log('[Git Init] 没有文件，创建默认Knowledge.json文件...');
      await writeLog('[Git Init] 没有文件，创建默认Knowledge.json文件...');
      // 创建一个 Knowledge.json 文件，确保有文件可以提交
      const knowledgePath = path.join(taskDir, 'Knowledge.json');
      const knowledgeData = {
        "project_name": taskName,
        "project_description": projectDescription,
        "device_information": "",
        "operation_guide": ""
      };
      await fs.writeFile(knowledgePath, JSON.stringify(knowledgeData, null, 2), 'utf8');
      console.log(`[Git Init] ✓ Knowledge.json 文件已创建: ${knowledgePath}`);
      await writeLog(`[Git Init] ✓ Knowledge.json 文件已创建: ${knowledgePath}`);
      
      // 立即通知前端文件已创建，不等待Git操作完成
      if (win && !win.isDestroyed()) {
        win.webContents.send('project-file-created', { taskName, fileName: 'Knowledge.json' });
      }
    }
    
    // 配置用户信息
    const gitUserName = userInfo.username || userInfo.displayName || 'Ruyi User';
    const gitUserEmail = userInfo.email || '<EMAIL>';
    
    console.log(`[Git Init] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    await writeLog(`[Git Init] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    
    try {
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.name',
        value: gitUserName
      });
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.email',
        value: gitUserEmail
      });
      console.log('[Git Init] ✓ Git用户信息配置完成');
      await writeLog('[Git Init] ✓ Git用户信息配置完成');
    } catch (error) {
      console.warn('[Git Init] Git用户信息配置失败:', error.message);
      await writeLog(`[Git Init] Git用户信息配置失败: ${error.message}`);
    }
    
    // 添加所有文件
    console.log('[Git Init] 添加文件到Git暂存区...');
    await writeLog('[Git Init] 添加文件到Git暂存区...');
    const filesToAdd = await fs.readdir(taskDir);
    for (const file of filesToAdd) {
      if (file !== '.git') {
        await git.add({ fs, dir: taskDir, filepath: file });
        console.log(`[Git Init] ✓ 已添加文件: ${file}`);
      }
    }
    await writeLog('[Git Init] ✓ 所有文件已添加到暂存区');
    
    // 检查是否有提交
    let hasCommits = false;
    console.log('[Git Init] 检查现有提交...');
    await writeLog('[Git Init] 检查现有提交...');
    try {
      const commits = await git.log({ fs, dir: taskDir, depth: 1 });
      hasCommits = commits.length > 0;
      console.log(`[Git Init] 仓库有 ${commits.length > 0 ? '现有' : '无'} 提交`);
      await writeLog(`[Git Init] 仓库有 ${commits.length > 0 ? '现有' : '无'} 提交`);
    } catch (error) {
      console.log('[Git Init] 没有找到提交，将创建初始提交');
      await writeLog('[Git Init] 没有找到提交，将创建初始提交');
    }
    
    // 检查是否有变更需要提交
    console.log('[Git Init] 检查待提交的变更...');
    await writeLog('[Git Init] 检查待提交的变更...');
    try {
      const status = await git.statusMatrix({ fs, dir: taskDir });
      const hasChanges = status.some(row => row[1] !== row[2] || row[2] !== row[3]);
      console.log(`[Git Init] 有变更需要提交: ${hasChanges}`);
      await writeLog(`[Git Init] 有变更需要提交: ${hasChanges}`);
      
      if (hasChanges) {
        // 提交更改
        const commitMessage = hasCommits ? `Update task: ${taskName}` : `Initial commit: ${taskName}`;
        console.log(`[Git Init] 正在提交变更，消息: ${commitMessage}`);
        await writeLog(`[Git Init] 正在提交变更，消息: ${commitMessage}`);
        const sha = await git.commit({
          fs,
          dir: taskDir,
          author: {
            name: gitUserName,
            email: gitUserEmail
          },
          message: commitMessage
        });
        console.log(`[Git Init] ✓ 提交完成，SHA: ${sha}`);
        await writeLog(`[Git Init] ✓ 提交完成，SHA: ${sha}`);
      } else {
        console.log('[Git Init] 没有变更需要提交');
        await writeLog('[Git Init] 没有变更需要提交');
      }
    } catch (error) {
      console.warn(`[Git Init] ✗ 提交失败: ${error.message}`);
      await writeLog(`[Git Init] ✗ 提交失败: ${error.message}`);
      // 如果提交失败，可能是因为没有变更，继续执行后续步骤
    }
    
    // 设置远程仓库
    const remoteUrl = `http://${repoToken}@${gitServerUrl}/repo/${uuid}/${encodeURIComponent(taskName)}.git`;
    console.log('[Git Init] remoteUrl: ' + remoteUrl);
    await writeLog(`[Git Init] 配置远程仓库: ${remoteUrl.replace(repoToken, '***')}`);
    
    try {
      // 获取现有的远程仓库
      console.log('[Git Init] 检查现有远程仓库...');
      await writeLog('[Git Init] 检查现有远程仓库...');
      const remotes = await git.listRemotes({ fs, dir: taskDir });
      const originRemote = remotes.find(remote => remote.remote === 'origin');
      
      if (originRemote) {
        console.log('[Git Init] 删除现有的origin远程仓库...');
        await writeLog('[Git Init] 删除现有的origin远程仓库...');
        // 如果存在 origin，删除并重新添加
        await git.deleteRemote({ fs, dir: taskDir, remote: 'origin' });
      }
      
      // 添加远程仓库
      console.log('[Git Init] 添加新的origin远程仓库...');
      await writeLog('[Git Init] 添加新的origin远程仓库...');
      await git.addRemote({
        fs,
        dir: taskDir,
        remote: 'origin',
        url: remoteUrl
      });
      console.log('[Git Init] ✓ 远程仓库配置完成');
      await writeLog('[Git Init] ✓ 远程仓库配置完成');
    } catch (error) {
      const errorMsg = `[Git Init] ✗ 配置远程仓库失败: ${error.message}`;
      console.warn(errorMsg);
      await writeLog(errorMsg);
      throw error;
    }
    
    // 推送到远程仓库
    console.log('[Git Init] 推送到远程仓库...');
    await writeLog('[Git Init] 推送到远程仓库...');
    try {
      await git.push({
        fs,
        http,
        dir: taskDir,
        remote: 'origin',
        ref: 'main',
        onAuth: () => ({ username: repoToken, password: '' }),
        onAuthFailure: () => {
          throw new Error('Authentication failed. Please check your repository token.');
        }
      });
      console.log('[Git Init] ✓ 推送到远程仓库成功');
      await writeLog('[Git Init] ✓ 推送到远程仓库成功');
    } catch (error) {
      const errorMsg = `[Git Init] ✗ 推送失败: ${error.message}`;
      console.error(errorMsg);
      await writeLog(errorMsg);
      throw error;
    }
    
    const successMsg = '[Git Init] ===== Git仓库初始化并推送成功 =====';
    console.log(successMsg);
    await writeLog(successMsg);
    return {
      success: true,
      message: 'Git repository initialized and pushed successfully using built-in Git'
    };
  } catch (error) {
    const errorMsg = `[Git Init] ===== Git操作失败: ${error.message} =====`;
    console.error(errorMsg);
    console.error('[Git Init] 详细错误信息:', error);
    await writeLog(errorMsg);
    await writeLog(`[Git Init] 详细错误堆栈: ${error.stack}`);
    return {
      success: false,
      message: `Git operation failed: ${error.message}`
    };
  }
}

// 注册 IPC 处理程序
ipcMain.handle('initialize-and-push-git-repo', async (event, { taskName, repoToken, projectDescription, userInfo }) => {
  return await initializeAndPushGitRepo(taskName, repoToken, projectDescription, userInfo);
});

/**
 * 更新Knowledge.json文件内容
 * @param {string} taskName 任务名称
 * @param {string} knowledgeContent 知识库内容（新格式或旧格式）
 * @returns {Promise<{success: boolean, message: string}>} 更新结果
 */
async function updateKnowledgeFile(taskName, knowledgeContent) {
  try {
    if (!taskName) {
      return { success: false, message: '任务名称不能为空' };
    }

    const taskDir = path.join(repoStoragePath, taskName);
    const knowledgeFilePath = path.join(taskDir, 'Knowledge.json');

    // 检查Knowledge.json文件是否存在
    try {
      await fs.access(knowledgeFilePath);
    } catch (error) {
      return { success: false, message: 'Knowledge.json文件不存在' };
    }

    // 读取现有的Knowledge.json文件
    const existingContent = await fs.readFile(knowledgeFilePath, 'utf8');
    const knowledgeData = JSON.parse(existingContent);

    // 解析传入的内容，如果是新格式的渲染内容，需要提取出设备信息和操作指南
    if (knowledgeContent.includes('# 设备信息') && knowledgeContent.includes('# 操作指南')) {
      // 新格式：从渲染内容中提取两个部分
      const lines = knowledgeContent.split('\n');
      let deviceInfoSection = '';
      let operationGuideSection = '';
      let currentSection = '';
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (line.trim() === '# 设备信息') {
          currentSection = 'device_info';
          continue;
        } else if (line.trim() === '# 操作指南') {
          currentSection = 'operation_guide';
          continue;
        }
        
        if (currentSection === 'device_info') {
          deviceInfoSection += (deviceInfoSection ? '\n' : '') + line;
        } else if (currentSection === 'operation_guide') {
          operationGuideSection += (operationGuideSection ? '\n' : '') + line;
        }
      }
      
      // 更新新格式的字段
      knowledgeData.device_information = deviceInfoSection.trim();
      knowledgeData.operation_guide = operationGuideSection.trim();
      
      // 删除旧的 knowledge 字段（如果存在）
      if ('knowledge' in knowledgeData) {
        delete knowledgeData.knowledge;
      }
    } else {
      // 向后兼容：如果传入的是简单字符串，根据现有结构决定如何保存
      if ('knowledge' in knowledgeData) {
        // 旧格式，直接更新 knowledge 字段
        knowledgeData.knowledge = knowledgeContent;
      } else {
        // 新格式，但传入的是简单内容，暂时保存到 device_information
        knowledgeData.device_information = knowledgeContent;
        if (!knowledgeData.operation_guide) {
          knowledgeData.operation_guide = '';
        }
      }
    }

    knowledgeData.updated_at = new Date().toISOString();

    // 写回文件
    await fs.writeFile(knowledgeFilePath, JSON.stringify(knowledgeData, null, 2), 'utf8');

    return {
      success: true,
      message: '项目知识库更新成功'
    };
  } catch (error) {
    console.error('更新Knowledge.json文件失败:', error);
    return {
      success: false,
      message: `更新文件失败: ${error.message}`
    };
  }
}

// 注册IPC处理程序
ipcMain.handle('update-knowledge-file', async (event, { taskName, knowledgeContent }) => {
  return await updateKnowledgeFile(taskName, knowledgeContent);
});

/**
 * 加载自定义提示模板
 * 从Desktop或Documents文件夹中搜索模板文件
 * @returns {Promise<string|null>} 模板内容或null
 */
async function loadPromptTemplate() {
  try {
    const os = require('os');
    const homeDir = os.homedir();

    // 定义搜索路径
    const searchPaths = [
      path.join(homeDir, 'Desktop', 'template.j2'),
      path.join(homeDir, 'Documents', 'template.j2')
    ];

    console.log('[loadPromptTemplate] 搜索自定义提示模板，路径:', searchPaths);

    // 依次尝试每个路径
    for (const templatePath of searchPaths) {
      try {
        await fs.access(templatePath);
        console.log(`[loadPromptTemplate] 找到模板文件: ${templatePath}`);

        const templateContent = await fs.readFile(templatePath, 'utf8');
        console.log(`[loadPromptTemplate] 成功加载模板，长度: ${templateContent.length}`);

        return templateContent;
      } catch (error) {
        // 文件不存在，继续尝试下一个路径
        console.log(`[loadPromptTemplate] 模板文件不存在: ${templatePath}`);
      }
    }

    console.log('[loadPromptTemplate] 未找到任何自定义提示模板');
    return null;
  } catch (error) {
    console.error('[loadPromptTemplate] 加载提示模板失败:', error);
    return null;
  }
}

/**
 * 加载自定义 JavaScript 模板文件
 * @returns {Promise<string|null>} 模板内容或null
 */
async function loadJSTemplate() {
  try {
    const os = require('os');
    const homeDir = os.homedir();

    // 定义搜索路径
    const searchPaths = [
      path.join(homeDir, 'Desktop', 'template.js'),
      path.join(homeDir, 'Documents', 'template.js')
    ];

    console.log('[loadJSTemplate] 搜索自定义JavaScript模板，路径:', searchPaths);

    // 依次尝试每个路径
    for (const templatePath of searchPaths) {
      try {
        await fs.access(templatePath);
        console.log(`[loadJSTemplate] 找到JavaScript模板文件: ${templatePath}`);

        const templateContent = await fs.readFile(templatePath, 'utf8');
        console.log(`[loadJSTemplate] 成功加载JavaScript模板，长度: ${templateContent.length}`);

        return templateContent;
      } catch (error) {
        // 文件不存在，继续尝试下一个路径
        console.log(`[loadJSTemplate] JavaScript模板文件不存在: ${templatePath}`);
      }
    }

    console.log('[loadJSTemplate] 未找到任何自定义JavaScript模板');
    return null;
  } catch (error) {
    console.error('[loadJSTemplate] 加载JavaScript模板失败:', error);
    return null;
  }
}

// 注册IPC处理程序
ipcMain.handle('load-prompt-template', async (event) => {
  return await loadPromptTemplate();
});

ipcMain.handle('load-js-template', async (event) => {
  return await loadJSTemplate();
});

/**
 * 在主进程中解析和执行 JavaScript 模板
 * @param {string} templateContent - JavaScript 模板内容
 * @returns {Object|null} 解析后的模板对象
 */
function parseJSTemplateInMain(templateContent) {
  try {
    console.log('[Main] Parsing JavaScript template in main process');

    // 在主进程中可以安全地使用 new Function
    const templateFunction = new Function('module', 'exports', 'window', templateContent);
    
    // 创建模拟对象
    const mockModule = { exports: {} };
    const mockWindow = { RuyiTemplate: null };
    
    // 执行模板
    templateFunction(mockModule, mockModule.exports, mockWindow);
    
    // 提取模板对象
    let templateObject = null;
    
    // 检查 Node.js 风格导出
    if (mockModule.exports && (mockModule.exports.templateVariables || mockModule.exports.templateFunctions)) {
      templateObject = mockModule.exports;
    }
    // 检查浏览器风格导出
    else if (mockWindow.RuyiTemplate) {
      templateObject = mockWindow.RuyiTemplate;
    }

    if (templateObject && templateObject.templateVariables && templateObject.templateFunctions) {
      console.log('[Main] Successfully parsed JavaScript template');
      console.log('[Main] Available template functions:', Object.keys(templateObject.templateFunctions));
      return templateObject;
    } else {
      console.error('[Main] Invalid template structure - missing templateVariables or templateFunctions');
      return null;
    }
  } catch (error) {
    console.error('[Main] Error parsing JavaScript template:', error);
    return null;
  }
}

/**
 * 处理模板执行请求
 * @param {string} mechanism - 模板机制名称
 * @param {Object} variables - 模板变量
 * @returns {Promise<string|null>} 处理后的模板或null
 */
async function processJSTemplate(mechanism, variables = {}) {
  try {
    console.log(`[Main] Processing JS template for mechanism: ${mechanism}`);
    
    // 加载模板内容
    const templateContent = await loadJSTemplate();
    
    if (!templateContent) {
      console.log('[Main] No custom JavaScript template found');
      return null;
    }
    
    // 解析模板
    const templateObject = parseJSTemplateInMain(templateContent);
    
    if (!templateObject) {
      console.error('[Main] Failed to parse JavaScript template');
      return null;
    }
    
    const { templateFunctions } = templateObject;
    const templateFunction = templateFunctions[mechanism];

    if (!templateFunction || typeof templateFunction !== 'function') {
      console.warn(`[Main] No template function found for mechanism: ${mechanism}`);
      return null;
    }

    // 设置默认语言代码
    const finalVariables = {
      languageCode: 'zh',
      ...variables
    };
    
    console.log('[Main] Final variables:', Object.keys(finalVariables));
    console.log('[Main] Final languageCode:', finalVariables.languageCode);
    
    // 执行模板函数
    const processedTemplate = templateFunction(finalVariables);
    
    if (typeof processedTemplate === 'string') {
      console.log(`[Main] Successfully processed template for ${mechanism}, length: ${processedTemplate.length}`);
      return processedTemplate;
    } else {
      console.error(`[Main] Template function for ${mechanism} did not return a string`);
      return null;
    }
  } catch (error) {
    console.error(`[Main] Error processing JS template for ${mechanism}:`, error);
    return null;
  }
}

ipcMain.handle('process-js-template', async (event, mechanism, variables) => {
  return await processJSTemplate(mechanism, variables);
});

/**
 * 保存当前项目的所有文件并推送到Git服务器
 * @param {string} taskName 任务名称
 * @param {string} repoToken 仓库访问令牌
 * @param {string} commitMessage 提交消息
 * @param {Object} userInfo 用户信息 {username, email}
 * @returns {Promise<{success: boolean, message: string}>} 操作结果
 */
async function saveAndPushProjectToGit(taskName, repoToken, commitMessage = '', userInfo = {}) {
  try {
    const uuid = currentUser?.uuid;
    if (!uuid) {
      const errorMsg = '[Git Save] ✗ 未找到用户UUID，请重新登录';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '未找到用户UUID，请重新登录' };
    }
    console.log(`[Git Save] ===== 开始保存项目到Git: ${taskName} =====`);
    await writeLog(`[Git Save] ===== 开始保存项目到Git: ${taskName} =====`);

    if (!taskName) {
      const errorMsg = '[Git Save] 项目名称不能为空';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '项目名称不能为空' };
    }

    if (!repoToken) {
      const errorMsg = '[Git Save] 仓库访问令牌不能为空';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '仓库访问令牌不能为空' };
    }

    // 使用配置中的服务器地址
    const gitServerUrl = GIT_SERVER_URL;
    const taskDir = path.join(repoStoragePath, taskName);
    console.log(`[Git Save] 项目目录: ${taskDir}`);
    console.log(`[Git Save] Git服务器地址: ${gitServerUrl}`);
    await writeLog(`[Git Save] 项目目录: ${taskDir}`);
    await writeLog(`[Git Save] Git服务器地址: ${gitServerUrl}`);
    
    // 检查项目目录是否存在
    try {
      await fs.access(taskDir);
      console.log('[Git Save] ✓ 项目目录存在');
      await writeLog('[Git Save] ✓ 项目目录存在');
    } catch (error) {
      const errorMsg = '[Git Save] ✗ 项目目录不存在，请先创建项目';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '项目目录不存在，请先创建项目' };
    }
    
    // 检查是否是git仓库，如果不是则初始化
    let isRepo = false;
    try {
      await fs.access(path.join(taskDir, '.git'));
      isRepo = true;
      console.log('[Git Save] ✓ Git仓库已存在');
      await writeLog('[Git Save] ✓ Git仓库已存在');
    } catch (error) {
      console.log('[Git Save] 正在初始化新的Git仓库...');
      await writeLog('[Git Save] 正在初始化新的Git仓库...');
      await git.init({ fs, dir: taskDir, defaultBranch: 'main' });
      console.log('[Git Save] ✓ Git仓库初始化完成');
      await writeLog('[Git Save] ✓ Git仓库初始化完成');
      
      // 设置远程仓库
      const remoteUrl = `http://${repoToken}@${gitServerUrl}/repo/${uuid}/${encodeURIComponent(taskName)}.git`;
      console.log('[Git Init] remoteUrl: ' + remoteUrl);
      console.log(`[Git Save] 正在添加远程仓库: ${remoteUrl.replace(repoToken, '***')}`);
      await writeLog(`[Git Save] 正在添加远程仓库: ${remoteUrl.replace(repoToken, '***')}`);
      await git.addRemote({
        fs,
        dir: taskDir,
        remote: 'origin',
        url: remoteUrl
      });
      console.log('[Git Save] ✓ 远程仓库添加完成');
      await writeLog('[Git Save] ✓ 远程仓库添加完成');
    }
    
    // 配置用户信息
    const gitUserName = userInfo.username || userInfo.displayName || 'Ruyi User';
    const gitUserEmail = userInfo.email || '<EMAIL>';
    
    console.log(`[Git Save] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    await writeLog(`[Git Save] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    
    try {
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.name',
        value: gitUserName
      });
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.email',
        value: gitUserEmail
      });
      console.log('[Git Save] ✓ Git用户信息配置完成');
      await writeLog('[Git Save] ✓ Git用户信息配置完成');
    } catch (error) {
      console.warn('[Git Save] Git用户信息配置失败:', error.message);
      await writeLog(`[Git Save] Git用户信息配置失败: ${error.message}`);
    }
    
    // 添加所有文件（排除.git目录）
    console.log('[Git Save] 正在读取项目文件...');
    await writeLog('[Git Save] 正在读取项目文件...');
    const files = await fs.readdir(taskDir);
    const filesToAdd = files.filter(file => file !== '.git');
    console.log(`[Git Save] 发现 ${filesToAdd.length} 个文件: ${filesToAdd.join(', ')}`);
    await writeLog(`[Git Save] 发现 ${filesToAdd.length} 个文件: ${filesToAdd.join(', ')}`);
    
    if (filesToAdd.length === 0) {
      const errorMsg = '[Git Save] ✗ 项目中没有文件可以保存';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '项目中没有文件可以保存' };
    }
    
    // 处理文件变更：添加存在的文件，移除已删除的文件
    console.log('[Git Save] 正在处理文件变更到Git暂存区...');
    await writeLog('[Git Save] 正在处理文件变更到Git暂存区...');
    
    // 获取当前Git状态来识别已删除的文件
    try {
      const statusMatrix = await git.statusMatrix({ fs, dir: taskDir });
      for (const [filepath, headStatus, workdirStatus, stageStatus] of statusMatrix) {
        if (workdirStatus === 0 && headStatus === 1) {
          // 文件在HEAD中存在但在工作目录中已删除，需要从索引中移除
          try {
            await git.remove({ fs, dir: taskDir, filepath });
            console.log(`[Git Save] ✓ 已移除删除的文件: ${filepath}`);
            await writeLog(`[Git Save] ✓ 已移除删除的文件: ${filepath}`);
          } catch (error) {
            console.warn(`[Git Save] ✗ 移除文件失败 ${filepath}: ${error.message}`);
            await writeLog(`[Git Save] ✗ 移除文件失败 ${filepath}: ${error.message}`);
          }
        }
      }
    } catch (error) {
      console.warn('[Git Save] 获取Git状态失败，将使用传统方式添加文件:', error.message);
      await writeLog(`[Git Save] 获取Git状态失败，将使用传统方式添加文件: ${error.message}`);
    }
    
    // 添加所有当前存在的文件
    for (const file of filesToAdd) {
      try {
        await git.add({ fs, dir: taskDir, filepath: file });
        console.log(`[Git Save] ✓ 已添加文件: ${file}`);
      } catch (error) {
        console.warn(`[Git Save] ✗ 添加文件失败 ${file}: ${error.message}`);
        await writeLog(`[Git Save] ✗ 添加文件失败 ${file}: ${error.message}`);
      }
    }
    
    // 检查是否有变更需要提交
    console.log('[Git Save] 正在检查Git状态...');
    await writeLog('[Git Save] 正在检查Git状态...');
    try {
      const status = await git.statusMatrix({ fs, dir: taskDir });
      const hasChanges = status.some(row => row[1] !== row[2] || row[2] !== row[3]);
      console.log(`[Git Save] Git状态检查完成，有变更: ${hasChanges}`);
      await writeLog(`[Git Save] Git状态检查完成，有变更: ${hasChanges}`);
      
      if (!hasChanges) {
        const msg = '[Git Save] ✓ 项目没有变更，无需保存';
        console.log(msg);
        await writeLog(msg);
        return { success: true, message: '项目没有变更，无需保存' };
      }
      
      // 提交变更
      const defaultCommitMessage = `Save project: ${taskName} at ${new Date().toISOString()}`;
      const finalCommitMessage = commitMessage || defaultCommitMessage;
      console.log(`[Git Save] 正在提交变更，消息: ${finalCommitMessage}`);
      await writeLog(`[Git Save] 正在提交变更，消息: ${finalCommitMessage}`);
      
      const sha = await git.commit({
        fs,
        dir: taskDir,
        author: {
          name: gitUserName,
          email: gitUserEmail
        },
        message: finalCommitMessage
      });
      console.log(`[Git Save] ✓ 提交完成，SHA: ${sha}`);
      await writeLog(`[Git Save] ✓ 提交完成，SHA: ${sha}`);
      
    } catch (error) {
      const errorMsg = `[Git Save] ✗ 提交失败: ${error.message}`;
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: `提交失败: ${error.message}` };
    }
    
    // 推送到远程仓库
    console.log('[Git Save] 正在推送到远程仓库...');
    await writeLog('[Git Save] 正在推送到远程仓库...');
    try {
      await git.push({
        fs,
        http,
        dir: taskDir,
        remote: 'origin',
        ref: 'main',
        onAuth: () => ({ username: repoToken, password: '' }),
        onAuthFailure: () => {
          throw new Error('Authentication failed. Please check your repository token.');
        }
      });
      console.log('[Git Save] ✓ 推送到远程仓库成功');
      await writeLog('[Git Save] ✓ 推送到远程仓库成功');
      
    } catch (error) {
      const errorMsg = `[Git Save] ✗ 推送失败: ${error.message}`;
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: `推送失败: ${error.message}` };
    }
    
    const successMsg = '[Git Save] ===== 项目保存成功并已推送到云端仓库 =====';
    console.log(successMsg);
    await writeLog(successMsg);
    return {
      success: true,
      message: '项目保存成功并已推送到云端仓库'
    };
    
  } catch (error) {
    const errorMsg = `[Git Save] ===== Git操作失败: ${error.message} =====`;
    console.error(errorMsg);
    console.error('[Git Save] 详细错误信息:', error);
    await writeLog(errorMsg);
    await writeLog(`[Git Save] 详细错误堆栈: ${error.stack}`);
    return {
      success: false,
      message: `保存失败: ${error.message}`
    };
  }
}

// 注册 IPC 处理程序
ipcMain.handle('save-and-push-project-to-git', async (event, { taskName, repoToken, commitMessage, userInfo }) => {
  return await saveAndPushProjectToGit(taskName, repoToken, commitMessage, userInfo);
});

/**
 * 克隆远程仓库到本地
 * @param {string} projectName 项目名称
 * @param {string} repoToken 仓库访问令牌
 * @param {Object} userInfo 用户信息 {username, email}
 * @returns {Promise<{success: boolean, message: string}>} 克隆结果
 */
async function cloneProjectFromGit(projectName, repoToken, userInfo = {}) {
  try {
    const uuid = currentUser?.uuid;
    if (!uuid) {
      const errorMsg = '[Git Clone] ✗ 未找到用户UUID，请重新登录';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '未找到用户UUID，请重新登录' };
    }
    console.log(`[Git Clone] ===== 开始克隆项目: ${projectName} =====`);
    await writeLog(`[Git Clone] ===== 开始克隆项目: ${projectName} =====`);

    if (!projectName) {
      const errorMsg = '[Git Clone] 项目名称不能为空';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '项目名称不能为空' };
    }

    if (!repoToken) {
      const errorMsg = '[Git Clone] 仓库访问令牌不能为空';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '仓库访问令牌不能为空' };
    }

    const gitServerUrl = GIT_SERVER_URL;
    const taskDir = path.join(repoStoragePath, projectName);
    const remoteUrl = `http://${repoToken}@${gitServerUrl}/repo/${uuid}/${encodeURIComponent(projectName)}.git`;
    console.log('[Git Clone] remoteUrl: ' + remoteUrl);
    
    console.log(`[Git Clone] 项目目录: ${taskDir}`);
    console.log(`[Git Clone] 远程URL: ${remoteUrl.replace(repoToken, '***')}`);
    await writeLog(`[Git Clone] 项目目录: ${taskDir}`);
    await writeLog(`[Git Clone] 远程URL: ${remoteUrl.replace(repoToken, '***')}`);

    // 检查目录是否已存在
    try {
      await fs.access(taskDir);
      const errorMsg = '[Git Clone] 项目目录已存在，请使用pull操作';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '项目目录已存在，请使用pull操作' };
    } catch (error) {
      // 目录不存在，可以继续克隆
      console.log('[Git Clone] 项目目录不存在，开始克隆');
      await writeLog('[Git Clone] 项目目录不存在，开始克隆');
    }

    // 确保父目录存在
    await fs.mkdir(repoStoragePath, { recursive: true });

    // 执行git clone
    console.log('[Git Clone] 正在克隆远程仓库...');
    await writeLog('[Git Clone] 正在克隆远程仓库...');
    
    await git.clone({
      fs,
      http,
      dir: taskDir,
      url: remoteUrl,
      ref: 'main',
      singleBranch: true,
      depth: 1,
      onAuth: () => ({ username: repoToken, password: '' }),
      onAuthFailure: () => {
        throw new Error('Authentication failed. Please check your repository token.');
      }
    });

    // 配置用户信息
    const gitUserName = userInfo.username || userInfo.displayName || 'Ruyi User';
    const gitUserEmail = userInfo.email || '<EMAIL>';
    
    console.log(`[Git Clone] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    await writeLog(`[Git Clone] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    
    try {
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.name',
        value: gitUserName
      });
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.email',
        value: gitUserEmail
      });
      console.log('[Git Clone] ✓ Git用户信息配置完成');
      await writeLog('[Git Clone] ✓ Git用户信息配置完成');
    } catch (error) {
      console.warn('[Git Clone] Git用户信息配置失败:', error.message);
      await writeLog(`[Git Clone] Git用户信息配置失败: ${error.message}`);
    }

    console.log('[Git Clone] ✓ 远程仓库克隆成功');
    await writeLog('[Git Clone] ✓ 远程仓库克隆成功');

    const successMsg = '[Git Clone] ===== 项目克隆成功 =====';
    console.log(successMsg);
    await writeLog(successMsg);
    return {
      success: true,
      message: '项目克隆成功'
    };
  } catch (error) {
    const errorMsg = `[Git Clone] ===== 克隆操作失败: ${error.message} =====`;
    console.error(errorMsg);
    console.error('[Git Clone] 详细错误信息:', error);
    await writeLog(errorMsg);
    await writeLog(`[Git Clone] 详细错误堆栈: ${error.stack}`);
    return {
      success: false,
      message: `克隆失败: ${error.message}`
    };
  }
}

/**
 * 拉取远程仓库最新代码
 * @param {string} projectName 项目名称
 * @param {string} repoToken 仓库访问令牌
 * @param {Object} userInfo 用户信息 {username, email}
 * @returns {Promise<{success: boolean, message: string, hasConflict?: boolean}>} 拉取结果
 */
async function pullProjectFromGit(projectName, repoToken, userInfo = {}) {
  try {
    console.log(`[Git Pull] ===== 开始拉取项目: ${projectName} =====`);
    await writeLog(`[Git Pull] ===== 开始拉取项目: ${projectName} =====`);

    if (!projectName) {
      const errorMsg = '[Git Pull] 项目名称不能为空';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '项目名称不能为空' };
    }

    if (!repoToken) {
      const errorMsg = '[Git Pull] 仓库访问令牌不能为空';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '仓库访问令牌不能为空' };
    }

    const taskDir = path.join(repoStoragePath, projectName);
    
    // 检查项目目录是否存在
    try {
      await fs.access(taskDir);
      console.log('[Git Pull] ✓ 项目目录存在');
      await writeLog('[Git Pull] ✓ 项目目录存在');
    } catch (error) {
      const errorMsg = '[Git Pull] 项目目录不存在，请先克隆项目';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '项目目录不存在，请先克隆项目' };
    }

    // 检查是否是git仓库
    try {
      await fs.access(path.join(taskDir, '.git'));
      console.log('[Git Pull] ✓ Git仓库存在');
      await writeLog('[Git Pull] ✓ Git仓库存在');
    } catch (error) {
      const errorMsg = '[Git Pull] 不是有效的Git仓库';
      console.error(errorMsg);
      await writeLog(errorMsg);
      return { success: false, message: '不是有效的Git仓库' };
    }

    // 确保Git用户信息已配置
    const gitUserName = userInfo.username || userInfo.displayName || 'Ruyi User';
    const gitUserEmail = userInfo.email || '<EMAIL>';
    
    console.log(`[Git Pull] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    await writeLog(`[Git Pull] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    
    try {
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.name',
        value: gitUserName
      });
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.email',
        value: gitUserEmail
      });
      console.log('[Git Pull] ✓ Git用户信息配置完成');
      await writeLog('[Git Pull] ✓ Git用户信息配置完成');
    } catch (error) {
      console.warn('[Git Pull] Git用户信息配置失败:', error.message);
      await writeLog(`[Git Pull] Git用户信息配置失败: ${error.message}`);
    }

    // 检查本地是否有未提交的更改
    console.log('[Git Pull] 检查本地更改...');
    await writeLog('[Git Pull] 检查本地更改...');
    const status = await git.statusMatrix({ fs, dir: taskDir });
    const hasLocalChanges = status.some(row => row[1] !== row[2] || row[2] !== row[3]);
    
    if (hasLocalChanges) {
      console.log('[Git Pull] 发现本地有未提交的更改，检查冲突');
      await writeLog('[Git Pull] 发现本地有未提交的更改，检查冲突');
      
      // 获取远程最新提交
      const remoteRef = await git.resolveRef({
        fs,
        dir: taskDir,
        ref: 'origin/main'
      }).catch(() => null);
      
      if (remoteRef) {
        // 先fetch获取最新的远程信息
        await git.fetch({
          fs,
          http,
          dir: taskDir,
          remote: 'origin',
          ref: 'main',
          onAuth: () => ({ username: repoToken, password: '' })
        });

        const localRef = await git.resolveRef({
          fs,
          dir: taskDir,
          ref: 'main'
        });

        if (remoteRef !== localRef) {
          console.log('[Git Pull] ✗ 检测到冲突：本地有更改且远程也有新提交');
          await writeLog('[Git Pull] ✗ 检测到冲突：本地有更改且远程也有新提交');
          return {
            success: false,
            message: '检测到冲突：本地有未提交的更改，且远程仓库也有新的提交',
            hasConflict: true
          };
        }
      }
    }

    // 执行pull操作
    console.log('[Git Pull] 正在拉取远程更新...');
    await writeLog('[Git Pull] 正在拉取远程更新...');
    
    try {
      await git.pull({
        fs,
        http,
        dir: taskDir,
        ref: 'main',
        singleBranch: true,
        author: {
          name: gitUserName,
          email: gitUserEmail
        },
        onAuth: () => ({ username: repoToken, password: '' }),
        onAuthFailure: () => {
          throw new Error('Authentication failed. Please check your repository token.');
        }
      });

      console.log('[Git Pull] ✓ 远程更新拉取成功');
      await writeLog('[Git Pull] ✓ 远程更新拉取成功');

      const successMsg = '[Git Pull] ===== 项目更新成功 =====';
      console.log(successMsg);
      await writeLog(successMsg);
      return {
        success: true,
        message: '项目更新成功'
      };
    } catch (error) {
      if (error.message.includes('merge conflict') || error.message.includes('conflict')) {
        console.log('[Git Pull] ✗ 拉取时发生冲突');
        await writeLog('[Git Pull] ✗ 拉取时发生冲突');
        return {
          success: false,
          message: '拉取时发生冲突，请选择使用本地版本还是远程版本',
          hasConflict: true
        };
      }
      throw error;
    }
  } catch (error) {
    const errorMsg = `[Git Pull] ===== 拉取操作失败: ${error.message} =====`;
    console.error(errorMsg);
    console.error('[Git Pull] 详细错误信息:', error);
    await writeLog(errorMsg);
    await writeLog(`[Git Pull] 详细错误堆栈: ${error.stack}`);
    return {
      success: false,
      message: `拉取失败: ${error.message}`
    };
  }
}

/**
 * 强制推送本地版本到远程（解决冲突时选择本地版本）
 * @param {string} projectName 项目名称
 * @param {string} repoToken 仓库访问令牌
 * @param {Object} userInfo 用户信息 {username, email}
 * @returns {Promise<{success: boolean, message: string}>} 操作结果
 */
async function forceUseLocalVersion(projectName, repoToken, userInfo = {}) {
  try {
    console.log(`[Git Force Local] ===== 强制使用本地版本: ${projectName} =====`);
    await writeLog(`[Git Force Local] ===== 强制使用本地版本: ${projectName} =====`);

    const taskDir = path.join(repoStoragePath, projectName);
    
    // 确保Git用户信息已配置
    const gitUserName = userInfo.username || userInfo.displayName || 'Ruyi User';
    const gitUserEmail = userInfo.email || '<EMAIL>';
    
    console.log(`[Git Force Local] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    await writeLog(`[Git Force Local] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    
    try {
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.name',
        value: gitUserName
      });
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.email',
        value: gitUserEmail
      });
    } catch (error) {
      console.warn('[Git Force Local] Git用户信息配置失败:', error.message);
      await writeLog(`[Git Force Local] Git用户信息配置失败: ${error.message}`);
    }
    
    // 先提交本地更改
    console.log('[Git Force Local] 提交本地更改...');
    await writeLog('[Git Force Local] 提交本地更改...');
    
    // 添加所有文件
    const files = await fs.readdir(taskDir);
    const filesToAdd = files.filter(file => file !== '.git');
    
    for (const file of filesToAdd) {
      await git.add({ fs, dir: taskDir, filepath: file });
    }
    
    // 提交更改
    const commitMessage = `Force local version: ${projectName} at ${new Date().toISOString()}`;
    await git.commit({
      fs,
      dir: taskDir,
      author: {
        name: gitUserName,
        email: gitUserEmail
      },
      message: commitMessage
    });
    
    console.log('[Git Force Local] ✓ 本地更改已提交');
    await writeLog('[Git Force Local] ✓ 本地更改已提交');

    // 强制推送到远程
    console.log('[Git Force Local] 正在强制推送到远程...');
    await writeLog('[Git Force Local] 正在强制推送到远程...');
    
    await git.push({
      fs,
      http,
      dir: taskDir,
      remote: 'origin',
      ref: 'main',
      force: true,
      onAuth: () => ({ username: repoToken, password: '' }),
      onAuthFailure: () => {
        throw new Error('Authentication failed. Please check your repository token.');
      }
    });

    console.log('[Git Force Local] ✓ 强制推送成功');
    await writeLog('[Git Force Local] ✓ 强制推送成功');

    const successMsg = '[Git Force Local] ===== 强制使用本地版本成功 =====';
    console.log(successMsg);
    await writeLog(successMsg);
    return {
      success: true,
      message: '已强制使用本地版本并推送到远程'
    };
  } catch (error) {
    const errorMsg = `[Git Force Local] ===== 强制使用本地版本失败: ${error.message} =====`;
    console.error(errorMsg);
    await writeLog(errorMsg);
    return {
      success: false,
      message: `强制使用本地版本失败: ${error.message}`
    };
  }
}

/**
 * 强制使用远程版本（解决冲突时选择远程版本）
 * @param {string} projectName 项目名称
 * @param {string} repoToken 仓库访问令牌
 * @param {Object} userInfo 用户信息 {username, email}
 * @returns {Promise<{success: boolean, message: string}>} 操作结果
 */
async function forceUseRemoteVersion(projectName, repoToken, userInfo = {}) {
  try {
    console.log(`[Git Force Remote] ===== 强制使用远程版本: ${projectName} =====`);
    await writeLog(`[Git Force Remote] ===== 强制使用远程版本: ${projectName} =====`);

    const taskDir = path.join(repoStoragePath, projectName);
    
    // 确保Git用户信息已配置
    const gitUserName = userInfo.username || userInfo.displayName || 'Ruyi User';
    const gitUserEmail = userInfo.email || '<EMAIL>';
    
    console.log(`[Git Force Remote] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    await writeLog(`[Git Force Remote] 配置Git用户信息: ${gitUserName} <${gitUserEmail}>`);
    
    try {
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.name',
        value: gitUserName
      });
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.email',
        value: gitUserEmail
      });
    } catch (error) {
      console.warn('[Git Force Remote] Git用户信息配置失败:', error.message);
      await writeLog(`[Git Force Remote] Git用户信息配置失败: ${error.message}`);
    }
    
    // 重置本地仓库到远程状态
    console.log('[Git Force Remote] 正在重置本地仓库...');
    await writeLog('[Git Force Remote] 正在重置本地仓库...');
    
    // 先fetch最新的远程状态
    await git.fetch({
      fs,
      http,
      dir: taskDir,
      remote: 'origin',
      ref: 'main',
      onAuth: () => ({ username: repoToken, password: '' })
    });
    
    // 强制重置到远程main分支
    await git.reset({
      fs,
      dir: taskDir,
      ref: 'origin/main',
      hard: true
    });
    
    console.log('[Git Force Remote] ✓ 本地仓库已重置为远程版本');
    await writeLog('[Git Force Remote] ✓ 本地仓库已重置为远程版本');

    const successMsg = '[Git Force Remote] ===== 强制使用远程版本成功 =====';
    console.log(successMsg);
    await writeLog(successMsg);
    return {
      success: true,
      message: '已强制使用远程版本覆盖本地版本'
    };
  } catch (error) {
    const errorMsg = `[Git Force Remote] ===== 强制使用远程版本失败: ${error.message} =====`;
    console.error(errorMsg);
    await writeLog(errorMsg);
    return {
      success: false,
      message: `强制使用远程版本失败: ${error.message}`
    };
  }
}

// 注册 IPC 处理程序
ipcMain.handle('clone-project-from-git', async (event, { projectName, repoToken, userInfo }) => {
  return await cloneProjectFromGit(projectName, repoToken, userInfo);
});

ipcMain.handle('pull-project-from-git', async (event, { projectName, repoToken, userInfo }) => {
  return await pullProjectFromGit(projectName, repoToken, userInfo);
});

ipcMain.handle('force-use-local-version', async (event, { projectName, repoToken, userInfo }) => {
  return await forceUseLocalVersion(projectName, repoToken, userInfo);
});

ipcMain.handle('force-use-remote-version', async (event, { projectName, repoToken, userInfo }) => {
  return await forceUseRemoteVersion(projectName, repoToken, userInfo);
});

// 全局变量用于防抖
let autoSaveTimers = new Map();
let autoSyncTimers = new Map();

/**
 * Git同步文件到云端（不重复保存本地文件）
 * @param {string} taskName 任务名称
 * @param {string} fileName 文件名称
 * @param {string} content 文件内容（用于确保同步的是最新内容）
 * @param {string} repoToken 仓库访问令牌
 * @param {Object} userInfo 用户信息
 * @returns {Promise<{success: boolean, message: string}>} 操作结果
 */
async function autoSaveAndSync(taskName, fileName, content, repoToken, userInfo = {}) {
  try {
    if (!taskName || !fileName) {
      return { success: false, message: '任务名称和文件名称不能为空' };
    }

    if (!repoToken) {
      return { success: false, message: '缺少仓库访问令牌' };
    }

    const fileKey = `${taskName}/${fileName}`;
    
    console.log(`[GitSync] 同步文件到云端: ${fileKey}`);
    await writeLog(`[GitSync] 同步文件到云端: ${fileKey}`);
    
    const taskDir = path.join(repoStoragePath, taskName);
    
    // 配置Git用户信息
    const gitUserName = userInfo.username || userInfo.displayName || 'Ruyi User';
    const gitUserEmail = userInfo.email || '<EMAIL>';
    
    try {
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.name',
        value: gitUserName
      });
      await git.setConfig({
        fs,
        dir: taskDir,
        path: 'user.email',
        value: gitUserEmail
      });
    } catch (error) {
      console.warn('[GitSync] Git用户信息配置失败:', error.message);
    }
    
    // 添加文件到Git
    await git.add({ fs, dir: taskDir, filepath: fileName });
    
    // 检查是否有变更需要提交
    const status = await git.statusMatrix({ fs, dir: taskDir });
    const hasChanges = status.some(row => row[1] !== row[2] || row[2] !== row[3]);
    
    if (hasChanges) {
      // 提交变更
      const commitMessage = `Auto-save: ${fileName} at ${new Date().toISOString()}`;
      await git.commit({
        fs,
        dir: taskDir,
        author: {
          name: gitUserName,
          email: gitUserEmail
        },
        message: commitMessage
      });
      
      console.log(`[GitSync] 已提交变更: ${fileName}`);
      await writeLog(`[GitSync] 已提交变更: ${fileName}`);
      
      // 推送到远程
      await git.push({
        fs,
        http,
        dir: taskDir,
        remote: 'origin',
        ref: 'main',
        onAuth: () => ({ username: repoToken, password: '' }),
        onAuthFailure: () => {
          throw new Error('Authentication failed');
        }
      });
      
      console.log(`[GitSync] ✓ 文件已同步到云端: ${fileName}`);
      await writeLog(`[GitSync] ✓ 文件已同步到云端: ${fileName}`);
      
      return {
        success: true,
        message: '文件已同步到云端'
      };
    } else {
      console.log(`[GitSync] 没有变更需要同步: ${fileName}`);
      return {
        success: true,
        message: '没有变更需要同步'
      };
    }
  } catch (error) {
    console.error('Git同步失败:', error);
    await writeLog(`Git同步失败: ${error.message}`);
    return {
      success: false,
      message: `Git同步失败: ${error.message}`
    };
  }
}

ipcMain.handle('auto-save-and-sync', async (event, { taskName, fileName, content, repoToken, userInfo }) => {
  return await autoSaveAndSync(taskName, fileName, content, repoToken, userInfo);
});

/**
 * 获取文件置顶配置
 * @param {string} taskName 任务名称
 * @returns {Promise<Object>} 置顶配置
 */
async function getFilePinnedConfig(taskName) {
  try {
    const configPath = path.join(repoStoragePath, taskName, '.file_pinned_config.json');
    
    try {
      await fs.access(configPath);
      const configContent = await fs.readFile(configPath, 'utf8');
      return JSON.parse(configContent);
    } catch (error) {
      // 文件不存在，返回空配置
      return {};
    }
  } catch (error) {
    console.error('获取置顶配置失败:', error);
    return {};
  }
}

/**
 * 保存文件置顶配置
 * @param {string} taskName 任务名称
 * @param {Object} config 置顶配置
 * @returns {Promise<boolean>} 保存结果
 */
async function saveFilePinnedConfig(taskName, config) {
  try {
    const taskDir = path.join(repoStoragePath, taskName);
    const configPath = path.join(taskDir, '.file_pinned_config.json');
    
    // 确保任务目录存在
    await fs.mkdir(taskDir, { recursive: true });
    
    await fs.writeFile(configPath, JSON.stringify(config, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('保存置顶配置失败:', error);
    return false;
  }
}

/**
 * 切换文件置顶状态
 * @param {string} taskName 任务名称
 * @param {string} fileName 文件名称
 * @returns {Promise<{success: boolean, isPinned: boolean, message: string}>} 操作结果
 */
async function toggleFilePinned(taskName, fileName) {
  try {
    if (!taskName || !fileName) {
      return { success: false, isPinned: false, message: '任务名称和文件名称不能为空' };
    }

    // 获取当前置顶配置
    const config = await getFilePinnedConfig(taskName);
    
    // 切换置顶状态
    if (config[fileName]) {
      // 取消置顶
      delete config[fileName];
      
      // 保存配置
      const saveResult = await saveFilePinnedConfig(taskName, config);
      if (saveResult) {
        return {
          success: true,
          isPinned: false,
          message: '文件已取消置顶'
        };
      } else {
        return {
          success: false,
          isPinned: true,
          message: '取消置顶失败'
        };
      }
    } else {
      // 设置置顶
      config[fileName] = {
        pinnedTime: new Date().toISOString()
      };
      
      // 保存配置
      const saveResult = await saveFilePinnedConfig(taskName, config);
      if (saveResult) {
        return {
          success: true,
          isPinned: true,
          message: '文件已置顶'
        };
      } else {
        return {
          success: false,
          isPinned: false,
          message: '置顶失败'
        };
      }
    }
  } catch (error) {
    console.error('切换文件置顶状态失败:', error);
    return {
      success: false,
      isPinned: false,
      message: `操作失败: ${error.message}`
    };
  }
}

// 注册置顶功能的IPC处理程序
ipcMain.handle('toggle-file-pinned', async (event, { taskName, fileName }) => {
  return await toggleFilePinned(taskName, fileName);
});

/**
 * 选择并导入本地文件到项目目录
 * @param {string} taskName 任务名称
 * @param {string} targetFolderPath 目标文件夹路径（可选）
 * @param {string} repoToken 仓库访问令牌
 * @param {Object} userInfo 用户信息
 * @returns {Promise<{success: boolean, message: string, fileName?: string}>} 导入结果
 */
async function selectAndImportFile(taskName, targetFolderPath = '', repoToken, userInfo = {}) {
  try {
    if (!taskName) {
      return { success: false, message: '任务名称不能为空' };
    }

    console.log(`[File Import] 开始文件导入: ${taskName}`);
    await writeLog(`[File Import] 开始文件导入: ${taskName}`);

    // 显示文件选择对话框
    const { canceled, filePaths } = await dialog.showOpenDialog(win, {
      title: '选择要导入的文件',
      properties: ['openFile'],
      filters: [
        { name: '所有文件', extensions: ['*'] },
        { name: '文本文件', extensions: ['txt', 'md', 'json', 'xml', 'csv'] },
        { name: '图片文件', extensions: ['png', 'jpg', 'jpeg', 'gif', 'bmp'] },
        { name: '数据文件', extensions: ['json', 'xml', 'csv', 'yaml', 'yml'] }
      ]
    });

    if (canceled || filePaths.length === 0) {
      console.log('[File Import] 用户取消了文件选择');
      return { success: false, message: '用户取消了文件选择' };
    }

    const selectedFilePath = filePaths[0];
    const fileName = path.basename(selectedFilePath);
    const taskDir = path.join(repoStoragePath, taskName);
    
    // 如果指定了目标文件夹路径，则在该路径下创建文件
    const finalTargetDir = targetFolderPath ? path.join(taskDir, targetFolderPath) : taskDir;

    console.log(`[File Import] 选择的文件: ${selectedFilePath}`);
    console.log(`[File Import] 目标文件名: ${fileName}`);
    console.log(`[File Import] 目标目录: ${finalTargetDir}`);
    await writeLog(`[File Import] 选择的文件: ${selectedFilePath}`);
    await writeLog(`[File Import] 目标文件名: ${fileName}`);
    await writeLog(`[File Import] 目标目录: ${finalTargetDir}`);

    // 确保目标目录存在
    await fs.mkdir(finalTargetDir, { recursive: true });

    // 检查目标文件是否已存在
    const targetFilePath = path.join(finalTargetDir, fileName);
    try {
      await fs.access(targetFilePath);
      // 文件已存在，询问是否覆盖
      const { response } = await dialog.showMessageBox(win, {
        type: 'question',
        buttons: ['覆盖', '取消'],
        defaultId: 1,
        title: '文件已存在',
        message: `文件 "${fileName}" 已存在，是否覆盖？`
      });
      
      if (response === 1) { // 用户选择取消
        return { success: false, message: '用户取消了文件导入' };
      }
    } catch (error) {
      // 文件不存在，可以继续导入
    }

    // 复制文件到项目目录
    await fs.copyFile(selectedFilePath, targetFilePath);
    console.log(`[File Import] ✓ 文件复制完成: ${fileName}`);
    await writeLog(`[File Import] ✓ 文件复制完成: ${fileName}`);

    // 计算相对文件路径用于同步
    const relativeFilePath = targetFolderPath ? path.join(targetFolderPath, fileName) : fileName;

    // 如果有仓库令牌，尝试自动同步到云端
    if (repoToken) {
      console.log('[File Import] 开始自动同步到云端...');
      await writeLog('[File Import] 开始自动同步到云端...');
      
      try {
        const syncResult = await autoSaveAndSync(taskName, relativeFilePath, '', repoToken, userInfo);
        if (syncResult.success) {
          console.log('[File Import] ✓ 文件已同步到云端');
          await writeLog('[File Import] ✓ 文件已同步到云端');
          return {
            success: true,
            message: '文件导入成功并已同步到云端',
            fileName: relativeFilePath
          };
        } else {
          console.warn('[File Import] 文件导入成功，但同步到云端失败:', syncResult.message);
          await writeLog(`[File Import] 文件导入成功，但同步到云端失败: ${syncResult.message}`);
          return {
            success: true,
            message: '文件导入成功，但同步到云端失败',
            fileName: relativeFilePath
          };
        }
      } catch (error) {
        console.warn('[File Import] 文件导入成功，但自动同步失败:', error);
        await writeLog(`[File Import] 文件导入成功，但自动同步失败: ${error.message}`);
        return {
          success: true,
          message: '文件导入成功，但自动同步失败',
          fileName: relativeFilePath
        };
      }
    } else {
      console.log('[File Import] ✓ 文件导入完成（无仓库令牌，跳过同步）');
      await writeLog('[File Import] ✓ 文件导入完成（无仓库令牌，跳过同步）');
      return {
        success: true,
        message: '文件导入成功',
        fileName: relativeFilePath
      };
    }
  } catch (error) {
    console.error('[File Import] 文件导入失败:', error);
    await writeLog(`[File Import] 文件导入失败: ${error.message}`);
    return {
      success: false,
      message: `文件导入失败: ${error.message}`
    };
  }
}

// 注册文件导入功能的IPC处理程序
ipcMain.handle('select-and-import-file', async (event, { taskName, targetFolderPath, repoToken, userInfo }) => {
  return await selectAndImportFile(taskName, targetFolderPath, repoToken, userInfo);
});

/**
 * 删除浏览器设备状态
 * @param {string} deviceId 设备ID
 * @returns {Promise<{success: boolean, message: string}>} 删除结果
 */
async function removeBrowserDeviceState(deviceId) {
  try {
    if (!deviceId) {
      return { success: false, message: '设备ID不能为空' };
    }

    console.log(`[Browser Device State] 删除设备状态: ${deviceId}`);
    
    // 调用BrowserHttpService删除设备状态
    const result = await BrowserHttpService.handleCommand('removeDeviceState', { deviceId });
    
    if (result && result.status === 'success') {
      console.log(`[Browser Device State] ✓ 设备状态删除成功: ${deviceId}`);
      return {
        success: true,
        message: `设备状态删除成功: ${deviceId}`
      };
    } else {
      console.warn(`[Browser Device State] 设备状态删除失败: ${deviceId}`, result);
      return {
        success: false,
        message: result?.message || '设备状态删除失败'
      };
    }
  } catch (error) {
    console.error('[Browser Device State] 删除设备状态时发生错误:', error);
    return {
      success: false,
      message: `删除设备状态失败: ${error.message}`
    };
  }
}

// 注册删除浏览器设备状态的IPC处理程序
ipcMain.handle('remove-browser-device-state', async (event, { deviceId }) => {
  return await removeBrowserDeviceState(deviceId);
});