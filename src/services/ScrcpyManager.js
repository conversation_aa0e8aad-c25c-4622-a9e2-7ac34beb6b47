const { fork, spawn, execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const yaml = require('js-yaml');
const os = require('os');
const { app } = require('electron');

const TAG = '[ScrcpyManager.js]';

class ScrcpyManager {
    constructor() {
        this.scrcpyProcess = null;
        this.flaskProcess = null;
        this.resourcesPath = null;
        this.appPath = null;
        this.isDev = null;
    }

    /**
     * 初始化 ScrcpyManager
     * @param {string} resourcesPath - 资源路径
     * @param {string} appPath - 应用路径
     * @param {boolean} isDev - 是否为开发环境
     */
    init(resourcesPath, appPath, isDev) {
        this.resourcesPath = resourcesPath;
        this.appPath = appPath;
        this.isDev = isDev;
    }

    /**
     * 统一的日志写入函数
     * @param {string} message - 日志信息
     * @param {string} logFileName - 日志文件名
     */
    writeLog(message, logFileName = 'ruyi.log') {
        // try {
        //     let logPath;
        //     if (process.platform === 'win32') {
        //         // Windows 系统使用 Documents 目录
        //         logPath = path.join(os.homedir(), 'Documents', logFileName);
        //     } else {
        //         // 其他系统（macOS/Linux）使用 Desktop 目录
        //         logPath = path.join(os.homedir(), 'Desktop', logFileName);
        //     }
        //     fs.appendFileSync(
        //         logPath,
        //         `[${new Date().toISOString()}] ${message}\n`,
        //         { encoding: 'utf8' }  // 指定文件编码为 utf8
        //     );
        // } catch (error) {
        //     console.error('写入日志失败:', error);
        // }
    }

    /**
     * 启动 ws-scrcpy frontend
     */
    startScrcpy() {
        this.writeLog(`${TAG} 开始启动ws-scrcpy服务`, 'ruyi_scrcpy.log');

        if (this.scrcpyProcess) {
            this.writeLog(`${TAG} ws-scrcpy已在运行中`, 'ruyi_scrcpy.log');
            console.log(`${TAG} ws-scrcpy is already running.`);
            return;
        }

        let wsScrcpyPath;
        if (this.isDev) {
            // wsScrcpyPath = path.join(this.appPath, 'src/phoneInterface/index.js');
            wsScrcpyPath = path.join(this.appPath, 'src/phoneInterface/index.js');
            // wsScrcpyPath = path.join(this.appPath, 'ws-scrcpy/dist/index.js');
        } else {
            // wsScrcpyPath = path.join(this.appPath, 'src/phoneInterface/index.js');
            wsScrcpyPath = path.join(this.appPath, 'src/phoneInterface/index.js');
            if (!fs.existsSync(wsScrcpyPath)) {
                console.error(`${TAG} ws-scrcpy path not found: ${wsScrcpyPath}`);
                return;
            }
        }

        this.writeLog(`${TAG} ws-scrcpy路径: ${wsScrcpyPath}`, 'ruyi_scrcpy.log');

        try {
            if (!fs.existsSync(wsScrcpyPath)) {
                const error = `ws-scrcpy路径不存在: ${wsScrcpyPath}`;
                this.writeLog(`${TAG} 错误: ${error}`, 'ruyi_scrcpy.log');
                throw new Error(error);
            }

            this.scrcpyProcess = fork(wsScrcpyPath);
            this.writeLog(`${TAG} ws-scrcpy进程已启动，PID: ${this.scrcpyProcess.pid}`, 'ruyi_scrcpy.log');

            this.scrcpyProcess.on('message', (message) => {
                this.writeLog(`${TAG} ws-scrcpy消息: ${message}`, 'ruyi_scrcpy.log');
                console.log(`${TAG} ws-scrcpy message: ${message}`);
            });

            this.scrcpyProcess.on('error', (error) => {
                this.writeLog(`${TAG} ws-scrcpy错误: ${error}`, 'ruyi_scrcpy.log');
                console.error(`${TAG} Error starting ws-scrcpy: ${error}`);
            });

            this.scrcpyProcess.on('exit', (code) => {
                this.writeLog(`${TAG} ws-scrcpy进程退出，退出码: ${code}`, 'ruyi_scrcpy.log');
                console.log(`${TAG} ws-scrcpy exited with code ${code}`);
                this.scrcpyProcess = null;
            });

            this.writeLog(`${TAG} ws-scrcpy启动成功`, 'ruyi_scrcpy.log');
            console.log(`${TAG} ws-scrcpy started successfully.`);

        } catch (error) {
            this.writeLog(`${TAG} 启动ws-scrcpy失败: ${error.message}`, 'ruyi_scrcpy.log');
            console.error(`${TAG} Failed to start ws-scrcpy: ${error}`);
        }
    }

    /**
     * 停止 ws-scrcpy frontend
     */
    stopScrcpy() {
        if (!this.scrcpyProcess) {
            console.log(`${TAG} ws-scrcpy is not running.`);
            return;
        }

        this.scrcpyProcess.on('exit', (code) => {
            console.log(`${TAG} ws-scrcpy stopped with exit code ${code}`);
            this.scrcpyProcess = null;
        });

        this.scrcpyProcess.kill();

        console.log(`${TAG} ws-scrcpy stopping.`);
    }

    /**
     * 启动 ws-scrcpy backend - Flask 服务器
     */
    startFlaskServer() {
        this.writeLog(`${TAG} 开始启动Flask服务器`, 'ruyi_flask.log');

        if (this.flaskProcess) {
            this.writeLog(`${TAG} Flask server is already running.`, 'ruyi_flask.log');
            console.log(`${TAG} Flask server is already running.`);
            return { success: true, message: 'Flask server is already running.' };
        }

        let pythonPath;
        let flaskScript;
        // let options = {};

        if (this.isDev) {
            if (process.platform === 'win32') {
                // pythonPath = 'D:\\anaconda3\\envs\\ruyi_ide\\python.exe';
                pythonPath = 'D:\\anaconda3\\envs\\ruyi_ide\\pythonw.exe';
            } else if (process.platform === 'darwin') {
                pythonPath = '/opt/miniconda3/envs/ruyi/bin/python';
            } else {
                // Linux 环境
                pythonPath = '/home/<USER>/miniconda3/envs/ruyi_ide/bin/python';
            }
            flaskScript = path.join(this.appPath, 'src/backend/run.py');
        } else {
            // const pythonExe = process.platform === 'win32' ? 'python.exe' : 'python';
            pythonPath = process.platform === 'win32'
                ? path.join(this.resourcesPath, 'ruyi-ide-backend/env', 'pythonw.exe')
                : path.join(this.resourcesPath, 'ruyi-ide-backend/env/bin', 'python');
            flaskScript = path.join(this.resourcesPath, 'ruyi-ide-backend/run.py');
        }

        this.writeLog(`${TAG} Python路径: ${pythonPath}`, 'ruyi_flask.log');
        console.log(`${TAG} Python路径: ${pythonPath}`)
        this.writeLog(`${TAG} Flask脚本路径: ${flaskScript}`, 'ruyi_flask.log');
        console.log(`${TAG} Flask脚本路径: ${flaskScript}`)

        // options = {
        //     cwd: path.dirname(flaskScript),
        //     shell: true,
        //     env: {
        //         ...process.env,
        //         PYTHONUNBUFFERED: '1',
        //         PATH: `${path.join(this.resourcesPath, 'ruyi-ide-backend/env/bin')}:${process.env.PATH}`,
        //         PYTHONPATH: path.join(this.resourcesPath, 'ruyi-ide-backend/lib/python3.12/site-packages'),
        //         PYTHONHOME: path.join(this.resourcesPath, 'ruyi-ide-backend'),
        //     }
        // };


        try {
            // 检查文件是否存在
            if (!fs.existsSync(pythonPath)) {
                this.writeLog(`${TAG} 错误: Python解释器不存在: ${pythonPath}`, 'ruyi_flask.log');
                console.log(`${TAG} Python解释器不存在: ${pythonPath}`)
                throw new Error(`Python解释器不存在: ${pythonPath}`);
            }
            if (!fs.existsSync(flaskScript)) {
                this.writeLog(`${TAG} 错误: Flask脚本不存在: ${flaskScript}`, 'ruyi_flask.log');
                console.log(`${TAG} Flask脚本不存在: ${flaskScript}`)
                throw new Error(`Flask脚本不存在: ${flaskScript}`);
            }

            // 测试Python解释器
            try {
                const pythonVersion = execSync(`"${pythonPath}" --version`, {
                    encoding: 'utf8',
                    shell: false,
                    stdio: ['pipe', 'pipe', 'pipe']
                });
                this.writeLog(`${TAG} Python版本: ${pythonVersion.trim()}`, 'ruyi_flask.log');
                console.log(`${TAG} Python版本: ${pythonVersion.trim()}`)
            } catch (error) {
                this.writeLog(`${TAG} 错误: Python解释器不可执行: ${error.message}`, 'ruyi_flask.log');
                console.log(`${TAG} Python解释器不可执行: ${error.message}`)
                this.writeLog(`${TAG} 错误详情: ${error.stderr}`, 'ruyi_flask.log');
                console.log(`${TAG} 错误详情: ${error.stderr}`)
                throw new Error(`Python解释器不可执行: ${error.message}`);
            }

            // 添加进程组设置
            const spawnOptions = {
                cwd: path.dirname(flaskScript),
                shell: false,
                env: {
                    ...process.env,
                    PYTHONUNBUFFERED: '1',
                    PYTHONIOENCODING: 'utf-8',
                    PYTHONPATH: this.isDev
                        ? [
                            path.join(this.appPath, 'src/backend/pyarmor_runtime_000000'),
                            process.env.PYTHONPATH
                        ].filter(Boolean).join(path.delimiter)
                        : [
                            path.join(this.resourcesPath, 'ruyi-ide-backend/pyarmor_runtime_000000'),
                            process.env.PYTHONPATH
                        ].filter(Boolean).join(path.delimiter),
                    PATH: this.isDev
                        ? process.env.PATH  // 开发环境直接使用系统 PATH
                        : [
                            process.platform === 'win32'
                                ? path.join(this.resourcesPath, 'ruyi-ide-backend/env')
                                : path.join(this.resourcesPath, 'ruyi-ide-backend/env/bin'),
                            process.env.PATH
                        ].join(path.delimiter),
                },
                // 添加这个选项使子进程在同一个进程组中
                detached: true
            };

            this.flaskProcess = spawn(pythonPath, [flaskScript], spawnOptions);

            // 设置进程组
            if (process.platform !== 'win32') {
                process.kill(-this.flaskProcess.pid, 0);
            }

            if (!this.flaskProcess.pid) {
                this.writeLog(`${TAG} 错误: Flask进程启动失败，没有获取到PID`, 'ruyi_flask.log');
                console.log(`${TAG} Flask进程启动失败，没有获取到PID`)
                throw new Error('Flask进程启动失败，没有获取到PID');
            }
            this.writeLog(`${TAG} Flask进程启动成功，PID: ${this.flaskProcess.pid}`, 'ruyi_flask.log');
            console.log(`${TAG} Flask进程启动成功，PID: ${this.flaskProcess.pid}`)

            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    if (!this.flaskProcess) {
                        this.writeLog(`${TAG} 错误: Flask服务器启动超时`, 'ruyi_flask.log');
                        console.log(`${TAG} Flask服务器启动超时`)
                        reject(new Error(`${TAG} Flask服务器启动超时`));
                    }
                }, 10000);

                this.flaskProcess.stdout.on('data', (data) => {
                    const output = data.toString('utf8');
                    this.writeLog(`${TAG} Flask输出: ${output}`, 'ruyi_flask.log');
                    console.log(`${TAG} Flask输出: ${output}`);
                    if (output.includes('Running on')) {
                        clearTimeout(timeout);
                        this.writeLog(`${TAG} Flask服务器启动成功`, 'ruyi_flask.log');
                        console.log(`${TAG} Flask服务器启动成功`)
                        resolve({ success: true, message: 'Flask服务器启动成功' });
                    }
                });

                this.flaskProcess.stderr.on('data', (data) => {
                    const error = data.toString('utf8');
                    this.writeLog(`${TAG} Flask错误: ${error}`, 'ruyi_flask.log');
                    console.log(`${TAG} Flask错误: ${error}`)
                });

                this.flaskProcess.on('error', (error) => {
                    this.writeLog(`${TAG} Flask启动错误: ${error.message}`, 'ruyi_flask.log');
                    console.log(`${TAG} Flask启动错误: ${error.message}`)
                    clearTimeout(timeout);
                    reject(new Error(`${TAG} Flask启动错误: ${error.message}`));
                });

                this.flaskProcess.on('exit', (code) => {
                    this.writeLog(`${TAG} Flask进程退出，退出码: ${code}`, 'ruyi_flask.log');
                    console.log(`${TAG} Flask进程退出，退出码: ${code}`)
                    clearTimeout(timeout);
                    this.flaskProcess = null;
                    reject(new Error(`${TAG} Flask进程意外退出，退出码: ${code}`));
                });
            });

        } catch (error) {
            this.writeLog(`${TAG} Flask服务器启动失败: ${error.message}`, 'ruyi_flask.log');
            console.log(`${TAG} Flask服务器启动失败: ${error.message}`)
            return { success: false, message: error.message };
        }
    }

    /**
     * 停止 ws-scrcpy backend - Flask 服务器
     */
    stopFlaskServer() {
        if (!this.flaskProcess) {
            console.log(`${TAG} Flask 服务未运行`);
            return;
        }
        
        console.log(`${TAG} 正在停止 Flask 进程 PID: ${this.flaskProcess.pid}`);
        
        try {
            // 在 Windows 上需要递归终止子进程
            if (process.platform === 'win32') {
                try {
                    // 添加超时控制
                    execSync(`taskkill /pid ${this.flaskProcess.pid} /T /F`, {
                        timeout: 3000, // 3秒超时
                        stdio: ['ignore', 'pipe', 'pipe']
                    });
                    console.log(`${TAG} 成功终止 Flask 进程 PID: ${this.flaskProcess.pid}`);
                } catch (error) {
                    console.error(`${TAG} 终止 Flask 进程失败: ${error.message}`);
                    // 如果失败，尝试直接杀死进程
                    try {
                        process.kill(this.flaskProcess.pid);
                        console.log(`${TAG} 使用 process.kill 终止 Flask 进程 PID: ${this.flaskProcess.pid}`);
                    } catch (killError) {
                        console.error(`${TAG} 使用 process.kill 终止 Flask 进程也失败: ${killError.message}`);
                    }
                }
            } else {
                // 在 Unix 系统上，发送 SIGTERM 信号
                try {
                    process.kill(-this.flaskProcess.pid, 'SIGTERM');
                    console.log(`${TAG} 已发送 SIGTERM 信号到 Flask 进程组 PID: ${this.flaskProcess.pid}`);
                } catch (error) {
                    console.error(`${TAG} 终止 Flask 进程失败: ${error.message}`);
                    // 如果失败，尝试 SIGKILL
                    try {
                        process.kill(-this.flaskProcess.pid, 'SIGKILL');
                        console.log(`${TAG} 已发送 SIGKILL 信号到 Flask 进程组 PID: ${this.flaskProcess.pid}`);
                    } catch (killError) {
                        console.error(`${TAG} 使用 SIGKILL 终止 Flask 进程也失败: ${killError.message}`);
                    }
                }
            }
        } catch (error) {
            console.error(`${TAG} 停止 Flask 服务器时发生未知错误: ${error.message}`);
        } finally {
            // 无论成功与否，都重置进程引用
            this.flaskProcess = null;
        }
    }

    /**
     * 更新配置文件中的 WebSocket IP 地址
     * @param {string} wsIp - 新的 WebSocket IP 地址
     * @returns {Object} 包含成功状态和错误信息的对象
     */
    updateWsIp(wsIp = "") {
        if (!wsIp) {
            wsIp = this.getWsIp();
            console.log(`${TAG} 获取到的 wsIp: ${wsIp}`);
        }
        let configPath;
        let configPath1;
        try {
            if (this.isDev) {
            //     configPath = path.join(this.appPath, 'backend/app/RuyiAgent/config.yaml');
            //     configPath1 = path.join(this.appPath, 'backend/config/config.yaml');
                configPath = path.join(this.appPath, 'src/backend/app/RuyiAgent/config.yaml');
                configPath1 = path.join(this.appPath, 'src/backend/config/config.yaml');
            } else {
                // configPath = path.join(this.resourcesPath, 'ruyi-ide-backend/app/RuyiAgent/config.yaml');
                // configPath1 = path.join(this.resourcesPath, 'ruyi-ide-backend/config/config.yaml');
                configPath = path.join(this.appPath, 'src/backend/app/RuyiAgent/config.yaml');
                configPath1 = path.join(this.appPath, 'src/backend/config/config.yaml');
                if (!fs.existsSync(configPath) && !fs.existsSync(configPath1)) {
                    console.error(`${TAG} Ruyi Agent config path not found: ${configPath}, ${configPath1}`);
                    return;
                }
            }
            this.writeWsIpToYaml(wsIp, configPath);
            this.writeWsIpToYaml(wsIp, configPath1);
            return { success: true };
        } catch (error) {
            console.error(`${TAG} 更新配置文件失败: ${error}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 调用 adb 获取手机的 ip 地址
     * @returns {string} 手机的 ip 地址
     */
    getWsIp() {
        // 使用 adb 命令获取手机的 ip 地址
        console.log(`${TAG} 开始获取手机的 ip 地址`);
        const adbCommand = 'adb shell ip route get *******';
        console.log(`${TAG} adb 命令: ${adbCommand}`);
        const result = execSync(adbCommand, { encoding: 'utf8' });
        console.log(`${TAG} adb 结果: ${result.trim()}`);
        // 解析结果以获取正确的 IP 地址
        const ipAddressMatch = result.match(/src (\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/)?.[1];
        const ipAddress = ipAddressMatch ? ipAddressMatch + ":6666" : null;
        console.log(`${TAG} 解析的 IP 地址: ${ipAddress}`);

        return ipAddress;
    }

    /**
     * 将 wsIp 写入到 yaml 文件中
     * @param {string} wsIp - 新的 WebSocket IP 地址
     * @param {string} configPath - 配置文件的路径
     */
    writeWsIpToYaml(wsIp, configPath) {
        // 读取 yaml 文件
        const config = yaml.load(fs.readFileSync(configPath, 'utf8'));

        // 更新配置中的WebSocket IP
        config.device_url = "ws://" + wsIp;

        // 将更新后的配置写回文件，保持原有的引号格式
        const dumpOptions = {
            quotingType: '"',      // 使用双引号
            forceQuotes: true,     // 强制为字符串添加引号
            lineWidth: -1,         // 防止长字符串被截断
            noRefs: true          // 避免使用引用标记
        };
        fs.writeFileSync(configPath, yaml.dump(config, dumpOptions), 'utf8');
    }

    getFlaskDetails() {
        let pythonPath, flaskScript;
        if (this.isDev) {
            if (process.platform === 'win32') {
                // pythonPath = 'D:\\anaconda3\\envs\\ruyi_ide\\python.exe';
                pythonPath = 'D:\\anaconda3\\envs\\ruyi_ide\\pythonw.exe';
            } else if (process.platform === 'darwin') {
                pythonPath = '/opt/miniconda3/envs/ruyi/bin/python';
            } else {
                // Linux 环境
                pythonPath = '/home/<USER>/miniconda3/envs/ruyi_ide/bin/python';
            }
            flaskScript = path.join(this.appPath, 'src/backend/run.py');
        } else {
            pythonPath = process.platform === 'win32'
                ? path.join(this.resourcesPath, 'ruyi-ide-backend/env', 'pythonw.exe')
                : path.join(this.resourcesPath, 'ruyi-ide-backend/env/bin', 'python');
            flaskScript = path.join(this.resourcesPath, 'ruyi-ide-backend/run.py');
        }
        return { pythonPath, flaskScript };
    }

    isFlaskRunning() {
        if (!this.flaskProcess || !this.flaskProcess.pid) {
            return false;
        }

        try {
            // 发送空信号来检查进程是否存在
            process.kill(this.flaskProcess.pid, 0);
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 检查 Flask 服务器状态并在需要时启动
     */
    async checkAndStartFlaskServer() {
        try {
            console.log(`${TAG} checkAndStartFlaskServer`)
            // 先检查服务器是否在运行
            const isRunning = await this.checkFlaskHealth();
            console.log(`${TAG} isRunning: ${isRunning}`)
            if (!isRunning) {
                console.log(`${TAG} Flask server is not running, starting...`)
                const result = await this.startFlaskServer();
                return result;
            }
            return { success: true, message: 'Flask server is already running.' };
        } catch (error) {
            console.error(`${TAG} Error checking/starting Flask server: ${error}`);
            return { success: false, message: error.message };
        }
    }

    /**
     * 检查 Flask 服务器健康状态
     */
    async checkFlaskHealth() {
        try {
            const { flask_port } = this.getPortConfig();
            const response = await fetch(`http://127.0.0.1:${flask_port}/health`);
            return response.ok;
        } catch (error) {
            console.error(`${TAG} Flask health check failed: ${error}`);
            return false;
        }
    }

    /**
     * 合并配置文件，确保所有必需的键都存在
     * @param {Object} currentConfig 当前配置
     * @param {Object} templateConfig 模板配置
     * @returns {Object} 合并后的配置
     */
    mergeConfigWithTemplate(currentConfig, templateConfig) {
        const mergedConfig = { ...templateConfig };
        
        // 递归合并配置
        const deepMerge = (target, source) => {
            for (const key in source) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    target[key] = target[key] || {};
                    deepMerge(target[key], source[key]);
                } else {
                    target[key] = source[key];
                }
            }
        };
        
        deepMerge(mergedConfig, currentConfig);
        return mergedConfig;
    }

    /**
     * 获取 Electron app 配置文件路径
     * @returns {string} 配置文件路径
     */
    getConfigPath() {
        if (this.isDev) {
            this.writeLog(`${TAG} this.isDev: ${this.isDev}`, 'ruyi_config.log');
            console.log(`${TAG} getConfigPath this.isDev: ${this.isDev}`)
            this.writeLog(`${TAG} this.appPath: ${this.appPath}`, 'ruyi_config.log');
            console.log(`${TAG} getConfigPath this.appPath: ${this.appPath}`)   
            console.log(`${TAG} getConfigPath config.yaml: ${path.join(this.appPath, 'src/config.yaml')}`)
            console.log(`${TAG} getConfigPath RETURNED`)
            return path.join(this.appPath, 'src/config.yaml');
        } else {
            console.log(`${TAG} getConfigPath this.isDev: ${this.isDev}`)
            // 生产环境中，将配置文件复制到用户数据目录（userData），保证有写入权限
            const userDataPath = app.getPath('userData');
            const configPath = path.join(userDataPath, 'config.yaml');
            this.writeLog(`${TAG} this.isDev: ${this.isDev}`, 'ruyi_config.log');
            this.writeLog(`${TAG} userDataPath: ${userDataPath}`, 'ruyi_config.log');
            this.writeLog(`${TAG} configPath: ${configPath}`, 'ruyi_config.log');
            
            // 确保配置文件正确初始化和迁移
            this.ensureConfigMigration(configPath);
            
            return configPath;
        }
    }

    /**
     * 确保配置文件正确迁移，包含所有必需的键
     * @param {string} configPath 配置文件路径
     */
    ensureConfigMigration(configPath) {
        try {
            // 获取模板配置文件路径
            const templateConfigPath = path.join(this.appPath, 'src/config.template.yaml');
            
            // 读取模板配置
            let templateConfig = {};
            if (fs.existsSync(templateConfigPath)) {
                const templateContent = fs.readFileSync(templateConfigPath, 'utf8');
                templateConfig = yaml.load(templateContent) || {};
            } else {
                console.warn(`${TAG} 模板配置文件不存在: ${templateConfigPath}`);
                // 使用内置默认配置作为后备
                templateConfig = {
                    flask_port: 11825,
                    scrcpy_port: 31825,
                    device_port: 51825,
                    electron_http_port: 18542,
                    language: "default",
                    app_language: "zh",
                    device_counts: {
                        browserDeviceCount: 1,
                        cloudPhoneCount: 0
                    },
                    browser_homepage: "https://www.baidu.com",
                    default_search_engine: "baidu",
                    ruyillm_key: "",
                    use_annotation: false,
                    device_serial_id: "",
                    device_mappings: {},
                    data_dir_path: ""
                };
            }

            let currentConfig = {};
            let needsMigration = false;

            // 如果配置文件存在，读取并检查是否需要迁移
            if (fs.existsSync(configPath)) {
                try {
                    const currentContent = fs.readFileSync(configPath, 'utf8');
                    currentConfig = yaml.load(currentContent) || {};
                    
                    // 检查是否缺少必需的键
                    const missingKeys = this.findMissingKeys(currentConfig, templateConfig);
                    if (missingKeys.length > 0) {
                        needsMigration = true;
                        console.log(`${TAG} 检测到缺失的配置键，需要迁移: ${missingKeys.join(', ')}`);
                        this.writeLog(`${TAG} 检测到缺失的配置键，需要迁移: ${missingKeys.join(', ')}`, 'ruyi_config.log');
                    }
                } catch (error) {
                    console.error(`${TAG} 读取现有配置文件失败，将使用默认配置: ${error.message}`);
                    needsMigration = true;
                }
            } else {
                // 配置文件不存在，使用模板配置
                needsMigration = true;
                console.log(`${TAG} 配置文件不存在，将创建新的配置文件`);
                this.writeLog(`${TAG} 配置文件不存在，将创建新的配置文件`, 'ruyi_config.log');
            }

            // 如果需要迁移，合并配置并保存
            if (needsMigration) {
                const migratedConfig = this.mergeConfigWithTemplate(currentConfig, templateConfig);
                
                // 定义 yaml.dump 的选项
                const dumpOptions = {
                    quotingType: '"',
                    forceQuotes: false,
                    lineWidth: -1,
                    noRefs: true
                };

                fs.writeFileSync(configPath, yaml.dump(migratedConfig, dumpOptions), 'utf8');
                console.log(`${TAG} 配置文件迁移完成: ${configPath}`);
                this.writeLog(`${TAG} 配置文件迁移完成: ${configPath}`, 'ruyi_config.log');
            }
        } catch (error) {
            console.error(`${TAG} 配置文件迁移失败: ${error.message}`);
            this.writeLog(`${TAG} 配置文件迁移失败: ${error.message}`, 'ruyi_config.log');
            throw error;
        }
    }

    /**
     * 递归查找缺失的配置键
     * @param {Object} current 当前配置
     * @param {Object} template 模板配置
     * @param {string} prefix 键前缀（用于嵌套对象）
     * @returns {Array} 缺失的键列表
     */
    findMissingKeys(current, template, prefix = '') {
        const missingKeys = [];
        
        for (const key in template) {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            
            if (!(key in current)) {
                missingKeys.push(fullKey);
            } else if (template[key] && typeof template[key] === 'object' && !Array.isArray(template[key])) {
                // 递归检查嵌套对象
                if (current[key] && typeof current[key] === 'object' && !Array.isArray(current[key])) {
                    missingKeys.push(...this.findMissingKeys(current[key], template[key], fullKey));
                } else {
                    missingKeys.push(fullKey);
                }
            }
        }
        
        return missingKeys;
    }

    /**
     * 验证当前配置文件的完整性
     * @returns {Object} 验证结果 { isValid: boolean, missingKeys: string[], errors: string[] }
     */
    validateConfiguration() {
        const result = {
            isValid: true,
            missingKeys: [],
            errors: []
        };

        try {
            // 验证主配置文件
            const configPath = this.getConfigPath();
            if (fs.existsSync(configPath)) {
                const templateConfigPath = path.join(this.appPath, 'src/config.template.yaml');
                if (fs.existsSync(templateConfigPath)) {
                    const currentConfig = yaml.load(fs.readFileSync(configPath, 'utf8')) || {};
                    const templateConfig = yaml.load(fs.readFileSync(templateConfigPath, 'utf8')) || {};
                    
                    const missingKeys = this.findMissingKeys(currentConfig, templateConfig);
                    if (missingKeys.length > 0) {
                        result.isValid = false;
                        result.missingKeys.push(...missingKeys.map(key => `config.yaml: ${key}`));
                    }
                }
            } else {
                result.isValid = false;
                result.errors.push('主配置文件不存在');
            }

            // 验证 Flask 配置文件
            const flaskConfigPath = this.getFlaskConfigPath();
            if (fs.existsSync(flaskConfigPath)) {
                const templateConfigPath = path.join(this.appPath, 'src/backend/config/config.template.yaml');
                if (fs.existsSync(templateConfigPath)) {
                    const currentConfig = yaml.load(fs.readFileSync(flaskConfigPath, 'utf8')) || {};
                    const templateConfig = yaml.load(fs.readFileSync(templateConfigPath, 'utf8')) || {};
                    
                    const missingKeys = this.findMissingKeys(currentConfig, templateConfig);
                    if (missingKeys.length > 0) {
                        result.isValid = false;
                        result.missingKeys.push(...missingKeys.map(key => `backend/config.yaml: ${key}`));
                    }
                }
            } else {
                result.errors.push('Flask 配置文件不存在');
            }

        } catch (error) {
            result.isValid = false;
            result.errors.push(`配置验证失败: ${error.message}`);
        }

        return result;
    }

    /**
     * 获取配置迁移状态信息
     * @returns {Object} 迁移状态信息
     */
    getConfigMigrationStatus() {
        const status = {
            mainConfig: { exists: false, needsMigration: false, missingKeys: [] },
            flaskConfig: { exists: false, needsMigration: false, missingKeys: [] },
            templateFiles: { main: false, flask: false }
        };

        try {
            // 检查模板文件
            const mainTemplatePath = path.join(this.appPath, 'src/config.template.yaml');
            const flaskTemplatePath = path.join(this.appPath, 'src/backend/config/config.template.yaml');
            status.templateFiles.main = fs.existsSync(mainTemplatePath);
            status.templateFiles.flask = fs.existsSync(flaskTemplatePath);

            // 检查主配置文件
            const configPath = this.getConfigPath();
            status.mainConfig.exists = fs.existsSync(configPath);
            if (status.mainConfig.exists && status.templateFiles.main) {
                const currentConfig = yaml.load(fs.readFileSync(configPath, 'utf8')) || {};
                const templateConfig = yaml.load(fs.readFileSync(mainTemplatePath, 'utf8')) || {};
                const missingKeys = this.findMissingKeys(currentConfig, templateConfig);
                status.mainConfig.missingKeys = missingKeys;
                status.mainConfig.needsMigration = missingKeys.length > 0;
            }

            // 检查 Flask 配置文件
            const flaskConfigPath = this.getFlaskConfigPath();
            status.flaskConfig.exists = fs.existsSync(flaskConfigPath);
            if (status.flaskConfig.exists && status.templateFiles.flask) {
                const currentConfig = yaml.load(fs.readFileSync(flaskConfigPath, 'utf8')) || {};
                const templateConfig = yaml.load(fs.readFileSync(flaskTemplatePath, 'utf8')) || {};
                const missingKeys = this.findMissingKeys(currentConfig, templateConfig);
                status.flaskConfig.missingKeys = missingKeys;
                status.flaskConfig.needsMigration = missingKeys.length > 0;
            }

        } catch (error) {
            console.error(`${TAG} 获取配置迁移状态失败: ${error.message}`);
        }

        return status;
    }

    /**
     * 获取默认搜索引擎配置
     * @returns {string} 默认搜索引擎标识符
     */
    getDefaultSearchEngineConfig() {
        const configPath = this.getConfigPath();
        try {
            if (fs.existsSync(configPath)) {
                const yamlContent = fs.readFileSync(configPath, 'utf8');
                const config = yaml.load(yamlContent) || {};
                const searchEngine = config.default_search_engine || 'baidu';
                this.writeLog(`${TAG} getDefaultSearchEngineConfig: ${searchEngine}`, 'ruyi_config.log');
                console.log(`${TAG} getDefaultSearchEngineConfig: ${searchEngine}`);
                return searchEngine;
            }
            return 'baidu';
        } catch (error) {
            console.error(`${TAG} Failed to read default search engine config:`, error);
            this.writeLog(`${TAG} Failed to read default search engine config: ${error.message}`, 'ruyi_config.log');
            return 'baidu';
        }
    }

    /**
     * 设置默认搜索引擎配置
     * @param {string} searchEngine 搜索引擎标识符 (google, baidu, bing, sougou)
     */
    setDefaultSearchEngineConfig(searchEngine) {
        console.log(`${TAG} Setting default search engine to:`, searchEngine);
        this.writeLog(`${TAG} Setting default search engine to: ${searchEngine}`, 'ruyi_config.log');
        
        // 验证搜索引擎参数
        const validEngines = ['google', 'baidu', 'bing', 'sougou'];
        if (!validEngines.includes(searchEngine)) {
            const error = new Error(`Invalid search engine: ${searchEngine}. Valid options: ${validEngines.join(', ')}`);
            console.error(`${TAG} ${error.message}`);
            this.writeLog(`${TAG} ${error.message}`, 'ruyi_config.log');
            throw error;
        }
        
        // 定义 yaml.dump 的选项
        const dumpOptions = {
            quotingType: '"',      // 使用双引号
            forceQuotes: true,     // 强制为字符串添加引号
            lineWidth: -1,         // 防止长字符串被截断
            noRefs: true           // 避免使用引用标记
        };

        // 更新 Electron 配置
        const configPath = this.getConfigPath();
        try {
            const electronConfig = yaml.load(fs.readFileSync(configPath, 'utf8'));
            electronConfig.default_search_engine = searchEngine;
            fs.writeFileSync(configPath, yaml.dump(electronConfig, dumpOptions), 'utf8');
            console.log(`${TAG} Default search engine saved successfully to Electron config: ${configPath}`);
            this.writeLog(`${TAG} Default search engine saved successfully to Electron config: ${configPath}`, 'ruyi_config.log');
        } catch (error) {
            console.error(`${TAG} Failed to save default search engine to Electron config:`, error);
            this.writeLog(`${TAG} Failed to save default search engine to Electron config: ${error.message}`, 'ruyi_config.log');
            throw error;
        }

        // 更新 Flask 配置
        const flaskConfigPath = this.getFlaskConfigPath();
        try {
            const flaskConfig = yaml.load(fs.readFileSync(flaskConfigPath, 'utf8'));
            flaskConfig.default_search_engine = searchEngine;
            fs.writeFileSync(flaskConfigPath, yaml.dump(flaskConfig, dumpOptions), 'utf8');
            console.log(`${TAG} Default search engine saved successfully to Flask config: ${flaskConfigPath}`);
            this.writeLog(`${TAG} Default search engine saved successfully to Flask config: ${flaskConfigPath}`, 'ruyi_config.log');
        } catch (error) {
            console.error(`${TAG} Failed to save default search engine to Flask config:`, error);
            this.writeLog(`${TAG} Failed to save default search engine to Flask config: ${error.message}`, 'ruyi_config.log');
            throw error;
        }
    }

    /**
     * 获取 Flask 配置文件路径
     * @returns {string} 配置文件路径
     */
    getFlaskConfigPath() {
        if (this.isDev) {
            return path.join(this.appPath, 'src/backend/config/config.yaml');
        } else {
            const configPath = path.join(this.resourcesPath, 'ruyi-ide-backend/config/config.yaml');
            // 确保 Flask 配置文件也经过迁移
            this.ensureFlaskConfigMigration(configPath);
            return configPath;
        }
    }

    /**
     * 确保 Flask 配置文件正确迁移，包含所有必需的键
     * @param {string} configPath Flask 配置文件路径
     */
    ensureFlaskConfigMigration(configPath) {
        try {
            // 获取模板配置文件路径
            const templateConfigPath = path.join(this.appPath, 'src/backend/config/config.template.yaml');
            
            // 读取模板配置
            let templateConfig = {};
            if (fs.existsSync(templateConfigPath)) {
                const templateContent = fs.readFileSync(templateConfigPath, 'utf8');
                templateConfig = yaml.load(templateContent) || {};
            } else {
                console.warn(`${TAG} Flask 模板配置文件不存在: ${templateConfigPath}`);
                // 使用内置默认配置作为后备
                templateConfig = {
                    flask_port: 11825,
                    device_port: 51825,
                    electron_http_port: 18542,
                    device_url: "ws://192.168.20.206:6666",
                    fm_api_url: "http://bit-swimming.cn/v1",
                    ruyi_client_version: "0.2.0.3",
                    browser_homepage: "https://www.baidu.com",
                    default_search_engine: "baidu",
                    fm_api_key: ""
                };
            }

            let currentConfig = {};
            let needsMigration = false;

            // 如果配置文件存在，读取并检查是否需要迁移
            if (fs.existsSync(configPath)) {
                try {
                    const currentContent = fs.readFileSync(configPath, 'utf8');
                    currentConfig = yaml.load(currentContent) || {};
                    
                    // 检查是否缺少必需的键
                    const missingKeys = this.findMissingKeys(currentConfig, templateConfig);
                    if (missingKeys.length > 0) {
                        needsMigration = true;
                        console.log(`${TAG} 检测到 Flask 配置缺失的键，需要迁移: ${missingKeys.join(', ')}`);
                        this.writeLog(`${TAG} 检测到 Flask 配置缺失的键，需要迁移: ${missingKeys.join(', ')}`, 'ruyi_config.log');
                    }
                } catch (error) {
                    console.error(`${TAG} 读取现有 Flask 配置文件失败，将使用默认配置: ${error.message}`);
                    needsMigration = true;
                }
            } else {
                // 配置文件不存在，使用模板配置
                needsMigration = true;
                console.log(`${TAG} Flask 配置文件不存在，将创建新的配置文件`);
                this.writeLog(`${TAG} Flask 配置文件不存在，将创建新的配置文件`, 'ruyi_config.log');
            }

            // 如果需要迁移，合并配置并保存
            if (needsMigration) {
                const migratedConfig = this.mergeConfigWithTemplate(currentConfig, templateConfig);
                
                // 定义 yaml.dump 的选项
                const dumpOptions = {
                    quotingType: '"',
                    forceQuotes: false,
                    lineWidth: -1,
                    noRefs: true
                };

                // 确保目录存在
                const configDir = path.dirname(configPath);
                if (!fs.existsSync(configDir)) {
                    fs.mkdirSync(configDir, { recursive: true });
                }

                fs.writeFileSync(configPath, yaml.dump(migratedConfig, dumpOptions), 'utf8');
                console.log(`${TAG} Flask 配置文件迁移完成: ${configPath}`);
                this.writeLog(`${TAG} Flask 配置文件迁移完成: ${configPath}`, 'ruyi_config.log');
            }
        } catch (error) {
            console.error(`${TAG} Flask 配置文件迁移失败: ${error.message}`);
            this.writeLog(`${TAG} Flask 配置文件迁移失败: ${error.message}`, 'ruyi_config.log');
            // 对于 Flask 配置，不抛出错误，而是警告
            console.warn(`${TAG} 将使用默认 Flask 配置继续运行`);
        }
    }

    /**
     * 获取 Ruyi Agent 配置文件路径
     * @returns {string} 配置文件路径
     */
    getRuyiAgentConfigPath() {
        if (this.isDev) {
            return path.join(this.appPath, 'src/backend/app/RuyiAgent/config.yaml');
        } else {
            return path.join(this.resourcesPath, 'ruyi-ide-backend/app/RuyiAgent/config.yaml');
        }
    }

    /**
     * 获取 Electron app 配置文件中的端口配置
     * @returns {Object} 包含各种端口的对象
     */
    getPortConfig() {
        const configPath = this.getConfigPath();
        const config = yaml.load(fs.readFileSync(configPath, 'utf8'));
        return {
            flask_port: config.flask_port,
            scrcpy_port: config.scrcpy_port,
            device_port: config.device_port,
            electron_http_port: config.electron_http_port
        };
    }

    /**
     * 设置 Electron app 和 flask app 中的配置文件 config.yaml 中的端口信息
     * @param {Object} config - 包含各种端口的对象
     */
    setPortConfig(config) {
        // 定义 yaml.dump 的选项
        const dumpOptions = {
            quotingType: '"',      // 使用双引号
            forceQuotes: true,     // 强制为字符串添加引号
            lineWidth: -1,         // 防止长字符串被截断
            noRefs: true           // 避免使用引用标记
        };

        // 更新 Electron 配置
        const configPath = this.getConfigPath();
        try {
            const electronConfig = yaml.load(fs.readFileSync(configPath, 'utf8'));
            electronConfig.flask_port = config.flask_port;
            electronConfig.scrcpy_port = config.scrcpy_port;
            electronConfig.device_port = config.device_port;
            electronConfig.electron_http_port = config.electron_http_port;
            fs.writeFileSync(configPath, yaml.dump(electronConfig, dumpOptions), 'utf8');
        } catch (error) {
            console.error(`${TAG} 更新 Electron 配置失败: ${error}`);
            throw error;
        }

        // 更新 Flask 配置
        const flaskConfigPath = this.getFlaskConfigPath();
        try {
            const flaskConfig = yaml.load(fs.readFileSync(flaskConfigPath, 'utf8'));
            flaskConfig.flask_port = config.flask_port;
            flaskConfig.device_port = config.device_port;
            flaskConfig.electron_http_port = config.electron_http_port;
            fs.writeFileSync(flaskConfigPath, yaml.dump(flaskConfig, dumpOptions), 'utf8');
        } catch (error) {
            console.error(`${TAG} 更新 Flask 配置失败: ${error}`);
            throw error;
        }

        // 更新 Ruyi Agent 中的 flask port 配置
        const ruyiAgentConfigPath = this.getRuyiAgentConfigPath();
        try {
            const ruyiAgentConfig = yaml.load(fs.readFileSync(ruyiAgentConfigPath, 'utf8'));
            ruyiAgentConfig.flask_port = config.flask_port;
            fs.writeFileSync(ruyiAgentConfigPath, yaml.dump(ruyiAgentConfig, dumpOptions), 'utf8');
        } catch (error) {
            console.error(`${TAG} 更新 Ruyi Agent flask port 失败: ${error}`);
            throw error;
        }
    }

    /**
     * 清理所有配置的端口
     */
    async cleanupConfiguredPorts() {
        try {
            console.log(`${TAG} cleanupConfiguredPorts START`)
            // 读取配置文件
            const configPath = this.getConfigPath();
            console.log(`${TAG} cleanupConfiguredPorts configPath: ${configPath}`)
            this.writeLog(`${TAG} cleanupConfiguredPorts configPath:${configPath}`, 'ruyi_config.log');
            const config = yaml.load(fs.readFileSync(configPath, 'utf8'));
            console.log(`${TAG} cleanupConfiguredPorts config: ${config}`)
            this.writeLog(`${TAG} cleanupConfiguredPorts config:${config}`, 'ruyi_config.log');

            // 获取所有需要清理的端口
            const portsToClean = [
                config.flask_port,
                config.scrcpy_port,
                config.device_port,
                config.electron_http_port
            ].filter(port => port); // 过滤掉未定义的端口

            console.log(`${TAG} 正在清理端口: ${portsToClean}`)
            this.writeLog(`${TAG} 正在清理端口:${portsToClean}`, 'ruyi_config.log');

            // 清理每个端口
            for (const port of portsToClean) {
                try {
                    if (process.platform === 'win32') {
                        // 改进的 Windows 端口清理方法
                        // 1. 首先获取端口对应的 PID
                        const findPidCmd = `netstat -ano | findstr :${port} | findstr LISTENING`;
                        const pidOutput = execSync(findPidCmd, {
                            encoding: 'utf8',
                            timeout: 3000, // 3秒超时
                            stdio: ['ignore', 'pipe', 'pipe']
                        }).toString();
                        
                        // 提取 PID
                        const pidMatch = pidOutput.match(/\s+(\d+)\s*$/m);
                        if (pidMatch && pidMatch[1]) {
                            const pid = pidMatch[1].trim();
                            console.log(`找到端口 ${port} 对应的进程 PID: ${pid}`);
                            
                            // 使用 taskkill 终止进程
                            execSync(`taskkill /F /PID ${pid}`, {
                                encoding: 'utf8',
                                timeout: 5000, // 5秒超时
                                stdio: ['ignore', 'pipe', 'pipe']
                            });
                            console.log(`已终止端口 ${port} 对应的进程 PID: ${pid}`);
                        } else {
                            console.log(`端口 ${port} 未被占用`);
                        }
                    } else {
                        // macOS 和 Linux
                        execSync(`lsof -i :${port} | grep LISTEN | awk '{print $2}' | xargs -r kill -9`, { stdio: 'ignore' });
                    }
                    console.log(`${TAG} 端口 ${port} 已清理`);
                    this.writeLog(`${TAG} 端口 ${port} 已清理`, 'ruyi_config.log');
                } catch (error) {
                    // 如果端口没有被占用，执行 kill 命令会抛出错误，这是正常的
                    console.log(`${TAG} 端口 ${port} 未被占用或清理失败: ${error.message}`);
                    this.writeLog(`${TAG} 端口 ${port} 未被占用或清理失败: ${error.message}`, 'ruyi_config.log');
                }
            }
        } catch (error) {
            console.error(`${TAG} 清理端口时发生错误: ${error}`);
            this.writeLog(`${TAG} 清理端口时发生错误:${error.message}`, 'ruyi_config.log');
        }
    }

    /**
     * 设置 flask app 和 Ruyi Agent 中的配置文件 config.yaml 中的 API KEY 的值
     * @param {string} apiKey - 配置文件中的 key
     */
    setApiKeyConfig(apiKey) {
        // 定义 yaml.dump 的选项
        const dumpOptions = {
            quotingType: '"',      // 使用双引号
            forceQuotes: true,     // 强制为字符串添加引号
            lineWidth: -1,         // 防止长字符串被截断
            noRefs: true          // 避免使用引用标记
        };

        // 更新 Flask 配置
        const flaskConfigPath = this.getFlaskConfigPath();
        try {
            const flaskConfig = yaml.load(fs.readFileSync(flaskConfigPath, 'utf8'));
            flaskConfig.fm_api_key = apiKey;
            fs.writeFileSync(flaskConfigPath, yaml.dump(flaskConfig, dumpOptions), 'utf8');
        } catch (error) {
            console.error(`${TAG} 更新 Flask 配置失败: ${error}`);
            throw error;
        }

        // 更新 Ruyi Agent 配置
        const ruyiAgentConfigPath = this.getRuyiAgentConfigPath();
        try {
            const ruyiAgentConfig = yaml.load(fs.readFileSync(ruyiAgentConfigPath, 'utf8'));
            // ruyiAgentConfig.fm_api_key = apiKey;
            // ruyiAgentConfig.molmo_apiKey = apiKey;
            ruyiAgentConfig.ruyillm_key = apiKey;
            fs.writeFileSync(ruyiAgentConfigPath, yaml.dump(ruyiAgentConfig, dumpOptions), 'utf8');
        } catch (error) {
            console.error(`${TAG} 更新 Ruyi Agent 配置失败: ${error}`);
            throw error;
        }

        // 更新 Electron app 配置
        const electronConfigPath = this.getConfigPath();
        try {
            const electronConfig = yaml.load(fs.readFileSync(electronConfigPath, 'utf8'));
            electronConfig.ruyillm_key = "sk-" + apiKey;
            fs.writeFileSync(electronConfigPath, yaml.dump(electronConfig, dumpOptions), 'utf8');
        } catch (error) {
            console.error(`${TAG} 更新 Electron 配置失败: ${error}`);
            throw error;
        }

    }

    /**
     * 获取 Electron app 的配置文件 config.yaml 中的 language 的值
     * @returns {string} 配置文件中的 language
     */
    getLanguageConfig() {
        const configPath = this.getConfigPath();
        const config = yaml.load(fs.readFileSync(configPath, 'utf8'));
        this.writeLog(`${TAG} getLanguageConfig config.language:${config.language}`, 'ruyi_config.log');
        console.log(`${TAG} getLanguageConfig config.language: ${config.language}`)
        return config.language;
    }

    /**
     * 设置 Electron app 的配置文件 config.yaml 中的 language 的值
     * @param {string} language - 配置文件中的 language
     */
    setLanguageConfig(language) {
        const configPath = this.getConfigPath();
        const config = yaml.load(fs.readFileSync(configPath, 'utf8'));
        config.language = language;
        fs.writeFileSync(configPath, yaml.dump(config), 'utf8');
        this.writeLog(`${TAG} setLanguageConfig config.language:${config.language}`, 'ruyi_config.log');
        console.log(`${TAG} setLanguageConfig config.language: ${config.language}`)
    }

    /**
     * 获取 Electron app 的配置文件 config.yaml 中的 app_language 的值
     * @returns {string} 配置文件中的 app_language
     */
    getAppLanguageConfig() {
        const configPath = this.getConfigPath();
        const config = yaml.load(fs.readFileSync(configPath, 'utf8'));
        this.writeLog(`${TAG} getAppLanguageConfig config.app_language:${config.app_language}`, 'ruyi_config.log');
        console.log(`${TAG} getAppLanguageConfig config.app_language: ${config.app_language}`)
        return config.app_language;
    }

    /**
     * 设置 Electron app 的配置文件 config.yaml 中的 app_language 的值
     * @param {string} appLanguage - 配置文件中的 app_language
     */
    setAppLanguageConfig(appLanguage) {
        const configPath = this.getConfigPath();
        const config = yaml.load(fs.readFileSync(configPath, 'utf8'));
        config.app_language = appLanguage;
        fs.writeFileSync(configPath, yaml.dump(config), 'utf8');
        this.writeLog(`${TAG} setAppLanguageConfig config.app_language:${config.app_language}`, 'ruyi_config.log');
        console.log(`${TAG} setAppLanguageConfig config.app_language: ${config.app_language}`)
    }

    /**
     * 获取 Electron app 的配置文件 config.yaml 中的 ruyillm_key 的值
     * @returns {string} 配置文件中的 ruyillm_key
     */
    getRuyiLLMKeyConfig() {
        const configPath = this.getConfigPath();
        const config = yaml.load(fs.readFileSync(configPath, 'utf8'));
        this.writeLog(`${TAG} getRuyiLLMKeyConfig config.ruyillm_key:${config.ruyillm_key}`, 'ruyi_config.log');
        console.log(`${TAG} getRuyiLLMKeyConfig config.ruyillm_key: ${config.ruyillm_key}`)
        return config.ruyillm_key;
    }

    /**
     * 获取 Ruyi Agent 配置文件中的 use_annotation 的值
     * @returns {boolean} 配置文件中的 use_annotation
     */
    getUseAnnotationConfig() {
        const configPath = this.getRuyiAgentConfigPath();
        const config = yaml.load(fs.readFileSync(configPath, 'utf8'));
        this.writeLog(`${TAG} getUseAnnotationConfig config.use_annotation:${config.use_annotation}`, 'ruyi_config.log');
        console.log(`${TAG} getUseAnnotationConfig config.use_annotation: ${config.use_annotation}`)
        return config.use_annotation;
    }

    /**
     * 设置 Ruyi Agent 配置文件中的 use_annotation 的值
     * @param {boolean} useAnnotation - 配置文件中的 use_annotation
     */
    setUseAnnotationConfig(useAnnotation) {
        const configPath = this.getRuyiAgentConfigPath();
        const config = yaml.load(fs.readFileSync(configPath, 'utf8'));
        config.use_annotation = useAnnotation;
        fs.writeFileSync(configPath, yaml.dump(config), 'utf8');
        this.writeLog(`${TAG} setUseAnnotationConfig config.use_annotation:${config.use_annotation}`, 'ruyi_config.log');
        console.log(`${TAG} setUseAnnotationConfig config.use_annotation: ${config.use_annotation}`)
    }

    /**
     * 设置 Ruyi Agent 配置文件中的 annotation_path 的值
     * @param {string} annotationPath - 标注文件存储路径
     */
    setAnnotationPathConfig(annotationPath) {
        const ruyiAgentConfigPath = this.getRuyiAgentConfigPath();
        try {
            const ruyiAgentConfig = yaml.load(fs.readFileSync(ruyiAgentConfigPath, 'utf8'));
            ruyiAgentConfig.annotation_path = annotationPath;
            fs.writeFileSync(ruyiAgentConfigPath, yaml.dump(ruyiAgentConfig), 'utf8');
            this.writeLog(`${TAG} setAnnotationPathConfig config.annotation_path:${ruyiAgentConfig.annotation_path}`, 'ruyi_config.log');
            console.log(`${TAG} setAnnotationPathConfig config.annotation_path: ${ruyiAgentConfig.annotation_path}`);
        } catch (error) {
            console.error(`${TAG} 更新 Ruyi Agent annotation_path 配置失败: ${error}`);
            throw error;
        }
    }

    /**
     * 设置 Ruyi Agent 配置文件中的 data_dir_path 的值
     * @param {string} dataDirPath 数据目录路径
     * @returns {Promise<boolean>}
     */
    async setDataDirPathConfig(dataDirPath) {
        const configPath = this.getRuyiAgentConfigPath();
        if (!configPath) {
            this.writeLog('Ruyi Agent 配置文件路径未设置');
            return false;
        }

        try {
            // 检查文件是否存在，不存在则不执行任何操作
            try {
                await fs.promises.access(configPath);
            } catch (error) {
                this.writeLog(`Ruyi Agent 配置文件不存在于: ${configPath}`);
                return false;
            }

            const configFile = await fs.promises.readFile(configPath, 'utf8');
            const config = yaml.load(configFile) || {};

            config.data_dir_path = dataDirPath;

            await fs.promises.writeFile(configPath, yaml.dump(config), 'utf8');
            this.writeLog(`Ruyi Agent 配置文件中的 data_dir_path 已更新为: ${dataDirPath}`);
            return true;
        } catch (error) {
            this.writeLog(`更新 Ruyi Agent 配置文件失败: ${error.message}`);
            console.error('更新 Ruyi Agent 配置文件失败:', error);
            return false;
        }
    }

    /**
     * 获取设备数量配置
     * @returns {Object} 设备数量配置对象
     */
    getDeviceCountConfig() {
        const configPath = this.getConfigPath();
        try {
            if (fs.existsSync(configPath)) {
                const yamlContent = fs.readFileSync(configPath, 'utf8');
                const config = yaml.load(yamlContent) || {};
                this.writeLog(`${TAG} getDeviceCountConfig config.device_counts:${JSON.stringify(config.device_counts)}`, 'ruyi_config.log');
                console.log(`${TAG} getDeviceCountConfig config.device_counts: ${JSON.stringify(config.device_counts)}`);
                return config.device_counts || {
                    browserDeviceCount: 1,
                    cloudPhoneCount: 1
                };
            }
            return {
                browserDeviceCount: 1,
                cloudPhoneCount: 1
            };
        } catch (error) {
            console.error(`${TAG} Failed to read device count config:`, error);
            this.writeLog(`${TAG} Failed to read device count config: ${error.message}`, 'ruyi_config.log');
            return {
                browserDeviceCount: 1,
                cloudPhoneCount: 1
            };
        }
    }

    /**
     * 设置设备数量配置
     * @param {Object} deviceCounts 设备数量配置对象
     */
    setDeviceCountConfig(deviceCounts) {
        console.log(`${TAG} Setting device count config to:`, deviceCounts);
        this.writeLog(`${TAG} Setting device count config to: ${JSON.stringify(deviceCounts)}`, 'ruyi_config.log');
        const configPath = this.getConfigPath();
        try {
            // 读取现有配置
            let config = {};
            if (fs.existsSync(configPath)) {
                const yamlContent = fs.readFileSync(configPath, 'utf8');
                config = yaml.load(yamlContent) || {};
            }

            // 更新配置
            config.device_counts = deviceCounts;

            // 写入配置文件
            const yamlString = yaml.dump(config);
            fs.writeFileSync(configPath, yamlString, 'utf8');
            console.log(`${TAG} Device count config saved successfully to: ${configPath}`);
            this.writeLog(`${TAG} Device count config saved successfully to: ${configPath}`, 'ruyi_config.log');
            return true;
        } catch (error) {
            console.error(`${TAG} Failed to save device count config:`, error);
            this.writeLog(`${TAG} Failed to save device count config: ${error.message}`, 'ruyi_config.log');
            throw error;
        }
    }

    /**
     * 设置设备序列号配置到 RuyiAgent 配置文件中
     * @param {string} deviceSerialId 设备序列号
     */
    // setDeviceSerialIdConfig(deviceSerialId) {
    //     console.log(`${TAG} Setting device serial ID to:`, deviceSerialId);
    //     this.writeLog(`${TAG} Setting device serial ID to: ${deviceSerialId}`, 'ruyi_config.log');
        
    //     // 定义 yaml.dump 的选项
    //     const dumpOptions = {
    //         quotingType: '"',      // 使用双引号
    //         forceQuotes: true,     // 强制为字符串添加引号
    //         lineWidth: -1,         // 防止长字符串被截断
    //         noRefs: true           // 避免使用引用标记
    //     };

    //     // 更新 RuyiAgent 配置文件
    //     const ruyiAgentConfigPath = this.getRuyiAgentConfigPath();
    //     try {
    //         const ruyiAgentConfig = yaml.load(fs.readFileSync(ruyiAgentConfigPath, 'utf8'));
    //         ruyiAgentConfig.device_serial_id = deviceSerialId;
    //         fs.writeFileSync(ruyiAgentConfigPath, yaml.dump(ruyiAgentConfig, dumpOptions), 'utf8');
            
    //         console.log(`${TAG} Device serial ID saved successfully to RuyiAgent config: ${ruyiAgentConfigPath}`);
    //         this.writeLog(`${TAG} Device serial ID saved successfully to RuyiAgent config: ${ruyiAgentConfigPath}`, 'ruyi_config.log');
    //         return true;
    //     } catch (error) {
    //         console.error(`${TAG} Failed to save device serial ID to RuyiAgent config:`, error);
    //         this.writeLog(`${TAG} Failed to save device serial ID to RuyiAgent config: ${error.message}`, 'ruyi_config.log');
    //         return false;
    //     }
    // }

    /**
     * 获取设备序列号配置
     * @returns {string} 设备序列号
     */
    getDeviceSerialIdConfig() {
        const ruyiAgentConfigPath = this.getRuyiAgentConfigPath();
        try {
            if (fs.existsSync(ruyiAgentConfigPath)) {
                const yamlContent = fs.readFileSync(ruyiAgentConfigPath, 'utf8');
                const config = yaml.load(yamlContent);
                return config.device_serial_id || '';
            }
            return '';
        } catch (error) {
            console.error(`${TAG} Failed to read device serial ID config:`, error);
            this.writeLog(`${TAG} Failed to read device serial ID config: ${error.message}`, 'ruyi_config.log');
            return '';
        }
    }

    /**
     * 获取浏览器默认首页配置
     * @returns {string} 浏览器默认首页URL
     */
    getBrowserHomepageConfig() {
        const configPath = this.getConfigPath();
        try {
            if (fs.existsSync(configPath)) {
                const yamlContent = fs.readFileSync(configPath, 'utf8');
                const config = yaml.load(yamlContent) || {};
                const homepage = config.browser_homepage || 'https://www.baidu.com';
                this.writeLog(`${TAG} getBrowserHomepageConfig: ${homepage}`, 'ruyi_config.log');
                console.log(`${TAG} getBrowserHomepageConfig: ${homepage}`);
                return homepage;
            }
            return 'https://www.baidu.com';
        } catch (error) {
            console.error(`${TAG} Failed to read browser homepage config:`, error);
            this.writeLog(`${TAG} Failed to read browser homepage config: ${error.message}`, 'ruyi_config.log');
            return 'https://www.baidu.com';
        }
    }

    /**
     * 设置浏览器默认首页配置
     * @param {string} homepage 浏览器默认首页URL
     */
    setBrowserHomepageConfig(homepage) {
        console.log(`${TAG} Setting browser homepage to:`, homepage);
        this.writeLog(`${TAG} Setting browser homepage to: ${homepage}`, 'ruyi_config.log');
        
        // 定义 yaml.dump 的选项
        const dumpOptions = {
            quotingType: '"',      // 使用双引号
            forceQuotes: true,     // 强制为字符串添加引号
            lineWidth: -1,         // 防止长字符串被截断
            noRefs: true           // 避免使用引用标记
        };

        // 更新 Electron 配置
        const configPath = this.getConfigPath();
        try {
            const electronConfig = yaml.load(fs.readFileSync(configPath, 'utf8'));
            electronConfig.browser_homepage = homepage;
            fs.writeFileSync(configPath, yaml.dump(electronConfig, dumpOptions), 'utf8');
            console.log(`${TAG} Browser homepage saved successfully to Electron config: ${configPath}`);
            this.writeLog(`${TAG} Browser homepage saved successfully to Electron config: ${configPath}`, 'ruyi_config.log');
        } catch (error) {
            console.error(`${TAG} Failed to save browser homepage to Electron config:`, error);
            this.writeLog(`${TAG} Failed to save browser homepage to Electron config: ${error.message}`, 'ruyi_config.log');
            throw error;
        }

        // 更新 Flask 配置
        const flaskConfigPath = this.getFlaskConfigPath();
        try {
            const flaskConfig = yaml.load(fs.readFileSync(flaskConfigPath, 'utf8'));
            flaskConfig.browser_homepage = homepage;
            fs.writeFileSync(flaskConfigPath, yaml.dump(flaskConfig, dumpOptions), 'utf8');
            console.log(`${TAG} Browser homepage saved successfully to Flask config: ${flaskConfigPath}`);
            this.writeLog(`${TAG} Browser homepage saved successfully to Flask config: ${flaskConfigPath}`, 'ruyi_config.log');
        } catch (error) {
            console.error(`${TAG} Failed to save browser homepage to Flask config:`, error);
            this.writeLog(`${TAG} Failed to save browser homepage to Flask config: ${error.message}`, 'ruyi_config.log');
            throw error;
        }
    }

    /**
     * 设置设备映射配置
     * @param {Object} deviceMappings - 设备名称到序列号的映射对象
     * @returns {boolean} 是否设置成功
     */
    setDeviceMappingsConfig(deviceMappings) {
        try {
            const configPath = this.getRuyiAgentConfigPath();
            const config = this.loadYamlConfig(configPath);
            config.device_mappings = deviceMappings;
            this.saveYamlConfig(configPath, config);
            this.writeLog(`Device mappings config updated: ${JSON.stringify(deviceMappings)}`, 'ruyi.log');
            return true;
        } catch (error) {
            this.writeLog(`Error setting device mappings config: ${error.message}`, 'ruyi_error.log');
            return false;
        }
    }

    /**
     * 获取设备映射配置
     * @returns {Object} 设备名称到序列号的映射对象
     */
    getDeviceMappingsConfig() {
        try {
            const configPath = this.getRuyiAgentConfigPath();
            const config = this.loadYamlConfig(configPath);
            return config.device_mappings || {};
        } catch (error) {
            this.writeLog(`Error getting device mappings config: ${error.message}`, 'ruyi_error.log');
            return {};
        }
    }

    /**
     * 设置 RuyiAgent 配置文件中的 device_name 和 device_type
     * @param {string} deviceName - 设备名称
     * @param {string} deviceType - 设备类型 (websocket 或 browser)
     * @returns {boolean} 是否设置成功
     */
    setRuyiAgentDeviceConfig(deviceName, deviceType) {
        try {
            const configPath = this.getRuyiAgentConfigPath();
            const config = this.loadYamlConfig(configPath);
            config.device_name = deviceName;
            config.device_type = deviceType;
            this.saveYamlConfig(configPath, config);
            this.writeLog(`RuyiAgent device config updated: device_name=${deviceName}, device_type=${deviceType}`, 'ruyi.log');
            return true;
        } catch (error) {
            this.writeLog(`Error setting RuyiAgent device config: ${error.message}`, 'ruyi_error.log');
            return false;
        }
    }

    loadYamlConfig(configPath) {
        if (fs.existsSync(configPath)) {
            const configFile = fs.readFileSync(configPath, 'utf8');
            return yaml.load(configFile);
        }
        return {};
    }

    saveYamlConfig(configPath, config) {
        const yamlString = yaml.dump(config);
        fs.writeFileSync(configPath, yamlString, 'utf8');
    }
}

// 导出类的实例
module.exports = new ScrcpyManager();
