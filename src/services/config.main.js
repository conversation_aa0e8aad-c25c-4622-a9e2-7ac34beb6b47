/**
 * main 进程的配置, 使用 Node.js 的 CommonJS 模块系统
 * 用于在 main.js 做代理, 处理 CORS
 * 只需要 API_BASE_URL 即可
 */

const config = {
    API_BASE_URL: 'http://bit-swimming.cn',  // 最后面不要加 "/"
    API_BASE_URL_DATA: 'https://wisewk.com',
    GIT_SERVER_URL: 'bit-swimming.cn',  // Git 服务器地址
};

// const API_BASE_URL = process.env.NODE_ENV === 'development' ? '' : config.API_BASE_URL;
const API_BASE_URL = config.API_BASE_URL;
const API_BASE_URL_DATA = config.API_BASE_URL_DATA;
const GIT_SERVER_URL = config.GIT_SERVER_URL;

module.exports = {
    API_BASE_URL,
    API_BASE_URL_DATA,
    GIT_SERVER_URL,
};