/**
 * renderer 进程的配置, 使用 ES6 的模块系统
 * 用于在 renderer.js 中使用
 * 需要 API_BASE_URL 和 API_ENDPOINTS
 */

const config = {
    API_BASE_URL: 'http://bit-swimming.cn',
    GIT_SERVER_URL: 'bit-swimming.cn',  // Git 服务器地址
    API_ENDPOINTS: {
        // 登录注册
        STATUS: '/api/status',
        LOGIN: '/api/user/login',
        REGISTER: '/api/user/register',
        LOGOUT: '/api/user/logout',
        VERIFICATION: '/api/verification',
        GET_USER_VERSIONS: '/api/user/versions',
        GET_USER_INFO: '/api/user/self',

        // 重置密码
        SEND_RESET_PASSWORD_EMAIL: '/api/reset_password',
        RESET_PASSWORD: '/api/user/reset',

        GET_ALL_PROJECTS: '/api/projects',
        CREATE_PROJECT: '/api/projects',

        GET_PROJECT: '/api/projects/{id}',
        UPDATE_PROJECT_NAME: '/api/projects/{id}',

        UPDATE_PROJECT_CONTENT: '/api/projects/{id}/content',
        SAVE_PROJECT_HISTORY: '/api/projects/{id}/history',

        PUBLISH_PROJECT: '/api/projects/{id}/productions',
        GET_PUBLISHED_VERSIONS: '/api/projects/{id}/productions',

        GET_PROJECT_HISTORY: '/api/projects/{id}/history',

        GET_API_KEY: '/api/token',

        DELETE_PROJECT: '/api/projects/{id}',

        EXCHANGE_TOKEN: '/api/user/topup',

        GENERATE_FIRST_SCRIPT: '/v1/ruyi_ide/{language_code}/generate_first_script/chat/completions',
        GENERATE_FIRST_SCRIPT_WITH_TASK_NAME: '/v1/ruyi_ide/{language_code}/generate_first_script_with_task_name/chat/completions',
        OPTIMIZE_SCRIPT_THROUGH_CHAT: '/v1/ruyi_ide/{language_code}/optimize_script_through_chat/chat/completions',
        TRANSFER_CODE_SCRIPT_TO_NL_SCRIPT: '/v1/ruyi_ide/{language_code}/transfer_code_script_to_nl_script/chat/completions',
        TRANSFER_NL_SCRIPT_TO_CODE_SCRIPT: '/v1/ruyi_ide/{language_code}/transfer_nl_script_to_code_script/chat/completions',
        TRANSFER_SELECTED_SCRIPT_FROM_NL_TO_CODE: '/v1/ruyi_ide/{language_code}/transfer_selected_script_from_nl_to_code/chat/completions',
        GENERATE_WORKFLOW: '/v1/ruyi_ide/{language_code}/generate_workflow/chat/completions',
        
        // 新的 workflow 相关 API 端点
        TRANSFER_WORKFLOW_TO_CODE: '/v1/ruyi_ide/{language_code}/transfer_workflow_to_code/chat/completions',
        TRANSFER_CODE_TO_WORKFLOW: '/v1/ruyi_ide/{language_code}/transfer_code_to_workflow/chat/completions',
        TRANSFER_SELECTED_WORKFLOW_TO_CODE: '/v1/ruyi_ide/{language_code}/transfer_selected_workflow_to_code/chat/completions',

        RUYIMODEL: '/v1/chat/completions',

        // 虚拟设备点击记录
        VIRTUAL_DEVICE_CLICK: '/api/promotion/click',

        // 反馈
        FEEDBACK: '/api/feedback',

        // 获取 server 通知
        NOTICE: '/api/notice',

        // 仓库管理
        REPO_CREATE: '/api/repo/create',
        REPO_DELETE: '/api/repo/delete',
        REPO_DESCRIPTION: '/api/repo/description',
        REPO_UPDATE_DESCRIPTION: '/api/repo/description',
        REPO_LIST: '/api/repo/list',
        REPO_RENAME: '/api/repo/rename',
        REPO_TOKEN: '/api/repo/token',
        REPO_VISIBILITY: '/api/repo/visibility',
        REPO_UPDATE_VISIBILITY: '/api/repo/visibility',
    }
};

// 普通 API：开发环境使用空字符串，让请求走 Vite 代理
export const API_BASE_URL = import.meta.env.DEV ? '' : config.API_BASE_URL;

// 其他 API：直接使用 config.API_BASE_URL
export const API_BASE_URL_PROD = config.API_BASE_URL;

export const API_ENDPOINTS = config.API_ENDPOINTS;

// Git 服务器地址
export const GIT_SERVER_URL = config.GIT_SERVER_URL;