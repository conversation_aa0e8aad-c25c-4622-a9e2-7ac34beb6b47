/**
 * Prompt Template Manager
 * Handles custom prompt template loading and processing for development mode
 * Supports 6 query mechanisms with Jinja2-like templating (CSP-compatible)
 */

export class PromptTemplateManager {
    constructor() {
        this.templateCache = new Map();
        this.templatePaths = [
            'Desktop/',
            'Documents/'
        ];
        this.templateFileName = 'template.j2';
    }

    /**
     * Check if we're in development mode
     */
    isDevelopmentMode() {
        return import.meta.env.DEV;
    }

    /**
     * Get the template file paths to search
     */
    getTemplatePaths() {
        return this.templatePaths.map(relativePath => ({
            path: relativePath,
            fullPath: `${relativePath}/${this.templateFileName}`
        }));
    }

    /**
     * Load custom template from file system
     * @returns {Promise<string|null>} Template content or null if not found
     */
    async loadCustomTemplate() {
        // if (!this.isDevelopmentMode()) {
        //     console.log('[PromptTemplateManager] Not in development mode, skipping custom template');
        //     return null;
        // }

        // Check cache first
        if (this.templateCache.has('custom_template')) {
            console.log('[PromptTemplateManager] Using cached custom template');
            return this.templateCache.get('custom_template');
        }

        try {
            console.log('[PromptTemplateManager] Loading custom template from file system');
            // Try to load template from electronAPI
            const templateContent = await window.electronAPI.loadPromptTemplate();

            if (templateContent) {
                console.log(`[PromptTemplateManager] Successfully loaded custom template, length: ${templateContent.length}`);
                this.templateCache.set('custom_template', templateContent);
                return templateContent;
            } else {
                console.log('[PromptTemplateManager] No custom template found');
            }
        } catch (error) {
            console.warn('[PromptTemplateManager] Failed to load custom prompt template:', error);
        }

        return null;
    }

    /**
     * Parse template sections for the 6 query mechanisms
     * @param {string} templateContent - The template content
     * @returns {Object} Parsed template sections
     */
    parseTemplateSections(templateContent) {
        const sections = {
            // generateFirstScript: '',
            // generateFirstScriptAndTaskName: '',
            // optimizeScriptThroughChat: '',
            // transferCodeScriptToNLScript: '',
            // transferNLScriptToCodeScript: '',
            // transferSelectedScriptFromNLToCode: '',

            generateWorkflow: '',
            transferWorkflowToCode: '',
            transferCodeToWorkflow: '',
            transferSelectedWorkflowToCode: '',
        };

        // Split template by comment-based section markers
        // Format: {# ===== SECTION: sectionName ===== #}
        const sectionRegex = /\{#\s*=+\s*SECTION:\s*(\w+)\s*=+\s*#\}([\s\S]*?)(?=\{#\s*=+\s*SECTION:\s*\w+\s*=+\s*#\}|$)/g;
        let match;

        while ((match = sectionRegex.exec(templateContent)) !== null) {
            const sectionName = match[1];
            const sectionContent = match[2].trim();

            if (sections.hasOwnProperty(sectionName)) {
                sections[sectionName] = sectionContent;
                console.log(`[PromptTemplateManager] Found section: ${sectionName}, length: ${sectionContent.length}`);
            }
        }

        return sections;
    }

    /**
     * Parse {% set %} variables from template
     * @param {string} templateContent - The template content
     * @returns {Object} Parsed set variables
     */
    parseSetVariables(templateContent) {
        const setVariables = {};
        const setRegex = /\{%\s*set\s+(\w+)\s*%\}([\s\S]*?)\{%\s*endset\s*%\}/g;
        let match;

        while ((match = setRegex.exec(templateContent)) !== null) {
            const varName = match[1];
            const varContent = match[2].trim();
            setVariables[varName] = varContent;
            console.log(`[PromptTemplateManager] Found set variable: ${varName}`);
        }

        return setVariables;
    }

    /**
     * Process template with variables (CSP-compatible Jinja2-like implementation)
     * @param {string} template - Template string
     * @param {Object} variables - Variables for template processing
     * @returns {string} Processed template
     */
    processTemplate(template, variables = {}) {
        try {
            console.log('[PromptTemplateManager] Processing template with custom parser, variables:', Object.keys(variables));

            let processed = template;

            // Step 1: Replace variables {{ variable }}
            processed = this.replaceVariables(processed, variables);

            // console.log('[PromptTemplateManager] Template processed after replaceVariables:', processed);

            // Step 2: Process conditionals (handle nested and complex conditions)
            processed = this.processConditionals(processed, variables);

            // console.log('[PromptTemplateManager] Template processed after processConditionals:', processed);

            // Step 3: Process loops
            processed = this.processLoops(processed, variables);

            // console.log('[PromptTemplateManager] Template processed after processLoops:', processed);

            console.log('[PromptTemplateManager] Template processed successfully');
            return processed;
        } catch (error) {
            console.error('[PromptTemplateManager] Error processing template:', error);
            console.error('[PromptTemplateManager] Template content:', template.substring(0, 200) + '...');
            console.error('[PromptTemplateManager] Variables:', variables);

            // Fallback to returning the original template if processing fails
            return template;
        }
    }

    /**
     * Replace variables in template {{ variable }}
     * @param {string} template - Template string
     * @param {Object} variables - Variables object
     * @returns {string} Template with variables replaced
     */
    replaceVariables(template, variables) {
        // Replace variables: {{ variable_name }}
        const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g;
        return template.replace(variableRegex, (match, varName) => {
            const value = variables[varName];
            return value !== undefined ? String(value) : '';
        });
    }

    /**
     * Process conditional statements {% if condition %}...{% endif %}
     * @param {string} template - Template string
     * @param {Object} variables - Variables object
     * @returns {string} Template with conditionals processed
     */
    processConditionals(template, variables) {
        let processed = template;

        // Handle conditionals with else: {% if condition %}...{% else %}...{% endif %}
        const conditionalWithElseRegex = /\{\%\s*if\s+([^%]+?)\s*\%\}([\s\S]*?)\{\%\s*else\s*\%\}([\s\S]*?)\{\%\s*endif\s*\%\}/g;
        processed = processed.replace(conditionalWithElseRegex, (match, condition, ifContent, elseContent) => {
            const conditionResult = this.evaluateCondition(condition.trim(), variables);
            return conditionResult ? ifContent.trim() : elseContent.trim();
        });

        // Handle simple conditionals: {% if condition %}...{% endif %}
        const conditionalRegex = /\{\%\s*if\s+([^%]+?)\s*\%\}([\s\S]*?)\{\%\s*endif\s*\%\}/g;
        processed = processed.replace(conditionalRegex, (match, condition, content) => {
            const conditionResult = this.evaluateCondition(condition.trim(), variables);
            return conditionResult ? content.trim() : '';
        });

        return processed;
    }

    /**
     * Process loop statements {% for item in items %}...{% endfor %}
     * @param {string} template - Template string
     * @param {Object} variables - Variables object
     * @returns {string} Template with loops processed
     */
    processLoops(template, variables) {
        // Handle loops: {% for item in items %}...{% endfor %}
        const loopRegex = /\{\%\s*for\s+(\w+)\s+in\s+(\w+)\s*\%\}([\s\S]*?)\{\%\s*endfor\s*\%\}/g;
        return template.replace(loopRegex, (match, itemVar, arrayVar, content) => {
            const array = variables[arrayVar];
            if (Array.isArray(array)) {
                return array.map(item => {
                    const itemVars = { ...variables, [itemVar]: item };
                    return this.processTemplate(content.trim(), itemVars);
                }).join('\n');
            }
            return '';
        });
    }

    /**
     * Evaluate conditional expressions (supports ==, !=, and truthiness)
     * @param {string} condition - The condition to evaluate
     * @param {Object} variables - Variables for evaluation
     * @returns {boolean} Result of the condition
     */
    evaluateCondition(condition, variables) {
        try {
            // Handle comparison operators
            if (condition.includes('==')) {
                const [left, right] = condition.split('==').map(s => s.trim());
                const leftValue = this.getVariableValue(left, variables);
                const rightValue = this.getVariableValue(right, variables);
                return leftValue === rightValue;
            }

            if (condition.includes('!=')) {
                const [left, right] = condition.split('!=').map(s => s.trim());
                const leftValue = this.getVariableValue(left, variables);
                const rightValue = this.getVariableValue(right, variables);
                return leftValue !== rightValue;
            }

            // Handle simple variable checks (truthy/falsy)
            const value = this.getVariableValue(condition, variables);
            return !!value;
        } catch (error) {
            console.warn(`[PromptTemplateManager] Error evaluating condition "${condition}":`, error);
            return false;
        }
    }

    /**
     * Get variable value, handling both variables and string literals
     * @param {string} expr - Expression to evaluate
     * @param {Object} variables - Variables object
     * @returns {any} The value
     */
    getVariableValue(expr, variables) {
        // Remove quotes for string literals
        if ((expr.startsWith('"') && expr.endsWith('"')) ||
            (expr.startsWith("'") && expr.endsWith("'"))) {
            return expr.slice(1, -1);
        }

        // Return variable value or undefined
        return variables[expr];
    }

    /**
     * Get processed template for a specific query mechanism
     * @param {string} mechanism - The query mechanism name
     * @param {Object} variables - Variables for template processing
     * @returns {Promise<string|null>} Processed template or null
     */
    async getProcessedTemplate(mechanism, variables = {}) {
        console.log(`[PromptTemplateManager] Getting processed template for mechanism: ${mechanism}`);
        console.log('[PromptTemplateManager] variables:', variables);

        const customTemplate = await this.loadCustomTemplate();

        if (!customTemplate) {
            console.log(`[PromptTemplateManager] No custom template available for mechanism: ${mechanism}`);
            return null;
        }

        const setVariables = this.parseSetVariables(customTemplate);
        const sections = this.parseTemplateSections(customTemplate);
        console.log(`[PromptTemplateManager] Parsed sections:`, Object.keys(sections));

        const sectionTemplate = sections[mechanism];

        if (!sectionTemplate) {
            console.warn(`[PromptTemplateManager] No template section found for mechanism: ${mechanism}`);
            return null;
        }

        console.log(`[PromptTemplateManager] Processing template for mechanism: ${mechanism} with variables:`, Object.keys(variables));
        const finalVariables = { ...setVariables, ...variables };
        const processedTemplate = this.processTemplate(sectionTemplate, finalVariables);
        console.log(`[PromptTemplateManager] Processed template length: ${processedTemplate.length}`);

        return processedTemplate;
    }

    /**
     * Clear template cache
     */
    clearCache() {
        this.templateCache.clear();
    }
}

// Create singleton instance
export const promptTemplateManager = new PromptTemplateManager();
