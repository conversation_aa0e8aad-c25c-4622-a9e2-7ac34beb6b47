import { API_BASE_URL, API_BASE_URL_PROD, API_ENDPOINTS } from './config.renderer';
import { jsTemplateManager } from './jsTemplateManager';

const request = async (endpoint, options = {}) => {
    const url = `${API_BASE_URL}${endpoint}`;
    
    try {
        let response;
        if (import.meta.env.DEV) {
            // 开发环境
            console.log('DEVELOPMENT MODE request url:', url);
            response = await fetch(url, {
                ...options,
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers,
                },
                credentials: 'include',
                mode: 'cors',
            });
            
            console.log('DEVELOPMENT MODE request response:', response);

            if (!response.ok) {
                throw new Error(`DEVELOPMENT MODE HTTP error! status: ${response.status}`);
            }

            // 开发环境的后端API直接获取整个响应体的JSON
            // 不需要解析 .data 字段
            // 检查响应体是否为空
            const text = await response.text();
            console.log('DEVELOPMENT MODE request response text:', text);
            
            if (!text || text.trim() === '') {
                console.log('DEVELOPMENT MODE empty response, returning success object');
                return { success: true };
            }
            
            try {
                const data = JSON.parse(text);
                console.log('DEVELOPMENT MODE request response.json():', data);
                return data;
            } catch (jsonError) {
                console.warn('DEVELOPMENT MODE JSON parse failed, returning text:', text);
                return { success: true, message: text };
            }
            // return await response.json();
        } else {
            // 生产环境
            console.log('production mode request url:', url);
            response = await window.electronAPI.request(
                options.method || 'GET',
                endpoint,
                // 直接传递 options.body，不要在这里解析
                options.body
            );
            console.log('PRODUCTION MODE request response:', response);

            if (!response.ok) {
                throw new Error(response.message || '请求失败');
            }

            // 生产环境的electron封装层返回的是包装对象（如 { ok: true, status: 200, data: '{"data": {...}}' }）,
            // 需要解析 .data 字段
            // 检查响应数据是否为空
            if (!response.data || response.data.trim() === '') {
                console.log('PRODUCTION MODE empty response data, returning success object');
                return { success: true };
            }
            
            try {
                const data = JSON.parse(response.data);
                console.log('production mode request JSON.parse(response.data):', data);
                return data;
            } catch (jsonError) {
                console.warn('PRODUCTION MODE JSON parse failed, returning success:', response.data);
                return { success: true, message: response.data };
            }
            // return JSON.parse(response.data);
        }
    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
};

export const userApi = {
    /**
     * 检查状态
     */
    status: () => {
        return request(API_ENDPOINTS.STATUS, {
            method: 'GET',
        });
    },

    /**
     * 登录
     */
    login: (username, password) => {
        return request(API_ENDPOINTS.LOGIN, {
            method: 'POST',
            // 确保这里已经是字符串
            body: JSON.stringify({ username, password })
        });
    },

    /**
     * 注册用户
     */
    register: (username, password, password2, email = '', verification_code = '', aff_code = null) => {
        return request(API_ENDPOINTS.REGISTER, {
            method: 'POST',
            body: JSON.stringify({
                username,
                password,
                password2,
                email,
                verification_code,
                aff_code
            }),
        });
    },

    /**
     * 验证用户邮箱
     */
    verification: (email) => {
        return request(`${API_ENDPOINTS.VERIFICATION}?email=${email}`, {
            method: 'GET',
        });
    },

    /**
     * 退出登录
     */
    logout: () => {
        return request(API_ENDPOINTS.LOGOUT, {
            method: 'GET',
        });
    },

    /**
     * 获取用户版本, 用于检查用户是否已登录
     */
    getUserVersions: () => {
        return request(API_ENDPOINTS.GET_USER_VERSIONS, {
            method: 'GET',
        });
    },

    /**
     * 获取用户版本, 用于检查用户是否已登录
     */
    getUserInfo: () => {
        return request(API_ENDPOINTS.GET_USER_INFO, {
            method: 'GET',
        });
    },

    /**
     * 发送重置密码邮件
     */
    sendResetPasswordEmail: (email) => {
        return request(`${API_ENDPOINTS.SEND_RESET_PASSWORD_EMAIL}?email=${email}`, {
            method: 'GET',
        });
    },

    /**
     * 重置密码
     */
    resetPassword: (email, password, password2, verification_code) => {
        return request(API_ENDPOINTS.RESET_PASSWORD, {
            method: 'POST',
            body: JSON.stringify({ email, password, password2, verification_code })
        });
    }
};

export const projectApi = {
    /**
     * 获取所有项目
     * @param {number} page - 页码
     * @param {number} page_size - 每页项目数量
     * @returns {Promise<{data: Array, total: number}>} 返回一个对象，包含：
     *   - data: 项目列表数组
     *   - total: 项目总数
     */
    getAllProjects: async (page = 1, page_size = 10) => {
        const endpoint = `${API_ENDPOINTS.GET_ALL_PROJECTS}?page=${page}&page_size=${page_size}`;
        const response = await request(endpoint);
        return response;
    },

    getProject: (id) => {
        return request(API_ENDPOINTS.GET_PROJECT.replace('{id}', id));
    },

    publishProject: (id, productionData) => {
        return request(API_ENDPOINTS.PUBLISH_PROJECT.replace('{id}', id), {
            method: 'POST',
            body: JSON.stringify(productionData)
        });
    },

    createProject: (projectData) => {
        return request(API_ENDPOINTS.CREATE_PROJECT, {
            method: 'POST',
            body: JSON.stringify(projectData)
        });
    },

    updateProjectName: (id, projectData) => {
        return request(API_ENDPOINTS.UPDATE_PROJECT_NAME.replace('{id}', id), {
            method: 'PUT',
            body: JSON.stringify(projectData)
        });
    },

    updateProjectContent: (id, projectData) => {
        return request(API_ENDPOINTS.UPDATE_PROJECT_CONTENT.replace('{id}', id), {
            method: 'PUT',
            body: JSON.stringify(projectData)
        });
    },

    getPublishedVersions: (id) => {
        return request(API_ENDPOINTS.GET_PUBLISHED_VERSIONS.replace('{id}', id));
    },

    saveProjectHistory: (id, comment) => {
        return request(API_ENDPOINTS.SAVE_PROJECT_HISTORY.replace('{id}', id), {
            method: 'POST',
            body: JSON.stringify(comment)
        });
    },

    getProjectHistory: async (id, page = 1, page_size = 10) => {
        const endpoint = `${API_ENDPOINTS.GET_PROJECT_HISTORY.replace('{id}', id)}?page=${page}&page_size=${page_size}`;
        return request(endpoint);
    },

    deleteProject: (id) => {
        return request(API_ENDPOINTS.DELETE_PROJECT.replace('{id}', id), {
            method: 'DELETE',
        });
    },
};

export const tokenApi = {
    getApiKey: () => {
        return request(API_ENDPOINTS.GET_API_KEY, {
            method: 'GET',
        });
    },

    /**
     * 兑换token
     */
    exchangeToken: (code) => {
        return request(API_ENDPOINTS.EXCHANGE_TOKEN, {
            method: 'POST',
            body: JSON.stringify({ key: code })
        });
    }
};

export const systemApi = {
    /**
     * 获取 server 通知
     */
    getNotice: () => {
        return request(API_ENDPOINTS.NOTICE, {
            method: 'GET',
        });
    },

    /**
     * 虚拟设备点击记录
     */
    recordVirtualDeviceClick: () => {
        return request(API_ENDPOINTS.VIRTUAL_DEVICE_CLICK, {
            method: 'POST',
        });
    },

    /**
     * 提交反馈
     */
    feedback: (category, content, image_url) => {
        return request(API_ENDPOINTS.FEEDBACK, {
            method: 'POST',
            body: JSON.stringify({ category, content, image_url })
        });
    }
};

export const repoApi = {
    /**
     * 创建新的 Git 仓库
     * @param {string} name - 仓库名称
     * @param {string} description - 仓库描述
     */
    createRepo: (name, description) => {
        return request(API_ENDPOINTS.REPO_CREATE, {
            method: 'POST',
            body: JSON.stringify({ name, description })
        });
    },

    /**
     * 删除指定 Git 仓库
     * @param {string} name - 仓库名称
     */
    deleteRepo: (name) => {
        return request(`${API_ENDPOINTS.REPO_DELETE}?name=${name}`, {
            method: 'DELETE',
        });
    },

    /**
     * 获取当前用户所有 Git 仓库
     * @param {number} page - 页码，默认为1
     * @param {number} page_size - 每页数量，默认为10
     */
    listRepos: (page = 1, page_size = 10) => {
        return request(`${API_ENDPOINTS.REPO_LIST}?page=${page}&page_size=${page_size}`, {
            method: 'GET',
        });
    },

    /**
     * 获取仓库可见性
     * @param {string} name - 仓库名
     */
    getRepoVisibility: (name) => {
        return request(`${API_ENDPOINTS.REPO_VISIBILITY}?name=${name}`, {
            method: 'GET',
        });
    },

    /**
     * 更新仓库可见性
     * @param {string} name - 仓库名
     * @param {boolean} isPrivate - 是否为私有
     */
    updateRepoVisibility: (name, isPrivate) => {
        return request(API_ENDPOINTS.REPO_UPDATE_VISIBILITY, {
            method: 'PUT',
            body: JSON.stringify({ name, private: isPrivate })
        });
    },

    /**
     * 更新仓库描述
     * @param {string} name - 仓库名
     * @param {string} description - 新的描述
     */
    updateRepoDescription: (name, description) => {
        return request(API_ENDPOINTS.REPO_UPDATE_DESCRIPTION, {
            method: 'PUT',
            body: JSON.stringify({ name, description })
        });
    },

    /**
     * 重命名仓库
     * @param {string} old_name - 旧仓库名
     * @param {string} new_name - 新仓库名
     */
    renameRepo: (old_name, new_name) => {
        return request(API_ENDPOINTS.REPO_RENAME, {
            method: 'POST',
            body: JSON.stringify({ old_name, new_name })
        });
    },

    /**
     * 获取仓库描述
     * @param {string} name - 仓库名
     */
    getRepoDescription: (name) => {
        return request(`${API_ENDPOINTS.REPO_DESCRIPTION}?name=${name}`, {
            method: 'GET',
        });
    },

    /**
     * 获取仓库访问令牌
     * @param {string} name - 仓库名
     */
    getRepoToken: (name) => {
        return request(`${API_ENDPOINTS.REPO_TOKEN}?name=${name}`, {
            method: 'GET',
        });
    }
};

const scriptRequest = async (endpoint, options = {}) => {
    let ruyillm_key = '';

    try {
        ruyillm_key = await window.electronAPI.getRuyiLLMKeyConfig();
        console.log('llmRequest ruyillm_key:', ruyillm_key);
    } catch (error) {
        console.error('Failed to get RuyiLLM key:', error);
    }

    // Add skip_prompt parameter in development mode
    const isDev = import.meta.env.DEV;
    let finalEndpoint = endpoint;

    // if (isDev) {
    const separator = endpoint.includes('?') ? '&' : '?';
    finalEndpoint = `${endpoint}${separator}skip_prompt=true`;
    console.log('Development mode: Added skip_prompt=true to endpoint:', finalEndpoint);
    // }

    const url = API_BASE_URL_PROD + finalEndpoint;
    console.log('llmRequest url:', url);
    const token = ruyillm_key;

    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };


    try {
        const response = await fetch(url, {
            headers: headers,
            ...options,
        });

        if (!response.ok) {
            console.log('llmRequest response:', response);
            if (response.status === 403) {
                throw new Error('Insufficient Token');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        console.log('llmRequest response:', response);
        const data = await response.json();
        console.log('llmRequest response.json():', data);
        console.log('llmRequest message.content:', data.choices[0].message.content);
        let content = data.choices[0].message.content;
        if (content.startsWith('```json')) {
            content = content.replace('```json', '').replace('```', '');
        }
        try {
            const contentJson = JSON.parse(content);
            return contentJson;
        } catch (error) {
            content = content.replace(/\n{/, '{')
                             .replace(/{\n/, '{')
                             .replace(/\n}/, '}')
                             .replace(/}\n/, '}')
                             .replace(/,\n\s*"NL_script"/, ',"NL_script"')
                             .replace(/,\n\s*"code_script"/, ',"code_script"')
                             .replace(/,\n\s*"task_name"/, ',"task_name"')
                             .replace(/\r?\n/g, '\\n');

            console.log('llmRequest content.replace():', content);
            const contentJson = JSON.parse(content);
            return contentJson;
        }
    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
};

const workflowRequest = async (endpoint, options = {}) => {
    let ruyillm_key = '';

    try {
        ruyillm_key = await window.electronAPI.getRuyiLLMKeyConfig();
        console.log('llmRequest ruyillm_key:', ruyillm_key);
    } catch (error) {
        console.error('Failed to get RuyiLLM key:', error);
    }

    // Add skip_prompt parameter in development mode
    const isDev = import.meta.env.DEV;
    let finalEndpoint = endpoint;

    // if (isDev) {
    const separator = endpoint.includes('?') ? '&' : '?';
    finalEndpoint = `${endpoint}${separator}skip_prompt=true`;
    console.log('Development mode: Added skip_prompt=true to endpoint:', finalEndpoint);
    // }

    const url = API_BASE_URL_PROD + finalEndpoint;
    console.log('llmRequest url:', url);
    const token = ruyillm_key;

    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };


    try {
        const response = await fetch(url, {
            headers: headers,
            ...options,
        });

        if (!response.ok) {
            console.log('llmRequest response:', response);
            if (response.status === 403) {
                throw new Error('Insufficient Token');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        console.log('llmRequest response:', response);
        const data = await response.json();
        console.log('llmRequest response.json():', data);
        console.log('llmRequest message.content:', data.choices[0].message.content);
        return data.choices[0].message.content;
    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
};

export const promptApi = {
    // /**
    //  * 生成第一个脚本
    //  */
    // generateFirstScript: async (taskName, taskDescription, appKnowledge = '', languageCode = 'zh') => {
    //     const endpoint = API_ENDPOINTS.GENERATE_FIRST_SCRIPT.replace('{language_code}', languageCode);

    //     // Try to get custom template in development mode
    //     const isDev = import.meta.env.DEV;
    //     let messages = [
    //         {"role": "user", "content": taskName},
    //         {"role": "user", "content": taskDescription}
    //     ];

    //     // if (isDev) {
    //     try {
    //         const customPrompt = await promptTemplateManager.getProcessedTemplate('generateFirstScript', {
    //             taskName,
    //             taskDescription,
    //             appKnowledge,
    //             languageCode
    //         });

    //         if (customPrompt) {
    //             console.log('Using custom template for generateFirstScript');
    //             console.log('customPrompt:', customPrompt);
    //             messages = [{"role": "user", "content": customPrompt}];
    //         }
    //     } catch (error) {
    //         console.warn('Failed to load custom template, using default:', error);
    //     }
    //     // }

    //     if (appKnowledge && !isDev) {
    //         messages.push({"role": "user", "content": appKnowledge});
    //     }

    //     return scriptRequest(endpoint, {
    //         method: 'POST',
    //         body: JSON.stringify({
    //             model: "gpt-4o",
    //             messages
    //         })
    //     });
    // },

    // /**
    //  * 使用任务名称生成第一个脚本
    //  */
    // generateFirstScriptAndTaskName: async (taskDescription, appKnowledge = '', languageCode = 'zh') => {
    //     const endpoint = API_ENDPOINTS.GENERATE_FIRST_SCRIPT_WITH_TASK_NAME.replace('{language_code}', languageCode);

    //     // Try to get custom template in development mode
    //     const isDev = import.meta.env.DEV;
    //     let messages = [
    //         {"role": "user", "content": taskDescription}
    //     ];

    //     // if (isDev) {
    //     try {
    //         const customPrompt = await promptTemplateManager.getProcessedTemplate('generateFirstScriptAndTaskName', {
    //             taskDescription,
    //             appKnowledge,
    //             languageCode
    //         });

    //         if (customPrompt) {
    //             console.log('Using custom template for generateFirstScriptAndTaskName');
    //             console.log('customPrompt:', customPrompt);
    //             messages = [{"role": "user", "content": customPrompt}];
    //         }
    //     } catch (error) {
    //         console.warn('Failed to load custom template, using default:', error);
    //     }
    //     // }

    //     if (appKnowledge && !isDev) {
    //         messages.push({"role": "user", "content": appKnowledge});
    //     }

    //     return scriptRequest(endpoint, {
    //         method: 'POST',
    //         body: JSON.stringify({
    //             model: "gpt-4o",
    //             messages
    //         })
    //     });
    // },

    // /**
    //  * 通过聊天优化脚本
    //  */
    // optimizeScriptThroughChat: async (taskName, conversationHistory, newUserMessage, pythonScript, NLScript, languageCode = 'zh') => {
    //     const endpoint = API_ENDPOINTS.OPTIMIZE_SCRIPT_THROUGH_CHAT.replace('{language_code}', languageCode);

    //     // Try to get custom template in development mode
    //     const isDev = import.meta.env.DEV;
    //     let messages = [
    //         {"role": "user", "content": taskName},
    //         {"role": "user", "content": newUserMessage},
    //         {"role": "user", "content": pythonScript},
    //         {"role": "user", "content": NLScript}
    //     ];

    //     // if (isDev) {
    //     try {
    //         const customPrompt = await promptTemplateManager.getProcessedTemplate('optimizeScriptThroughChat', {
    //             taskName,
    //             conversationHistory,
    //             newUserMessage,
    //             pythonScript,
    //             NLScript,
    //             languageCode
    //         });

    //         if (customPrompt) {
    //             console.log('Using custom template for optimizeScriptThroughChat');
    //             messages = [{"role": "user", "content": customPrompt}];
    //         }
    //     } catch (error) {
    //         console.warn('Failed to load custom template, using default:', error);
    //     }
    //     // }

    //     return scriptRequest(endpoint, {
    //         method: 'POST',
    //         body: JSON.stringify({
    //             model: "gpt-4o",
    //             messages
    //         })
    //     });
    // },

    // /**
    //  * 将代码脚本转换为自然语言脚本
    //  */
    // transferCodeScriptToNLScript: async (taskName, oldPythonScript, NLScript, newPythonScript, languageCode = 'zh') => {
    //     const endpoint = API_ENDPOINTS.TRANSFER_CODE_SCRIPT_TO_NL_SCRIPT.replace('{language_code}', languageCode);

    //     // Try to get custom template in development mode
    //     const isDev = import.meta.env.DEV;
    //     let messages = [
    //         {"role": "user", "content": taskName},
    //         {"role": "user", "content": oldPythonScript},
    //         {"role": "user", "content": NLScript},
    //         {"role": "user", "content": newPythonScript}
    //     ];

    //     // if (isDev) {
    //     try {
    //         const customPrompt = await promptTemplateManager.getProcessedTemplate('transferCodeScriptToNLScript', {
    //             taskName,
    //             newPythonScript,
    //             oldPythonScript,
    //             NLScript,
    //             languageCode
    //         });

    //         if (customPrompt) {
    //             console.log('Using custom template for transferCodeScriptToNLScript');
    //             messages = [{"role": "user", "content": customPrompt}];
    //         }
    //     } catch (error) {
    //         console.warn('Failed to load custom template, using default:', error);
    //     }
    //     // }

    //     return scriptRequest(endpoint, {
    //         method: 'POST',
    //         body: JSON.stringify({
    //             model: "gpt-4o",
    //             messages
    //         })
    //     });
    // },

    // /**
    //  * 将自然语言脚本转换为代码脚本
    //  */
    // transferNLScriptToCodeScript: async (taskName, oldNLScript, pythonScript, newNLScript, languageCode = 'zh') => {
    //     const endpoint = API_ENDPOINTS.TRANSFER_NL_SCRIPT_TO_CODE_SCRIPT.replace('{language_code}', languageCode);

    //     // Try to get custom template in development mode
    //     const isDev = import.meta.env.DEV;
    //     let messages = [
    //         {"role": "user", "content": taskName},
    //         {"role": "user", "content": oldNLScript},
    //         {"role": "user", "content": pythonScript},
    //         {"role": "user", "content": newNLScript}
    //     ];

    //     console.log('[api.js] transferNLScriptToCodeScript messages:', messages);

    //     // if (isDev) {
    //     try {
    //         console.log('[api.js] Using custom template for transferNLScriptToCodeScript START');
    //         const customPrompt = await promptTemplateManager.getProcessedTemplate('transferNLScriptToCodeScript', {
    //             taskName,
    //             newNLScript,
    //             oldNLScript,
    //             pythonScript,
    //             languageCode
    //         });
            
    //         console.log('[api.js] customPrompt:', customPrompt);

    //         if (customPrompt) {
    //             console.log('Using custom template for transferNLScriptToCodeScript');
    //             messages = [{"role": "user", "content": customPrompt}];
    //         }
    //     } catch (error) {
    //         console.warn('Failed to load custom template, using default:', error);
    //     }
    //     // }

    //     return scriptRequest(endpoint, {
    //         method: 'POST',
    //         body: JSON.stringify({
    //             model: "gpt-4o",
    //             messages
    //         })
    //     });
    // },

    // /**
    //  * 将选定的自然语言脚本转换为代码脚本
    //  */
    // transferSelectedScriptFromNLToCode: async (taskName, pythonScript, NLScript, startLine, endLine, selectedNLScript, languageCode = 'zh') => {
    //     const endpoint = API_ENDPOINTS.TRANSFER_SELECTED_SCRIPT_FROM_NL_TO_CODE.replace('{language_code}', languageCode);

    //     // Try to get custom template in development mode
    //     const isDev = import.meta.env.DEV;
    //     let messages = [
    //         {"role": "user", "content": taskName},
    //         {"role": "user", "content": pythonScript},
    //         {"role": "user", "content": NLScript},
    //         {"role": "user", "content": startLine.toString()},
    //         {"role": "user", "content": endLine.toString()},
    //         {"role": "user", "content": selectedNLScript}
    //     ];

    //     // if (isDev) {
    //     try {
    //         const customPrompt = await promptTemplateManager.getProcessedTemplate('transferSelectedScriptFromNlToCode', {
    //             taskName,
    //             selectedNLScript,
    //             NLScript,
    //             pythonScript,
    //             startLine,
    //             endLine,
    //             languageCode
    //         });

    //         if (customPrompt) {
    //             console.log('Using custom template for transferSelectedScriptFromNlToCode');
    //             messages = [{"role": "user", "content": customPrompt}];
    //         }
    //     } catch (error) {
    //         console.warn('Failed to load custom template, using default:', error);
    //     }
    //     // }

    //     return scriptRequest(endpoint, {
    //         method: 'POST',
    //         body: JSON.stringify({
    //             model: "gpt-4o",
    //             messages
    //         })
    //     });
    // },

    /**
     * 生成 workflow
     */
    generateWorkflow: async (conversationHistory, newUserMessage, currentWorkflow, appKnowledge, needTaskName, languageCode = 'zh') => {
        const endpoint = API_ENDPOINTS.GENERATE_WORKFLOW.replace('{language_code}', languageCode);

        // Try to get custom template in development mode
        // const isDev = import.meta.env.DEV;
        let messages = [
            {"role": "user", "content": conversationHistory},
            {"role": "user", "content": newUserMessage},
            {"role": "user", "content": currentWorkflow},
            {"role": "user", "content": appKnowledge},
            {"role": "user", "content": needTaskName},
        ];

        console.log('[api.js] generateWorkflow messages:', messages);
        // appKnowledge = "appKnowledge"  // 调试使用
        try {
            const customPrompt = await jsTemplateManager.getProcessedTemplate('generateWorkflow', {
                conversationHistory,
                newUserMessage,
                currentWorkflow,
                appKnowledge,
                needTaskName,
                languageCode
            });

            if (customPrompt) {
                console.log('Using custom JavaScript template for generateWorkflow');
                console.log('[api.js] customPrompt:', customPrompt);
                messages = [{"role": "user", "content": customPrompt}];
            }
        } catch (error) {
            console.warn('Failed to load custom JavaScript template, using default:', error);
        }

        return workflowRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify({
                model: "gpt-4o",
                messages
            })
        });
    },

    /**
     * 将 workflow 转换为 Python 脚本
     */
    transferWorkflowToCode: async (taskDescription, currentLabeledPythonScript, oldLabeledWorkflow, currentLabeledWorkflow, languageCode = 'zh') => {
        console.log('[api.js] transferWorkflowToCode taskDescription:', typeof taskDescription, taskDescription);
        console.log('[api.js] transferWorkflowToCode currentLabeledPythonScript:', typeof currentLabeledPythonScript, currentLabeledPythonScript);
        console.log('[api.js] transferWorkflowToCode oldLabeledWorkflow:', typeof oldLabeledWorkflow, oldLabeledWorkflow);
        console.log('[api.js] transferWorkflowToCode currentLabeledWorkflow:', typeof currentLabeledWorkflow, currentLabeledWorkflow);
        console.log('[api.js] transferWorkflowToCode languageCode:', typeof languageCode, languageCode);

        const endpoint = API_ENDPOINTS.TRANSFER_WORKFLOW_TO_CODE.replace('{language_code}', languageCode);

        // 初始化默认的 messages
        let messages = [
            {"role": "user", "content": taskDescription},
            {"role": "user", "content": currentLabeledPythonScript},
            {"role": "user", "content": oldLabeledWorkflow},
            {"role": "user", "content": currentLabeledWorkflow},
        ];

        try {
            console.log('[api.js] transferWorkflowToCode Using custom JavaScript template for transferWorkflowToCode START');
            const customPrompt = await jsTemplateManager.getProcessedTemplate('transferWorkflowToCode', {
                taskDescription,
                currentLabeledPythonScript,
                oldLabeledWorkflow,
                currentLabeledWorkflow,
                languageCode
            });
            console.log('[api.js] transferWorkflowToCode customPrompt:', customPrompt);
            console.log('[api.js] transferWorkflowToCode Using custom JavaScript template for transferWorkflowToCode END');

            if (customPrompt) {
                console.log('Using custom JavaScript template for transferWorkflowToCode');
                console.log('[api.js] customPrompt:', customPrompt);
                messages = [{"role": "user", "content": customPrompt}];
            }
        } catch (error) {
            console.warn('Failed to load custom JavaScript template, using default:', error);
        }

        return workflowRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify({
                model: "gpt-4o",
                messages
            })
        });

    },

    /**
     * 将 Python 脚本转换为 workflow
     */
    transferCodeToWorkflow: async (taskDescription, currentLabeledWorkflow, oldLabeledPythonScript, currentPythonScript, languageCode = 'zh') => {
        const endpoint = API_ENDPOINTS.TRANSFER_CODE_TO_WORKFLOW.replace('{language_code}', languageCode);

        // 初始化默认的 messages
        let messages = [
            {"role": "user", "content": taskDescription},
            {"role": "user", "content": currentLabeledWorkflow},
            {"role": "user", "content": oldLabeledPythonScript},
            {"role": "user", "content": currentPythonScript},
        ];

        console.log('[api.js] transferCodeToWorkflow messages:', messages);

        try {
            const customPrompt = await jsTemplateManager.getProcessedTemplate('transferCodeToWorkflow', {
                taskDescription,
                currentLabeledWorkflow,
                oldLabeledPythonScript,
                currentPythonScript,
                languageCode
            });

            if (customPrompt) {
                console.log('Using custom JavaScript template for transferCodeToWorkflow');
                console.log('[api.js] customPrompt:', customPrompt);
                messages = [{"role": "user", "content": customPrompt}];
            }
        } catch (error) {
            console.warn('Failed to load custom JavaScript template, using default:', error);
        }

        return workflowRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify({
                model: "gpt-4o",
                messages
            })
        });
    },

    /**
     * 转换选中的 workflow 为 Python 脚本
     */
    transferSelectedWorkflowToCode: async (taskDescription, currentLabeledWorkflow, currentLabeledPythonScript, selectedLabeledWorkflow, languageCode = 'zh') => {
        const endpoint = API_ENDPOINTS.TRANSFER_SELECTED_WORKFLOW_TO_CODE.replace('{language_code}', languageCode);

        // 初始化默认的 messages
        let messages = [
            {"role": "user", "content": taskDescription},
            {"role": "user", "content": currentLabeledWorkflow},
            {"role": "user", "content": currentLabeledPythonScript},
            {"role": "user", "content": selectedLabeledWorkflow},
        ];

        console.log('[api.js] transferSelectedWorkflowToCode messages:', messages);

        try {
            const customPrompt = await jsTemplateManager.getProcessedTemplate('transferSelectedWorkflowToCode', {
                taskDescription,
                currentLabeledWorkflow,
                currentLabeledPythonScript,
                selectedLabeledWorkflow,
                languageCode
            });

            if (customPrompt) {
                console.log('Using custom JavaScript template for transferSelectedWorkflowToCode');
                console.log('[api.js] customPrompt:', customPrompt);
                messages = [{"role": "user", "content": customPrompt}];
            }
        } catch (error) {
            console.warn('Failed to load custom JavaScript template, using default:', error);
        }

        return workflowRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify({
                model: "gpt-4o",
                messages
            })
        });
    },
}; 

const llmRequest = async (endpoint, options = {}) => {
    let ruyillm_key = '';
    
    try {
        ruyillm_key = await window.electronAPI.getRuyiLLMKeyConfig();
        console.log('llmRequest ruyillm_key:', ruyillm_key);
    } catch (error) {
        console.error('Failed to get RuyiLLM key:', error);
    }

    const url = API_BASE_URL_PROD + endpoint;
    console.log('llmRequest url:', url);
    const token = ruyillm_key;

    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };


    try {
        const response = await fetch(url, {
            headers: headers,
            ...options,
        });

        if (!response.ok) {
            console.log('llmRequest response:', response);
            if (response.status === 403) {
                throw new Error('Insufficient Token');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        console.log('llmRequest response:', response);
        const data = await response.json();
        console.log('llmRequest response.json():', data);
        console.log('llmRequest message.content:', data.choices[0].message.content);
        let content = data.choices[0].message.content;
        return content;

    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
};

export const ruyiModelApi = {
    /**
     * 检查 grounding 能力
     */
    grounding: async (description, image) => {
        const endpoint = API_ENDPOINTS.RUYIMODEL;

        const messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "You are a GUI agent.\nGiven a screenshot and an element description, you need to locate the element.\nIf the element does not exist, return <|box_start|>null null null null<|box_end|>.\nOutput Format:\n<|box_start|>x1, y1, x2, y2<|box_end|>\nDescription: " + description
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": image}
                    }
                ]
            },
        ];
        return llmRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify({
                "messages": messages,
                "model": "ruyillm",
            }),
            verify: false
        });
    },

    /**
     * 枚举图片中的元素
     */
    enumerate: async (description, image) => {
        const endpoint = API_ENDPOINTS.RUYIMODEL;
        const messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "You are a GUI agent.\nGiven a screenshot, a general description of a set of elements, and a list of matched elements, you need to return the description of elements that are not included in the list.\nOutput Format:\n[description1, description2, ...]\nGeneral description: " + description + "\nExisting elements: []\nScreenshot: "
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": image}
                    }
                ]
            },
        ];

        return llmRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify({
                "messages": messages,
                "model": "ruyillm",
            }),
            verify: false
        });
    },

    /**
     * 检查图片是否符合描述
     */
    check: async (condition, image) => {
        const endpoint = API_ENDPOINTS.RUYIMODEL;

        const messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "You are a GUI agent.\nGiven a screenshot and several descriptions, you need to check if the descriptions are correct.\nOutput Format (A list of boolean values):\nTrue/False, True/False, ...\nDescriptions: " + condition + "\nScreenshot: "
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": image}
                    }
                ]
            },
        ];

        return llmRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify({
                "messages": messages,
                "model": "ruyillm",
            }),
            verify: false
        });
    },

    /**
     * 检查 ensure 模式是否达到目标
     */
    ensure: async (description, image) => {
        const endpoint = API_ENDPOINTS.RUYIMODEL;
        
        const messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "You are a GUI agent.\nGiven a screenshot and a description of the target GUI state, you need to return the bbox to click.\nIf the current GUI state is already the target state, return <|box_start|>null null null null<|box_end|>.\nOutput Format:\n<|box_start|>x1, y1, x2, y2<|box_end|>\nDescription: " + description + "\nScreenshot: "
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": image}
                    }
                ]
            },
        ];

        return llmRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify({
                "messages": messages,
                "model": "ruyillm",
            }),
            verify: false
        });
    },

    describe: async (x, y, image) => {
        const endpoint = API_ENDPOINTS.RUYIMODEL;

        const messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "You are a GUI agent.\nGiven a screenshot and a coordinate, you need to describe the element at the coordinate.\nOutput Format:\nA description of the element at the coordinate.\nCoordinate: <|box_start|>" + x + " " + y + "<|box_end|>\nScreenshot: \n"
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": image}
                    }
                ]
            },
        ]

        return llmRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify({
                "messages": messages,
                "model": "ruyillm",
            }),
            verify: false
        });
    }
};