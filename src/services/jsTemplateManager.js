/**
 * JavaScript Template Manager
 * Replaces the Jinja2-based promptTemplateManager with pure JavaScript templating
 * Loads and processes template.js files from user's Desktop or Documents directories
 */

export class JSTemplateManager {
    constructor() {
        // 根据系统平台动态设置模板路径
        this.templatePaths = this.getBasePaths();
        this.templateFileName = 'template.js';
    }

    /**
     * Get the template file paths to search based on platform
     */
    getTemplatePaths() {
        const basePaths = this.getBasePaths();
        return basePaths.map(relativePath => ({
            path: relativePath,
            fullPath: `${relativePath}${this.templateFileName}`
        }));
    }

    /**
     * Get base paths based on platform
     * @returns {string[]} Array of base paths
     */
    getBasePaths() {
        // 检测当前系统平台
        const platform = this.detectPlatform();
        
        if (platform === 'windows') {
            // Windows 系统只使用 Documents 文件夹
            return [
                'Documents/'
            ];
        } else {
            // Mac 系统保持原有的路径形式
            return [
                'Desktop/',
                'Documents/'
            ];
        }
    }

    /**
     * Detect current platform
     * @returns {string} 'windows' or 'mac'
     */
    detectPlatform() {
        // 优先使用 Electron API 检测平台
        if (window.electronAPI && window.electronAPI.platform) {
            return window.electronAPI.platform.startsWith('win') ? 'windows' : 'mac';
        }
        
        // 备用方案：使用 navigator.platform
        const platform = navigator.platform.toLowerCase();
        if (platform.includes('win')) {
            return 'windows';
        } else if (platform.includes('mac')) {
            return 'mac';
        }
        
        // 默认返回 mac（原有行为）
        return 'mac';
    }

    /**
     * Check if JavaScript template is available
     * @returns {Promise<boolean>} True if template is available
     */
    async isTemplateAvailable() {
        try {
            console.log('[JSTemplateManager] Checking if JavaScript template is available');
            
            // Try to load template from electronAPI
            const templateContent = await window.electronAPI.loadJSTemplate();
            
            if (templateContent && templateContent.length > 0) {
                console.log(`[JSTemplateManager] JavaScript template is available, length: ${templateContent.length}`);
                return true;
            } else {
                console.log('[JSTemplateManager] No custom JavaScript template found');
                return false;
            }
        } catch (error) {
            console.warn('[JSTemplateManager] Failed to check JavaScript template availability:', error);
            return false;
        }
    }

    /**
     * Get processed template for a specific mechanism
     * @param {string} mechanism - The template mechanism name
     * @param {Object} variables - Variables for template processing
     * @returns {Promise<string|null>} Processed template or null
     */
    async getProcessedTemplate(mechanism, variables = {}) {
        console.log(`[JSTemplateManager] Getting processed template for mechanism: ${mechanism}`);
        console.log('[JSTemplateManager] Variables:', Object.keys(variables));

        try {
            // 直接调用主进程的模板处理方法，避免 CSP 限制
            const processedTemplate = await window.electronAPI.processJSTemplate(mechanism, variables);
            
            if (processedTemplate && typeof processedTemplate === 'string') {
                console.log(`[JSTemplateManager] Successfully got processed template for ${mechanism}, length: ${processedTemplate.length}`);
                return processedTemplate;
            } else {
                console.log(`[JSTemplateManager] No custom template available for mechanism: ${mechanism}`);
                return null;
            }
        } catch (error) {
            console.error(`[JSTemplateManager] Error getting processed template for ${mechanism}:`, error);
            return null;
        }
    }

    /**
     * Get available template mechanisms from main process
     * @returns {Promise<string[]>} Array of available mechanism names
     */
    async getAvailableMechanisms() {
        try {
            // 检查模板是否可用
            const available = await this.isTemplateAvailable();
            
            if (available) {
                // 返回预期的机制名称列表，这些应该在 template.js 中定义
                return ['generateWorkflow', 'transferWorkflowToCode', 'transferCodeToWorkflow', 'transferSelectedWorkflowToCode'];
            }
            
            return [];
        } catch (error) {
            console.error('[JSTemplateManager] Error getting available mechanisms:', error);
            return [];
        }
    }
}

// Create singleton instance
export const jsTemplateManager = new JSTemplateManager(); 