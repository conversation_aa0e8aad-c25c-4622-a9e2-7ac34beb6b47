const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { BrowserView, session, app } = require('electron');
const fs = require('fs').promises;
const path = require('path');

class BrowserHttpService {
    constructor() {
        this.app = express();
        this.server = null;
        this.browserViews = new Map(); // deviceId -> BrowserView
        this.currentDeviceId = null;
        this.win = null;
        this.port = 18542; // 默认端口
        this.isVisible = true;

        // 窗口焦点状态跟踪
        this.isWindowFocused = true; // 默认假设窗口有焦点

        // 状态持久化文件路径 - 将在start方法中根据环境初始化
        this.stateFile = null;

        // 配置中间件
        this.app.use(cors());
        this.app.use(bodyParser.json());

        // 配置路由
        this._setupRoutes();
    }

    /**
     * 有条件地设置 BrowserView 焦点（仅在主窗口有焦点时）
     * @param {BrowserView} browserView - 要设置焦点的 BrowserView
     * @private
     */
    _conditionallyFocusBrowserView(browserView) {
        if (!browserView || !browserView.webContents) {
            return;
        }

        if (this.isWindowFocused) {
            browserView.webContents.focus();
            console.log('[BrowserHttpService] BrowserView 获得焦点（窗口在前台）');
        } else {
            console.log('[BrowserHttpService] 跳过 BrowserView 焦点设置（窗口在后台）');
        }
    }

    /**
     * 设置 HTTP 路由
     * @private
     */
    _setupRoutes() {
        // 创建 BrowserView
        this.app.post('/browser/create', (req, res) => {
            try {
                const { url, deviceId } = req.body || {};
                
                        // 获取配置的默认首页，如果没有则使用默认值
        const ScrcpyManager = require('./ScrcpyManager');
        const configuredHomepage = ScrcpyManager.getBrowserHomepageConfig();
        const finalUrl = url || configuredHomepage;
                const finalDeviceId = deviceId || `default-browser-${Date.now()}`;

                this.createOrSwitchBrowserView(finalDeviceId, finalUrl);
                res.status(200).send();
            } catch (error) {
                console.error('Error creating browser view:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // 获取 BrowserView 边界
        this.app.post('/browser/command', (req, res) => {
            try {
                const { command, params, deviceId } = req.body;

                if (!command) {
                    return res.status(400).json({ error: 'No command specified' });
                }

                // 如果指定了deviceId，临时切换到该设备执行命令
                const originalDeviceId = this.currentDeviceId;
                if (deviceId && deviceId !== this.currentDeviceId) {
                    console.log(`[BrowserHttpService] 临时切换到设备 ${deviceId} 执行命令 ${command}`);
                    this.currentDeviceId = deviceId;
                }

                this.handleCommand(command, params).then(result => {
                    // 恢复原设备ID
                    if (deviceId && originalDeviceId !== deviceId) {
                        this.currentDeviceId = originalDeviceId;
                        console.log(`[BrowserHttpService] 恢复到原设备 ${originalDeviceId}`);
                    }
                    res.json(result);
                }).catch(error => {
                    // 恢复原设备ID
                    if (deviceId && originalDeviceId !== deviceId) {
                        this.currentDeviceId = originalDeviceId;
                        console.log(`[BrowserHttpService] 恢复到原设备 ${originalDeviceId} (错误情况)`);
                    }
                    console.error('Error executing browser command:', error);
                    res.status(500).json({ error: error.message });
                });
            } catch (error) {
                console.error('Error executing browser command:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // 销毁 BrowserView
        this.app.post('/browser/destroy', (req, res) => {
            try {
                this.destroyBrowserView();
                res.status(200).send();
            } catch (error) {
                console.error('Error destroying browser view:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // 显示 BrowserView
        this.app.post('/browser/show', (req, res) => {
            try {
                this.showBrowserView();
                res.status(200).send();
            } catch (error) {
                console.error('Error showing browser view:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // 隐藏 BrowserView
        this.app.post('/browser/hide', (req, res) => {
            try {
                const { temporary = false } = req.body || {};
                this.hideBrowserView(temporary);
                res.status(200).send();
            } catch (error) {
                console.error('Error hiding browser view:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // 获取保存的设备状态
        this.app.get('/browser/saved-states', (req, res) => {
            try {
                this._loadDeviceState().then(state => {
                    res.json({
                        devices: state.devices || {},
                        lastUpdate: state.lastUpdate
                    });
                }).catch(error => {
                    console.error('Error loading device states:', error);
                    res.status(500).json({ error: error.message });
                });
            } catch (error) {
                console.error('Error getting saved states:', error);
                res.status(500).json({ error: error.message });
            }
        });

        // 获取搜索引擎配置
        this.app.get('/get-search-engine-config', (req, res) => {
            try {
                const ScrcpyManager = require('./ScrcpyManager');
                const searchEngine = ScrcpyManager.getDefaultSearchEngineConfig();
                res.json({ searchEngine: searchEngine });
            } catch (error) {
                console.error('Error getting search engine config:', error);
                res.status(500).json({ error: error.message });
            }
        });
    }

    /**
     * 创建或切换到指定设备的 BrowserView
     * @param {string} deviceId 设备ID
     * @param {string} url 要加载的URL
     */
    createOrSwitchBrowserView(deviceId, url) {
        console.log(`[BrowserHttpService] 请求切换到设备: ${deviceId}, 当前设备: ${this.currentDeviceId}, 可见状态: ${this.isVisible}`);
        
        // 如果当前已经显示的就是目标设备，直接返回
        if (this.currentDeviceId === deviceId && this.isVisible) {
            console.log(`[BrowserHttpService] 设备 ${deviceId} 已经在显示中，跳过切换`);
            return;
        }

        // 隐藏当前显示的 BrowserView（如果不是目标设备）
        if (this.currentDeviceId && this.currentDeviceId !== deviceId && this.browserViews.has(this.currentDeviceId)) {
            console.log(`[BrowserHttpService] 隐藏当前设备: ${this.currentDeviceId}`);
            this.hideCurrentBrowserView();
        }

        // 检查是否已存在该设备的 BrowserView
        if (this.browserViews.has(deviceId)) {
            console.log(`[BrowserHttpService] 切换到已存在的设备: ${deviceId}`);
            this.showBrowserViewForDevice(deviceId);
        } else {
            console.log(`[BrowserHttpService] 为设备 ${deviceId} 创建新的 BrowserView`);
            this.createNewBrowserView(deviceId, url);
        }

        this.currentDeviceId = deviceId;
        console.log(`[BrowserHttpService] 设备切换完成，当前设备: ${this.currentDeviceId}`);
    }

    /**
     * 为指定设备创建新的 BrowserView
     * @param {string} deviceId 设备ID
     * @param {string} url 要加载的URL
     * @param {boolean} shouldShow 是否立即显示（默认true）
     */
    createNewBrowserView(deviceId, url, shouldShow = true) {
        // 为设备创建独立的持久化 session
        const persistentSession = session.fromPartition(`browser-device-${deviceId}`, {
            cache: true
        });

        const browserView = new BrowserView({
            webPreferences: {
                session: persistentSession,
                nodeIntegration: false,
                contextIsolation: true
            }
        });

        if (shouldShow) {
            this.win.addBrowserView(browserView);

            // 有条件地设置 BrowserView 焦点
            this._conditionallyFocusBrowserView(browserView);

            // 设置 BrowserView 的位置和大小
            const initialBounds = this._calculateBrowserViewBounds();
            if (initialBounds) {
                browserView.setBounds(initialBounds);
            }
        }

        // 设置背景色
        browserView.setBackgroundColor('#ffffff');

        // 设置移动设备 User Agent
        const mobileUserAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1';
        browserView.webContents.setUserAgent(mobileUserAgent);

        // 添加事件监听
        this._setupBrowserViewEvents(browserView);

        // 保存到映射中
        this.browserViews.set(deviceId, browserView);

        // 加载 URL
        browserView.webContents.loadURL(url);

        // 如果是显示模式，保存状态
        if (shouldShow) {
            this._saveDeviceState(); // 不等待，异步保存
            
            // 页面加载完成后发送初始导航状态
            browserView.webContents.once('did-finish-load', () => {
                this._sendCurrentNavigationState(browserView);
            });
        }
    }

    /**
     * 显示指定设备的 BrowserView
     * @param {string} deviceId 设备ID
     */
    showBrowserViewForDevice(deviceId) {
        const browserView = this.browserViews.get(deviceId);
        console.log(`[BrowserHttpService] 尝试显示设备 ${deviceId} 的 BrowserView, 存在: ${!!browserView}`);
        
        if (browserView) {
            // 检查窗口是否存在且未销毁
            if (!this.win || this.win.isDestroyed()) {
                console.error(`[BrowserHttpService] 窗口不存在或已销毁，无法显示 BrowserView`);
                return;
            }

            try {
                // 先添加到窗口
                this.win.addBrowserView(browserView);
                console.log(`[BrowserHttpService] 已将设备 ${deviceId} 的 BrowserView 添加到窗口`);

                // 有条件地设置焦点
                this._conditionallyFocusBrowserView(browserView);
                
                // 计算并设置位置和大小
                const bounds = this._calculateBrowserViewBounds();
                console.log(`[BrowserHttpService] 计算的边界:`, bounds);
                
                if (bounds) {
                    browserView.setBounds(bounds);
                    console.log(`[BrowserHttpService] 已设置设备 ${deviceId} 的 BrowserView 边界:`, bounds);
                    
                    // 验证边界是否设置成功
                    const actualBounds = browserView.getBounds();
                    console.log(`[BrowserHttpService] 实际设置的边界:`, actualBounds);
                } else {
                    console.error(`[BrowserHttpService] 无法计算边界，窗口信息可能有误`);
                }
                
                this.isVisible = true;
                console.log(`[BrowserHttpService] 设备 ${deviceId} 的 BrowserView 显示完成`);
                
                // 切换设备时，立即发送当前设备的导航状态给前端
                this._sendCurrentNavigationState(browserView);
            } catch (error) {
                console.error(`[BrowserHttpService] 显示设备 ${deviceId} 的 BrowserView 时出错:`, error);
            }
        } else {
            console.error(`[BrowserHttpService] 设备 ${deviceId} 的 BrowserView 不存在`);
        }
    }

    /**
     * 隐藏当前显示的 BrowserView
     */
    hideCurrentBrowserView() {
        if (this.currentDeviceId && this.browserViews.has(this.currentDeviceId)) {
            const browserView = this.browserViews.get(this.currentDeviceId);
            if (browserView && this.win && !this.win.isDestroyed()) {
                try {
                    this.win.removeBrowserView(browserView);
                } catch (error) {
                    console.warn('Failed to remove browser view:', error.message);
                }
            }
        }
        this.currentDeviceId = null;
        this.isVisible = false;
    }

    /**
     * 发送当前导航状态到前端
     * @private
     */
    _sendCurrentNavigationState(browserView) {
        if (!browserView || !this.win) return;
        
        try {
            const navigationState = {
                canGoBack: browserView.webContents.canGoBack(),
                canGoForward: browserView.webContents.canGoForward(),
                url: browserView.webContents.getURL()
            };
            
            this.win.webContents.send('browser-navigation-state', navigationState);
            console.log(`[BrowserHttpService] 发送导航状态:`, navigationState);
        } catch (error) {
            console.warn('Failed to send navigation state:', error.message);
        }
    }

    /**
     * 设置 BrowserView 事件监听
     * @private
     */
    _setupBrowserViewEvents(browserView) {
        if (!browserView || !this.win) return;

        browserView.webContents.on('did-start-loading', () => {
            this.win.webContents.send('browser-loading-state', { isLoading: true });
        });

        browserView.webContents.on('did-stop-loading', () => {
            this.win.webContents.send('browser-loading-state', { isLoading: false });
        });

        browserView.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
            this.win.webContents.send('browser-load-error', {
                errorCode,
                errorDescription
            });
        });

        browserView.webContents.on('did-navigate', (event, url) => {
            this.win.webContents.send('browser-navigation-state', {
                canGoBack: browserView.webContents.canGoBack(),
                canGoForward: browserView.webContents.canGoForward(),
                url
            });
            
            // URL变化时保存状态
            this._saveDeviceState();
        });

        browserView.webContents.on('did-navigate-in-page', (event, url) => {
            this.win.webContents.send('browser-navigation-state', {
                canGoBack: browserView.webContents.canGoBack(),
                canGoForward: browserView.webContents.canGoForward(),
                url
            });
            
            // URL变化时保存状态
            this._saveDeviceState();
        });

        // 注入移动设备视口设置
        browserView.webContents.on('did-finish-load', () => {
            browserView.webContents.executeJavaScript(`
                if (!document.querySelector('meta[name="viewport"]')) {
                    const meta = document.createElement('meta');
                    meta.name = 'viewport';
                    meta.content = 'width=1152, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
                    document.head.appendChild(meta);
                }
            `);
        });
    }

    /**
     * 销毁所有 BrowserView
     */
    destroyBrowserView() {
        this.browserViews.forEach((browserView, deviceId) => {
            if (browserView && this.win && !this.win.isDestroyed()) {
                try {
                    this.win.removeBrowserView(browserView);
                } catch (error) {
                    console.warn(`Failed to remove browser view for device ${deviceId}:`, error.message);
                }
            }
        });
        this.browserViews.clear();
        this.currentDeviceId = null;
    }

    // 保持原有的兼容性方法
    get browserView() {
        return this.currentDeviceId ? this.browserViews.get(this.currentDeviceId) : null;
    }

    /**
     * 启动 HTTP 服务器
     * @param {number} port 服务器端口
     * @param {BrowserWindow} win Electron 主窗口
     */
    /**
     * 获取状态文件的正确路径
     * @private
     */
    _getStateFilePath() {
        // 判断是否为开发环境
        const isDev = !app.isPackaged;
        
        if (isDev) {
            // 开发环境：保存到项目根目录
            return path.join(process.cwd(), 'browser-devices-state.json');
        } else {
            // 生产环境：保存到用户数据目录
            const userDataPath = app.getPath('userData');
            return path.join(userDataPath, 'browser-devices-state.json');
        }
    }

    start(port, win) {
        if (this.server) {
            console.warn('HTTP server is already running');
            return;
        }

        try {
            this.port = port || this.port;
            console.log(`[BrowserHttpService] 使用端口: ${this.port}`);
            this.win = win;

            // 初始化状态文件路径
            this.stateFile = this._getStateFilePath();
            console.log(`[BrowserHttpService] 状态文件路径: ${this.stateFile}`);

            // 添加窗口大小变化的监听
            win.on('resize', () => {
                // 只有当前有活跃设备、BrowserView可见且真正添加到窗口中时才调整边界
                if (this.currentDeviceId && this.isVisible && this.browserViews.has(this.currentDeviceId)) {
                    const browserView = this.browserViews.get(this.currentDeviceId);
                    // 检查BrowserView是否真的在窗口中
                    const browserViews = this.win.getBrowserViews();
                    if (browserView && browserViews.includes(browserView)) {
                        const newBounds = this._calculateBrowserViewBounds();
                        if (newBounds) {
                            browserView.setBounds(newBounds);
                        }
                    }
                }
            });

            // 添加窗口焦点状态监听
            win.on('focus', () => {
                this.isWindowFocused = true;
                console.log('[BrowserHttpService] 窗口获得焦点');
            });

            win.on('blur', () => {
                this.isWindowFocused = false;
                console.log('[BrowserHttpService] 窗口失去焦点');
            });

            // 初始化窗口焦点状态
            this.isWindowFocused = win.isFocused();
            console.log(`[BrowserHttpService] 初始窗口焦点状态: ${this.isWindowFocused}`);

            this.server = this.app.listen(this.port, () => {
                console.log(`[BrowserHttpService] HTTP服务器已在端口 ${this.port} 上启动`);
                
                // 服务器启动后，恢复之前的设备状态
                setTimeout(() => {
                    this.restoreDeviceStates();
                }, 1000); // 延迟1秒确保窗口完全初始化
            }).on('error', (err) => {
                console.error(`[BrowserHttpService] 启动HTTP服务器失败: ${err.message}`);
                if (err.code === 'EADDRINUSE') {
                    console.error(`[BrowserHttpService] 端口 ${this.port} 已被占用，请尝试其他端口`);
                }
            });
        } catch (error) {
            console.error(`[BrowserHttpService] 启动HTTP服务器时发生错误: ${error.message}`);
        }
    }

    /**
     * 停止 HTTP 服务器
     */
    stop() {
        if (this.server) {
            // 先保存状态再关闭
            this._saveDeviceState();
            this.server.close();
            this.server = null;
            this.destroyBrowserView();
            console.log('Browser HTTP server stopped');
        }
    }

    /**
     * 计算浏览器视图的正确位置和尺寸
     * @private
     */
    _calculateBrowserViewBounds() {
        if (!this.win) {
            console.error(`[BrowserHttpService] 窗口对象不存在，无法计算边界`);
            return null;
        }
        
        const bounds = this.win.getBounds();
        console.log(`[BrowserHttpService] 窗口边界:`, bounds);
        
        const rightPanelWidth = Math.floor(bounds.width * 0.3);
        const headerHeight = 100;
        const cardHeaderHeight = 48;
        const totalTopMargin = headerHeight + cardHeaderHeight;
        const padding = 16;

        // 可用空间
        const availableWidth = rightPanelWidth - (padding * 2);
        const availableHeight = bounds.height - totalTopMargin - (padding * 2);
        
        console.log(`[BrowserHttpService] 右侧面板宽度: ${rightPanelWidth}, 可用空间: ${availableWidth}x${availableHeight}`);
        
        // 固定宽高比 (手机屏幕比例，高:宽 = 2400:1080)
        const aspectRatio = 2400 / 1080;
        
        let targetWidth, targetHeight;
        
        // 计算基于高度的宽度
        const widthBasedOnHeight = Math.floor(availableHeight / aspectRatio);
        
        // 计算基于宽度的高度
        const heightBasedOnWidth = Math.floor(availableWidth * aspectRatio);
        
        // 选择合适的尺寸，确保不超出可用空间并保持宽高比
        if (heightBasedOnWidth <= availableHeight) {
            // 如果基于宽度计算的高度不超过可用高度，使用完整宽度
            targetWidth = availableWidth;
            targetHeight = heightBasedOnWidth;
        } else {
            // 否则，基于可用高度计算宽度
            targetWidth = widthBasedOnHeight;
            targetHeight = availableHeight;
        }
        
        // 确保尺寸为整数
        targetWidth = Math.floor(targetWidth);
        targetHeight = Math.floor(targetHeight);
        
        // 计算在右侧面板中的居中位置
        const centerX = bounds.width - rightPanelWidth / 2;
        const x = Math.floor(centerX - targetWidth / 2);
        
        const result = {
            x: x,
            y: totalTopMargin + padding,
            width: targetWidth,
            height: targetHeight
        };
        
        console.log(`[BrowserHttpService] 计算BrowserView尺寸: ${targetWidth}x${targetHeight}, 位置: (${x}, ${totalTopMargin + padding}), 宽高比: ${(targetHeight/targetWidth).toFixed(2)}`);

        // 验证计算结果是否合理
        if (result.width <= 0 || result.height <= 0) {
            console.error(`[BrowserHttpService] 计算的尺寸无效:`, result);
            return null;
        }
        
        if (result.x < 0 || result.y < 0) {
            console.warn(`[BrowserHttpService] 计算的位置可能有问题:`, result);
        }

        return result;
    }

    /**
     * 显示 BrowserView
     */
    showBrowserView() {
        // 如果当前有设备ID且存在对应的BrowserView
        if (this.currentDeviceId && this.browserViews.has(this.currentDeviceId)) {
            const browserView = this.browserViews.get(this.currentDeviceId);
            if (browserView && this.win && !this.win.isDestroyed()) {
                // 检查BrowserView是否已经在窗口中
                const existingViews = this.win.getBrowserViews();
                if (!existingViews.includes(browserView)) {
                    this.win.addBrowserView(browserView);
                }
                
                this.isVisible = true;
                // 重新计算正确的位置和尺寸
                const newBounds = this._calculateBrowserViewBounds();
                if (newBounds) {
                    browserView.setBounds(newBounds);
                }
                
                // 有条件地设置BrowserView焦点
                this._conditionallyFocusBrowserView(browserView);
                console.log(`[BrowserHttpService] 显示设备 ${this.currentDeviceId} 的 BrowserView`);
            }
        } else {
            console.warn(`[BrowserHttpService] 无法显示BrowserView: currentDeviceId=${this.currentDeviceId}, 存在的设备=${Array.from(this.browserViews.keys()).join(', ')}`);
        }
    }

    /**
     * 隐藏 BrowserView
     * @param {boolean} temporary - 是否为临时隐藏（如模态框遮挡），false表示设备切换
     */
    hideBrowserView(temporary = false) {
        if (this.currentDeviceId && this.browserViews.has(this.currentDeviceId)) {
            const browserView = this.browserViews.get(this.currentDeviceId);
            if (browserView && this.win && !this.win.isDestroyed()) {
                this.isVisible = false;
                try {
                    // 使用removeBrowserView而不是setBounds，更可靠地隐藏
                    this.win.removeBrowserView(browserView);
                    console.log(`[BrowserHttpService] 隐藏设备 ${this.currentDeviceId} 的 BrowserView (临时: ${temporary})`);
                } catch (error) {
                    console.warn(`Failed to hide browser view for device ${this.currentDeviceId}:`, error.message);
                }
            }
        }
        
        // 只有在非临时隐藏时才重置currentDeviceId（即真正的设备切换）
        if (!temporary) {
            console.log(`[BrowserHttpService] 重置当前设备ID，从 ${this.currentDeviceId} 到 null`);
            this.currentDeviceId = null;
        } else {
            console.log(`[BrowserHttpService] 临时隐藏，保留当前设备ID: ${this.currentDeviceId}`);
        }
    }

    async handleCommand(command, params) {
        // switchDevice和removeDeviceState命令允许在没有BrowserView的情况下执行
        if (command !== 'switchDevice' && command !== 'removeDeviceState' && !this.browserView) {
            throw new Error(`${command} Browser view not initialized`);
        }

        switch (command) {
            case 'getBounds':
                const bounds = this.browserView.getBounds();
                return bounds;

            case 'capturePage':
                const image = await this.browserView.webContents.capturePage();
                const dataUrl = image.toDataURL();
                return { data: dataUrl };

            case 'keyPress':
                const key = params?.key;
                if (!key) {
                    throw new Error('No key specified');
                }
                this.browserView.webContents.sendInputEvent({
                    type: 'keyDown',
                    keyCode: key
                });
                this.browserView.webContents.sendInputEvent({
                    type: 'keyUp',
                    keyCode: key
                });
                return { status: 'success' };

            case 'destroy':
                this.destroyBrowserView();
                return { status: 'success' };

            case 'loadURL':
                const loadUrl = params?.url;
                if (!loadUrl) {
                    throw new Error('No URL specified');
                }
                this.browserView.webContents.loadURL(loadUrl);
                return { status: 'success' };

            case 'goBack':
                if (this.browserView.webContents.canGoBack()) {
                    this.browserView.webContents.goBack();
                    return { status: 'success' };
                }
                return { status: 'error', message: 'Cannot go back' };

            case 'goForward':
                if (this.browserView.webContents.canGoForward()) {
                    this.browserView.webContents.goForward();
                    return { status: 'success' };
                }
                return { status: 'error', message: 'Cannot go forward' };

            case 'reload':
                this.browserView.webContents.reload();
                return { status: 'success' };

            case 'showHighlight': {
                const { x, y, radius = 10, duration = 1000, color = '#ff0000' } = params;
                // 注入高亮效果的 CSS 和 JavaScript，添加错误处理和重试机制
                const maxRetries = 3;
                let retryCount = 0;
                let success = false;

                while (retryCount < maxRetries && !success) {
                    try {
                        await this.browserView.webContents.executeJavaScript(`
                            try {
                                // 获取正确的 document 对象
                                const doc = document.body ? document : window.frames[0]?.document;
                                if (!doc || !doc.body) {
                                    throw new Error('Document body not available');
                                }

                                // 移除旧的高亮元素
                                const oldHighlight = doc.getElementById('ruyi-click-highlight');
                                if (oldHighlight) {
                                    oldHighlight.remove();
                                }

                                // 解析颜色并创建半透明背景色
                                const color = '${color}';
                                let bgColor, borderColor;
                                if (color.startsWith('#')) {
                                    // 十六进制颜色转换为 rgba
                                    const r = parseInt(color.slice(1, 3), 16);
                                    const g = parseInt(color.slice(3, 5), 16);
                                    const b = parseInt(color.slice(5, 7), 16);
                                    bgColor = \`rgba(\${r}, \${g}, \${b}, 0.3)\`;
                                    borderColor = \`rgba(\${r}, \${g}, \${b}, 0.5)\`;
                                } else {
                                    // 假设是 rgb 格式
                                    bgColor = color.replace(')', ', 0.3)').replace('rgb', 'rgba');
                                    borderColor = color.replace(')', ', 0.5)').replace('rgb', 'rgba');
                                }

                                // 创建新的高亮元素
                                const highlight = doc.createElement('div');
                                highlight.id = 'ruyi-click-highlight';
                                highlight.style.position = 'fixed';  // 使用 fixed 而不是 absolute
                                highlight.style.left = '${x - radius}px';
                                highlight.style.top = '${y - radius}px';
                                highlight.style.width = '${radius * 2}px';
                                highlight.style.height = '${radius * 2}px';
                                highlight.style.borderRadius = '50%';
                                highlight.style.backgroundColor = bgColor;
                                highlight.style.border = \`2px solid \${borderColor}\`;
                                highlight.style.pointerEvents = 'none';
                                highlight.style.zIndex = '9999';
                                highlight.style.transition = 'opacity 0.3s ease-out';
                                
                                // 添加脉冲动画
                                highlight.style.animation = 'ruyiPulse 0.6s ease-in-out infinite alternate';
                                
                                // 添加动画样式（如果不存在）
                                if (!doc.getElementById('ruyi-highlight-style')) {
                                    const style = doc.createElement('style');
                                    style.id = 'ruyi-highlight-style';
                                    style.textContent = \`
                                        @keyframes ruyiPulse {
                                            0% { transform: scale(1); opacity: 0.7; }
                                            100% { transform: scale(1.1); opacity: 1; }
                                        }
                                    \`;
                                    doc.head.appendChild(style);
                                }
                                
                                doc.body.appendChild(highlight);

                                // 设置淡出动画
                                setTimeout(() => {
                                    highlight.style.opacity = '0';
                                    setTimeout(() => {
                                        highlight.remove();
                                    }, 300);
                                }, ${duration});

                                true;  // 返回 true 表示成功
                            } catch (error) {
                                console.error('Highlight error:', error);
                                false;  // 返回 false 表示失败
                            }
                        `);
                        success = true;
                    } catch (error) {
                        console.error(`Highlight attempt ${retryCount + 1} failed:`, error);
                        retryCount++;
                        if (retryCount < maxRetries) {
                            await new Promise(resolve => setTimeout(resolve, 100));  // 等待 100ms 后重试
                        }
                    }
                }

                // 即使高亮失败，也继续执行点击操作
                return { status: 'success' };
            }

            case 'longTouch': {
                const { x, y, duration = 1000 } = params;

                // 有条件地设置 BrowserView 焦点
                this._conditionallyFocusBrowserView(this.browserView);
                await new Promise(resolve => setTimeout(resolve, 50));
                
                // 显示高亮效果
                await this.handleCommand('showHighlight', { x, y, duration: 200 });

                // 使用 Electron 的原生输入事件，直接使用传入的坐标
                this.browserView.webContents.sendInputEvent({
                    type: 'mouseDown',
                    x: x,
                    y: y,
                    button: 'left',
                    clickCount: 1
                });

                // 等待指定的持续时间
                await new Promise(resolve => setTimeout(resolve, duration));

                // 发送 mouseUp 事件
                this.browserView.webContents.sendInputEvent({
                    type: 'mouseUp',
                    x: x,
                    y: y,
                    button: 'left',
                    clickCount: 1
                });

                // 等待一小段时间让焦点事件处理完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 检查焦点状态
                const focusInfo = await this.handleCommand('checkFocus');

                return { 
                    status: 'success',
                    focusInfo: focusInfo
                };
            }

            case 'click': {
                const { x, y, duration = 200 } = params;

                // 有条件地设置 BrowserView 焦点
                this._conditionallyFocusBrowserView(this.browserView);
                await new Promise(resolve => setTimeout(resolve, 50));
                
                // 显示高亮效果
                await this.handleCommand('showHighlight', { x, y, duration: 200 });

                // 使用 Electron 的原生输入事件，直接使用传入的坐标
                this.browserView.webContents.sendInputEvent({
                    type: 'mouseDown',
                    x: x,
                    y: y,
                    button: 'left',
                    clickCount: 1
                });

                // 等待指定的持续时间
                await new Promise(resolve => setTimeout(resolve, duration));

                // 发送 mouseUp 事件
                this.browserView.webContents.sendInputEvent({
                    type: 'mouseUp',
                    x: x,
                    y: y,
                    button: 'left',
                    clickCount: 1
                });

                // 等待一小段时间让焦点事件处理完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 检查焦点状态
                const focusInfo = await this.handleCommand('checkFocus');

                return { 
                    status: 'success',
                    focusInfo: focusInfo
                };
            }

            case 'checkFocus': {
                // 检查当前焦点状态
                const focusInfo = await this.browserView.webContents.executeJavaScript(`
                    (() => {
                        try {
                            const activeElement = document.activeElement;
                            const hasFocus = document.hasFocus();
                            
                            let elementInfo = {
                                tagName: activeElement ? activeElement.tagName : null,
                                type: activeElement ? activeElement.type : null,
                                id: activeElement ? activeElement.id : null,
                                className: activeElement ? activeElement.className : null,
                                contentEditable: activeElement ? activeElement.contentEditable : null,
                                isInput: false,
                                canInput: false,
                                value: null,
                                placeholder: activeElement ? activeElement.placeholder : null,
                                // 添加更多焦点相关信息
                                matches: null,
                                selectionStart: null,
                                selectionEnd: null,
                                focused: false
                            };

                            // 判断是否是可输入元素
                            if (activeElement) {
                                const isInputElement = activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA';
                                const isContentEditable = activeElement.contentEditable === 'true';
                                const isSelectElement = activeElement.tagName === 'SELECT';
                                
                                elementInfo.isInput = isInputElement || isContentEditable;
                                elementInfo.canInput = (isInputElement || isContentEditable) && !activeElement.disabled && !activeElement.readOnly;
                                
                                // 获取当前值
                                if (isInputElement || isSelectElement) {
                                    elementInfo.value = activeElement.value;
                                    // 检查选择状态（这是判断真正焦点的好方法）
                                    try {
                                        elementInfo.selectionStart = activeElement.selectionStart;
                                        elementInfo.selectionEnd = activeElement.selectionEnd;
                                    } catch (e) {
                                        // 某些输入类型不支持 selection
                                    }
                                } else if (isContentEditable) {
                                    elementInfo.value = activeElement.textContent || activeElement.innerText;
                                }
                                
                                // 检查是否真正获得焦点（通过 :focus 伪类）
                                elementInfo.matches = activeElement.matches(':focus');
                                
                                // 尝试检查是否可以接收键盘输入
                                elementInfo.focused = activeElement === document.activeElement && 
                                                    (activeElement.matches(':focus') || 
                                                     activeElement.tagName === 'INPUT' || 
                                                     activeElement.tagName === 'TEXTAREA');
                            }

                            return {
                                hasFocus: hasFocus,
                                activeElement: elementInfo,
                                timestamp: Date.now(),
                                // 添加更多调试信息
                                windowFocused: window.document.hasFocus(),
                                visibilityState: document.visibilityState,
                                readyState: document.readyState
                            };
                        } catch (error) {
                            return {
                                error: error.message,
                                hasFocus: false,
                                activeElement: null,
                                timestamp: Date.now()
                            };
                        }
                    })()
                `);

                return focusInfo;
            }

            case 'setText': {
                const { text, x, y } = params;

                // 有条件地设置 BrowserView 焦点
                this._conditionallyFocusBrowserView(this.browserView);
                await new Promise(resolve => setTimeout(resolve, 50));
                
                // 先检查焦点状态
                const focusInfo = await this.handleCommand('checkFocus');
                
                // 如果没有焦点元素，但提供了坐标，尝试通过坐标找到元素
                if ((!focusInfo.activeElement || !focusInfo.activeElement.canInput) && x !== undefined && y !== undefined) {
                    const elementResult = await this.browserView.webContents.executeJavaScript(`
                        (() => {
                            try {
                                const element = document.elementFromPoint(${x}, ${y});
                                if (element && (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.contentEditable === 'true')) {
                                    element.focus();
                                    return { success: true, element: element.tagName, id: element.id };
                                }
                                return { success: false, error: 'No input element found at coordinates' };
                            } catch (error) {
                                return { success: false, error: error.message };
                            }
                        })()
                    `);
                    
                    if (!elementResult.success) {
                        return {
                            status: 'error',
                            message: 'No input element is focused or found at coordinates',
                            focusInfo: focusInfo,
                            elementResult: elementResult
                        };
                    }
                    
                    // 等待一下让焦点生效
                    await new Promise(resolve => setTimeout(resolve, 100));
                } else if (!focusInfo.activeElement || !focusInfo.activeElement.canInput) {
                    return {
                        status: 'error',
                        message: 'No input element is focused or element cannot accept input',
                        focusInfo: focusInfo
                    };
                }

                // 设置文本
                const result = await this.browserView.webContents.executeJavaScript(`
                    (() => {
                        try {
                            const activeElement = document.activeElement;
                            if (!activeElement) {
                                return { success: false, error: 'No active element' };
                            }

                            const isInputElement = activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA';
                            const isContentEditable = activeElement.contentEditable === 'true';

                            if (isInputElement) {
                                // 清除现有内容并设置新文本
                                activeElement.value = '${text.replace(/'/g, "\\'")}';
                                
                                // 触发相关事件
                                activeElement.dispatchEvent(new Event('input', { bubbles: true }));
                                activeElement.dispatchEvent(new Event('change', { bubbles: true }));
                                
                                return { 
                                    success: true, 
                                    method: 'value',
                                    finalValue: activeElement.value 
                                };
                            } else if (isContentEditable) {
                                // 对于 contentEditable 元素
                                activeElement.textContent = '${text.replace(/'/g, "\\'")}';
                                
                                // 触发相关事件
                                activeElement.dispatchEvent(new Event('input', { bubbles: true }));
                                
                                return { 
                                    success: true, 
                                    method: 'textContent',
                                    finalValue: activeElement.textContent 
                                };
                            } else {
                                return { 
                                    success: false, 
                                    error: 'Element is not a valid input element' 
                                };
                            }
                        } catch (error) {
                            return { 
                                success: false, 
                                error: error.message 
                            };
                        }
                    })()
                `);

                return {
                    status: result.success ? 'success' : 'error',
                    message: result.error || 'Text set successfully',
                    result: result,
                    focusInfo: focusInfo
                };
            }

            case 'drag': {
                const { startX, startY, endX, endY, duration = 1000 } = params;
                
                if (startX === undefined || startY === undefined || endX === undefined || endY === undefined) {
                    throw new Error('Missing drag coordinates');
                }
                
                // 有条件地设置 BrowserView 焦点
                this._conditionallyFocusBrowserView(this.browserView);
                await new Promise(resolve => setTimeout(resolve, 50));
                
                // 显示起始点高亮效果
                await this.handleCommand('showHighlight', { x: startX, y: startY, duration: 200 });
                
                // 开始拖拽：鼠标按下
                this.browserView.webContents.sendInputEvent({
                    type: 'mouseDown',
                    x: startX,
                    y: startY,
                    button: 'left',
                    clickCount: 1
                });
                
                // 等待一小段时间
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // 计算拖拽路径的中间点（可选：添加平滑拖拽）
                const steps = Math.max(10, Math.floor(duration / 50)); // 每50ms一步
                const stepDuration = duration / steps;
                const deltaX = (endX - startX) / steps;
                const deltaY = (endY - startY) / steps;
                
                // 执行拖拽移动
                for (let i = 1; i <= steps; i++) {
                    const currentX = startX + (deltaX * i);
                    const currentY = startY + (deltaY * i);
                    
                    this.browserView.webContents.sendInputEvent({
                        type: 'mouseMove',
                        x: Math.round(currentX),
                        y: Math.round(currentY)
                    });
                    
                    await new Promise(resolve => setTimeout(resolve, stepDuration));
                }
                
                // 显示结束点高亮效果
                await this.handleCommand('showHighlight', { x: endX, y: endY, duration: 200 });
                
                // 结束拖拽：鼠标释放
                this.browserView.webContents.sendInputEvent({
                    type: 'mouseUp',
                    x: endX,
                    y: endY,
                    button: 'left',
                    clickCount: 1
                });
                
                return { 
                    status: 'success',
                    startPoint: { x: startX, y: startY },
                    endPoint: { x: endX, y: endY },
                    duration: duration,
                    steps: steps
                };
            }

            case 'scroll': {
                const { x, y, deltaX = 0, deltaY = 0, direction, distance = 350, duration = 1000 } = params;
                
                // 如果提供了方向和距离，转换为 delta 值
                let scrollDeltaX = deltaX;
                let scrollDeltaY = deltaY;
                
                if (direction) {
                    switch (direction) {
                        case 'up':
                            scrollDeltaY = -Math.abs(distance);
                            break;
                        case 'down':
                            scrollDeltaY = Math.abs(distance);
                            break;
                        case 'left':
                            scrollDeltaX = -Math.abs(distance);
                            break;
                        case 'right':
                            scrollDeltaX = Math.abs(distance);
                            break;
                        default:
                            throw new Error(`Invalid scroll direction: ${direction}`);
                    }
                }
                
                // 有条件地设置 BrowserView 焦点
                this._conditionallyFocusBrowserView(this.browserView);
                await new Promise(resolve => setTimeout(resolve, 50));
                
                // 显示滚动高亮效果
                const scrollX = x || Math.round(this.browserView.getBounds().width / 2);
                const scrollY = y || Math.round(this.browserView.getBounds().height / 2);
                
                // 显示滚动起始位置的高亮
                await this.handleCommand('showHighlight', { 
                    x: scrollX, 
                    y: scrollY, 
                    duration: 300,
                    color: '#4CAF50',  // 绿色表示滚动起始点
                    radius: 30
                });
                
                // 如果有方向，显示滚动方向指示
                if (direction) {
                    let arrowX = scrollX;
                    let arrowY = scrollY;
                    
                    // 计算箭头指向位置
                    switch (direction) {
                        case 'up':
                            arrowY = Math.max(scrollY - 50, 25);
                            break;
                        case 'down':
                            arrowY = Math.min(scrollY + 50, this.browserView.getBounds().height - 25);
                            break;
                        case 'left':
                            arrowX = Math.max(scrollX - 50, 25);
                            break;
                        case 'right':
                            arrowX = Math.min(scrollX + 50, this.browserView.getBounds().width - 25);
                            break;
                    }
                    
                    // 显示方向指示高亮
                    setTimeout(async () => {
                        await this.handleCommand('showHighlight', { 
                            x: arrowX, 
                            y: arrowY, 
                            duration: 200,
                            color: '#FF9800',  // 橙色表示滚动方向
                            radius: 20
                        });
                    }, 150);
                }
                
                // 使用原生滚轮事件
                this.browserView.webContents.sendInputEvent({
                    type: 'mouseWheel',
                    x: scrollX,
                    y: scrollY,
                    deltaX: scrollDeltaX,
                    deltaY: scrollDeltaY,
                    wheelTicksX: scrollDeltaX / 120, // 标准滚轮刻度
                    wheelTicksY: scrollDeltaY / 120,
                    accelerationRatioX: 1.0,
                    accelerationRatioY: 1.0,
                    hasPreciseScrollingDeltas: true,
                    canScroll: true
                });
                
                return { 
                    status: 'success',
                    scrollPosition: { x: scrollX, y: scrollY },
                    delta: { x: scrollDeltaX, y: scrollDeltaY },
                    direction: direction || 'custom'
                };
            }

            case 'appendText': {
                const { text, x, y } = params;
                
                if (!text) {
                    throw new Error('No text provided for append operation');
                }
                
                // 有条件地设置 BrowserView 焦点
                this._conditionallyFocusBrowserView(this.browserView);
                await new Promise(resolve => setTimeout(resolve, 50));
                
                // 复用 setText 的逻辑，但不清空现有文本
                const result = await this.browserView.webContents.executeJavaScript(`
                    (() => {
                        try {
                            const text = '${text.replace(/'/g, "\\'")}';
                            let targetElement = null;
                            let success = false;
                            let error = null;
                            let currentValue = '';
                            let finalValue = '';
                            
                            // 如果提供了坐标，尝试根据坐标找到元素
                            if (${x !== undefined} && ${y !== undefined}) {
                                targetElement = document.elementFromPoint(${x || 0}, ${y || 0});
                                if (targetElement && (targetElement.tagName === 'INPUT' || targetElement.tagName === 'TEXTAREA' || targetElement.contentEditable === 'true')) {
                                    targetElement.focus();
                                }
                            }
                            
                            // 如果没有通过坐标找到，使用当前焦点元素
                            if (!targetElement) {
                                targetElement = document.activeElement;
                            }
                            
                            // 检查元素是否可以输入文本
                            if (targetElement && (targetElement.tagName === 'INPUT' || targetElement.tagName === 'TEXTAREA')) {
                                currentValue = targetElement.value || '';
                                
                                // 追加文本而不是替换
                                targetElement.value = currentValue + text;
                                finalValue = targetElement.value;
                                
                                // 触发输入事件
                                targetElement.dispatchEvent(new Event('input', { bubbles: true }));
                                targetElement.dispatchEvent(new Event('change', { bubbles: true }));
                                
                                success = true;
                            } else if (targetElement && targetElement.contentEditable === 'true') {
                                // 处理可编辑元素
                                currentValue = targetElement.textContent || targetElement.innerText || '';
                                
                                // 追加文本
                                if (targetElement.textContent !== undefined) {
                                    targetElement.textContent = currentValue + text;
                                } else {
                                    targetElement.innerText = currentValue + text;
                                }
                                finalValue = targetElement.textContent || targetElement.innerText || '';
                                
                                // 触发输入事件
                                targetElement.dispatchEvent(new Event('input', { bubbles: true }));
                                
                                success = true;
                            } else {
                                error = 'No suitable input element found or element is not editable';
                            }
                            
                            return {
                                success: success,
                                error: error,
                                elementType: targetElement ? targetElement.tagName : 'none',
                                elementId: targetElement ? targetElement.id : null,
                                currentValue: currentValue,
                                appendedText: text,
                                finalValue: finalValue,
                                coordinates: { x: ${x || 0}, y: ${y || 0} }
                            };
                        } catch (e) {
                            return {
                                success: false,
                                error: e.message,
                                elementType: 'unknown',
                                elementId: null,
                                currentValue: '',
                                appendedText: '${text.replace(/'/g, "\\'")}',
                                finalValue: '',
                                coordinates: { x: ${x || 0}, y: ${y || 0} }
                            };
                        }
                    })();
                `);
                
                // 检查焦点状态
                const focusInfo = await this.handleCommand('checkFocus');
                
                return {
                    status: result.success ? 'success' : 'error',
                    message: result.error || 'Text appended successfully',
                    result: result,
                    focusInfo: focusInfo
                };
            }

            case 'switchDevice': {
                const { deviceId, deviceName } = params;
                
                if (!deviceId) {
                    throw new Error('No deviceId specified for switch operation');
                }
                
                console.log(`[BrowserHttpService] 执行设备切换命令: ${deviceName || 'unnamed'} (${deviceId})`);
                
                            // 使用现有的createOrSwitchBrowserView方法进行设备切换
            // 如果设备不存在，将创建一个新的BrowserView
            const ScrcpyManager = require('./ScrcpyManager');
            const defaultUrl = ScrcpyManager.getBrowserHomepageConfig();
                this.createOrSwitchBrowserView(deviceId, defaultUrl);
                
                // 通知前端更新UI状态
                if (this.win && !this.win.isDestroyed()) {
                    // 构造设备信息，模拟用户选择设备的效果
                    const deviceInfo = {
                        id: deviceId,
                        deviceNumber: deviceName || deviceId,
                        type: 'browser',
                        url: defaultUrl,
                        brand: 'Browser',
                        product: deviceName || `Browser Device ${deviceId}`
                    };
                    
                    console.log(`[BrowserHttpService] 通知前端更新设备状态:`, deviceInfo);
                    
                    // 发送设备切换通知到前端
                    this.win.webContents.send('browser-device-switched', {
                        device: deviceInfo,
                        showDeviceSelect: false  // 隐藏设备选择界面
                    });
                    
                    // 给前端一些时间设置监听器，然后发送当前导航状态
                    setTimeout(() => {
                        const browserView = this.browserViews.get(deviceId);
                        if (browserView) {
                            console.log(`[BrowserHttpService] 延迟发送导航状态给前端`);
                            this._sendCurrentNavigationState(browserView);
                        }
                    }, 100); // 100ms延迟
                }
                
                return {
                    status: 'success',
                    message: `Successfully switched to device: ${deviceName || deviceId}`,
                    deviceId: deviceId,
                    deviceName: deviceName || 'unnamed',
                    currentDeviceId: this.currentDeviceId
                };
            }

            case 'removeDeviceState': {
                const { deviceId } = params;
                
                if (!deviceId) {
                    throw new Error('No deviceId specified for remove operation');
                }
                
                console.log(`[BrowserHttpService] 执行删除设备状态命令: ${deviceId}`);
                
                try {
                    // 从browserViews中移除设备
                    if (this.browserViews.has(deviceId)) {
                        const browserView = this.browserViews.get(deviceId);
                        
                        // 如果当前显示的是这个设备，先隐藏
                        if (this.currentDeviceId === deviceId) {
                            this.hideBrowserView(false);
                            this.currentDeviceId = null;
                        }
                        
                        // 从窗口中移除BrowserView
                        if (this.win && !this.win.isDestroyed()) {
                            this.win.removeBrowserView(browserView);
                        }
                        
                        // 销毁BrowserView
                        browserView.webContents.destroy();
                        
                        // 从Map中移除
                        this.browserViews.delete(deviceId);
                        
                        console.log(`[BrowserHttpService] 已从内存中移除设备: ${deviceId}`);
                    }
                    
                    // 从状态文件中移除设备状态
                    const state = await this._loadDeviceState();
                    if (state.devices && state.devices[deviceId]) {
                        delete state.devices[deviceId];
                        
                        // 保存更新后的状态
                        const stateDir = path.dirname(this.stateFile);
                        try {
                            await fs.access(stateDir);
                        } catch {
                            await fs.mkdir(stateDir, { recursive: true });
                        }
                        
                        await fs.writeFile(this.stateFile, JSON.stringify(state, null, 2), 'utf8');
                        console.log(`[BrowserHttpService] 已从状态文件中移除设备: ${deviceId}`);
                    }
                    
                    return {
                        status: 'success',
                        message: `Successfully removed device state: ${deviceId}`,
                        deviceId: deviceId
                    };
                } catch (error) {
                    console.error(`[BrowserHttpService] 删除设备状态失败: ${deviceId}`, error);
                    return {
                        status: 'error',
                        message: `Failed to remove device state: ${error.message}`,
                        deviceId: deviceId
                    };
                }
            }

            case 'getUITree': {
                const { mode = 'full' } = params;

                try {
                    const content = await this.browserView.webContents.executeJavaScript(`
                        (() => {
                            const mode = '${mode}';

                            try {
                                switch (mode) {
                                    case 'full':
                                        // 返回完整的HTML文档
                                        return document.documentElement.outerHTML;

                                    case 'body':
                                        // 返回body标签内容
                                        return document.body ? document.body.outerHTML : '';

                                    case 'visible':
                                        // 返回可见元素的HTML
                                        const visibleElements = [];
                                        const walker = document.createTreeWalker(
                                            document.body || document.documentElement,
                                            NodeFilter.SHOW_ELEMENT,
                                            {
                                                acceptNode: function(node) {
                                                    const style = window.getComputedStyle(node);
                                                    const rect = node.getBoundingClientRect();

                                                    // 检查元素是否可见
                                                    if (style.display === 'none' ||
                                                        style.visibility === 'hidden' ||
                                                        style.opacity === '0' ||
                                                        rect.width === 0 ||
                                                        rect.height === 0) {
                                                        return NodeFilter.FILTER_REJECT;
                                                    }

                                                    return NodeFilter.FILTER_ACCEPT;
                                                }
                                            }
                                        );

                                        let node;
                                        const visibleHTML = [];
                                        while (node = walker.nextNode()) {
                                            if (node.outerHTML) {
                                                visibleHTML.push(node.outerHTML);
                                            }
                                        }

                                        return visibleHTML.join('\\n');

                                    case 'text':
                                        // 返回纯文本内容
                                        return document.body ? document.body.innerText || document.body.textContent : '';

                                    case 'structured':
                                        // 返回结构化的元素树
                                        function buildElementTree(element, depth = 0) {
                                            if (!element || depth > 10) return null; // 防止过深递归

                                            const info = {
                                                tagName: element.tagName,
                                                id: element.id || null,
                                                className: element.className || null,
                                                text: element.childNodes.length === 1 &&
                                                      element.childNodes[0].nodeType === Node.TEXT_NODE ?
                                                      element.textContent.trim() : null,
                                                attributes: {},
                                                children: []
                                            };

                                            // 收集重要属性
                                            const importantAttrs = ['href', 'src', 'alt', 'title', 'type', 'value', 'placeholder', 'name'];
                                            importantAttrs.forEach(attr => {
                                                if (element.hasAttribute(attr)) {
                                                    info.attributes[attr] = element.getAttribute(attr);
                                                }
                                            });

                                            // 递归处理子元素
                                            for (let child of element.children) {
                                                const childInfo = buildElementTree(child, depth + 1);
                                                if (childInfo) {
                                                    info.children.push(childInfo);
                                                }
                                            }

                                            return info;
                                        }

                                        const tree = buildElementTree(document.body || document.documentElement);
                                        return JSON.stringify(tree, null, 2);

                                    default:
                                        return document.documentElement.outerHTML;
                                }
                            } catch (error) {
                                return 'Error extracting UI tree: ' + error.message;
                            }
                        })()
                    `);

                    return {
                        status: 'success',
                        content: content,
                        mode: mode,
                        timestamp: Date.now()
                    };

                } catch (error) {
                    console.error('Error getting UI tree:', error);
                    return {
                        status: 'error',
                        message: error.message,
                        mode: mode
                    };
                }
            }

            case 'getCurrentURL': {
                try {
                    const currentUrl = this.browserView.webContents.getURL();
                    return {
                        status: 'success',
                        url: currentUrl,
                        timestamp: Date.now()
                    };
                } catch (error) {
                    console.error('Error getting current URL:', error);
                    return {
                        status: 'error',
                        message: error.message,
                        url: null
                    };
                }
            }

            default:
                throw new Error(`Unknown command: ${command}`);
        }
    }

    /**
     * 保存浏览器设备状态到文件
     * @private
     */
    async _saveDeviceState() {
        if (!this.stateFile) {
            console.warn('[BrowserHttpService] 状态文件路径未初始化，跳过保存');
            return;
        }

        try {
            const state = {
                devices: {},
                lastUpdate: Date.now()
            };

            // 保存每个设备的当前URL
            for (const [deviceId, browserView] of this.browserViews) {
                try {
                    const currentUrl = browserView.webContents.getURL();
                    const ScrcpyManager = require('./ScrcpyManager');
                    const defaultHomepage = ScrcpyManager.getBrowserHomepageConfig();
                    state.devices[deviceId] = {
                        currentUrl: currentUrl || defaultHomepage,
                        lastAccessed: Date.now()
                    };
                } catch (error) {
                    console.warn(`Failed to get URL for device ${deviceId}:`, error.message);
                    const ScrcpyManager = require('./ScrcpyManager');
                    const defaultHomepage = ScrcpyManager.getBrowserHomepageConfig();
                    state.devices[deviceId] = {
                        currentUrl: defaultHomepage,
                        lastAccessed: Date.now()
                    };
                }
            }

            // 确保目录存在
            const stateDir = path.dirname(this.stateFile);
            try {
                await fs.access(stateDir);
            } catch {
                await fs.mkdir(stateDir, { recursive: true });
            }

            await fs.writeFile(this.stateFile, JSON.stringify(state, null, 2), 'utf8');
            console.log(`[BrowserHttpService] 已保存 ${Object.keys(state.devices).length} 个设备的状态到: ${this.stateFile}`);
        } catch (error) {
            console.error('[BrowserHttpService] 保存设备状态失败:', error);
        }
    }

    /**
     * 从文件加载浏览器设备状态
     * @private
     */
    async _loadDeviceState() {
        if (!this.stateFile) {
            console.warn('[BrowserHttpService] 状态文件路径未初始化，返回空状态');
            return { devices: {}, lastUpdate: Date.now() };
        }

        try {
            const data = await fs.readFile(this.stateFile, 'utf8');
            const state = JSON.parse(data);
            console.log(`[BrowserHttpService] 从 ${this.stateFile} 加载了 ${Object.keys(state.devices || {}).length} 个设备的状态`);
            return state;
        } catch (error) {
            if (error.code === 'ENOENT') {
                console.log(`[BrowserHttpService] 状态文件 ${this.stateFile} 不存在，将创建新的状态`);
                return { devices: {}, lastUpdate: Date.now() };
            }
            console.error('[BrowserHttpService] 加载设备状态失败:', error);
            return { devices: {}, lastUpdate: Date.now() };
        }
    }

    /**
     * 恢复之前保存的浏览器设备
     */
    async restoreDeviceStates() {
        if (!this.win) {
            console.warn('[BrowserHttpService] 窗口未初始化，无法恢复设备状态');
            return;
        }

        try {
            const state = await this._loadDeviceState();
            const devices = state.devices || {};

            for (const [deviceId, deviceState] of Object.entries(devices)) {
                console.log(`[BrowserHttpService] 恢复设备 ${deviceId}，URL: ${deviceState.currentUrl}`);
                
                // 创建BrowserView但不显示
                this.createNewBrowserView(deviceId, deviceState.currentUrl, false);
            }

            console.log(`[BrowserHttpService] 成功恢复 ${Object.keys(devices).length} 个浏览器设备`);
        } catch (error) {
            console.error('[BrowserHttpService] 恢复设备状态失败:', error);
        }
    }

    /**
     * 批量清空所有浏览器设备状态（用于用户切换时）
     */
    async clearAllDeviceStates() {
        // 清空内存中的 browserViews
        for (const [deviceId, browserView] of this.browserViews) {
            try {
                if (this.win && !this.win.isDestroyed()) {
                    this.win.removeBrowserView(browserView);
                }
                browserView.webContents.destroy();
            } catch {}
        }
        this.browserViews.clear();
        this.currentDeviceId = null;
        // 清空状态文件
        if (this.stateFile) {
            const fs = require('fs').promises;
            try { await fs.unlink(this.stateFile); } catch {}
        }
        console.log('[BrowserHttpService] 已批量清空所有浏览器设备状态');
    }
}

// 导出单例实例
module.exports = new BrowserHttpService(); 