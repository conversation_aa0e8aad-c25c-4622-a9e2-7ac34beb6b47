class FlaskApiService {
  constructor() {
    this.baseUrl = 'http://127.0.0.1:11825';
  }

  static getInstance() {
    if (!FlaskApiService.instance) {
      FlaskApiService.instance = new FlaskApiService();
    }
    return FlaskApiService.instance;
  }

  async init() {
    try {
      const config = await window.electronAPI.getConfig();
      this.baseUrl = `http://127.0.0.1:${config.flask_port}`;
    } catch (error) {
      console.error('获取 Flask 端口配置失败:', error);
    }
  }

  async fetchApi(endpoint, options = {}) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'Accept': '*/*',
          ...options.headers,
        },
      });
      return response;
    } catch (error) {
      console.error(`API 请求失败 (${endpoint}):`, error);
      throw error;
    }
  }

  //   // Script 相关 API
  //   /**
  //    * 生成第一个脚本的 API
  //    * @param taskName 任务名称
  //    * @param taskDescription 任务描述
  //    * @param appLanguage 应用语言
  //    * @returns 
  //    */
  //   async generateFirstScript(taskName, taskDescription, appLanguage) {
  //     const response = await this.fetchApi('/generate_first_script', {
  //       method: 'POST',
  //       body: JSON.stringify({ taskName, taskDescription, appLanguage })
  //     });
  //     return response.json();
  //   }

  //   /**
  //    * 通过 chat 优化 script
  //    * @param message 消息
  //    * @param task 任务名称
  //    * @param codeScript 代码脚本
  //    * @param NLScript 自然语言描述的脚本
  //    * @param question 问题
  //    * @param appLanguage 应用语言
  //    * @returns 
  //    */
  //   async optimizeScriptThroughChat(message, task, codeScript, NLScript, question, appLanguage) {
  //     const response = await this.fetchApi('/chat', {
  //       method: 'POST',
  //       body: JSON.stringify({
  //         message,
  //         task,
  //         codeScript,
  //         NLScript,
  //         question,
  //         appLanguage
  //       })
  //     });
  //     return response.json();
  //   }

  //   /**
  //    * 将 code 版本的 script 转换为自然语言描述的 script
  //    * @param task 任务名称
  //    * @param originalCodeScript 原始 code script
  //    * @param codeScript 新的 code script
  //    * @param NLScript 新的 NL script
  //    * @param appLanguage 应用语言
  //    * @returns 
  //    */
  //   async transferScriptFromCodeToNL(task, originalCodeScript, codeScript, NLScript, appLanguage) {
  //     const response = await this.fetchApi('/transfer_script_from_code_to_NL', {
  //       method: 'POST',
  //       body: JSON.stringify({ task, originalCodeScript, codeScript, NLScript, appLanguage })
  //     });
  //     return response;
  //   }

  //   /**
  //    * 将自然语言描述的 script 转换为 code 版本的 script
  //    * @param task 任务名称
  //    * @param originalNLScript 原始 NL script
  //    * @param NLScript 新的 NL script
  //    * @param codeScript 新的 code script
  //    * @param appLanguage 应用语言
  //    * @returns 
  //    */
  //   async transferScriptFromNLToCode(task, originalNLScript, NLScript, codeScript, appLanguage) {
  //     const response = await this.fetchApi('/transfer_script_from_NL_to_code', {
  //       method: 'POST',
  //       body: JSON.stringify({
  //         task,
  //         originalNLScript,
  //         NLScript,
  //         codeScript,
  //         appLanguage
  //       })
  //     });
  //     return response;
  //   }

  //   /**
  //    * 将选中的自然语言描述的 script 转换为 code 版本的 script
  //    * @param task 任务名称
  //    * @param originalCodeScript 原始 code script
  //    * @param originalNLScript 原始 NL script
  //    * @param selectedNLScript 选中的 NL script
  //    * @param startLine 开始行
  //    * @param endLine 结束行
  //    * @param appLanguage 应用语言
  //    * @returns 
  //    */
  //   async transferSelectedScriptFromNLToCode(task, originalCodeScript, originalNLScript, selectedNLScript, startLine, endLine, appLanguage) {
  //     const response = await this.fetchApi('/transfer_selected_script_from_NL_to_code', {
  //       method: 'POST',
  //       body: JSON.stringify({
  //         task,
  //         originalCodeScript,
  //         originalNLScript,
  //         selectedNLScript,
  //         startLine,
  //         endLine,
  //         appLanguage })
  //     });
  //     return response;
  //   }

  //   // 知识库相关 API
  //   /**
  //    * 设置应用知识库的 API
  //    * @param appKnowledge 应用知识库
  //    * @returns 
  //    */
  //   async setAppKnowledge(appKnowledge) {
  //     const response = await this.fetchApi('/set_app_knowledge', {
  //       method: 'POST',
  //       body: JSON.stringify({ appKnowledge })
  //     });
  //     return response;
  //   }


  //   // Demo 相关 API
  //   /**
  //    * 开始 Demo 的 API
  //    * @param task 任务名称
  //    * @param codeScript 代码脚本
  //    * @param NLScript 自然语言描述的脚本
  //    * @param question 问题
  //    * @param selectedCodeScript 选中的代码脚本
  //    * @param selectedNLScript 选中的自然语言描述的脚本
  //    * @param startLine 开始行
  //    * @param endLine 结束行
  //    * @param appLanguage 应用语言
  //    * @returns 
  //    */
  //   async startDemo(
  //     task, 
  //     appLanguage,
  //     codeScript,
  //     NLScript, 
  //     question = '', 
  //     selectedCodeScript = '', 
  //     selectedNLScript = '',
  //     startLine = -1,
  //     endLine = -1,
  //   ) {
  //     const response = await this.fetchApi('/start_demo', {
  //       method: 'POST',
  //       body: JSON.stringify({
  //         task,
  //         codeScript,
  //         NLScript,
  //         question,
  //         selectedCodeScript,
  //         selectedNLScript,
  //         startLine,
  //         endLine,
  //         appLanguage
  //       })
  //     });
  //     return response;
  //   }

  //   /**
  //    * 结束 Demo 的 API
  //    * @param appLanguage 应用语言
  //    * @returns 
  //    */
  //   async endDemo(appLanguage) {
  //     const response = await this.fetchApi('/end_demo', {
  //       method: 'POST',
  //       body: JSON.stringify({ appLanguage })
  //     });
  //     return response;
  //   }

  // 执行代码相关 API
  /**
   * 执行代码的 API
   * @param task 任务名称
   * @param codeScript 代码脚本
   * @returns 
   */
  async executeCode(task, codeScript, labeledCodeScript, labeledNLScript) {
    const response = await this.fetchApi('/execute', {
      method: 'POST',
      body: JSON.stringify({
        task,
        codeScript,
        codeScriptLabeled: labeledCodeScript,
        NLScriptLabeled: labeledNLScript
      })
    });
    return response;
  }

  // 停止执行相关 API
  /**
   * 停止执行的 API
   * @returns 
   */
  async stopExecute() {
    const response = await this.fetchApi('/stop_execute', {
      method: 'POST'
    });
    return response;
  }

  //   // Action 相关 API
  //   /**
  //    * 发送 Action 的 API
  //    * @param actionData Action 数据
  //    * @returns 
  //    */
  //   async sendAction(actionData) {
  //     const response = await this.fetchApi('/action', {
  //       method: 'POST',
  //       body: JSON.stringify(actionData)
  //     });
  //     return response.json();
  //   }


  // 设备相关 API
  /**
   * 设置设备的 API
   * @param deviceId 设备 ID
   * @param deviceType 设备类型
   * @returns 
   */
  async setDevice(deviceId, deviceType = 'websocket') {
    const response = await this.fetchApi('/set_device', {
      method: 'POST',
      body: JSON.stringify({ 
        device_id: deviceId,
        device_type: deviceType
      })
    });
    return response;
  }

  // Ruyi Client 相关 API
  /**
   * 检查 Ruyi Client 是否已安装的 API
   * @param deviceId 设备 ID
   * @returns 
   */
  async checkRuyiClientInstalled(deviceId) {
    const response = await this.fetchApi('/check_ruyi_client_installed', {
      method: 'POST',
      body: JSON.stringify({ device_id: deviceId })
    });
    return response.json();
  }

  /**
   * 安装 Ruyi Client 的 API
   * @param deviceId 设备 ID
   * @returns 
   */
  async installRuyiClient(deviceId) {
    const response = await this.fetchApi('/install_ruyi_client', {
      method: 'POST',
      body: JSON.stringify({ device_id: deviceId })
    });
    return response;
  }

  /**
   * 启动 Ruyi Client 的 API
   * @param deviceId 设备 ID
   * @returns 
   */
  async startRuyiClient(deviceId) {
    const response = await this.fetchApi('/start_ruyi_client', {
      method: 'POST',
      body: JSON.stringify({ device_id: deviceId })
    });
    return response;
  }

  // Ruyi Client 连接相关 API
  /**
   * 检查 Ruyi Client 连接状态的 API
   * @param deviceId 设备 ID
   * @returns 
   */
  async checkRuyiClientConnected(deviceId) {
    const response = await this.fetchApi('/check_ruyi_client_connected', {
      method: 'POST',
      body: JSON.stringify({ device_id: deviceId })
    });
    return response.json();
  }

  // Ruyi Client 权限相关 API
  /**
   * 检查 Ruyi Client 权限是否已授权的 API
   * @param deviceId 设备 ID
   * @returns 
   */
  async checkRuyiClientAuthorized(deviceId) {
    const response = await this.fetchApi('/check_ruyi_client_authorized', {
      method: 'POST',
      body: JSON.stringify({ device_id: deviceId })
    });
    return response.json();
  }

  /**
   * 检查 Ruyi Client 版本的 API
   * @param deviceId 设备 ID
   * @returns 
   */
  async checkRuyiClientVersion(deviceId) {
    const response = await this.fetchApi('/check_ruyi_client_version', {
      method: 'POST',
      body: JSON.stringify({ device_id: deviceId })
    });
    return response.json();
  }

  /**
   * 检查并自动更新 Ruyi Client 的 API
   * @param deviceId 设备 ID
   * @returns 
   */
  async checkAndUpdateRuyiClient(deviceId) {
    const response = await this.fetchApi('/check_and_update_ruyi_client', {
      method: 'POST',
      body: JSON.stringify({ device_id: deviceId })
    });
    return response.json();
  }

  /**
   * 获取 Ruyi Agent 日志
   * @returns 
   */
  async getRuyiAgentLog() {
    const response = await this.fetchApi('/get_ruyi_agent_log', {
      method: 'POST'
    });
    return response.json();
  }

  /**
   * 获取任务执行状态
   * @returns 
   */
  async getTaskExecutionStatus() {
    const response = await this.fetchApi('/get_task_execution_status', {
      method: 'POST'
    });
    return response.json();
  }

  /**
   * 获取示例任务
   * @returns 
   */
  async getExampleTasks() {
    const response = await this.fetchApi('/get_example_tasks', {
      method: 'POST'
    });
    return response.json();
  }

  /**
   * 获取 VH 信息
   * @returns 
   */
  async getVH() {
    const response = await this.fetchApi('/get_vh', {
      method: 'POST'
    });
    return response.json();
  }

  /**
   * 获取截图
   * @returns 
   */
  async getScreenshot() {
    const response = await this.fetchApi('/get_screenshot', {
      method: 'POST'
    });
    const data = await response.json();
    return {
      ...data,
      device_type: data.device_type || 'unknown'
    };
  }
}

export const flaskApi = FlaskApiService.getInstance();
