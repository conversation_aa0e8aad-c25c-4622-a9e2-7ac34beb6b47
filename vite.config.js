import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
// import vue from '@vitejs/plugin-vue';
// import vuetify from 'vite-plugin-vuetify';

// import Markdown from './plugins/md-loader.js';
// import Binary from './plugins/binary-loader.js';

export default defineConfig({
  plugins: [
    react(),
    // vue(),
    // vuetify({ autoImport: true }),
    // Markdown(),
    // Binary(),
  ],
  base: './',
  root: path.join(__dirname, 'src/renderer'),
  build: {
    outDir: path.join(__dirname, 'dist'),
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: undefined
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    }
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://bit-swimming.cn',

        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('[vite.config.js] proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('[vite.config.js] Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('[vite.config.js] Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        }
      }
    }
  }
});