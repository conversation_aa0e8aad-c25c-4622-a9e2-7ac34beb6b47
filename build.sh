#!/bin/bash
set -e

npm run build

VERSION=$(node -p "require('./package.json').version")

DMG_FILE="Ruyi Studio-${VERSION}-arm64.dmg"
ZIP_FILE="Ruyi Studio-${VERSION}-arm64.zip"

echo "开始处理版本: ${VERSION}"

# 并行执行公证
# 子进程处理 DMG 文件：提交公证，然后进行装订
(
  echo "正在后台提交并装订 ${DMG_FILE}..."
  xcrun notarytool submit "./out/${DMG_FILE}" --keychain-profile "ruyi-credentials" --wait && \
  xcrun stapler staple "./out/${DMG_FILE}"
) &

# 子进程处理 ZIP 文件：仅提交公证
(
  echo "正在后台提交 ${ZIP_FILE}..."
  xcrun notarytool submit "./out/${ZIP_FILE}" --keychain-profile "ruyi-credentials" --wait
) &

# 等待所有后台公证任务完成
echo "正在等待所有公证任务完成..."
wait
echo "公证完成。"

echo "正在上传发布版本..."
SSHPASS='xxx' sshpass -e npm run upload-release

echo "脚本执行完毕。"