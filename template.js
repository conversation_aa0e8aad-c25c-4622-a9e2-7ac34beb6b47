// JavaScript Template for RuyiCloudIDE
// Place this file at ~/Desktop/template.js or ~/Documents/template.js

const templateVariables = {
    ruyiDSLSyntax: `
我将为你介绍一种新的编程语言，叫做Ruyi，这是一种自然语言风格的语言（DSL），用于描述任务执行流程。你需要熟练掌握这种语言，并完成我安排的任务。

# Ruyi语言语法

## 1. 基本语法与概念

Ruyi 的语法类似于自然语言，但遵循一些结构和概念将有助于系统更精确地理解你的意图。

Ruyi 程序由一系列语句组成，通常每行一个语句，描述一个操作或一个步骤，使用祈使句或清晰的陈述句。

### 注释

使用 \`#\` 符号开始一行，表示这一行是注释。注释用于解释代码，系统在执行时会忽略它们。

\`\`\`
# 这是一个注释，系统不会执行这句话
告诉用户 "你好，注释让代码更易懂" # 这也是一个行内注释
\`\`\`

### 变量

在 Ruyi 中，变量用大括号包裹的词语表示。

* **声明（记录）变量**：通常使用 \`记为{变量名}\` 或类似的自然语言方式。

  \`\`\`
  询问AI"目前人工智能领域的头部公司有哪些？"，输出公司列表，记为{公司列表}
  设置变量 {用户姓名} 为 "小明"
  将 {当前城市} 设置为 “北京”
  创建变量 {来访人数}，初始值为 0
  \`\`\`

* **使用变量**：在需要用到存储信息的地方，使用 \`{变量名}\`，即代表将变量的值填入对应的位置。

  \`\`\`
  告诉用户 "你好，{用户姓名}"
  在浏览器中搜索 "{当前城市} 人口数量"，记为 {人口数量}
  告诉用户 "{当前城市} 的人口数量是 {人口数量}。"
  \`\`\`

* 变量名建议使用有意义的词语，方便理解。
* 在有些变量名称比较显然的情形中，可以省略变量名的显式声明，使得代码更为简洁。

  \`\`\`
  询问用户"您目前在哪个城市？"，输出城市名称
  在浏览器中搜索用户所在城市的天气
  将天气信息告知用户
  \`\`\`

### 数据类型

每个变量在系统中存储和传递时，是被理解为不同类型的数据进行实际操作的。不同类型的变量用于不同场景的数据处理，例如数字类型变量用于数据统计、文本类型的变量用于文档操作、对话等。

在 Ruyi 中，你不需要专门关心变量的类型，Ruyi 会根据你的代码自动理解每个变量的类型。当然你也可以在定义变量时补充说明类型，以对整个处理过程更有控制感。例如：

\`\`\`
询问AI "北京的人口数量是多少？"，输出数字，记为{人口数}
询问AI "北京去年的人均GDP是多少？"，输出文本，记为{人均GDP}
告诉用户 "北京的人口数量是 {人口数}，人均GDP为 {人均GDP}。"
\`\`\`

这样你就得到了一个类型是数字的变量 {人口数}、和一个类型是文本的变量 {人均GDP}。

#### 基本数据类型

Ruyi 目前支持下列基本数据类型：

* **文本** (Text)：用引号括起来的字符序列，如 \`"Hello world!"\` 或 \`'你好，世界！'\`。

* **数字** (Number)：整数或小数，如 \`10\` 或 \`3.14\`。

* **布尔** (Boolean)：\`真\` 或 \`假\` (或者 \`是\` / \`否\`)，用于表示条件或状态。

  \`\`\`
  询问用户 "你的年龄是多少？"，结果记为 {年龄}
  判断 {年龄} 是否大于 18，记为 {是否成年} # {是否成年} 会是 真 或 假
  如果 {是否成年} 为真：
      告诉用户 "你已成年。"
  \`\`\`

* **图像** (Image)：代表图片数据。可以通过拍照、读取文件或AI生成等方式获得。

  \`\`\`
  拍照，记为 {产品图片}
  读取图片文件 "logo.png"，记为 {公司Logo}
  询问AI "生成一张卡通猫的图片"，输出图像，记为 {卡通猫图片}
  告诉用户 "这是拍摄的产品图片："
  把{产品图片}发送给用户
  \`\`\`

#### 对象 (Object)

对象是包含多个**属性**的集合，用于表示一个具体的事物或实体。每个属性都有一个名称和一个对应的值（也称为键值对）。属性的值可能是一个基本类型的变量，也可能是另外一个对象。

* **创建对象**：

  \`\`\`
  创建一个对象，名为 {我的汽车}
  设置 {我的汽车} 的 {颜色} 为 "红色"
  设置 {我的汽车} 的 {品牌} 为 "特斯拉"
  设置 {我的汽车} 的 {型号} 为 "Model Y"
  
  # 或者更简洁的方式，AI会理解你的意图
  创建一个对象 {客户}，其属性包括： "姓名" 是 "李明"，"年龄" 是 24，"城市" 是 "上海"
  \`\`\`

* **访问对象属性**：
  可以直接在大括号中使用自然语言访问到属性值，或者使用更接近传统编程的点 \`.\` 操作符。

  \`\`\`
  告诉用户 "我的汽车颜色是 {我的汽车的颜色}"
  如果 {客户的年龄} 大于 18： # 假设{客户}是一个前面定义的对象
      告诉用户 "{客户的姓名}是成年人。"
  
  # 或者使用{变量名.属性名}的方式
  告诉用户 "我的汽车颜色是：{我的汽车.颜色}，品牌是{我的汽车.品牌}，型号是{我的汽车.型号}"
  \`\`\`

#### 列表 (List)

列表是一系列有序的基本类型元素或对象。

\`\`\`
# 创建列表
将列表["苹果", "香蕉", "橙子"]记为 {水果列表}
询问AI："目前人工智能领域的上市公司有哪些？"，输出公司名称列表，记为{公司列表} # {公司列表} 是一个文本列表

# 访问列表元素（AI 会智能理解）
获取 {水果列表} 的第一个，记为{水果1}
告诉用户 "我最喜欢的水果是：{水果1}"
获取 {公司列表} 中元素的数量，记为{公司数量}
告诉用户 "AI领域上市公司大约有 {公司数量} 家。"
\`\`\`

#### 数据表 (Table)

数据表类似于电子表格，包含行和列。**数据表可以看作是由多个结构相同的对象组成的列表**。表中的每一行对应一个对象，每一列对应对象的一个属性。你可以创建数据表、添加、删除、修改数据表中的数据、将数据表从文件导入或导出到文件等。

\`\`\`
# 创建数据表
建立一个空的数据表，包含 "姓名"、"部门"、"工号" 列，记为 {员工信息表}

# 添加一行 (可以理解为添加一个符合结构的对象)
创建一个对象 {新员工A}，包含属性 "姓名"是"张三"，"部门"是"技术部"，"工号"是"T001"
将 {新员工A} 作为新行添加到 {员工信息表}

# 添加一行内容 (更简洁的方式)
将一行数据 {"姓名":"小明", "部门":"市场部", "工号":"M023"} 添加到 {员工信息表}

# 从文件加载数据到数据表
读取表格文件 "外部员工数据.xlsx" 的内容，存入新的数据表 {外部员工表}

# 保存数据表到文件
将{员工信息表}保存到文件，文件名为"员工信息.xlsx"
\`\`\`

#### 数据处理

Ruyi 集成了AI大模型能力，你只需要描述你的处理需求，即可完成各种复杂的数据处理操作。

* **筛选**：

  \`\`\`
  从{公司数据表}中筛选出所有"市值"（单位：亿）大于100的公司记录，结果记为{大型公司表}
  \`\`\`

* **提取**：

  \`\`\`
  获取{公司数据表}中的"国家"列，去重后记为{国家名称列表}
  \`\`\`

* **汇总**：

  \`\`\`
  计算{公司数据表}中所有"国家"属性为"中国"的公司的"市值"总和，记为{中国公司总市值}
  \`\`\`

* **排序**：

  \`\`\`
  将{公司数据表}按"市值"列的值从大到小排序
  \`\`\`

* **合并**：

  \`\`\`
  将{国内销售表}和{海外销售表}两个数据表按"产品ID"列合并，记为{全球销售总表}
  \`\`\`

* **计算**：

  \`\`\`
  将{公司数据表}中的"市值"除以"员工数量"，增加为"人均市值"列
  \`\`\`

对于**图像类数据**，也有很多内置的处理操作可以执行：

\`\`\`
# 例子1
拍照，记为 {原始照片}
调整 {原始照片} 的尺寸为宽度500像素，高度自动，记为 {调整后照片}
将 {调整后照片} 转换为灰度图像，记为 {灰度照片}
询问AI： "{原始照片} 给这个照片配一个10个字以内的标题"，记为 {照片标题}
将 {灰度照片} 保存到文件，文件名是 "{照片标题}.jpg"
\`\`\`

\`\`\`
# 例子2
读取图片文件 "风景.jpg"，记为 {风景照片}
询问AI： "给这张照片 {风景照片} 应用复古滤镜效果"，输出处理后的图像，记为 {复古风景照片}
询问AI： "{风景照片} 给这张照片配诗"，输出文字，记为 {照片配诗}
将 {复古风景照片} 发给用户
告诉用户 "这是你照片的配诗：{照片配诗}"
\`\`\`

### 流程控制

代码默认是一行一行顺序执行的，但你可以通过流程控制语法，改变代码的执行流程。

#### 循环

当需要对一组项目中的每一个重复执行相同操作时，可以使用循环。

一个循环包括循环条件、循环内容两部分。循环条件描述循环的方式、次数等信息，循环内容则是每轮循环中需要重复执行的步骤。

循环内容的代码通常需要缩进（即在每行循环内容代码前面相对于循环条件留一些空格，通常使用四个空格），以表示这些操作是循环内的一部分。Ruyi 的 AI 解释器对于缩进有一定容错性，但良好缩进有助于可读性和准确性。

\`\`\`
# 可以指定循环次数进行循环
创建一个列表，名为{人员名单}
重复执行 5 次：
    告诉用户 "这是第 {循环当前次数} 次循环！" # {循环当前次数} 是一个特殊变量，从1开始
    询问用户 "请输入第{循环当前次数}个人名"，获取用户回答，插入{人员名单}列表
将 {人员名单} 保存到文件，文件名是 "名单"

# 也可以按照时间循环
每隔5分钟，执行：
    告诉用户 "当前时间是{当前时间}，这是第{循环当前次数}次问好！"

# 遍历{数据集合}中的每一个{单个项目变量名}
将列表 {"苹果派", "香蕉船", "橙汁"} 记为 {甜点菜单}
遍历{甜点菜单}中的每一个甜点，记为{当前甜点}
    告诉用户 "我们有售：{当前甜点}"
\`\`\`

#### 条件分支

当程序需要根据不同的情况执行不同的操作时，使用条件分支。

在条件分支中，程序根据条件表达式的判定情况，决定执行哪一条分支。

条件表达式一般是一个判断，例如 \`{市值}小于100亿\`、 \`{医院列表}是空的\` 等，也可以是一个包含布尔值的变量。

* **基本格式**：

  \`\`\`
  如果{条件1}: 
      ... （条件1成立时执行）
  否则如果{条件2}:    # 可选
      ... （条件1不成立，但条件2成立时执行）
  否则:              # 可选
      ... （条件1和条件2都不成立时执行）
  \`\`\`

- **例子：**

  \`\`\`
  # 例子1
  询问用户"请输入你的年龄"，获取用户回答，记为{用户年龄}
  如果 {用户年龄} 小于 18：
      告诉用户 "你是未成年人。"
  否则如果 {用户年龄} 大于等于 18 并且 {用户年龄} 小于 60：
      告诉用户 "你是成年人。"
  否则：
      告诉用户 "你已步入老年。"
  
  # 例子2
  询问AI： "今天天气怎么样？"，输出天气描述，记为 {天气情况}
  如果 {天气情况} 包含 "雨"：
      告诉用户 "出门请记得带伞。"
  否则：
      告诉用户 "今天天气不错！"
  \`\`\`

### 任务分解（函数）

在 Ruyi 中，你可以将一组常用的操作定义为一个"任务"，这类似于传统编程语言中的"函数"或"子程序"。这样做可以使你的主流程更简洁，也方便代码复用。

* **定义任务**：
  使用 \`定义一个任务，名为"{任务名称}"\` 开始。任务可以有输入参数和输出（返回值）。

  \`\`\`
  # 定义一个任务，用于向特定用户问好
  定义一个任务，名为 "定制问候语"
      任务输入： {用户名} (类型为文本)
      将 "你好，{用户名}！欢迎使用如意。" 记为 {问候语文本}
      告诉用户 {问候语文本}
      # 这个任务没有明确的返回值
  
  # 定义一个任务，用于计算两个数的和并返回结果
  定义一个任务，名为 "计算两数之和"
      任务输入： {数字一} (数字类型) 和 {数字二} (数字类型)
      将 {数字一} 加上 {数字二} 的结果记为 {计算结果}
      任务返回 {计算结果} # 任务结束，并输出{计算结果}
  \`\`\`

  任务定义的第一行或紧随其后的几行通常用来描述任务需要的输入参数及其类型（可选，但推荐）。

* **调用任务**：
  使用 \`执行任务 "{任务名称}"\`，并提供必要的输入参数，（可选）接收输出结果。

  \`\`\`
  # 调用问候任务
  执行任务 "定制问候语"，{用户名} 为 "张三"
  
  # 调用计算任务并获取输出
  执行任务 "计算两数之和"，{数字一} 是 10，{数字二} 是 25，输出结果记为 {总和}
  告诉用户 "10 + 25 的结果是：{总和}"
  \`\`\`

  Ruyi解释器会智能匹配输入参数名。如果任务定义中的参数名和调用时提供的参数名不完全一致但意思相近，Ruyi解释器也会尝试理解。

利用函数（任务），你可以将一个复杂的大任务分解成一系列更小、更易于管理和描述的子任务，进而逐个完成。
这个分解任务的能力实际上就是架构和逻辑的能力，需要精心设计，使用变量在流程的不同阶段传递信息，同时保证程序总体结构的精简可读性。

## 2. 使用工具

很多复杂的功能需要依靠强大的工具实现，上面介绍的例子中实际上已经涉及到了一些，本章将详细介绍。

Ruyi支持的工具包括AI大模型、用户交互、浏览器、手机、文件、传感器等，通过搭配使用这些工具，可以实现很多日常工作和生活中的功能。

### 与大模型交互

Ruyi 语言的核心能力之一是与大模型 （如ChatGPT、DeepSeek、Claude、豆包等）进行交互，以获取信息、进行决策或生成内容。

AI大模型具备通用的语言和图像理解能力和生成能力，你可以把它当作一个有问必答的助手，但它的答案具有随机性，还可能不太可靠，需要注意提问方式以及问题边界。大模型的能力一直在变化，所以需要在代码开发过程中体会哪些问题适合用大模型来给出回答，哪些适合人工分解解决。

* **标准格式**：\`询问AI：{问题内容}，输出{期望的输出描述}，记为{输出变量}\`

  * \`问题内容\`：可以是直接的文本、变量，或者多个变量和文本的拼接。
  * \`输出\`：描述你期望 AI 返回什么类型或格式的信息。
  * \`记为\`：将 AI 的输出保存到指定的变量中，方便后续使用。

  \`\`\`
  询问AI："法国的首都是哪里？"，输出城市名称，记为{法国首都}
  告诉用户 "法国首都是：{法国首都}"
  
  # 询问AI的输入可以是变量和文本的拼接，系统会根据输入内容和格式，自动选择合适的模型
  获取当前屏幕截图，记为{当前截图}
  询问AI："{当前截图} 这家公司的主要业务是什么？总结不超过50字"，输出文本，记为{公司业务简介}
  告诉用户 {公司业务简介}
  
  # AI做决策
  询问AI："我手头有{食材列表}，晚餐想吃中餐，请推荐几道菜并给出主要步骤"，输出菜谱列表（每个菜谱包含菜名和步骤列表），记为{菜谱列表}
  遍历 {菜谱列表} 中每个 {菜谱}:
      告诉用户 "菜名：{菜谱.菜名}，制作步骤：\\n{菜谱.制作步骤}"
  \`\`\`

- **自动提问**：当你不想自己撰写给大模型的提问时，你可以给一个模糊的需求，Ruyi解释器会自动生成合适的发给大模型的问题。

  \`\`\`
  询问AI，获取当前界面中的文字语言
  # 上面这行的作用大致等同于：
  #     询问AI："{当前界面} 中的文字的语言是什么？"，输出语言名称，记为"文字语言"
  \`\`\`

### 与用户交互

Ruyi 程序可以与用户收发消息，进行信息交换。

* **获取用户输入**：

  用户的输入可能是文本、图片、选择等等，Ruyi采用相同的语法提示用户获得输入：

  \`\`\`
  # 获取文本输入
  询问用户 "请输入你的名字："，获取用户回答，记为{用户姓名}
  询问用户 "你今年多少岁了？"，获取用户回答，记为{用户年龄}
  
  # 获取文件输入
  询问用户 "请发给我一张你的自拍照"，获取用户文件，记为{用户图片}
  
  # 获取用户选择
  询问用户 "你同意用户协议吗？(是/否)"，获取用户选择，记为{同意协议}
  将列表 {"红色", "蓝色", "绿色"} 记为 {颜色选项}
  询问用户 "你喜欢哪种颜色？请从以下选项中选择：{颜色选项}"，获取用户选择，记为{用户选择的颜色}
  告诉用户 "你选择了 {同意协议} 和 {用户选择的颜色}。"
  \`\`\`

  与询问AI的方式类似，你也可以使用一些模糊的方式与用户交互，当没有给定用引号包裹的具体消息时，Ruyi解释器会智能生成给用户发送的消息内容。例如当你说 \`询问用户获得症状信息\` ，Ruyi解释器会主动设计问题来获取完整的症状信息，并将结果存入一个变量（如 \`{症状信息}\`）中。

  \`\`\`
  # 模糊的用户交互指令
  询问用户获得症状信息  # 效果大致等同于：询问用户"请描述您的症状信息"，获取用户回答，记为{症状信息}
  告知用户当前的时间  # 效果大致等同于：告知用户"当前的时间是 {当前时间}"
  \`\`\`

* **向用户显示信息**：

  \`\`\`
  告诉用户 "处理完成！结果保存在 'output.txt' 文件中。"
  将 {生成的报告} 发送给用户
  将 {分析结果图} 发送给用户 # 假设{分析结果图}是一个图像变量
  \`\`\`

### 操作文件

Ruyi 支持读取和保存文件，包括图片、文档、表格等。文件名建议用引号包裹起来，文件名是相对于当前IDE中工作区的文件路径。

\`\`\`
将文本内容 "这是写入的第一行。\\n这是第二行。" 保存到文档 "我的笔记"
读取文档 "我的笔记" 的全部文本内容，记为{笔记内容}
告诉用户 "笔记内容是：\\n{笔记内容}"

将{公司市值表}保存为表格文件 "公司市值报告"
读取表格文件 "公司市值报告" 中的数据，记为{市值表}

检查文件 "论文初稿" 是否存在
如果文件存在：
    读取文件 "论文初稿" 的内容，记为{论文初稿}
否则：
    告诉用户 "文档不存在。"
\`\`\`

Ruyi 会根据文件名后缀和你的描述来智能处理文件的读写格式。

### 操作设备（手机与浏览器）

浏览器和手机提供了丰富的工具（网页、应用程序、传感器等），能够帮我们获取各种类型的信息，执行各种类型的任务。

在 Ruyi 中，你可以通过编写代码来操纵这些设备来实现任何你想要的功能。

#### 手机（Android）

我们先以手机为例，第一个手机设备名称默认为"手机1"，第二个手机设备名称为"手机2"，以此类推。

在Android手机设备上，你可以选择使用手机中的应用程序（App）和传感器。

**使用App**

通过 Ruyi 代码使用App就好像指挥一个助手完成各种设备操作。举例如下：

\`\`\`
# 可以使用一步一步精确具体的操作指令
在设备 {手机1} 中：  # 指定要执行下列动作的设备，如果不指定，默认在当前预览的设备中执行
    打开 "联系人" 应用
    点击 "搜索联系人"
    在 "搜索框" 中输入 "张"
    点击 "搜索"
    获取当前屏幕的截图，记为 {全屏幕快照}
    将 {全屏幕快照} 保存到文件 "screenshot.png"
    对于当前界面中 "每个联系人" 元素，最多10个，执行：
        点击该元素
        获取 "姓名" 内容，记为 {姓名}
        获取 "电话" 内容，记为 {电话}
        将 {姓名}、{电话} 加入{联系方式列表}
告诉用户"名字包含'张'的朋友联系方式：{联系方式列表}"
\`\`\`

在上面的例子中，采用了精确的、一步一步的操作指令，包括具体的点击位置、输入内容等，这种方式操作设备适合实现精确的控制，但可能难以处理动态变化的情况，需要人工检查每一步操作的正确性。如果出现精确操作指令出错的情况（例如程序点击界面中不存在的内容，或者点击不到你设想的界面元素），就需要改换操作的表述方式。

在更多情形下（尤其是根据用户的任务描述自动生成workflow时），建议采用下面的更精简概括的描述方式（Ruyi的AI解释器会自动把它变成具体的动作）：

\`\`\`
在设备"手机1"中的"联系人"应用中：
    查询名字包含"张"的联系人，获取最多10个，记为{联系人列表}
    对于{联系人列表}中的每个{联系人}：
        导航到"{联系人}的信息"，获取"姓名"、"电话"，加入{联系方式列表}
告诉用户"名字包含'张'的朋友联系方式：{联系方式列表}"
\`\`\`

可以看到，在这种描述方式中，我们不再使用界面元素点击、输入等具体动作，直接用"寻找目标+执行动作"的方式描述每一个操作，其中：
- "寻找目标"可以是导航到某个界面或功能，或者是查询某种类型的数据
- "执行动作"则是目标界面下可以完成的一些具体的功能，例如获取某种数据、修改表单内容等。
这样的编写方式从更高的层面保证了功能逻辑的通用性，而具体操作则交给AI解释器来动态处理。

**使用传感器（相机、麦克风、GPS等）**

Android设备上通常内置很多有用的传感器，通过将这些设备放置在不同的位置，可以实现对不同环境信息的感知。Ruyi 可以操作这些传感器获取环境信息，AI 模型会负责处理具体的硬件访问权限和细节。

* **拍照**：

  \`\`\`
  在设备"手机1"中：
      使用后置摄像头拍照，记为 {会议室照片}
      使用前置摄像头拍照，记为 {自拍照}
  将 {会议室照片} 保存为图片文件 "meeting_room_snapshot.jpg"
  \`\`\`

* **录音**：

  \`\`\`
  在设备"手机1"中，录音10秒，记为 {录音}
  将 {录音} 保存为音频文件 "recording.wav"
  \`\`\`

* **获取地理位置**：

  \`\`\`
  在设备"手机1"中获取地理位置，记为 {当前位置}
  # {当前位置} 是一个包含经度、纬度、海拔、精度等属性的对象
  告诉用户 "你当前的经度是 {当前位置.经度}，纬度是 {当前位置.纬度}"
  \`\`\`

* **其他传感器** (根据设备支持范围而定)：

  \`\`\`
  在设备"手机1"中：
    获取环境光线强度（勒克斯），记为 {光照度数值}
  在设备"手机1"中：
    获取电池电量百分比，记为 {电池电量}
  \`\`\`

  使用这些功能时，请确保Ruyi执行环境（IDE或应用）已获得操作系统授予的相应权限。

#### 浏览器

在 Ruyi 中，使用浏览器的方式与使用手机App类似，只不过操作的对象从手机App变成了网页。同样，有概括和具体两种描述操作的方式。大部分情况下，推荐使用概括的编写方式以保证通用性。

\`\`\`
# 使用精简概括的操作方式
在设备"浏览器1"中：
    搜索"Ruyi编程语言"，获取搜索结果的内容摘要
将内容摘要发送给用户

# 另一个例子
在设备"浏览器1"中：
    导航到"www.unitconverters.net"的质量转换界面，计算1000磅对应的千克数，记为{千克重量}
告诉用户"1000磅等于{千克重量}千克"
\`\`\`

\`\`\`
# 使用精确具体的操作方式
在设备"浏览器1"中：
    打开网址 "https://www.bing.com"
    在 "搜索框" 中输入 "Ruyi编程语言"
    点击 "搜索" 按钮
    等待 5 秒 # 等待页面加载
    点击 "搜索结果中的第一个"
    询问AI，获取当前界面的内容摘要
    将内容摘要发送给用户
\`\`\`

#### 多设备操作

有时你可能需要同时操作多个设备执行同一组动作，要实现这个目标，首先在设备列表中添加所有想要执行操作的设备。然后在代码中就可以用类似于单设备操作的方式操作多个设备，区别仅仅在于如何获取多设备执行的结果。

\`\`\`
在设备"手机1"、"手机2"、"手机3"上执行：  # 下列语句会在多个设备上运行
    打开"联系人"应用
    拍照，记为{照片}
    获取设备屏幕截图，记为{设备截图}
    询问AI："{设备截图}中有什么?"，输出记为{截图描述}
汇总所有设备上的{照片}、{截图描述}，记为{批量执行结果}
# 上面这行是一个多设备操作特有的指令，功能是把多个设备上的运行数据汇总起来
# {批量执行结果}可以理解为一个列表，每个元素对应一个设备上的数据
把 {批量执行结果} 发送给用户
获取{批量执行结果}中所有设备的{截图描述}，记为{截图描述列表}
把 {截图描述列表} 发送给用户

# 多设备操作与单设备操作是兼容的，你仍然可以使用除了{批量执行结果}之外的变量
把{设备截图}发送给用户    # 该变量中保存的是第一个设备（例如"手机1"）上的数据
\`\`\`

## 3. 进阶用法

随着你对 Ruyi 越来越熟悉，你可以开始构建更复杂的自动化流程。

### 后台任务执行

有时你可能希望Ruyi可以同时执行多个任务，而不是一个接一个地等待，这就需要用到后台任务的概念。

要想执行后台任务，首先定义任务，然后使用\`后台执行任务\`语句启动该任务。在后台任务启动之后，可以使用\`等待后台任务\`、\`停止后台任务\`指令进行对后台任务的等待和手动停止。

\`\`\`
# 首先定义希望后台执行的任务
定义一个任务，名为 "收集高校信息"
    任务输入：{高校名称列表}
    创建一个列表，记为{高校信息列表}
    遍历{高校名称列表}中的每一个{高校名称}：
        询问AI："告诉我{高校名称}的国际排名、教师数量"，输出记为{国际排名}、{教师数量}
        将{'高校':{高校名称}, '排名':{国际排名}, '教师数量':{教师数量}}加入{高校信息列表}
    任务返回{高校信息列表}

# 启动后台执行
后台执行任务 "收集高校信息", {高校名称列表}是{"斯坦福", "麻省理工", "哈佛大学"}
告诉用户 "高校信息收集中"  # 由于上一句是后台执行的任务，这条消息会立即发给用户
# 你可以在后台任务执行过程中做任何其他任务，包括启动其他后台任务

# 等待后台任务结束后，可以获取到任务的执行结果
等待任务"收集高校信息"结束，结果记为{高校信息列表}
将{高校信息列表}发送给用户

# 在后台任务执行过程中，也可以随时打断
后台执行任务 "收集高校信息2", {高校名称列表}是{"清华大学", "北京大学"}
等待5秒
中止任务"收集高校信息2"
\`\`\`

### 使用复杂提示词

在与AI交互时，尤其是对于生成复杂内容或执行精细指令的任务，可能需要使用精心设计的、较长的提示词（Prompt）。Ruyi允许你将这些提示词存储在变量中或从外部文件加载，以保持主代码的整洁。

\`\`\`
# 假设存在一个名为 "product_description_generator.txt" 的文件，内容如下：
# ===== 文件: product_description_generator.txt =====
# 作为一名顶级的电商文案专家，请为以下产品撰写一段吸引人的产品描述。
# 产品名称：{产品名}
# 核心卖点：{卖点列表}
# 目标人群：{目标客户描述}
# 字数要求：200-300字
# 风格要求：生动有趣，突出价值
# ===============================================================

加载文件 "product_description_generator.txt" 的内容，记为 {产品描述提问}
在{产品描述提问}中：
    将 {产品名} 设置为 "智能降噪耳机Pro"
    将 {卖点列表} 设置为 {"极致静谧体验", "30小时超长续航", "佩戴舒适"}
    将 {目标客户} 设置为 "经常需要出差和在嘈杂环境中工作的商务人士"

询问AI： {产品描述提问}，输出 {最终产品描述}
告诉用户 "AI生成的产品描述是：\\n{最终产品描述}"
\`\`\`

这种方式使得提示词的管理、版本控制和复用变得更加容易。

### Ruyi 程序编写原则

1. 精确性。通过变量、任务、控制流等结构确保程序是无歧义的。
2. 可读性。程序的语句应该简洁流畅，便于普通用户理解和修改。
3. 可行性。程序应该符合用户的设备和软件环境，确保能够正确执行。
4. 在生成程序的结尾，确保输出一句“告知用户，任务结束了宝子（调试）”。
`,

    ChineseRuyiDSLSyntaxExamples: `## 4. 实战案例

以下摘录几个有代表性的例子，展示Ruyi编程语言在不同场景下的能力。

### 案例一：收集微信最近20位联系人的信息，包括备注、昵称、头像、聊天主要内容、最近朋友圈内容。
\`\`\`
建立一个数据表 {联系人信息表}，包含 "称呼", "昵称", "ID", "头像", "聊天摘要", "最近朋友圈" 列

在设备 "phone1" 中：
    导航到 "微信" 应用的聊天列表页面，对于聊天列表中的每个聊天会话，执行：
        导航到该聊天会话，执行：
            判断当前聊天会话是不是与真人的一对一聊天，如果不是则跳过当前会话
            获取当前聊天会话的联系人，记为{当前联系人}
            获取当前聊天内容，记为{聊天内容}
            询问AI: "请根据聊天内容生成摘要：{聊天内容}", 输出记为 {聊天摘要}
        导航到{当前联系人}的资料页面，获取称呼、昵称、微信ID、头像
        导航到{当前联系人}的朋友圈，获取最近朋友圈内容
        将称呼、昵称、微信ID、头像、聊天摘要、最近朋友圈内容插入联系人信息表
        告知用户："已完成{当前联系人}信息的收集，已收集{联系人信息表.行数}条"
        如果已经收集了20个联系人的信息，跳出循环
        
告诉用户 "已成功收集最近20位联系人的信息，详情请查看文件 {联系人信息表}。"
\`\`\`

### 案例二：将手机1、手机2、手机3中的联系人汇总成一张表格
\`\`\`
定义一个任务，名为 "获取设备联系人列表"，任务输入：{设备名}
    在设备 {设备名} 上执行：
        导航到 "联系人" 应用的联系人列表界面，遍历每位联系人，执行：
            导航到该联系人的资料页面，获取该联系人的姓名、邮箱、电话号码
            在 {当前设备联系人列表} 表格中添加一行数据，包含："设备": {设备名}, "姓名": {姓名}, "邮箱": {邮箱}, "电话号码": {电话号码}
        返回 {当前设备联系人列表}

创建一个空的数据表，记为 {所有联系人列表}
在设备 '手机1'、'手机2'、'手机3' 中：
    告诉用户 "捕获设备 {设备名} 的联系人列表"
    执行任务 "获取设备联系人列表"，输入是 {设备名}，输出结果记为 {当前设备联系人列表}
    将 {当前设备联系人列表} 添加到 {所有联系人列表}

将 {所有联系人列表} 发送给用户
\`\`\`

### 案例三：收集中国历史上的王朝信息，包括朝代、年号、帝王、起止时间、主要事件、以及对应的西方历史朝代和中西方文化交流事件，每个帝王一行
\`\`\`
# 第一步：准备工作，建立一个用于存储最终结果的数据表
建立一个空的数据表，包含 "朝代"、"年号"、"帝王"、"在位时间"、"主要事件"、"同期西方历史"、"中西文化交流事件" 列，记为 {中国历史王朝信息表}

# 第二步：获取中国所有主要朝代的列表
询问AI："请列出中国历史上所有主要的朝代，例如秦、汉、唐、宋、元、明、清等"，输出朝代列表，记为{朝代列表}
告诉用户 "已获取朝代列表，将开始逐一收集详细信息..."

# 第三步：遍历每一个朝代，收集其帝王信息
遍历{朝代列表}中的每一个{当前朝代}：
    告诉用户 "正在收集【{当前朝代}】的信息..."
    # 3.1 获取当前朝代的所有帝王
    询问AI："请列出{当前朝代}的所有帝王姓名列表"，输出帝王姓名列表，记为{帝王列表}

    # 3.2 遍历当前朝代的每一位帝王，查询并记录其详细信息
    遍历{帝王列表}中的每一个{当前帝王}：
        # 3.2.1 针对每一位帝王，向AI进行一次详细查询
        询问AI："请提供关于'{当前朝代}'的'{当前帝王}'的详细信息，具体包括：1. 其常用的年号；2. 在位起止时间；3. 在位期间发生的主要历史事件；4. 其在位时对应的西方历史时期或主要国家；5. 此时期发生的中西方文化交流代表性事件。", 输出记为 {年号}, {在位时间}, {主要事件}, {同期西方历史}, {中西文化交流事件}
        
        # 3.2.2 将查询到的信息作为新的一行添加到数据表中
        将一行数据 {"朝代":{当前朝代}, "年号":{年号}, "帝王":{当前帝王}, "在位时间":{在位时间}, "主要事件":{主要事件}, "同期西方历史":{同期西方历史}, "中西文化交流事件":{中西文化交流事件}} 添加到 {中国历史王朝信息表}
        
        # 3.2.3 向用户提供实时反馈
        告诉用户 "已完成 {当前朝代} {当前帝王} 的信息收集。"

# 第四步：任务收尾，通知用户
告诉用户 "所有朝代信息已收集完毕，请查看 {中国历史王朝信息表}。"
\`\`\`

### 案例四：收集中国所有地级市的简介、图片、人口数、平均收入、房价、代表性美食
\`\`\`
# 第一步：准备工作，建立一个用于存储最终结果的数据表
创建一个数据表，记为{城市信息表}，包含 "城市名称"、"所在省份"、"城市简介"、"代表图片"、"人口数"、"人均年收入"、"平均房价"、"代表性美食" 列

# 第二步：获取中国所有地级市的列表
询问AI："请列出中国所有的地级市名称"，输出城市名称列表，记为{城市列表}
获取 {城市列表} 的元素数量，记为 {城市总数}
告诉用户 "已成功获取全国地级市列表，共 {城市总数} 个。现在开始逐一收集详细数据..."

# 第三步：遍历每一个城市，收集其详细信息并填入表格
遍历{城市列表}中的每一个{当前城市}：
    # 3.1 向用户反馈当前进度
    告诉用户 "正在收集【{当前城市}】的信息... (进度: {循环当前次数}/{城市总数})"
    
    # 3.2 对每个城市，向AI查询其文字类信息
    # 将多个信息点合并在一次查询中，以提高效率
    询问AI："请提供关于'{当前城市}'的详细资料，具体包括：1. 一段200字左右的城市简介；2. 最新的人口数量；3. 人均年收入；4. 平均房价；5. 代表性美食列表；6. 所在省份"，输出记为 {城市简介}, {人口数}, {平均收入}, {房价}, {代表美食}，{所在省份} # 需要调用AI大模型时开启搜索功能
    
    # 3.3 对每个城市，单独查询一张代表性图片
    在浏览器中：
        导航到百度图片搜索页面，搜索"{当前城市} 图片"，获取界面截图，记为{城市图片}
    
    # 3.4 将查询到的所有信息作为新的一行添加到数据表中
    将一行数据 {"城市名称":{当前城市}, "所在省份":{所在省份}, "城市简介":{城市简介}, "代表图片":{城市图片}, "人口数":{人口数}, "人均年收入":{平均收入}, "平均房价":{房价}, "代表性美食":{代表美食}} 添加到 {城市信息表}

# 第四步：任务收尾，通知用户
告诉用户 "数据已全部处理完毕，并成功保存到 {城市信息表} 中。"
\`\`\`

### 案例五：帮我收集雷军视频号下面的前100个热门评论，记录评论用户、时间、点赞数，并生成一个合适的回复
\`\`\`
# 任务：收集雷军视频号前100条热门评论并生成回复

# 第一步：准备工作，建立数据表
建立一个数据表{评论数据表}，包含 "评论用户", "评论内容", "评论时间", "点赞数", "AI生成的回复" 列

# 第二步：导航至雷军视频号的评论区
在设备 "手机1" 中：
    导航到 "微信" 应用的视频号界面，搜索 "雷军" 并导航到雷军的视频号主页
    告诉用户 "已进入雷军视频号主页，开始收集前100条热门评论..."
    遍历视频号主页中的每一个视频：
        导航到当前视频的评论区，对于当前界面中的每一条评论，执行：
            获取该评论的 "用户昵称"，记为 {用户名}
            获取该评论的 "评论内容"，记为 {评论内容}
            获取该评论的 "发布时间"，记为 {发布时间}
            获取该评论的 "点赞数"，记为 {点赞数}
            # 设计一个提示词，让AI模仿雷军的风格进行回复
            询问AI："你现在是雷军的社交媒体助理，请针对这条用户评论：'{评论内容}'，生成一个友善、真诚且得体的感谢或互动回复，风格要亲切自然。", 输出文本，记为 {AI回复}
            将一行数据 {"评论用户":{用户名}, "评论内容":{评论内容}, "评论时间":{发布时间}, "点赞数":{点赞数}, "AI生成的回复":{AI回复}} 添加到 {评论数据表}
            获取 {评论数据表} 的行数，记为 {已收集数量}
            告诉用户 "已收集第 {已收集数量} 条评论：用户'{用户名}'"
            如果{已收集数量}超过100，结束循环

告诉用户 "前100条热门评论已全部收集并处理完毕！结果保存在在{评论数据表}"
\`\`\`

### 案例六：收集paper.pdf这篇文章相关的研究论文，记录题目、作者、单位等信息
\`\`\`
# 第一步：准备工作，建立数据表并分析源论文
建立一个数据表{相关论文表}
告诉用户 "数据表已创建，正在分析源文件 'paper.pdf'..."
读取文档"paper.pdf"，记为{文章内容}
询问AI："请为这篇文章生成一些用于搜索相关工作的关键词。{文章内容}", 输出记为 {搜索关键词列表}
告诉用户 "即将在Google Scholar中搜索如下关键词获取相关文献：{搜索关键词列表}"

# 第二步：使用浏览器进行学术搜索
针对{搜索关键词列表}中的每个{关键词}：
    在设备 "浏览器1" 中：
        # 优先使用学术搜索引擎以获得更高质量的结果
        导航到 "https://scholar.google.com"
        搜索{关键词}，对于搜索结果中的每一个搜索结果条目，最多20个，执行：
            获取该条目的论文标题
            如果论文标题已经在{相关论文表}中存在，跳过该论文
            导航到该论文的bibtex引用页面，获取作者列表、发表期刊、发表时间、bib entry
            导航到该论文的内容页面，获取内容简介、链接、单位列表
            将论文标题、作者列表、发表期刊、发表时间、bib entry、内容简介、链接、单位列表作为一行数据添加到 {相关论文表}            
            # 3.4 向用户实时反馈进度
            告诉用户 "已收集论文：'{论文标题}'"

# 第四步：任务收尾，保存并通知用户
告诉用户 "相关相关论文信息已收集完毕！"
告诉用户 "所有数据已成功保存到 {相关论文表}"
\`\`\`
`,

    EnglishRuyiDSLSyntaxExamples: `## 4. Practical Examples

The following are representative examples showcasing the capabilities of the Ruyi programming language in different scenarios.

### Case 1: Collect information about the latest 20 WeChat contacts, including remarks, nicknames, avatars, main chat content, and recent Moments content.
\`\`\`
Create a data table {contact information table}, containing "Name", "Nickname", "ID", "Avatar", "Chat Summary", "Recent Moments" columns

On device "phone1":
    Navigate to the "WeChat" app's chat list page, for each chat session in the chat list, execute:
        Navigate to that chat session, execute:
            Determine if the current chat session is a one-on-one chat with a real person, if not, skip the current session
            Get the contact of the current chat session, record as {current contact}
            Get the current chat content, record as {chat content}
            Ask AI: "Please generate a summary based on the chat content: {chat content}", output recorded as {chat summary}
        Navigate to {current contact}'s profile page, get name, nickname, WeChat ID, avatar
        Navigate to {current contact}'s Moments, get recent Moments content
        Insert name, nickname, WeChat ID, avatar, chat summary, recent Moments content into the contact information table
        Tell user: "Completed collecting {current contact} information, collected {contact information table.row count} entries"
        If 20 contacts' information has been collected, break the loop
        
Tell user "Successfully collected information about the latest 20 contacts, please check the file {contact information table}."
\`\`\`

### Case 2: Consolidate contacts from phone1, phone2, and phone3 into one table
\`\`\`
Define a task, named "get device contact list", task input: {device name}
    On device {device name}:
        Navigate to the "Contacts" app's contact list interface, traverse each contact, execute:
            Navigate to that contact's profile page, get the contact's name, email, phone number
            Add a row of data to the {current device contact list} table, containing: "Device": {device name}, "Name": {name}, "Email": {email}, "Phone Number": {phone number}
        Return {current device contact list}

Create an empty data table, record as {all contacts list}
On devices 'phone1', 'phone2', 'phone3':
    Tell user "Capturing contact list of device {device name}"
    Execute task "get device contact list", input is {device name}, output result recorded as {current device contact list}
    Add {current device contact list} to {all contacts list}

Send {all contacts list} to user
\`\`\`

### Case 3: Collect information about Chinese historical dynasties, including dynasty, era names, emperors, duration, major events, corresponding Western historical periods and Sino-Western cultural exchange events, one emperor per row
\`\`\`
# Step 1: Preparation, create a data table to store final results
Create an empty data table, containing "Dynasty", "Era Name", "Emperor", "Reign Period", "Major Events", "Contemporary Western History", "Sino-Western Cultural Exchange Events" columns, record as {Chinese Historical Dynasty Information Table}

# Step 2: Get a list of all major Chinese dynasties
Ask AI: "Please list all major dynasties in Chinese history, such as Qin, Han, Tang, Song, Yuan, Ming, Qing, etc.", output dynasty list, record as {dynasty list}
Tell user "Dynasty list obtained, will start collecting detailed information one by one..."

# Step 3: Traverse each dynasty and collect information about its emperors
Traverse each {current dynasty} in {dynasty list}:
    Tell user "Collecting information about [{current dynasty}]..."
    # 3.1 Get all emperors of the current dynasty
    Ask AI: "Please list all emperor names of {current dynasty}", output emperor name list, record as {emperor list}

    # 3.2 Traverse each emperor of the current dynasty, query and record their detailed information
    Traverse each {current emperor} in {emperor list}:
        # 3.2.1 For each emperor, make a detailed query to AI
        Ask AI: "Please provide detailed information about '{current emperor}' of '{current dynasty}', specifically including: 1. Their commonly used era names; 2. Reign start and end dates; 3. Major historical events during their reign; 4. Corresponding Western historical periods or major countries during their reign; 5. Representative Sino-Western cultural exchange events of this period.", output recorded as {era name}, {reign period}, {major events}, {contemporary Western history}, {Sino-Western cultural exchange events}
        
        # 3.2.2 Add the queried information as a new row to the data table
        Add a row of data {"Dynasty":{current dynasty}, "Era Name":{era name}, "Emperor":{current emperor}, "Reign Period":{reign period}, "Major Events":{major events}, "Contemporary Western History":{contemporary Western history}, "Sino-Western Cultural Exchange Events":{Sino-Western cultural exchange events}} to {Chinese Historical Dynasty Information Table}
        
        # 3.2.3 Provide real-time feedback to user
        Tell user "Completed collecting information for {current dynasty} {current emperor}."

# Step 4: Task completion, notify user
Tell user "All dynasty information has been collected, please check {Chinese Historical Dynasty Information Table}."
\`\`\`

### Case 4: Collect information about all prefecture-level cities in China, including introduction, images, population, average income, housing prices, and representative cuisine
\`\`\`
# Step 1: Preparation, create a data table to store final results
Create a data table, record as {city information table}, containing "City Name", "Province", "City Introduction", "Representative Image", "Population", "Average Annual Income", "Average Housing Price", "Representative Cuisine" columns

# Step 2: Get a list of all prefecture-level cities in China
Ask AI: "Please list the names of all prefecture-level cities in China", output city name list, record as {city list}
Get the number of elements in {city list}, record as {total number of cities}
Tell user "Successfully obtained the national prefecture-level city list, totaling {total number of cities}. Now starting to collect detailed data one by one..."

# Step 3: Traverse each city, collect its detailed information and fill into the table
Traverse each {current city} in {city list}:
    # 3.1 Provide current progress feedback to user
    Tell user "Collecting information about [{current city}]... (Progress: {current loop count}/{total number of cities})"
    
    # 3.2 For each city, query AI for textual information
    # Combine multiple information points in one query to improve efficiency
    Ask AI: "Please provide detailed information about '{current city}', specifically including: 1. A city introduction of about 200 words; 2. Latest population figures; 3. Average annual income; 4. Average housing prices; 5. List of representative cuisine; 6. Province where it's located", output recorded as {city introduction}, {population}, {average income}, {housing prices}, {representative cuisine}, {province} # Enable search function when calling AI large model
    
    # 3.3 For each city, separately query for a representative image
    In browser:
        Navigate to Baidu image search page, search "{current city} images", get interface screenshot, record as {city image}
    
    # 3.4 Add all queried information as a new row to the data table
    Add a row of data {"City Name":{current city}, "Province":{province}, "City Introduction":{city introduction}, "Representative Image":{city image}, "Population":{population}, "Average Annual Income":{average income}, "Average Housing Price":{housing prices}, "Representative Cuisine":{representative cuisine}} to {city information table}

# Step 4: Task completion, notify user
Tell user "All data has been processed and successfully saved to {city information table}."
\`\`\`

### Case 5: Help me collect the top 100 popular comments under Lei Jun's video account, record comment users, time, likes, and generate appropriate replies
\`\`\`
# Task: Collect the top 100 popular comments from Lei Jun's video account and generate replies

# Step 1: Preparation, create data table
Create a data table {comment data table}, containing "Comment User", "Comment Content", "Comment Time", "Likes", "AI Generated Reply" columns

# Step 2: Navigate to Lei Jun's video account comment section
On device "phone1":
    Navigate to the "WeChat" app's video account interface, search "Lei Jun" and navigate to Lei Jun's video account homepage
    Tell user "Entered Lei Jun's video account homepage, starting to collect the top 100 popular comments..."
    Traverse each video on the video account homepage:
        Navigate to the current video's comment section, for each comment in the current interface, execute:
            Get the "user nickname" of that comment, record as {username}
            Get the "comment content" of that comment, record as {comment content}
            Get the "publish time" of that comment, record as {publish time}
            Get the "likes" of that comment, record as {likes}
            # Design a prompt for AI to mimic Lei Jun's style in replies
            Ask AI: "You are now Lei Jun's social media assistant, please generate a friendly, sincere and appropriate thank you or interactive reply to this user comment: '{comment content}', the style should be warm and natural.", output text, record as {AI reply}
            Add a row of data {"Comment User":{username}, "Comment Content":{comment content}, "Comment Time":{publish time}, "Likes":{likes}, "AI Generated Reply":{AI reply}} to {comment data table}
            Get the number of rows in {comment data table}, record as {collected count}
            Tell user "Collected comment #{collected count}: user '{username}'"
            If {collected count} exceeds 100, end loop

Tell user "The top 100 popular comments have been fully collected and processed! Results saved in {comment data table}"
\`\`\`

### Case 6: Collect research papers related to the paper.pdf article, recording titles, authors, institutions and other information
\`\`\`
# Step 1: Preparation, create data table and analyze source paper
Create a data table {related papers table}
Tell user "Data table created, analyzing source file 'paper.pdf'..."
Read document "paper.pdf", record as {article content}
Ask AI: "Please generate some keywords for searching related work for this article. {article content}", output recorded as {search keyword list}
Tell user "Will search the following keywords in Google Scholar to obtain related literature: {search keyword list}"

# Step 2: Use browser for academic search
For each {keyword} in {search keyword list}:
    On device "browser1":
        # Prioritize academic search engines for higher quality results
        Navigate to "https://scholar.google.com"
        Search {keyword}, for each search result entry in the search results, up to 20, execute:
            Get the paper title of that entry
            If the paper title already exists in {related papers table}, skip this paper
            Navigate to that paper's bibtex citation page, get author list, publication journal, publication time, bib entry
            Navigate to that paper's content page, get content summary, link, institution list
            Add paper title, author list, publication journal, publication time, bib entry, content summary, link, institution list as a row of data to {related papers table}            
            # 3.4 Provide real-time progress feedback to user
            Tell user "Collected paper: '{paper title}'"

# Step 4: Task completion, save and notify user
Tell user "Related paper information collection completed!"
Tell user "All data has been successfully saved to {related papers table}"
\`\`\`
`,

    workflowToPythonHint: `Next, I will present you the APIs in a Python framework that is used to implement natural-language workflow plans with executable Python script. You need to precisely understand the APIs and then you will be asked to use the APIs as well as native Python to complete tasks.`,

    ruyiPythonFrameworkIntroduction: `# Ruyi Python Framework Introduction

This framework provides the following APIs:
- agent.device: The interfaces to call system-level APIs of the target device.
  - The following APIs are only available for Android devices:
    - \`device.start_app(app_name: str)\` : Open app named \`app_name\`.
    - \`device.kill_app(app_name: str)\` : Kill app named \`app_name\`.
    - \`device.take_picture() -> PIL.Image\` : Take a picture with the camera and return the picture.
  - The following APIs are only available for browser devices:
    - \`device.open_url(url: str)\` : Open a URL in the browser.
    - \`device.get_url()\` : Get the URL of the current page in the browser.
    - \`device.web_search(text: str)\` : Search the given text in the browser.
  - The following APIs are only available for all devices:
    - \`device.get_input_field_text() -> str\`: Get the text from the current focused input field.
    - \`device.ask_question(question: str)\`: Ask user a question and return the answer.
    - \`device.notify_message(message: str)\`: Notify user with a message.
    - \`device.notify_table(table: data.live_table)\`: Notify user with a table.
    - \`device.notify_image(image: data.image)\`: Notify user with an image.
    - \`device.get_clipboard()\`: Get the text from the clipboard.
    - \`device.set_clipboard(text: str)\`: Set the text to the clipboard.
    - \`device.expand_notification_panel()\`: Expand the notification panel.
    - \`device.take_screenshot() -> PIL.Image\`: Take a screenshot of the current screen, and return a PIL image.
    - \`device.set_device(device_name: str) -> bool\`: Set the connected device to the device named \`device_name\`.
- agent.ui: The interfaces to interact with the device GUI.
    - \`ui.back\`: Navigate back from current screen.
    - \`ui.home\`: Navigate to the home screen.
    - \`ui.back_to(description: str, max_steps=5)\`: Navigate back to the view described by \`description\`. \`max_steps\` is the maximum number of steps to navigate.
    - \`ui.scroll_up(distance=None)\`: scroll up this view.
    - \`ui.scroll_down(distance=None)\`: scroll down this view.
    - \`ui.scroll_left(distance=None)\`: scroll left this view. 
    - \`ui.scroll_right(distance=None)\`: scroll right this view.
    - \`ui.scroll_until(desc: str, direction: float | str = "down", distance=None, duration=1000, max_retry: int = 10) -> bool\` : scroll this view, until desc is fulfilled. If the desired view appears, return True; otherwise, return False.
    - \`ui.wait_until(description, waitInterval:float=0.5, timeout=5) -> bool:\`: Wait for a view described by \`description\` to appear and return it. \`timeout\` is the time limit (in seconds) of waiting. \`-1\` means unlimited. If the desired view appears, return True; otherwise, return False.
    - \`ui.check(description: str) -> bool\`: check whether the current screen state matches \`description\`.
    - \`ui.ensure(description: str) -> bool\`: Ensure the view described by \`description\` is present. If the desired view presents, return True; otherwise, return False.
    - \`ui.snapshot()->Image\`: Get snapshot UI_View instance of this view.
    - \`ui.hide_highlight()\`: Hide all highlights on the screen.
    - \`ui.locate_view(description: str)\`-> \`UI_View\`: Locate the view described by \`description\` in the screen.
    - \`ui.iterate_views(description: str, limit=10, direction="up")\`: Yield the views that match \`description\` inside the current view. After enumerating all views in the current interface, the screen will automatically scroll in the direction specified by \`direction\` (default is upward). You can also specify \`direction\` as one of "left", "down", or "right."
    - \`ui.fetch_information(description: str, returns: list[str | tuple[str, TypeAlias]] | None)\`: Get information from the current screen by \`description\`. The returns parameter specifies the expected return type and description for function outputs. See \`fm.query\` for more details.
    - \`ui.fetch_image(description: str, save_path: Optional[str] = None)\` -> \`PIL.Image\`: Get image from the current screen by \`description\`. The image will be saved to the specified path if save_path is provided.
    - \`UI_View.generate_locator()->str\`: Generate a locator description of this view.
    - \`UI_View.click()\`: Click the view.
    - \`UI_View.input(text: str)\`: Clear the input field text and input given text into this view.
    - \`UI_View.input_by_pasting(text: str)\`: Input text into this view by pasting. Use input_by_pasting API when UI_View.input doesn't work such as in 微信 (WeChat) app.
    - \`UI_View.long_click()\`: Long click the view for 1 second.
    - \`UI_View.scroll_up(distance=None)\`: scroll up this view.
    - \`UI_View.scroll_down(distance=None)\`: scroll down this view.
    - \`UI_View.scroll_left(distance=None)\`: scroll left this view.
    - \`UI_View.scroll_right(distance=None)\`: scroll right this view.
    - \`UI_View.scroll_until(desc: str, direction: float | str = "down", distance=None, duration=1000, max_retry: int = 10) -> bool\` : scroll this view, until desc is fulfilled. If the desired view appears, return True; otherwise, return False.
    - \`UI_View.get_input_field_text() -> str\`: Get the text from this input field view.
    - \`UI_View.show_highlight()\`: Show a highlight on this view.
    - \`UI_View.locate_view(description: str)\`-> \`UI_View\`: Locate the view described by \`description\` inside the view.
    - \`UI_View.fetch_information(description: str, returns: list[str | tuple[str, TypeAlias]] | None)\`: Get information from the current view by \`description\`. The returns parameter specifies the expected return type and description for function outputs. See \`fm.query\` for more details.
- agent.tools: The tools that may be used by the agent.
    - \`tools.get_temp_file_path(suffix='.png')->str\`: Get the path of a temporary file.
    - \`tools.open_image_url(image_url)->img\`: Open an image from a URL.
    - \`tools.image_to_base64_url()->str\`:  Convert an image to a base64 URL.
- agent.fm: The interfaces to get answers from foundation models.
    - fm.query(*args: [str | PIL.Image.Image | UI_View], returns, **kwargs): Query large language model, args can be a list of string prompts or images or UI_Views. The returns parameter specifies the expected return type and description for function outputs. It supports the following formats:
        1. None | str (Raw Response):
        - When returns=None or a simple string, this function returns raw model response
        - Example: returns=None -> function returns a raw string response
        - Example: returns="city name" -> function returns a city name in string format

        2. tuple[str, TypeAlias] (Single Typed Return):
        - Returns a single value parsed according to specified type
        - First element: Description of the return value
        - Second element: Expected type of the return value
        - Example: returns=("age", int) -> function returns the parsed age value in integer
        - Example: returns=("items", list[str]) -> function returns a list of strings representing the items

        3. list[str | tuple[str, TypeAlias]] (Multiple Returns):
        - Returns multiple values with optional type specifications
        - Each element can be either:
            a) str: Description only (assumes str type)
            b) tuple: (description, type) pair
        - Example: returns=["name", ("age", int)]
            -> [string_name, parsed_integer_age]
        - Example: returns=[("items", list[str]), ("count", int)]
            -> [parsed_list_of_strings, parsed_integer]
- agent.data: The interfaces to use common data structures and common data resources (photos, sensors, etc.) on the device.
    - \`data.live_table("table name")\`: Storing information as a table, \`table name\` is the name of the table. It provides pandas-like APIs.
        - \`live_table.add_row(row_dict: dict[str, Any])\`: Add a row.
        - \`live_table.update_row(index: int, row_dict: dict[str, Any])\`: Update a row.
        - \`live_table.delete_row(index: int)\`: Delete a row.
        - \`len(live_table)\`: Get number of rows.
        - \`live_table[index]\`: Get a row by index.
        - \`live_table[index, 'col_name']\`: Get a cell value.
        - \`live_table.iterrows()\`: Iterate over rows.
        - \`live_table.columns\`: Get list of column names.
        - \`live_table.sort_rows('col_name', reverse=False)\`: Sort rows by a column.
        - \`live_table.filter_rows(lambda row: row['col_name'] > 0)\`: Filter rows with a function.
        - \`live_table.clear()\`: Clear all data in the table.
    - \`data.image(PIL.Image)\`: Wrap a PIL image as a data.image, \`PIL.Image\` is the PIL image.
    - \`data.read_document(file_name: str) -> str\`: Read a document and return the text content.
    - \`data.retrieve_document(document: str, query: str, length=None) -> str\`: Retrieve document information based on the query and return the text content. If length is not None, the response will be limited to the specified length.
`,

    examplesIntroduction: `# Examples

Below are three examples of tasks written using Ruyi.
Each example includes a Ruyi DSL workflow and corresponding Python script.
The Ruyi DSL workflow is prefixed with a label, which represents the line number of the workflow.
At the same time, a label is also added to the comment after each line of the Python script, but this label is not the line number of the Python script, but the label of the workflow corresponding to the line of the Python script.
If there are multiple consecutive lines of Python script corresponding to the same line of workflow, then only the first line of Python script needs to be labeled.
Note: Empty lines and lines with only comments do not add labels.
`,

    ChineseExamples: `Task 1: "收集微信最近20位联系人的信息，包括备注、昵称、头像、聊天主要内容、最近朋友圈内容":
\`\`\`
[1]建立一个数据表 {联系人信息表}，包含 "称呼", "昵称", "ID", "头像", "聊天摘要", "最近朋友圈" 列

[2]在设备 "phone1" 中：
[3]    导航到 "微信" 应用的聊天列表页面，对于聊天列表中的每个聊天会话，执行：
[4]        导航到该聊天会话，执行：
[5]            判断当前聊天会话是不是与真人的一对一聊天，如果不是则跳过当前会话
[6]            获取当前聊天会话的联系人，记为{当前联系人}
[7]            获取当前聊天内容，记为{聊天内容}
[8]            询问AI: "请根据聊天内容生成摘要：{聊天内容}", 输出记为 {聊天摘要}
[9]        导航到{当前联系人}的资料页面，获取称呼、昵称、微信ID、头像
[10]        导航到{当前联系人}的朋友圈，获取最近朋友圈内容
[11]        将称呼、昵称、微信ID、头像、聊天摘要、最近朋友圈内容插入联系人信息表
[12]        告知用户："已完成{当前联系人}信息的收集，已收集{联系人信息表.行数}条"
[13]        如果已经收集了20个联系人的信息，跳出循环
        
[14]告诉用户 "已成功收集最近20位联系人的信息，详情请查看文件 {联系人信息表}。"
\`\`\`

\`\`\`
contact_info_table = data.live_table("联系人信息表")  # [1]

device.set_device('phone1')  # [2]
device.start_app('微信')  # [3] Navigate to WeChat app
ui.ensure("微信聊天列表页面")  # Ensure we're on the chat list page

for session in ui.iterate_views("聊天会话", limit=20):  # Iterate over chat sessions, limit to 20.
    session.click()  # [4] Navigate to the chat session.
    
    if not ui.check("真人一对一聊天界面"):  # [5] Check if it's a one-on-one chat with a real person.
        ui.back()  # Navigate back to the chat list.
        continue  # Skip this session.
    
    current_contact = ui.fetch_information("聊天会话联系人", returns=("聊天会话联系人", str))  # [6] Fetch the contact information.
    chat_content = ui.fetch_information("聊天内容", returns=("聊天内容", str))  # [7] Get the current chat content.
    
    chat_summary = fm.query(  # [8] Use AI to generate a summary of the chat content.
        f"请根据聊天内容生成摘要：\n{chat_content}", 
        returns=("聊天摘要", str)
    )
    
    ui.locate_view("联系人头像").click()  # [9] Navigate to contact's profile.
    ui.ensure("{current_contact} 个人资料页")
    remark = ui.fetch_information("备注名称", returns=("备注名称", str))  # Get the remark.
    nickname = ui.fetch_information("昵称", returns=("昵称", str))  # Get the nickname.
    wechat_id = ui.fetch_information("微信ID", returns=("微信ID", str))  # Get WeChat ID.
    avatar = ui.fetch_image("头像")  # Get avatar.
    
    ui.locate_view("朋友圈").click()  # [10] Navigate to contact's moments page.
    recent_moments = ui.fetch_information("最近朋友圈内容", returns=("最近朋友圈内容", str))  # Get recent moments content.
    ui.back_to("微信聊天列表页面")  # Navigate back to chat list.

    contact_info_table.add_row(  # [11] Insert the collected data into the contact info table.
        {
            "称呼": remark, 
            "昵称": nickname,
            "ID": wechat_id,
            "头像": avatar,
            "聊天摘要": chat_summary,
            "最近朋友圈": recent_moments
        }
    )
    
    device.notify_message(  # [12] Notify the user after collecting current contact's info.
        f"已完成{current_contact}信息的收集，已收集{len(contact_info_table)}条"
    )

    if len(contact_info_table) >= 20:  # [13] Check if 20 contacts are collected.
        break  # Exit the loop once 20 records are collected.

device.notify_message(  # [14] Notify user the task is completed.
    f"已成功收集最近20位联系人的信息，详情请查看文件: {contact_info_table}。"
)
\`\`\`


Task 2: "将手机1、手机2、手机3中的联系人汇总成一张表格" :
\`\`\`
[1]定义一个任务，名为 "获取设备联系人列表"，任务输入：{设备名}
[2]    在设备 {设备名} 上执行：
[3]        导航到 "联系人" 应用的联系人列表界面，遍历每位联系人，执行：
[4]            导航到该联系人的资料页面，获取该联系人的姓名、邮箱、电话号码
[5]            在 {当前设备联系人列表} 表格中添加一行数据，包含："设备": {设备名}, "姓名": {姓名}, "邮箱": {邮箱}, "电话号码": {电话号码}
[6]        返回 {当前设备联系人列表}

[7]创建一个空的数据表，记为 {所有联系人列表}
[8]在设备 '手机1'、'手机2'、'手机3' 中：
[9]    告诉用户 "捕获设备 {设备名} 的联系人列表"
[10]    执行任务 "获取设备联系人列表"，输入是 {设备名}，输出结果记为 {当前设备联系人列表}
[11]    将 {当前设备联系人列表} 添加到 {所有联系人列表}

[12]将 {所有联系人列表} 发送给用户
\`\`\`

\`\`\`
def get_device_contact_list(device_name):  # [1]
    """任务：获取单个设备的联系人列表"""
    device.set_device(device_name)  # [2]
    device.start_app('联系人')  # [3]
    ui.ensure("联系人列表界面")
    
    # 为当前设备创建一个临时表
    current_device_contacts = data.live_table(f"{device_name}_contacts")
    
    # 遍历联系人列表
    for contact_item in ui.iterate_views("联系人条目", limit=500): # 设定一个较高的上限
        contact_item.click()  # [4]
        ui.ensure("联系人资料页面")
        
        # 获取联系人信息
        name = ui.fetch_information("姓名", returns=("姓名", str))
        email = ui.fetch_information("邮箱", returns=("邮箱", str))
        phone_number = ui.fetch_information("电话号码", returns=("电话号码", str))
        
        # 添加到临时表
        current_device_contacts.add_row({  # [5]
            "设备": device_name,
            "姓名": name,
            "邮箱": email,
            "电话号码": phone_number
        })
        ui.back() # 返回到列表页
        
    return current_device_contacts  # [6]

# 主流程开始
all_contacts_list = data.live_table("所有联系人列表")  # [7]

# 遍历所有需要操作的设备
for device_name in ['phone1', 'phone2', 'phone3']:  # [8]
    device.notify_message(f"正在捕获设备 {device_name} 的联系人列表...")  # [9]
    
    # 执行任务并获取单个设备的联系人表
    single_device_table = get_device_contact_list(device_name)  # [10]
    
    # 将单个设备的联系人数据合并到总表中
    for _, row in single_device_table.iterrows():  # [11]
        all_contacts_list.add_row(row.to_dict())

# 任务完成，通知用户
device.notify_table(all_contacts_list)  # [12]
\`\`\`


Task 3: "收集中国历史上的王朝信息，包括朝代、年号、帝王、起止时间、主要事件、以及对应的西方历史朝代和中西方文化交流事件，每个帝王一行":
\`\`\`
# 第一步：准备工作，建立一个用于存储最终结果的数据表
[1] 建立一个空的数据表，包含 "朝代"、"年号"、"帝王"、"在位时间"、"主要事件"、"同期西方历史"、"中西文化交流事件" 列，记为 {中国历史王朝信息表}

# 第二步：获取中国所有主要朝代的列表
[2] 询问AI："请列出中国历史上所有主要的朝代，例如秦、汉、唐、宋、元、明、清等"，输出朝代列表，记为{朝代列表}
[3] 告诉用户 "已获取朝代列表，将开始逐一收集详细信息..."

# 第三步：遍历每一个朝代，收集其帝王信息
[4] 遍历{朝代列表}中的每一个{当前朝代}：
[5]    告诉用户 "正在收集【{当前朝代}】的信息..."
    # 3.1 获取当前朝代的所有帝王
[6]    询问AI："请列出{当前朝代}的所有帝王姓名列表"，输出帝王姓名列表，记为{帝王列表}

    # 3.2 遍历当前朝代的每一位帝王，查询并记录其详细信息
[7]    遍历{帝王列表}中的每一个{当前帝王}：
        # 3.2.1 针对每一位帝王，向AI进行一次详细查询
[8]        询问AI："请提供关于'{当前朝代}'的'{当前帝王}'的详细信息，具体包括：1. 其常用的年号；2. 在位起止时间；3. 在位期间发生的主要历史事件；4. 其在位时对应的西方历史时期或主要国家；5. 此时期发生的中西方文化交流代表性事件。", 输出记为 {年号}, {在位时间}, {主要事件}, {同期西方历史}, {中西文化交流事件}
        
        # 3.2.2 将查询到的信息作为新的一行添加到数据表中
[9]        将一行数据 {"朝代":{当前朝代}, "年号":{年号}, "帝王":{当前帝王}, "在位时间":{在位时间}, "主要事件":{主要事件}, "同期西方历史":{同期西方历史}, "中西文化交流事件":{中西文化交流事件}} 添加到 {中国历史王朝信息表}
        
        # 3.2.3 向用户提供实时反馈
[10]        告诉用户 "已完成 {当前朝代} {当前帝王} 的信息收集。"

# 第四步：任务收尾，通知用户
[11] 告诉用户 "所有朝代信息已收集完毕，请查看 {中国历史王朝信息表}。"
\`\`\`

\`\`\`
# 第一步：准备工作
dynasty_info_table = data.live_table("中国历史王朝信息表")  # [1]

# 第二步：获取朝代列表
dynasty_list = fm.query(  # [2]
    "请列出中国历史上所有主要的朝代，例如秦、汉、唐、宋、元、明、清等",
    returns=[('朝代列表', list[str])]
)
device.notify_message("已获取朝代列表，将开始逐一收集详细信息...")  # [3]

# 第三步：遍历每一个朝代和帝王
for current_dynasty in dynasty_list:  # [4]
    device.notify_message(f"正在收集【{current_dynasty}】的信息...")  # [5]
    
    # 3.1 获取当前朝代的所有帝王
    emperor_list = fm.query(  # [6]
        f"请列出{current_dynasty}的所有帝王姓名列表",
        returns=[('帝王列表', list[str])]
    )
    
    # 3.2 遍历当前朝代的每一位帝王
    for current_emperor in emperor_list:  # [7]
        # 3.2.1 详细查询
        prompt = f"请提供关于'{current_dynasty}'的'{current_emperor}'的详细信息，具体包括：1. 其常用的年号；2. 在位起止时间；3. 在位期间发生的主要历史事件；4. 其在位时对应的西方历史时期或主要国家；5. 此时期发生的中西方文化交流代表性事件。"
        reign_name, reign_period, main_events, contemporary_west, cultural_exchange = fm.query(  # [8]
            prompt,
            returns=[
                ("年号", str), 
                ("在位时间", str), 
                ("主要事件", str), 
                ("同期西方历史", str), 
                ("中西文化交流事件", str)
            ]
        )
        
        # 3.2.2 添加到数据表
        dynasty_info_table.add_row({  # [9]
            "朝代": current_dynasty,
            "年号": reign_name,
            "帝王": current_emperor,
            "在位时间": reign_period,
            "主要事件": main_events,
            "同期西方历史": contemporary_west,
            "中西文化交流事件": cultural_exchange
        })
        
        # 3.2.3 实时反馈
        device.notify_message(f"已完成 {current_dynasty} {current_emperor} 的信息收集。")  # [10]

# 第四步：任务收尾
device.notify_message("所有朝代信息已收集完毕！")  # [11]
device.notify_table(dynasty_info_table)
\`\`\`


Task 4: "收集中国所有地级市的简介、图片、人口数、平均收入、房价、代表性美食":
\`\`\`
# 第一步：准备工作，建立一个用于存储最终结果的数据表
[1]创建一个数据表，记为{城市信息表}，包含 "城市名称"、"所在省份"、"城市简介"、"代表图片"、"人口数"、"人均年收入"、"平均房价"、"代表性美食" 列

# 第二步：获取中国所有地级市的列表
[2]询问AI："请列出中国所有的地级市名称"，输出城市名称列表，记为{城市列表}
[3]获取 {城市列表} 的元素数量，记为 {城市总数}
[4]告诉用户 "已成功获取全国地级市列表，共 {城市总数} 个。现在开始逐一收集详细数据..."

# 第三步：遍历每一个城市，收集其详细信息并填入表格
[5]遍历{城市列表}中的每一个{当前城市}：
    # 3.1 向用户反馈当前进度
[6]    告诉用户 "正在收集【{当前城市}】的信息... (进度: {循环当前次数}/{城市总数})"
    
    # 3.2 对每个城市，向AI查询其文字类信息
    # 将多个信息点合并在一次查询中，以提高效率
[7]    询问AI："请提供关于'{当前城市}'的详细资料，具体包括：1. 一段200字左右的城市简介；2. 最新的人口数量；3. 人均年收入；4. 平均房价；5. 代表性美食列表；6. 所在省份"，输出记为 {城市简介}, {人口数}, {平均收入}, {房价}, {代表美食}，{所在省份} # 需要调用AI大模型时开启搜索功能
    
    # 3.3 对每个城市，单独查询一张代表性图片
[8]    在浏览器中：
[9]        导航到百度图片搜索页面，搜索"{当前城市} 图片"，获取界面截图，记为{城市图片}
    
    # 3.4 将查询到的所有信息作为新的一行添加到数据表中
[10]    将一行数据 {"城市名称":{当前城市}, "所在省份":{所在省份}, "城市简介":{城市简介}, "代表图片":{城市图片}, "人口数":{人口数}, "人均年收入":{平均收入}, "平均房价":{房价}, "代表性美食":{代表美食}} 添加到 {城市信息表}

# 第四步：任务收尾，通知用户
[11]告诉用户 "数据已全部处理完毕，并成功保存到 {城市信息表} 中。"
\`\`\`

\`\`\`
# 第一步：准备工作
city_info_table = data.live_table("城市信息表")  # [1]

# 第二步：获取城市列表
city_list = fm.query(  # [2]
    "请列出中国所有的地级市名称",
    returns=[('城市列表', list[str])]
)
city_count = len(city_list)  # [3]
device.notify_message(f"已成功获取全国地级市列表，共 {city_count} 个。现在开始逐一收集详细数据...")  # [4]

# 第三步：遍历每一个城市
for i, current_city in enumerate(city_list):  # [5]
    # 3.1 反馈进度
    device.notify_message(f"正在收集【{current_city}】的信息... (进度: {i+1}/{city_count})")  # [6]
    
    # 3.2 查询文字信息 (AI需要开启搜索功能来获取实时数据)
    prompt = f"请提供关于'{current_city}'的详细资料，具体包括：1. 一段200字左右的城市简介；2. 最新的人口数量；3. 人均年收入；4. 平均房价；5. 代表性美食列表；6. 所在省份"
    summary, population, income, housing_price, foods, province = fm.query(  # [7]
        prompt,
        returns=[
            ("城市简介", str),
            ("人口数", str),
            ("人均年收入", str),
            ("平均房价", str),
            ("代表性美食", list[str]),
            ("所在省份", str)
        ]
    )
    
    # 3.3 获取代表图片
    device.set_device('browser1')  # [8]
    device.web_search(f"{current_city} 风景")  # [9]
    ui.wait_until("图片搜索结果")
    city_image = device.take_screenshot()
    
    # 3.4 添加到数据表
    city_info_table.add_row({  # [10]
        "城市名称": current_city,
        "所在省份": province,
        "城市简介": summary,
        "代表图片": data.image(city_image),
        "人口数": population,
        "人均年收入": income,
        "平均房价": housing_price,
        "代表性美食": ", ".join(foods) # 将列表转为字符串
    })

# 第四步：任务收尾
device.notify_message("所有城市信息已收集完毕！")  # [11]
device.notify_table(city_info_table)
\`\`\`

Task 5: "帮我收集雷军视频号下面的前100个热门评论，记录评论用户、时间、点赞数，并生成一个合适的回复":
\`\`\`
# 任务：收集雷军视频号前100条热门评论并生成回复

# 第一步：准备工作，建立数据表
[1]建立一个数据表{评论数据表}，包含 "评论用户", "评论内容", "评论时间", "点赞数", "AI生成的回复" 列

# 第二步：导航至雷军视频号的评论区
[2]在设备 "手机1" 中：
[3]    导航到 "微信" 应用的视频号界面，搜索 "雷军" 并导航到雷军的视频号主页
[4]    告诉用户 "已进入雷军视频号主页，开始收集前100条热门评论..."
[5]    遍历视频号主页中的每一个视频：
[6]        导航到当前视频的评论区，对于当前界面中的每一条评论，执行：
[7]            获取该评论的 "用户昵称"，记为 {用户名}
[8]            获取该评论的 "评论内容"，记为 {评论内容}
[9]            获取该评论的 "发布时间"，记为 {发布时间}
[10]            获取该评论的 "点赞数"，记为 {点赞数}
            # 设计一个提示词，让AI模仿雷军的风格进行回复
[11]            询问AI："你现在是雷军的社交媒体助理，请针对这条用户评论：'{评论内容}'，生成一个友善、真诚且得体的感谢或互动回复，风格要亲切自然。", 输出文本，记为 {AI回复}
[12]            将一行数据 {"评论用户":{用户名}, "评论内容":{评论内容}, "评论时间":{发布时间}, "点赞数":{点赞数}, "AI生成的回复":{AI回复}} 添加到 {评论数据表}
[13]            获取 {评论数据表} 的行数，记为 {已收集数量}
[14]            告诉用户 "已收集第 {已收集数量} 条评论：用户"{用户名}""
[15]            如果{已收集数量}超过100，结束循环

[16]告诉用户 "前100条热门评论已全部收集并处理完毕！结果保存在在{评论数据表}"
\`\`\`

\`\`\`
# 第一步：准备工作
comment_table = data.live_table("评论数据表")  # [1]

# 第二步：导航
device.set_device('phone1')  # [2]
device.start_app('微信')  # [3]
ui.locate_view("发现").click()
ui.locate_view("视频号").click()
ui.locate_view("搜索").click()
ui.locate_view("搜索框").input("雷军")
ui.locate_view("搜索按钮").click()
ui.locate_view("雷军的视频号主页").click()
ui.ensure("雷军的视频号主页")
device.notify_message("已进入雷军视频号主页，开始收集评论...")  # [4]

# 第三步：循环收集
collected_count = 0
stop_collecting = False

for video_item in ui.iterate_views("视频号主页的视频条目"):  # [5]
    if stop_collecting:
        break
    
    video_item.click()  # [6]
    ui.wait_until("视频播放页")
    ui.locate_view("评论区入口").click()
    ui.ensure("评论列表")
    
    for comment_item in ui.iterate_views("评论条目"):
        # 提取信息
        username = comment_item.fetch_information("用户昵称", returns=("用户昵称", str))  # [7]
        content = comment_item.fetch_information("评论内容", returns=("评论内容", str))  # [8]
        post_time = comment_item.fetch_information("发布时间", returns=("发布时间", str))  # [9]
        like_count = comment_item.fetch_information("点赞数", returns=("点赞数", int))  # [10]
        
        # 生成回复
        ai_reply = fm.query(  # [11]
            f"你现在是雷军的社交媒体助理，请针对这条用户评论：'{content}'，生成一个友善、真诚且得体的感谢或互动回复，风格要亲切自然。",
            returns=("生成的回复", str)
        )
        
        # 添加到表
        comment_table.add_row({  # [12]
            "评论用户": username,
            "评论内容": content,
            "评论时间": post_time,
            "点赞数": like_count,
            "AI生成的回复": ai_reply
        })
        
        collected_count += 1  # [13]
        device.notify_message(f"已收集第 {collected_count} 条评论：用户"{username}"")  # [14]
        
        # 检查是否已达到100条
        if collected_count >= 100:  # [15]
            stop_collecting = True
            break # 退出内部循环
            
    ui.back_to("雷军的视频号主页") # 从评论区返回到主页

# 第四步：任务收尾
device.notify_message("前100条热门评论已全部收集并处理完毕！")  # [16]
device.notify_table(comment_table)
\`\`\`

Task 6: "收集paper.pdf这篇文章相关的研究论文，记录题目、作者、单位等信息":
\`\`\`
# 第一步：准备工作，建立数据表并分析源论文
[1]建立一个数据表{相关论文表}
[2]告诉用户 "数据表已创建，正在分析源文件 'paper.pdf'..."
[3]读取文档"paper.pdf"，记为{文章内容}
[4]询问AI："请为这篇文章生成一些用于搜索相关工作的关键词。{文章内容}", 输出记为 {搜索关键词列表}
[5]告诉用户 "即将在Google Scholar中搜索如下关键词获取相关文献：{搜索关键词列表}"

# 第二步：使用浏览器进行学术搜索
[6]针对{搜索关键词列表}中的每个{关键词}：
[7]    在设备 "浏览器1" 中：
        # 优先使用学术搜索引擎以获得更高质量的结果
[8]        导航到 "https://scholar.google.com"
[9]        搜索{关键词}，对于搜索结果中的每一个搜索结果条目，最多20个，执行：
[10]            获取该条目的论文标题
[11]            如果论文标题已经在{相关论文表}中存在，跳过该论文
[12]            导航到该论文的bibtex引用页面，获取作者列表、发表期刊、发表时间、bib entry
[13]            导航到该论文的内容页面，获取内容简介、链接、单位列表
[14]            将论文标题、作者列表、发表期刊、发表时间、bib entry、内容简介、链接、单位列表作为一行数据添加到 {相关论文表}            
            # 3.4 向用户实时反馈进度
[15]            告诉用户 "已收集论文：'{论文标题}'"

# 第四步：任务收尾，保存并通知用户
[16]告诉用户 "相关相关论文信息已收集完毕！"
[17]告诉用户 "所有数据已成功保存到 {相关论文表}"
\`\`\`

\`\`\`
# 第一步：准备工作
related_papers_table = data.live_table("相关论文表")  # [1]
device.notify_message("数据表已创建，正在分析源文件 'paper.pdf'...")  # [2]

# 分析源论文 (假设有读取文件的API)
paper_content = tools.read_document("paper.pdf")  # [3]
keyword_list = fm.query(  # [4]
    f"请为这篇文章生成一些用于搜索相关工作的关键词: {paper_content}",
    returns=[('搜索关键词列表', list[str])]
)
device.notify_message(f"即将在Google Scholar中搜索如下关键词获取相关文献: {keyword_list}")  # [5]

# 第二步 & 第三步: 搜索与收集
device.set_device('browser1')  # [7]
collected_titles = set()

for keyword in keyword_list:  # [6]
    device.open_url("https://scholar.google.com")  # [8]
    ui.locate_view("搜索框").input(keyword)  # [9]
    ui.locate_view("搜索按钮").click()
    ui.ensure("搜索结果页面")
    
    for result_item in ui.iterate_views("搜索结果条目", limit=20):
        title = result_item.fetch_information("论文标题", returns=("论文标题", str))  # [10]
        
        # 去重
        if title in collected_titles:  # [11]
            continue
        collected_titles.add(title)

        # 获取BibTeX信息
        result_item.locate_view("引用").click()  # [12]
        ui.locate_view("BibTeX").click()
        ui.ensure("BibTeX详情页")
        bibtex_entry = ui.fetch_information("BibTeX纯文本内容", returns=("BibTeX纯文本内容", str))
        
        # 用AI从BibTeX中解析结构化数据
        authors, journal, year, publisher = fm.query(
            f"从以下BibTeX条目中提取作者、期刊或会议名称、年份、以及出版社: \n{bibtex_entry}",
            returns=[("作者", str), ("期刊或会议", str), ("年份", str), ("出版社", str)]
        )
        ui.back_to("搜索结果页")

        # 获取摘要和链接
        result_item.click()  # [13]
        if ui.check('当前是{title}的内容页面'):
            summary = ui.fetch_information("摘要", returns=("摘要", str))
            link = device.get_url()
            ui.back_to("搜索结果页")
        else:
            summary = result_item.fetch_information("摘要", returns=("摘要", str))
            link = result_item.fetch_information("论文链接", returns=("论文链接", str))
        
        related_papers_table.add_row({  # [14]
            "论文题目": title,
            "作者": authors,
            "发表单位": publisher,
            "发表期刊": journal,
            "发表时间": year,
            "内容简介": summary,
            "链接": link,
            "BibTeX": bibtex_entry
        })
        device.notify_message(f"已收集论文：'{title}'")  # [15]

# 第四步：任务收尾
device.notify_message("相关论文信息已收集完毕！")  # [16]
device.notify_table(related_papers_table)  # [17]
\`\`\`
`,

    EnglishExamples: `Task 1: "Collect information about the latest 20 WeChat contacts, including remarks, nicknames, avatars, main chat content, and recent Moments content.":
\`\`\`
[1]Create a data table {contact information table}, containing "Name", "Nickname", "ID", "Avatar", "Chat Summary", "Recent Moments" columns

[2]On device "phone1":
[3]    Navigate to the "WeChat" app's chat list page, for each chat session in the chat list, execute:
[4]        Navigate to that chat session, execute:
[5]            Determine if the current chat session is a one-on-one chat with a real person, if not, skip the current session
[6]            Get the contact of the current chat session, record as {current contact}
[7]            Get the current chat content, record as {chat content}
[8]            Ask AI: "Please generate a summary based on the chat content: {chat content}", output recorded as {chat summary}
[9]        Navigate to {current contact}'s profile page, get name, nickname, WeChat ID, avatar
[10]        Navigate to {current contact}'s Moments, get recent Moments content
[11]        Insert name, nickname, WeChat ID, avatar, chat summary, recent Moments content into the contact information table
[12]        Tell user: "Completed collecting {current contact} information, collected {contact information table.row count} entries"
[13]        If 20 contacts' information has been collected, break the loop
        
[14]Tell user "Successfully collected information about the latest 20 contacts, please check the file {contact information table}."
\`\`\`

\`\`\`
contact_info_table = data.live_table("Contact Information Table")  # [1]

device.set_device('phone1')  # [2]
device.start_app('WeChat')  # [3] Navigate to WeChat app
ui.ensure("WeChat chat list page")  # Ensure we're on the chat list page

for session in ui.iterate_views("chat session", limit=20):  # Iterate over chat sessions, limit to 20.
    session.click()  # [4] Navigate to the chat session.
    
    if not ui.check("one-on-one chat with real person"):  # [5] Check if it's a one-on-one chat with a real person.
        ui.back()  # Navigate back to the chat list.
        continue  # Skip this session.
    
    current_contact = ui.fetch_information("chat session contact", returns=("chat session contact", str))  # [6] Fetch the contact information.
    chat_content = ui.fetch_information("chat content", returns=("chat content", str))  # [7] Get the current chat content.
    
    chat_summary = fm.query(  # [8] Use AI to generate a summary of the chat content.
        f"Please generate a summary based on the chat content:\n{chat_content}", 
        returns=("chat summary", str)
    )
    
    ui.locate_view("contact avatar").click()  # [9] Navigate to contact's profile.
    ui.ensure(f"{current_contact} profile page")
    remark = ui.fetch_information("remark name", returns=("remark name", str))  # Get the remark.
    nickname = ui.fetch_information("nickname", returns=("nickname", str))  # Get the nickname.
    wechat_id = ui.fetch_information("WeChat ID", returns=("WeChat ID", str))  # Get WeChat ID.
    avatar = ui.fetch_image("avatar")  # Get avatar.
    
    ui.locate_view("Moments").click()  # [10] Navigate to contact's moments page.
    recent_moments = ui.fetch_information("recent Moments content", returns=("recent Moments content", str))  # Get recent moments content.
    ui.back_to("WeChat chat list page")  # Navigate back to chat list.

    contact_info_table.add_row(  # [11] Insert the collected data into the contact info table.
        {
            "Name": remark, 
            "Nickname": nickname,
            "ID": wechat_id,
            "Avatar": avatar,
            "Chat Summary": chat_summary,
            "Recent Moments": recent_moments
        }
    )
    
    device.notify_message(  # [12] Notify the user after collecting current contact's info.
        f"Completed collecting {current_contact} information, collected {len(contact_info_table)} entries"
    )

    if len(contact_info_table) >= 20:  # [13] Check if 20 contacts are collected.
        break  # Exit the loop once 20 records are collected.

device.notify_message(  # [14] Notify user the task is completed.
    f"Successfully collected information about the latest 20 contacts, please check the file: {contact_info_table}."
)
\`\`\`


Task 2: "Consolidate contacts from phone1, phone2, and phone3 into one table":
\`\`\`
[1]Define a task, named "get device contact list", task input: {device name}
[2]    On device {device name}:
[3]        Navigate to the "Contacts" app's contact list interface, traverse each contact, execute:
[4]            Navigate to that contact's profile page, get the contact's name, email, phone number
[5]            Add a row of data to the {current device contact list} table, containing: "Device": {device name}, "Name": {name}, "Email": {email}, "Phone Number": {phone number}
[6]        Return {current device contact list}

[7]Create an empty data table, record as {all contacts list}
[8]On devices 'phone1', 'phone2', 'phone3':
[9]    Tell user "Capturing contact list of device {device name}"
[10]    Execute task "get device contact list", input is {device name}, output result recorded as {current device contact list}
[11]    Add {current device contact list} to {all contacts list}

[12]Send {all contacts list} to user
\`\`\`

\`\`\`
def get_device_contact_list(device_name):  # [1]
    """Task: Get contact list from a single device"""
    device.set_device(device_name)  # [2]
    device.start_app('Contacts')  # [3]
    ui.ensure("contact list interface")
    
    # Create a temporary table for current device
    current_device_contacts = data.live_table(f"{device_name}_contacts")
    
    # Traverse contact list
    for contact_item in ui.iterate_views("contact entry", limit=500): # Set a higher limit
        contact_item.click()  # [4]
        ui.ensure("contact profile page")
        
        # Get contact information
        name = ui.fetch_information("name", returns=("name", str))
        email = ui.fetch_information("email", returns=("email", str))
        phone_number = ui.fetch_information("phone number", returns=("phone number", str))
        
        # Add to temporary table
        current_device_contacts.add_row({  # [5]
            "Device": device_name,
            "Name": name,
            "Email": email,
            "Phone Number": phone_number
        })
        ui.back() # Return to list page
        
    return current_device_contacts  # [6]

# Main process starts
all_contacts_list = data.live_table("All Contacts List")  # [7]

# Traverse all devices to operate
for device_name in ['phone1', 'phone2', 'phone3']:  # [8]
    device.notify_message(f"Capturing contact list of device {device_name}...")  # [9]
    
    # Execute task and get single device contact table
    single_device_table = get_device_contact_list(device_name)  # [10]
    
    # Merge single device contact data into total table
    for _, row in single_device_table.iterrows():  # [11]
        all_contacts_list.add_row(row.to_dict())

# Task completed, notify user
device.notify_table(all_contacts_list)  # [12]
\`\`\`


Task 3: "Collect information about Chinese historical dynasties, including dynasty, era names, emperors, duration, major events, corresponding Western historical periods and Sino-Western cultural exchange events, one emperor per row":
\`\`\`
# Step 1: Preparation, create a data table to store final results
[1]Create an empty data table, containing "Dynasty", "Era Name", "Emperor", "Reign Period", "Major Events", "Contemporary Western History", "Sino-Western Cultural Exchange Events" columns, record as {Chinese Historical Dynasty Information Table}

# Step 2: Get a list of all major Chinese dynasties
[2]Ask AI: "Please list all major dynasties in Chinese history, such as Qin, Han, Tang, Song, Yuan, Ming, Qing, etc.", output dynasty list, record as {dynasty list}
[3]Tell user "Dynasty list obtained, will start collecting detailed information one by one..."

# Step 3: Traverse each dynasty and collect information about its emperors
[4]Traverse each {current dynasty} in {dynasty list}:
[5]    Tell user "Collecting information about [{current dynasty}]..."
    # 3.1 Get all emperors of the current dynasty
[6]    Ask AI: "Please list all emperor names of {current dynasty}", output emperor name list, record as {emperor list}

    # 3.2 Traverse each emperor of the current dynasty, query and record their detailed information
[7]    Traverse each {current emperor} in {emperor list}:
        # 3.2.1 For each emperor, make a detailed query to AI
[8]        Ask AI: "Please provide detailed information about '{current emperor}' of '{current dynasty}', specifically including: 1. Their commonly used era names; 2. Reign start and end dates; 3. Major historical events during their reign; 4. Corresponding Western historical periods or major countries during their reign; 5. Representative Sino-Western cultural exchange events of this period.", output recorded as {era name}, {reign period}, {major events}, {contemporary Western history}, {Sino-Western cultural exchange events}
        
        # 3.2.2 Add the queried information as a new row to the data table
[9]        Add a row of data {"Dynasty":{current dynasty}, "Era Name":{era name}, "Emperor":{current emperor}, "Reign Period":{reign period}, "Major Events":{major events}, "Contemporary Western History":{contemporary Western history}, "Sino-Western Cultural Exchange Events":{Sino-Western cultural exchange events}} to {Chinese Historical Dynasty Information Table}
        
        # 3.2.3 Provide real-time feedback to user
[10]        Tell user "Completed collecting information for {current dynasty} {current emperor}."

# Step 4: Task completion, notify user
[11]Tell user "All dynasty information has been collected, please check {Chinese Historical Dynasty Information Table}."
\`\`\`

\`\`\`
# Step 1: Preparation
dynasty_info_table = data.live_table("Chinese Historical Dynasty Information Table")  # [1]

# Step 2: Get dynasty list
dynasty_list = fm.query(  # [2]
    "Please list all major dynasties in Chinese history, such as Qin, Han, Tang, Song, Yuan, Ming, Qing, etc.",
    returns=[('dynasty list', list[str])]
)
device.notify_message("Dynasty list obtained, will start collecting detailed information one by one...")  # [3]

# Step 3: Traverse each dynasty and emperor
for current_dynasty in dynasty_list:  # [4]
    device.notify_message(f"Collecting information about [{current_dynasty}]...")  # [5]
    
    # 3.1 Get all emperors of current dynasty
    emperor_list = fm.query(  # [6]
        f"Please list all emperor names of {current_dynasty}",
        returns=[('emperor list', list[str])]
    )
    
    # 3.2 Traverse each emperor of current dynasty
    for current_emperor in emperor_list:  # [7]
        # 3.2.1 Detailed query
        prompt = f"Please provide detailed information about '{current_emperor}' of '{current_dynasty}', specifically including: 1. Their commonly used era names; 2. Reign start and end dates; 3. Major historical events during their reign; 4. Corresponding Western historical periods or major countries during their reign; 5. Representative Sino-Western cultural exchange events of this period."
        reign_name, reign_period, main_events, contemporary_west, cultural_exchange = fm.query(  # [8]
            prompt,
            returns=[
                ("era name", str), 
                ("reign period", str), 
                ("major events", str), 
                ("contemporary Western history", str), 
                ("Sino-Western cultural exchange events", str)
            ]
        )
        
        # 3.2.2 Add to data table
        dynasty_info_table.add_row({  # [9]
            "Dynasty": current_dynasty,
            "Era Name": reign_name,
            "Emperor": current_emperor,
            "Reign Period": reign_period,
            "Major Events": main_events,
            "Contemporary Western History": contemporary_west,
            "Sino-Western Cultural Exchange Events": cultural_exchange
        })
        
        # 3.2.3 Real-time feedback
        device.notify_message(f"Completed collecting information for {current_dynasty} {current_emperor}.")  # [10]

# Step 4: Task completion
device.notify_message("All dynasty information has been collected!")  # [11]
device.notify_table(dynasty_info_table)
\`\`\`


Task 4: "Collect information about all prefecture-level cities in China, including introduction, images, population, average income, housing prices, and representative cuisine":
\`\`\`
# Step 1: Preparation, create a data table to store final results
[1]Create a data table, record as {city information table}, containing "City Name", "Province", "City Introduction", "Representative Image", "Population", "Average Annual Income", "Average Housing Price", "Representative Cuisine" columns

# Step 2: Get a list of all prefecture-level cities in China
[2]Ask AI: "Please list the names of all prefecture-level cities in China", output city name list, record as {city list}
[3]Get the number of elements in {city list}, record as {total number of cities}
[4]Tell user "Successfully obtained the national prefecture-level city list, totaling {total number of cities}. Now starting to collect detailed data one by one..."

# Step 3: Traverse each city, collect its detailed information and fill into the table
[5]Traverse each {current city} in {city list}:
    # 3.1 Provide current progress feedback to user
[6]    Tell user "Collecting information about [{current city}]... (Progress: {current loop count}/{total number of cities})"
    
    # 3.2 For each city, query AI for textual information
    # Combine multiple information points in one query to improve efficiency
[7]    Ask AI: "Please provide detailed information about '{current city}', specifically including: 1. A city introduction of about 200 words; 2. Latest population figures; 3. Average annual income; 4. Average housing prices; 5. List of representative cuisine; 6. Province where it's located", output recorded as {city introduction}, {population}, {average income}, {housing prices}, {representative cuisine}, {province} # Enable search function when calling AI large model
    
    # 3.3 For each city, separately query for a representative image
[8]    In browser:
[9]        Navigate to Baidu image search page, search "{current city} images", get interface screenshot, record as {city image}
    
    # 3.4 Add all queried information as a new row to the data table
[10]    Add a row of data {"City Name":{current city}, "Province":{province}, "City Introduction":{city introduction}, "Representative Image":{city image}, "Population":{population}, "Average Annual Income":{average income}, "Average Housing Price":{housing prices}, "Representative Cuisine":{representative cuisine}} to {city information table}

# Step 4: Task completion, notify user
[11]Tell user "All data has been processed and successfully saved to {city information table}."
\`\`\`

\`\`\`
# Step 1: Preparation
city_info_table = data.live_table("City Information Table")  # [1]

# Step 2: Get city list
city_list = fm.query(  # [2]
    "Please list the names of all prefecture-level cities in China",
    returns=[('city list', list[str])]
)
city_count = len(city_list)  # [3]
device.notify_message(f"Successfully obtained the national prefecture-level city list, totaling {city_count}. Now starting to collect detailed data one by one...")  # [4]

# Step 3: Traverse each city
for i, current_city in enumerate(city_list):  # [5]
    # 3.1 Progress feedback
    device.notify_message(f"Collecting information about [{current_city}]... (Progress: {i+1}/{city_count})")  # [6]
    
    # 3.2 Query textual information (AI needs to enable search function for real-time data)
    prompt = f"Please provide detailed information about '{current_city}', specifically including: 1. A city introduction of about 200 words; 2. Latest population figures; 3. Average annual income; 4. Average housing prices; 5. List of representative cuisine; 6. Province where it's located"
    summary, population, income, housing_price, foods, province = fm.query(  # [7]
        prompt,
        returns=[
            ("city introduction", str),
            ("population", str),
            ("average annual income", str),
            ("average housing price", str),
            ("representative cuisine", list[str]),
            ("province", str)
        ]
    )
    
    # 3.3 Get representative image
    device.set_device('browser1')  # [8]
    device.web_search(f"{current_city} scenery")  # [9]
    ui.wait_until("image search results")
    city_image = device.take_screenshot()
    
    # 3.4 Add to data table
    city_info_table.add_row({  # [10]
        "City Name": current_city,
        "Province": province,
        "City Introduction": summary,
        "Representative Image": data.image(city_image),
        "Population": population,
        "Average Annual Income": income,
        "Average Housing Price": housing_price,
        "Representative Cuisine": ", ".join(foods) # Convert list to string
    })

# Step 4: Task completion
device.notify_message("All city information has been collected!")  # [11]
device.notify_table(city_info_table)
\`\`\`

Task 5: "Help me collect the top 100 popular comments under Lei Jun's video account, record comment users, time, likes, and generate appropriate replies":
\`\`\`
# Task: Collect the top 100 popular comments from Lei Jun's video account and generate replies

# Step 1: Preparation, create data table
[1]Create a data table {comment data table}, containing "Comment User", "Comment Content", "Comment Time", "Likes", "AI Generated Reply" columns

# Step 2: Navigate to Lei Jun's video account comment section
[2]On device "phone1":
[3]    Navigate to the "WeChat" app's video account interface, search "Lei Jun" and navigate to Lei Jun's video account homepage
[4]    Tell user "Entered Lei Jun's video account homepage, starting to collect the top 100 popular comments..."
[5]    Traverse each video on the video account homepage:
[6]        Navigate to the current video's comment section, for each comment in the current interface, execute:
[7]            Get the "user nickname" of that comment, record as {username}
[8]            Get the "comment content" of that comment, record as {comment content}
[9]            Get the "publish time" of that comment, record as {publish time}
[10]            Get the "likes" of that comment, record as {likes}
            # Design a prompt for AI to mimic Lei Jun's style in replies
[11]            Ask AI: "You are now Lei Jun's social media assistant, please generate a friendly, sincere and appropriate thank you or interactive reply to this user comment: '{comment content}', the style should be warm and natural.", output text, record as {AI reply}
[12]            Add a row of data {"Comment User":{username}, "Comment Content":{comment content}, "Comment Time":{publish time}, "Likes":{likes}, "AI Generated Reply":{AI reply}} to {comment data table}
[13]            Get the number of rows in {comment data table}, record as {collected count}
[14]            Tell user "Collected comment #{collected count}: user '{username}'"
[15]            If {collected count} exceeds 100, end loop

[16]Tell user "The top 100 popular comments have been fully collected and processed! Results saved in {comment data table}"
\`\`\`

\`\`\`
# Step 1: Preparation
comment_table = data.live_table("Comment Data Table")  # [1]

# Step 2: Navigation
device.set_device('phone1')  # [2]
device.start_app('WeChat')  # [3]
ui.locate_view("Discover").click()
ui.locate_view("Video Account").click()
ui.locate_view("Search").click()
ui.locate_view("Search Box").input("Lei Jun")
ui.locate_view("Search Button").click()
ui.locate_view("Lei Jun's Video Account Homepage").click()
ui.ensure("Lei Jun's Video Account Homepage")
device.notify_message("Entered Lei Jun's video account homepage, starting to collect comments...")  # [4]

# Step 3: Loop collection
collected_count = 0
stop_collecting = False

for video_item in ui.iterate_views("video account homepage video entry"):  # [5]
    if stop_collecting:
        break
    
    video_item.click()  # [6]
    ui.wait_until("video playing page")
    ui.locate_view("comment section entrance").click()
    ui.ensure("comment list")
    
    for comment_item in ui.iterate_views("comment entry"):
        # Extract information
        username = comment_item.fetch_information("user nickname", returns=("user nickname", str))  # [7]
        content = comment_item.fetch_information("comment content", returns=("comment content", str))  # [8]
        post_time = comment_item.fetch_information("publish time", returns=("publish time", str))  # [9]
        like_count = comment_item.fetch_information("likes", returns=("likes", int))  # [10]
        
        # Generate reply
        ai_reply = fm.query(  # [11]
            f"You are now Lei Jun's social media assistant, please generate a friendly, sincere and appropriate thank you or interactive reply to this user comment: '{content}', the style should be warm and natural.",
            returns=("generated reply", str)
        )
        
        # Add to table
        comment_table.add_row({  # [12]
            "Comment User": username,
            "Comment Content": content,
            "Comment Time": post_time,
            "Likes": like_count,
            "AI Generated Reply": ai_reply
        })
        
        collected_count += 1  # [13]
        device.notify_message(f"Collected comment #{collected_count}: user '{username}'")  # [14]
        
        # Check if 100 comments collected
        if collected_count >= 100:  # [15]
            stop_collecting = True
            break # Exit inner loop
            
    ui.back_to("Lei Jun's Video Account Homepage") # Return from comment section to homepage

# Step 4: Task completion
device.notify_message("The top 100 popular comments have been fully collected and processed!")  # [16]
device.notify_table(comment_table)
\`\`\`

Task 6: "Collect research papers related to the paper.pdf article, recording titles, authors, institutions and other information":
\`\`\`
# Step 1: Preparation, create data table and analyze source paper
[1]Create a data table {related papers table}
[2]Tell user "Data table created, analyzing source file 'paper.pdf'..."
[3]Read document "paper.pdf", record as {article content}
[4]Ask AI: "Please generate some keywords for searching related work for this article. {article content}", output recorded as {search keyword list}
[5]Tell user "Will search the following keywords in Google Scholar to obtain related literature: {search keyword list}"

# Step 2: Use browser for academic search
[6]For each {keyword} in {search keyword list}:
[7]    On device "browser1":
        # Prioritize academic search engines for higher quality results
[8]        Navigate to "https://scholar.google.com"
[9]        Search {keyword}, for each search result entry in the search results, up to 20, execute:
[10]            Get the paper title of that entry
[11]            If the paper title already exists in {related papers table}, skip this paper
[12]            Navigate to that paper's bibtex citation page, get author list, publication journal, publication time, bib entry
[13]            Navigate to that paper's content page, get content summary, link, institution list
[14]            Add paper title, author list, publication journal, publication time, bib entry, content summary, link, institution list as a row of data to {related papers table}            
            # 3.4 Provide real-time progress feedback to user
[15]            Tell user "Collected paper: '{paper title}'"

# Step 4: Task completion, save and notify user
[16]Tell user "Related paper information collection completed!"
[17]Tell user "All data has been successfully saved to {related papers table}"
\`\`\`

\`\`\`
# Step 1: Preparation
related_papers_table = data.live_table("Related Papers Table")  # [1]
device.notify_message("Data table created, analyzing source file 'paper.pdf'...")  # [2]

# Analyze source paper (assuming there's an API to read files)
paper_content = data.read_document("paper.pdf")  # [3]
keyword_list = fm.query(  # [4]
    f"Please generate some keywords for searching related work for this article: {paper_content}",
    returns=[('search keyword list', list[str])]
)
device.notify_message(f"Will search the following keywords in Google Scholar to obtain related literature: {keyword_list}")  # [5]

# Step 2 & Step 3: Search and collection
device.set_device('browser1')  # [7]
collected_titles = set()

for keyword in keyword_list:  # [6]
    device.open_url("https://scholar.google.com")  # [8]
    ui.locate_view("search box").input(keyword)  # [9]
    ui.locate_view("search button").click()
    ui.ensure("search results page")
    
    for result_item in ui.iterate_views("search result entry", limit=20):
        title = result_item.fetch_information("paper title", returns=("paper title", str))  # [10]
        
        # Deduplication
        if title in collected_titles:  # [11]
            continue
        collected_titles.add(title)

        # Get BibTeX information
        result_item.locate_view("cite").click()  # [12]
        ui.locate_view("BibTeX").click()
        ui.ensure("BibTeX details page")
        bibtex_entry = ui.fetch_information("BibTeX plain text content", returns=("BibTeX plain text content", str))
        
        # Use AI to parse structured data from BibTeX
        authors, journal, year, publisher = fm.query(
            f"Extract authors, journal or conference name, year, and publisher from the following BibTeX entry: \n{bibtex_entry}",
            returns=[("authors", str), ("journal or conference", str), ("year", str), ("publisher", str)]
        )
        ui.back_to("search results page")

        # Get abstract and link
        result_item.click()  # [13]
        if ui.check(f'current is {title} content page'):
            summary = ui.fetch_information("abstract", returns=("abstract", str))
            link = device.get_url()
            ui.back_to("search results page")
        else:
            summary = result_item.fetch_information("abstract", returns=("abstract", str))
            link = result_item.fetch_information("paper link", returns=("paper link", str))
        
        related_papers_table.add_row({  # [14]
            "Paper Title": title,
            "Authors": authors,
            "Institution": publisher,
            "Publication Journal": journal,
            "Publication Time": year,
            "Content Summary": summary,
            "Link": link,
            "BibTeX": bibtex_entry
        })
        device.notify_message(f"Collected paper: '{title}'")  # [15]

# Step 4: Task completion
device.notify_message("Related paper information collection completed!")  # [16]
device.notify_table(related_papers_table)  # [17]
\`\`\`
`
};

// Template functions for each section
const templateFunctions = {
    // Generate Workflow template
    generateWorkflow: (params) => {
        const { conversationHistory, newUserMessage, currentWorkflow, appKnowledge, needTaskName, languageCode = 'zh' } = params;
        
        return `${templateVariables.ruyiDSLSyntax}

${languageCode === 'zh' ? templateVariables.ChineseRuyiDSLSyntaxExamples : templateVariables.EnglishRuyiDSLSyntaxExamples}

# Your Job

You are a professional assistant trained to use the Ruyi language to complete diverse and complex tasks. The tasks can be related to data collection/processing, browser/device use, daily routine automation, etc.
You should carefully understand the user's requirement based on conversation, then write professional workflows in Ruyi language that can fulfill the user's needs. You are encouraged to do your job precisely and thoughtfully, beyond the user's expectations.

# Your Work Procedure

## Phase 1: Requirement Communication

In phase 1, your goal is to precisely understand the user's task requirement based on his/her messages. If there is any ambiguity, you can ask the user for clarification. If the task requirement is clear to you, proceed to the next phase. For example, if the user requests "book a ticket", you should ask where the destination is, which dates the user would like to travel, etc.

## Phase 2: Workflow Generation

In phase 2, your goal is to generate a high-level workflow that summarizes the steps to complete the user-given task. The workflow should:
1. Follow the syntax of Ruyi (a natural-language-style domain-specific language designed to describe workflows). 
2. Decide whether and how to use the files, devices and apps in the workspace. Any non-existing file/device/app is not allowed in the workflow.
Try to make the workflow generalizable (i.e. does not involve low-level operations that are only valid for specific data formats or UI design patterns).

${appKnowledge ? `## Additional Notes

The following information is provided by the user, which may contain custom requirements, preferences, and hints:

${appKnowledge}` : ''}

# The Current Task

Here is your conversation history with the user:

${conversationHistory}

Now, the user sends you a message:

${newUserMessage}

# The Current Workflow

Here is the existing workflow (The user may request to modify the current workflow or create a new one):

\`\`\`
${currentWorkflow}
\`\`\`

# Output Instruction

${languageCode === 'zh' ? 
    'Note: Current user\'s language is 中文. When writing workflow/question/task_name, you need to use 中文 to write.' : 
    'Note: Current user\'s language is English. When writing workflow/question/task_name, you need to use English to write.'
}

Now, please first decide which phase to take. Then respond with the following content:

Your thoughts (wrapped with <thought></thought>), which contains your understanding about the task and plan of how to do the task in natural language.

The question (wrapped with <question></question>), which is the question to the user (If you decide phase 1 to take). Or the workflow (wrapped with <workflow></workflow>), which is the workflow steps in Ruyi syntax generated based on your understanding (If you decide phase 2 to take).

${needTaskName ? 'Besides, you should summarize the user\'s task into a task name, and wrap it in <task_name></task_name>.' : ''}`;
    },

    // Transfer Workflow to Code template
    transferWorkflowToCode: (params) => {
        const { taskDescription, currentLabeledPythonScript, oldLabeledWorkflow, currentLabeledWorkflow, languageCode = 'zh' } = params;
        
        const exampleSection = languageCode === 'zh' ? templateVariables.ChineseExamples : templateVariables.EnglishExamples;
        
        if (currentLabeledPythonScript !== '' && currentLabeledPythonScript !== undefined && currentLabeledPythonScript !== null) {
            // Update existing Python script
            return `${templateVariables.workflowToPythonHint}

${templateVariables.ruyiPythonFrameworkIntroduction}

${templateVariables.examplesIntroduction}

${exampleSection}

# Your Job

You are a professional assistant trained to use the Ruyi language to complete diverse and complex tasks. The tasks can be related to data collection/processing, browser/device use, daily routine automation, etc.
Now, the user has updated a workflow in Ruyi language, and you need to update the Python script to match the new workflow.
You can use the Ruyi Python Framework or native python to update the Python script.

# The Task / Workflow / Python Script

Here is the current task:

${taskDescription}

Here is the old workflow:

\`\`\`
${oldLabeledWorkflow}
\`\`\`

Here is the current workflow (updated):

\`\`\`
${currentLabeledWorkflow}
\`\`\`

Here is the current Python script (to be updated):

\`\`\`
${currentLabeledPythonScript}
\`\`\`

# Output Instruction

Note:
    For Python script: 
        The 'agent, device, ui, data, fm, tools' variables are predefined in context, you don't need to import them. You can directly use them.
        The device name in device.set_device needs to be written in English, such as 'phone1', 'browser1'.

Please respond with the following content:

Your thoughts (wrapped with <thought></thought>), which contains your plan of how to update the Python script to match the new workflow.

The labeled Python script (wrapped with <labeled_python_script></labeled_python_script>) updated from current Python script.`;
        } else {
            // Create new Python script
            return `${templateVariables.workflowToPythonHint}

${templateVariables.ruyiPythonFrameworkIntroduction}

${templateVariables.examplesIntroduction}

${exampleSection}

# Your Job

You are a professional assistant trained to use the Ruyi language to complete diverse and complex tasks. The tasks can be related to data collection/processing, browser/device use, daily routine automation, etc.
Now, the user has written a workflow in Ruyi language, and you need to convert it to Python script.
You can use the Ruyi Python Framework or native python to convert the Ruyi workflow to Python script.

# The Current Task / Workflow

Here is the current task:

${taskDescription}

Here is the current workflow:

\`\`\`
${currentLabeledWorkflow}
\`\`\`

# Output Instruction

Note:
    For Python script: 
    The 'agent, device, ui, data, fm, tools' variables are predefined in context, you don't need to import them. You can directly use them.
    The device name in device.set_device needs to be written in English, such as 'phone1', 'browser1'.

Please respond with the following content:

Your thoughts (wrapped with <thought></thought>), which contains your plan of how to convert the Ruyi workflow to Python script.

The labeled Python script (wrapped with <labeled_python_script></labeled_python_script>) converted from the Ruyi workflow.`;
        }
    },

    // Transfer Code to Workflow template
    transferCodeToWorkflow: (params) => {
        const { taskDescription, currentLabeledWorkflow, oldLabeledPythonScript, currentPythonScript, languageCode = 'zh' } = params;
        
        const exampleSection = languageCode === 'zh' ? templateVariables.ChineseExamples : templateVariables.EnglishExamples;
        
        return `${templateVariables.workflowToPythonHint}

${templateVariables.ruyiPythonFrameworkIntroduction}

${templateVariables.examplesIntroduction}

${exampleSection}

# Your Job

You are a professional assistant trained to use the Ruyi language to complete diverse and complex tasks. The tasks can be related to data collection/processing, browser/device use, daily routine automation, etc.
Now, the user has updated a Python script, and you need to update the workflow to match the new Python script.
You need to use the Ruyi DSL to update the workflow. After that, you need to add label to the current Python script.

# The Task / Workflow / Python Script

Here is the current task:

${taskDescription}

Here is the old Python script:

\`\`\`
${oldLabeledPythonScript}
\`\`\`

Here is the current Python script (updated, to be labeled):

\`\`\`
${currentPythonScript}
\`\`\`

Here is the current workflow (to be updated):

\`\`\`
${currentLabeledWorkflow}
\`\`\`

# Output Instruction

Please respond with the following content:

Your thoughts (wrapped with <thought></thought>), which contains your plan of how to update the workflow to match the new Python script.

The labeled workflow (wrapped with <labeled_workflow></labeled_workflow>) updated from current workflow.

The labeled Python script (wrapped with <labeled_python_script></labeled_python_script>) updated from current Python script.`;
    },

    // Transfer Selected Workflow to Code template
    transferSelectedWorkflowToCode: (params) => {
        const { taskDescription, currentLabeledWorkflow, currentLabeledPythonScript, selectedLabeledWorkflow, languageCode = 'zh' } = params;
        
        const exampleSection = languageCode === 'zh' ? templateVariables.ChineseExamples : templateVariables.EnglishExamples;
        
        return `${templateVariables.workflowToPythonHint}

${templateVariables.ruyiPythonFrameworkIntroduction}

${templateVariables.examplesIntroduction}

${exampleSection}

# Your Job

You are a professional assistant trained to use the Ruyi language to complete diverse and complex tasks. The tasks can be related to data collection/processing, browser/device use, daily routine automation, etc.
The user has written a workflow using Ruyi DSL, and this workflow has been transferred to Python script.
Now, the user selects a part of the workflow, and you need to transfer this part to Python script.

# The Task / Workflow / Python Script

Here is the current task:

${taskDescription}

Here is the current workflow:

\`\`\`
${currentLabeledWorkflow}
\`\`\`

Here is the selected workflow lines:

\`\`\`
${selectedLabeledWorkflow}
\`\`\`

Here is the current Python script:

\`\`\`
${currentLabeledPythonScript}
\`\`\`

# Output Instruction

Note:
    If the selected part of the workflow requires necessary context (e.g., variable or function definitions) to execute, and this information is missing, it may cause syntax errors in the Python Script. Please add this context information to the converted Python script to ensure it can be executed without syntax errors.

Please respond with the following content:

Your thoughts (wrapped with <thought></thought>), which contains your plan of how to transfer the selected workflow to Python script.

The labeled Python script (wrapped with <labeled_python_script></labeled_python_script>) transferred from selected workflow.`;
    }
};

// Export the template functions for use by the template manager
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = {
        templateVariables,
        templateFunctions
    };
} else {
    // Browser environment
    window.RuyiTemplate = {
        templateVariables,
        templateFunctions
    };
}
