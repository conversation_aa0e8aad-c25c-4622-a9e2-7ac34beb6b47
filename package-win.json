{"name": "ruyi-ide", "version": "0.4.19", "description": "Ruyi Studio", "main": "src/main/main.js", "author": "<PERSON><PERSON>i Developer Team", "license": "MIT", "scripts": {"start": "concurrently \"npm run start:renderer\" \"npm run start:main\"", "start:renderer": "vite", "start:main": "electron .", "build": "rimraf dist dist-src && npm run build:renderer && npm run build:main && npm run build:electron", "build:renderer": "vite build", "build:main": "webpack --config webpack.main.config.js", "build:electron": "set DEBUG=electron-builder&& electron-builder", "build:electron-unsigned": "electron-builder --config.mac.identity=null", "build:mac-unsigned": "npm run build:renderer && npm run build:main && electron-builder --config.mac.identity=null", "preview": "vite preview", "rebuild": "electron-rebuild -f -w node-pty", "upload-release": "node scripts/upload-release.js", "release": "npm run build && npm run upload-release", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/x": "^1.0.5", "@dead50f7/adbkit": "2.11.5", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "antd": "^5.0.0", "appium-support": "2.55.0", "appium-xcuitest-driver": "3.62.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "electron-squirrel-startup": "^1.0.1", "electron-store": "^10.1.0", "electron-updater": "^6.1.7", "express": "^4.19.2", "form-data": "^4.0.2", "ios-device-lib": "0.9.3", "isomorphic-git": "^1.30.2", "js-yaml": "^4.1.0", "lucide-react": "^0.517.0", "monaco-editor": "^0.52.2", "node-fetch": "^2.7.0", "node-mjpeg-proxy": "0.3.2", "node-pty": "^1.1.0-beta22", "npmlog": "6.0.2", "papaparse": "^5.5.3", "portfinder": "1.0.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.0.0", "socket.io-client": "^4.7.5", "tar": "^7.4.3", "tslib": "2.6.2", "ws": "8.17.1", "xlsx": "^0.18.5", "yaml": "2.4.1"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/papaparse": "^5.3.16", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "concurrently": "^8.0.0", "electron": "32.2.6", "electron-builder": "^25.1.8", "electron-rebuild": "^3.2.9", "rimraf": "^5.0.10", "terser-webpack-plugin": "^5.3.14", "vite": "5.4.11", "wait-on": "^7.0.1", "webpack": "^5.99.6", "webpack-cli": "^6.0.1"}}