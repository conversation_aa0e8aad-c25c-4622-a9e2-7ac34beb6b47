appId: com.ruyi.ide
productName: Ruyi Studio
directories:
  output: out
  buildResources: build
files:
  - dist/**/*
  - from: "dist-src/main"
    to: "src/main"
  - from: "dist-src/services"
    to: "src/services"
  - src/**/*
  - package.json
  - "!src/main/**"
  - "!src/services/**"
  - "!src/backend/**"
  - "!src/backend-pr/**"
  - "!src/backend-full/**"
  - "!src/backend-simplified/**"
  - "!src/backend-obf/**"
  - "!src/renderer/**"
  - "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}"
  - "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}"
  - "!**/.DS_Store"
  - "!**/._*"
  - "!**/.git"
  - "!**/.git/**/*"
extraResources:
  # - from: "frontend/dist"
  #   to: "ruyi-ide-frontend"
  - from: "src/backend"
    to: "ruyi-ide-backend"
    filter:
      - "**/*"
      # - "!env/**"
      - "!ruyi_ide_env.tar.gz"
      # - "!assets"
      - "!app/RuyiDSL/**"
      - "!app/RuyiAgent-ZSH/**"
      - "!app/RuyiAgent-MobileLLM/**"
      - "!RecordResult/**"
      - "!test.ipynb"
      - "!.idea/**"
      - "!__pycache__/**"
      - "!**/__pycache__/**"
      - "!**/tests/**" # 排除测试文件夹
      - "!**/*.pyc" # 排除编译的 Python 文件
      - "!**/.DS_Store"
      - "!**/._*"
      - "!**/.git" # 新增：排除所有 .git 文件
      - "!**/.git/**/*"
      - "!**/Squirrel.framework/**"
  - from: "resources/platform-tools/${platform}" # ${platform} 会自动匹配当前平台
    to: "platform-tools"
    filter:
      - "**/*"
  # 添加字体文件配置
  - from: "src/renderer/pages/fonts"
    to: "fonts"
    filter:
      - "**/*.ttf"
asar: true # 启用 asar 压缩
asarUnpack:
  - "resources/**/*"
  - "node_modules/graceful-fs/**/*"
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: "resources/icon/ruyi.ico"
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  installerIcon: "resources/icon/ruyi.ico"
  uninstallerIcon: "resources/icon/ruyi.ico"
  installerHeaderIcon: "resources/icon/ruyi.ico"
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: "Ruyi Studio"
  installerLanguages: ["zh_CN", "en_US"]
  deleteAppDataOnUninstall: false
mac:
  target:
    # - dir
    - dmg
    - zip
    # - target: dir  # 先只输出目录，不打包
  # arch:
  #   - arm64  # 只构建 arm64 版本
  # identity: "-"
  # hardenedRuntime: false # 关闭硬化运行时
  notarize: false
  identity: "Shanhui Zhao (2QL2QZNHGZ)"
  # identity: null
  hardenedRuntime: true
  entitlements: "resources/entitlements.mac.plist"
  entitlementsInherit: "resources/entitlements.mac.plist"
  category: public.app-category.developer-tools
  icon: "resources/icon/ruyiAppIcon.icns"
  artifactName: "Ruyi Studio-${version}-${arch}.${ext}"
  electronLanguages: ["zh_CN", "en"]
linux:
  target:
    - AppImage
    - deb
  icon: "resources/icon/Ruyi-圆角.png"
  category: Development  # 应用程序分类
  maintainer: "Ruyi Studio Team"  # 维护者信息
  synopsis: "Ruyi Studio for Linux"  # 简短描述
  description: "Ruyi Studio - 开发工具"  # 详细描述
  artifactName: "Ruyi Studio-${version}-${arch}.${ext}"  # 输出文件命名格式
# afterPack: "scripts/clean-mac.js"
# afterSign: "scripts/notarize.js"
publish:
  # provider: github
  # owner: MobileLLM
  # repo: RuyiCloudIDE
  # private: true
  # token: ${env.GH_TOKEN_RUYI_IDE}
  # releaseType: draft
  provider: generic
  url: "https://wisewk.com/updates/ruyi-ide/"
  channel: latest
