const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  mode: 'production',
  target: 'electron-main',
  entry: {
    'main/main': './src/main/main.js',
    'main/preload': './src/main/preload.js',
    'services/FlaskApiService': './src/services/FlaskApiService.js',
    'services/config.main': './src/services/config.main.js',
    'services/config.renderer': './src/services/config.renderer.js',
    'services/api': './src/services/api.js',
    'services/ScrcpyManager': './src/services/ScrcpyManager.js'
  },
  output: {
    path: path.resolve(__dirname, 'dist-src'),
    filename: '[name].js'
  },
  externals: {
    'graceful-fs': 'commonjs2 graceful-fs',
    'fs': 'commonjs2 fs',
    'path': 'commonjs2 path',
    'electron': 'commonjs2 electron',
    'child_process': 'commonjs2 child_process',
    'os': 'commonjs2 os',
    'crypto': 'commonjs2 crypto',
    'node-fetch': 'commonjs2 node-fetch'
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: false,
            drop_debugger: true,
            pure_funcs: ['console.info', 'console.debug', 'console.trace']
          },
          mangle: {
            reserved: [
              'process', 
              'require', 
              'module', 
              'exports', 
              '__dirname', 
              '__filename',
              'Buffer',
              'global',
              'GLOBAL'
            ],
            properties: false
          },
          output: {
            comments: false
          },
          keep_fnames: true,
          keep_classnames: true
        },
        extractComments: false
      })
    ]
  },
  resolve: {
    extensions: ['.js', '.json'],
    fallback: {
      "path": false,
      "fs": false,
      "crypto": false,
      "os": false
    }
  },
  node: {
    __dirname: false,
    __filename: false,
    global: true
  }
}; 