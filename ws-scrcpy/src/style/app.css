:root {
    --main-bg-color: hsl(0, 0%, 100%);
    --stream-bg-color: hsl(0, 0%, 85%);
    --shell-bg-color: hsl(0, 0%, 0%);
    --text-shadow-color: hsl(218, 67%, 95%);
    --header-bg-color: hsl(0, 0%, 95%);
    --controls-bg-color: hsla(0, 0%, 95%, 0.8);
    --control-buttons-bg-color: hsl(0, 0%, 95%);
    --text-color: hsl(210, 16%, 22%);
    --text-color-light: hsl(200, 16%, 52%);
    --link-color: hsl(218, 85%, 43%);
    --link-color-light: hsl(218, 85%, 73%);
    --link-color_visited: hsl(271, 68%, 32%);
    --link-color_visited-light: hsl(271, 68%, 72%);
    --svg-checkbox-bg-color: hsl(172, 100%, 37%);
    --svg-button-fill: hsl(199, 17%, 46%);
    --kill-button-hover-color: hsl(342, 100%, 37%);
    --url-color: hsl(0, 0%, 60%);
    --button-text-color: hsl(214, 82%, 51%);
    --button-border-color: hsl(0, 0%, 70%);
    --progress-background-color: hsla(225, 100%, 50%, 0.2);
    --progress-background-error-color: hsla(0, 100%, 50%, 0.2);
    --font-size: 14px;
}

@media (prefers-color-scheme: dark) {
    :root {
        --main-bg-color: hsl(0, 0%, 14%);
        --stream-bg-color: hsl(0, 0%, 20%);
        --shell-bg-color: hsl(0, 0%, 0%);
        --text-shadow-color: hsl(218, 17%, 18%);
        --header-bg-color: hsl(0, 0%, 20%);
        --controls-bg-color: hsla(201, 18%, 19%, 0.8);
        --control-buttons-bg-color: hsl(201, 18%, 19%);
        --text-color: hsl(0, 0%, 90%);
        --text-color-light: hsl(0, 0%, 60%);
        --link-color: hsl(218, 63%, 70%);
        --link-color-light: hsl(218, 63%, 50%);
        --link-color_visited: hsl(267, 31%, 47%);
        --link-color_visited-light: hsl(267, 31%, 27%);
        --svg-checkbox-bg-color: hsl(172, 100%, 27%);
        --svg-button-fill: hsl(0, 0%, 100%);
        --kill-button-hover-color: hsl(342, 100%, 27%);
        --url-color: hsl(0, 0%, 60%);
        --device-list-stripe-color: hsl(0, 0%, 16%);
        --device-list-default-color: hsl(0, 0%, 14%);
        --button-text-color: hsl(214, 82%, 76%);
        --button-border-color: hsl(213, 5%, 39%);
        --progress-background-color: hsla(225, 100%, 50%, 0.2);
        --progress-background-error-color: hsla(0, 100%, 50%, 0.2);
    }
}

html {
    font-size: var(--font-size);
}

a {
    color: var(--link-color);
}

a:visited {
    color: var(--link-color_visited);
}

body {
    color: var(--text-color);
    background-color: var(--main-bg-color);
    position: absolute;
    margin: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
}


body.shell {
    background-color: var(--shell-bg-color);
}

body.stream {
    background-color: var(--stream-bg-color);
}

.terminal-container {
    width: 100%;
    height: 100%;
    padding: 5px;
}

:focus {
    outline: none;
}

.flex-center {
    display: flex;
    align-items: center;
}

.wait {
    cursor: wait;
}

.video-layer {
    position: absolute;
    z-index: 0;
}

.touch-layer {
    position: absolute;
    z-index: 1;
}

.video {
    float: right;
    max-height: 100%;
    max-width: 100%;
    background-color: #000000;
}


.control-buttons-list {
    float: right;
    width: 3.715rem;
    background-color: var(--control-buttons-bg-color);
}

.control-button {
    margin: .357rem .786rem;
    padding: 0;
    width: 2.143rem;
    height: 2.143rem;
    border: none;
    opacity: 0.75;
    background-color: var(--control-buttons-bg-color);
}

.control-button:hover {
    opacity: 1;
}

.control-wrapper > input[type=checkbox] {
    display: none;
}

.control-wrapper > label {
    display: inline-block;
}

.control-button > svg {
    fill: var(--svg-button-fill);
}

.control-wrapper > input[type=checkbox].two-images:checked + label > svg.image-on {
    display: block;
}

.control-wrapper > input[type=checkbox].two-images:not(:checked) + label > svg.image-on {
    display: none;
}

.control-wrapper > input[type=checkbox].two-images:checked + label > svg.image-off {
    display: none;
}

.control-wrapper > input[type=checkbox].two-images:not(:checked) + label > svg.image-off {
    display: block;
}

.control-wrapper > input[type=checkbox]:checked + label > svg {
    fill: var(--svg-checkbox-bg-color);
}
