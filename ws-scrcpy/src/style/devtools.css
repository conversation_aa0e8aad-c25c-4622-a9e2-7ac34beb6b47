
body.devtools {
    font-family: <PERSON>bu<PERSON><PERSON>, Arial, sans-serif;
    font-size: 13px;
}

body.devtools .device {
    padding: 20px;
}

body.devtools .device-header {
    -webkit-box-align: baseline;
    -webkit-box-orient: horizontal;
    display: -webkit-box;
    margin: 10px 0 0;
    padding: 2px 0;
}

body.devtools .device-name {
    font-size: 150%;
}

body.devtools .device-serial {
    color: var(--url-color);
    font-size: 80%;
    margin-left: 6px;
}

body.devtools .browser-header {
    align-items: center;
    display: flex;
    flex-flow: row wrap;
    min-height: 33px;
    padding-top: 10px;
}

body.devtools .browser-header > .browser-name {
    font-size: 110%;
    font-weight: bold;
}

body.devtools div.list {
    margin-top: 5px;
}

body.devtools div.list > .row {
    padding: 6px 0;
    position: relative;
}

body.devtools .properties-box {
    display: flex;
}

body.devtools .properties-box > img {
    flex-shrink: 0;
    height: 23px;
    padding-left: 2px;
    padding-right: 5px;
    vertical-align: top;
    width: 23px;
}

body.devtools .subrow-box {
    display: inline-block;
    vertical-align: top;
}

body.devtools .subrow {
    display: flex;
    flex-flow: row wrap;
}

body.devtools .subrow > div {
    margin-right: 0.5em;
}

.body.devtools url {
    color: var(--url-color);
    max-width: 200px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

body.devtools .action {
    color: var(--link-color);
    cursor: pointer;
    margin-right: 15px;
}

body.devtools .action.disabled {
    color: var(--url-color);
    cursor: not-allowed;
}

body.devtools a.action {
    text-decoration: none;
}

body.devtools a.action.copy {
    cursor: copy;
}

body.devtools .browser-header .action {
    margin-left: 10px;
}

body.devtools .open > input {
    border: 1px solid #aaa;
    height: 17px;
    line-height: 17px;
    margin-left: 20px;
    padding: 0 2px;
}

body.devtools .tooltip {
    z-index: 1;
    position: absolute;
    padding: 2px;
    color: var(--controls-bg-color);
    background-color: var(--text-color);
}
