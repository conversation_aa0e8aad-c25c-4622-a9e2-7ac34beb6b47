{"name": "ws-scrcpy", "version": "0.9.0-dev", "description": "Web client for scrcpy and more", "scripts": {"clean": "npx rimraf dist", "dist:dev": "webpack --config webpack/ws-scrcpy.dev.ts --stats-error-details", "dist:prod": "webpack --config webpack/ws-scrcpy.prod.ts --stats-error-details", "dist": "npm run dist:prod", "start": "npm run dist && cd dist && npm start", "build": "cd dist && rm -rf * && cd .. && npm run dist && npx rimraf ../src/phoneInterface/* && cp -r dist/ ../src/phoneInterface/", "script:dist:start": "node ./index.js", "lint": "eslint src/ --ext .ts", "format": "eslint src/ --fix --ext .ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@dead50f7/adbkit": "^2.11.4", "express": "^4.21.2", "ios-device-lib": "^0.9.2", "node-mjpeg-proxy": "^0.3.2", "node-pty": "^0.10.1", "portfinder": "^1.0.28", "tslib": "^2.3.1", "ws": "^8.18.0", "yaml": "^2.2.2"}, "devDependencies": {"@dead50f7/generate-package-json-webpack-plugin": "^2.6.1", "@types/bluebird": "^3.5.36", "@types/dom-webcodecs": "^0.1.3", "@types/express": "^4.17.13", "@types/node": "^12.20.47", "@types/node-forge": "^0.10.0", "@types/npmlog": "^4.1.4", "@types/webpack-node-externals": "^2.5.3", "@types/ws": "^7.4.7", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "buffer": "^6.0.3", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-progress": "0.0.1", "file-loader": "^6.2.0", "h264-converter": "^0.1.4", "html-webpack-plugin": "^5.5.0", "ifdef-loader": "^2.3.2", "mini-css-extract-plugin": "^2.6.1", "mkdirp": "^1.0.4", "path-browserify": "^1.0.1", "prettier": "^2.6.2", "recursive-copy": "^2.0.14", "rimraf": "^3.0.0", "svg-inline-loader": "^0.8.2", "sylvester.js": "^0.1.1", "tinyh264": "^0.0.7", "ts-loader": "^9.3.1", "ts-node": "^10.9.1", "typescript": "^4.7.4", "webpack": "^5.94.0", "webpack-cli": "^4.10.0", "webpack-node-externals": "^2.5.2", "worker-loader": "^3.0.8", "xterm": "^4.5.0", "xterm-addon-attach": "^0.6.0", "xterm-addon-fit": "^0.5.0"}, "optionalDependencies": {"appium-xcuitest-driver": "^3.62.0"}}