# RuyiCloudIDE

> User-friendly task development tools for ruyi agent, built on web frontend and backend technologies.

## Preparation

### Frontend

- Install [Node.js](https://nodejs.org/en/download/package-manager) (use a version no later than node17, recommended to install node16)
- Install project dependencies

```shell
cd frontend
npm install
```

### Backend

- Install appium and start a appium process to record phone information.

```shell
npm i -g appium
appium driver install uiautomator2
appium
```

- Install [scrcpy](https://github.com/Genymobile/scrcpy)
- Install conda environment

```shell
cd backend
conda env create -f environment.yml
conda activate ruyi_ide
```

## Run

### Frontend

```shell
cd frontend
npm start
```

### Backend

- Set OpenAI API url/key in environment variables `OPENAI_API_URL`/`OPENAI_API_KEY`
- Start an Android emulator or connect an Android device to your machine via ADB.
- Set the `device_name` and `device_id` variables in the `flask_app.py` file to the name and id of your Android device.
- Start the flask app.

```shell
cd backend
python flask_app.py
```
