const { exec } = require('child_process');
const path = require('path');

// 配置信息
const config = {
    server: 'wisewk.com',
    username: 'root',
    remotePath: '/var/www/updates/ruyi-ide',
    localPath: path.join(__dirname, '../out')
};

// 获取版本号从 package.json
const packageJson = require('../package.json');
const version = packageJson.version;

// 根据平台生成需要上传的文件
let files = [];
if (process.platform === 'darwin') {
  files = [
    `"${config.localPath}/Ruyi Studio-${version}-arm64.dmg"`,
    `"${config.localPath}/Ruyi Studio-${version}-arm64.dmg.blockmap"`,
    `"${config.localPath}/Ruyi Studio-${version}-arm64.zip"`,
    `"${config.localPath}/Ruyi Studio-${version}-arm64.zip.blockmap"`,
    `"${config.localPath}/latest-mac.yml"`
  ];
} else if (process.platform === 'win32') {
  files = [
    `"${config.localPath}/Ruyi Studio Setup ${version}.exe"`,
    `"${config.localPath}/Ruyi Studio Setup ${version}.exe.blockmap"`,
    `"${config.localPath}/Ruyi Studio ${version}.exe"`,
    `"${config.localPath}/latest.yml"`
  ];
} else if (process.platform === 'linux') {
  files = [
    `"${config.localPath}/Ruyi Studio-${version}.AppImage"`,
    `"${config.localPath}/Ruyi Studio-${version}.deb"`,
    `"${config.localPath}/Ruyi Studio-${version}.rpm"`,
    `"${config.localPath}/latest-linux.yml"`
  ];
}

console.log('\n准备上传以下文件:');
files.forEach(file => console.log(`- ${file.replace(/"/g, '')}`));
console.log(`\n目标服务器: ${config.username}@${config.server}:${config.remotePath}\n`);

// 使用 scp 直接上传文件
const scpCommand = `scp ${files.join(' ')} ${config.username}@${config.server}:${config.remotePath}`;

console.log('开始上传...\n');

// 创建子进程并实时显示输出
const uploadProcess = exec(scpCommand);

// 将 stdout 和 stderr 输出重定向到控制台
uploadProcess.stdout.on('data', (data) => {
    process.stdout.write(data);
});

uploadProcess.stderr.on('data', (data) => {
    process.stderr.write(data);
});

// 监听进程结束
uploadProcess.on('close', (code) => {
    if (code !== 0) {
        console.error('\n❌ 上传失败');
        return;
    }
    
    console.log('\n✅ 文件上传成功！');
    console.log(`版本 ${version} 的更新文件已上传到更新服务器`);
    console.log(`更新服务器地址: http://${config.server}/updates/ruyi-ide`);
    if (process.platform === 'darwin') {
        console.log('Mac 版本更新文件已上传');
    } else if (process.platform === 'win32') {
        console.log('Windows 版本更新文件已上传');
    } else if (process.platform === 'linux') {
        console.log('Linux 版本更新文件已上传');
    }
    console.log();
});