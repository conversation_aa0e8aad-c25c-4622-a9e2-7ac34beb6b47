const { execSync, exec } = require('child_process');
const path = require('path');
const util = require('util');
const execPromise = util.promisify(exec);

async function executeCommand(command) {
  try {
    const { stdout, stderr } = await execPromise(command);
    console.log('执行命令:', command);
    console.log('输出:', stdout);
    if (stderr) console.warn('警告:', stderr);
    return true;
  } catch (error) {
    console.error('命令执行失败:', command);
    console.error('错误信息:', error.message);
    return false;
  }
}

async function cleanExtraFiles(appPath) {
  // 删除所有以 ._ 开头的隐藏文件
  const deleteCommand = `find "${appPath}" -name "._*" -delete`;
  await executeCommand(deleteCommand);
  
  // 增强型清理：使用rsync复制应用目录以去除所有元数据
  const tempPath = path.join(path.dirname(appPath), 'temp.app');
  await executeCommand(`rsync -a --delete "${appPath}/" "${tempPath}"`);
  await executeCommand(`rm -rf "${appPath}"`);
  await executeCommand(`mv "${tempPath}" "${appPath}"`);

  // 使用更全面的xattr清理
  await executeCommand(`xattr -cr "${appPath}"`);
  await executeCommand(`xattr -dr com.apple.FinderInfo "${appPath}"`);
  await executeCommand(`xattr -dr com.apple.ResourceFork "${appPath}"`);
}

async function main() {
  try {
    const appPath = 'out/mac-arm64/Ruyi IDE.app';
    const frameworksPath = `${appPath}/Contents/Frameworks`;
    
    // 1. 清理所有 xattr 和隐藏文件（增强清理顺序）
    console.log('清理 xattr 和隐藏文件...');
    await executeCommand('xattr -cr out/mac-arm64/Ruyi\\ IDE.app');
    await cleanExtraFiles(appPath);
    
    // 2. 签名顺序调整：先签名最深层组件
    console.log('开始签名 Helper 应用...');
    const helperApps = [
      // 调整顺序：从最深层开始签名
      'Ruyi IDE Helper (Renderer).app',
      'Ruyi IDE Helper (Plugin).app',
      'Ruyi IDE Helper (GPU).app',
      'Ruyi IDE Helper.app'
    ];
    
    // 在签名命令中添加--timestamp参数
    for (const helper of helperApps) {
      console.log(`\n正在签名 ${helper}...`);
      const success = await executeCommand(
        `codesign --force --timestamp --options runtime --sign "Shanhui Zhao (2QL2QZNHGZ)" --entitlements "resources/entitlements.mac.plist" "${frameworksPath}/${helper}" -v --verbose=4`
      );
      if (!success) {
        console.warn(`警告: ${helper} 签名失败，继续下一个文件`);
      }
    }
    
    // 3. 签名 Frameworks
    console.log('\n开始签名 Frameworks...');
    const frameworks = [
      'Electron Framework.framework',
      'Mantle.framework',
      'ReactiveObjC.framework',
      'Squirrel.framework'
    ];
    
    for (const framework of frameworks) {
      console.log(`\n正在签名 ${framework}...`);
      const success = await executeCommand(
        `codesign --force --options runtime --sign "Shanhui Zhao (2QL2QZNHGZ)" --deep "${frameworksPath}/${framework}" -v --verbose=4`
      );
      if (!success) {
        console.warn(`警告: ${framework} 签名失败，继续下一个文件`);
      }
    }
    
    // 4. 签名主应用时移除--deep参数
    console.log('\n开始签名主应用...');
    const mainAppSuccess = await executeCommand(
      `codesign --force --timestamp --options runtime --sign "Shanhui Zhao (2QL2QZNHGZ)" --entitlements "resources/entitlements.mac.plist" "${appPath}" -v --verbose=4`
    );
    if (!mainAppSuccess) {
      throw new Error('主应用签名失败');
    }
    
    // 5. 验证签名
    console.log('\n验证签名...');
    const verifySuccess = await executeCommand(
      `codesign --verify --deep --strict --verbose=4 "${appPath}"`
    );
    if (!verifySuccess) {
      throw new Error('签名验证失败');
    }
    
    // 6. 打包 DMG 和 ZIP
    console.log('\n开始打包 DMG 和 ZIP...');
    await executeCommand(
      'electron-builder --config.mac.target=dmg --config.mac.target=zip --arm64'
    );
    
    console.log('\n所有步骤完成！');
  } catch (error) {
    console.error('发生错误:', error);
    process.exit(1);
  }
}

main(); 